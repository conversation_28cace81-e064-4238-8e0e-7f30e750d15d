2025-08-04 16:15:45.790	INFO	src/parameter.go:174	businessConf:
role: master
fileSavePath: /data/projects/back_to_the_realm_v2/thirdparty/model_pool_go/op/files
cluster: ""
ip: **********
port: "10013"
httpPort: "10014"
name: ""
maxStorage: 1073741824
hashCheck: crc32
statisticsBufferSize: 500
neighborsPerNode: 16
maxConcurrentPushes: 3
fetchHistoricalFiles: false
useLruFileDelete: false
downloadFromMasterIfMissing: false
fileSizeParameters:
    - 4MB 2s 3 16 5s 2m
    - 8MB 2s 4 12 6s 2m
    - 16MB 3s 5 10 7s 2m
    - 32MB 4s 6 8 8s 2m
    - 64MB 5s 7 6 14s 3m
    - 128MB 6s 8 6 20s 5m
    - 256MB 8s 8 6 36s 9m
    - 512MB 10s 8 6 1m 15m
    - 1GB 10s 16 5 2m 20m
    - 2GB 10s 32 5 4m 40m
    - 4GB 10s 64 5 6m 80m
    - 8GB 10s 128 5 8m 160m
    - 16GB 10s 256 5 8m 320m
    - 32GB 10s 512 5 8m 640m
    - 1TB 10s 3000 5 8m 3000m

2025-08-04 16:15:45.791	INFO	server/service.go:130	process:33175, trpc service:trpc.modelpool.main.ModelPool launch success, tcp:0.0.0.0:10013, serving ...
2025-08-04 16:15:45.791	INFO	server/service.go:130	process:33175, http service:trpc.modelpool.main.ModelPoolHTTP launch success, tcp:0.0.0.0:10014, serving ...
2025-08-04 16:16:02.465	INFO	src/httpFileTransport.go:202	[modelpool-debug] httpUpload time cost: 115.120407ms

2025-08-04 16:16:02.465	INFO	src/master.go:61	pushing file c_GeWJuPTHyD4B3NzVroxr3zTDSIzjDg, size: 5.9291MB, key: model.ckpt_back_to_the_realm_v2_ppo_0, filename: kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz, hash: crc32:MrQ_Wg
2025-08-04 16:16:02.483	INFO	src/fileStorage.go:523	[modelpool-debug] _deleteFiles time cost: 2.599µs

2025-08-04 16:16:02.484	INFO	src/delete.go:18	[modelpool-debug] Delete time cost: 776.811µs

