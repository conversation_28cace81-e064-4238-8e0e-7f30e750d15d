# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: arena2plat.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x61rena2plat.proto\x12\x05\x61rena\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf7\x01\n\x08GameData\x12\x0c\n\x04name\x18\x01 \x01(\t\x12!\n\x06status\x18\x02 \x01(\x0e\x32\x11.arena.GameStatus\x12\x16\n\x0estatus_message\x18\x03 \x01(\t\x12\x14\n\x0cproject_code\x18\x04 \x01(\t\x12.\n\nstart_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1e\n\x05\x63\x61mps\x18\x10 \x03(\x0b\x32\x0f.arena.CampInfo\x12\x0e\n\x06\x66rames\x18\x11 \x01(\t\"V\n\x08\x43\x61mpInfo\x12\x11\n\tcamp_type\x18\x01 \x01(\t\x12\x11\n\tcamp_code\x18\x02 \x01(\t\x12\x12\n\nstart_info\x18\x03 \x01(\t\x12\x10\n\x08\x65nd_info\x18\x04 \x01(\t*?\n\nGameStatus\x12\x0b\n\x07unknown\x10\x00\x12\x0b\n\x07success\x10\x01\x12\t\n\x05\x65rror\x10\x02\x12\x0c\n\x08overtime\x10\x03\x42HZFgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_protob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'arena2plat_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZFgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto'
  _globals['_GAMESTATUS']._serialized_start=398
  _globals['_GAMESTATUS']._serialized_end=461
  _globals['_GAMEDATA']._serialized_start=61
  _globals['_GAMEDATA']._serialized_end=308
  _globals['_CAMPINFO']._serialized_start=310
  _globals['_CAMPINFO']._serialized_end=396
# @@protoc_insertion_point(module_scope)
