# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: scene.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bscene.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\"G\n\x04\x43\x61ke\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12-\n\x08\x63ollider\x18\x02 \x02(\x0b\x32\x1b.sgame_state.SphereCollider\"\xc0\x01\n\x06\x42ullet\x12\x12\n\nruntime_id\x18\x01 \x02(\x05\x12%\n\x04\x63\x61mp\x18\x02 \x02(\x0e\x32\x17.sgame_state.PLAYERCAMP\x12\x14\n\x0csource_actor\x18\x03 \x02(\x05\x12-\n\tslot_type\x18\x04 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x10\n\x08skill_id\x18\x05 \x02(\x05\x12$\n\x08location\x18\x06 \x02(\x0b\x32\x12.sgame_state.VInt3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'scene_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_CAKE']._serialized_start=42
  _globals['_CAKE']._serialized_end=113
  _globals['_BULLET']._serialized_start=116
  _globals['_BULLET']._serialized_end=308
# @@protoc_insertion_point(module_scope)
