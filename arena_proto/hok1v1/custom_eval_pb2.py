# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom_eval.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x63ustom_eval.proto\x12\x0c\x61rena.hok1v1\"J\n\tMoneyFrom\x12\x0f\n\x07soldier\x18\x01 \x01(\x02\x12\x0f\n\x07monster\x18\x02 \x01(\x02\x12\r\n\x05tower\x18\x03 \x01(\x02\x12\x0c\n\x04hero\x18\x04 \x01(\x02\"\xf0\x01\n\tStartInfo\x12\x0c\n\x04mode\x18\x01 \x01(\t\x12)\n\tcamp_type\x18\x02 \x01(\x0e\x32\x16.arena.hok1v1.CampType\x12\x37\n\x06heroes\x18\x03 \x03(\x0b\x32\'.arena.hok1v1.StartInfo.HeroStartConfig\x12\x12\n\nextra_info\x18\x04 \x01(\t\x1a]\n\x0fHeroStartConfig\x12\x0f\n\x07hero_id\x18\x01 \x01(\t\x12\x11\n\tskill_ids\x18\x02 \x03(\t\x12\x12\n\ncontroller\x18\x03 \x01(\t\x12\x12\n\nextra_info\x18\x04 \x01(\t\"\xa7\x08\n\x07\x45ndInfo\x12)\n\tcamp_type\x18\x01 \x01(\x0e\x32\x16.arena.hok1v1.CampType\x12\x0f\n\x07win_cnt\x18\x02 \x01(\x02\x12\r\n\x05\x66rame\x18\x03 \x01(\x02\x12\x0c\n\x04step\x18\x04 \x01(\x02\x12\x33\n\x06heroes\x18\x05 \x03(\x0b\x32#.arena.hok1v1.EndInfo.HeroDetailHOK\x12\r\n\x05money\x18\x06 \x01(\x02\x12+\n\nmoney_from\x18\x07 \x01(\x0b\x32\x17.arena.hok1v1.MoneyFrom\x12\x0b\n\x03\x65xp\x18\x08 \x01(\x02\x12\x10\n\x08kill_cnt\x18\t \x01(\x02\x12\x12\n\nassist_cnt\x18\n \x01(\x02\x12\x10\n\x08\x64\x65\x61\x64_cnt\x18\x0b \x01(\x02\x12\x12\n\ntotal_hurt\x18\x0c \x01(\x02\x12\x14\n\x0churt_by_hero\x18\r \x01(\x02\x12\x14\n\x0churt_to_hero\x18\x0e \x01(\x02\x12\x19\n\x11\x64\x65stroy_tower_cnt\x18\x0f \x01(\x02\x12\x17\n\x0fkill_baojun_cnt\x18\x10 \x01(\x02\x12\x1b\n\x13kill_big_dragon_cnt\x18\x11 \x01(\x02\x12 \n\x18kill_dark_big_dragon_cnt\x18\x12 \x01(\x02\x12\x18\n\x10kill_monster_cnt\x18\x13 \x01(\x02\x12\x1a\n\x12kill_blue_buff_cnt\x18\x14 \x01(\x02\x12\x19\n\x11kill_red_buff_cnt\x18\x15 \x01(\x02\x12\x18\n\x10kill_soldier_cnt\x18\x16 \x01(\x02\x12\x13\n\x0blineup_code\x18\x17 \x01(\t\x12\x0b\n\x03kda\x18\x18 \x01(\x02\x1a\xcc\x03\n\rHeroDetailHOK\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12\r\n\x05money\x18\x02 \x01(\x02\x12+\n\nmoney_from\x18\x03 \x01(\x0b\x32\x17.arena.hok1v1.MoneyFrom\x12\x0b\n\x03\x65xp\x18\x04 \x01(\x02\x12\x10\n\x08kill_cnt\x18\x05 \x01(\x02\x12\x12\n\nassist_cnt\x18\x06 \x01(\x02\x12\x10\n\x08\x64\x65\x61\x64_cnt\x18\x07 \x01(\x02\x12\x12\n\ntotal_hurt\x18\x08 \x01(\x02\x12\x14\n\x0churt_by_hero\x18\t \x01(\x02\x12\x14\n\x0churt_to_hero\x18\n \x01(\x02\x12\x19\n\x11\x64\x65stroy_tower_cnt\x18\x0b \x01(\x02\x12\x17\n\x0fkill_baojun_cnt\x18\x0c \x01(\x02\x12\x1b\n\x13kill_big_dragon_cnt\x18\r \x01(\x02\x12 \n\x18kill_dark_big_dragon_cnt\x18\x0e \x01(\x02\x12\x18\n\x10kill_monster_cnt\x18\x0f \x01(\x02\x12\x1a\n\x12kill_blue_buff_cnt\x18\x10 \x01(\x02\x12\x19\n\x11kill_red_buff_cnt\x18\x11 \x01(\x02\x12\x18\n\x10kill_soldier_cnt\x18\x12 \x01(\x02\x12\x0b\n\x03kda\x18\x13 \x01(\x02\"\xc3\x13\n\x05\x46rame\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\'\n\x05\x63\x61mps\x18\x02 \x03(\x0b\x32\x18.arena.hok1v1.Frame.Camp\x12(\n\x06heroes\x18\x03 \x03(\x0b\x32\x18.arena.hok1v1.Frame.Hero\x12-\n\x08monsters\x18\x04 \x03(\x0b\x32\x1b.arena.hok1v1.Frame.Monster\x12)\n\x06organs\x18\x05 \x03(\x0b\x32\x19.arena.hok1v1.Frame.Organ\x12\'\n\x05\x63\x61kes\x18\x06 \x03(\x0b\x32\x18.arena.hok1v1.Frame.Cake\x12\x11\n\tmap_state\x18\x07 \x01(\x08\x12\x41\n\x13monster_spawn_infos\x18\x08 \x03(\x0b\x32$.arena.hok1v1.Frame.MonsterSpawnInfo\x1a\xe3\x03\n\x04\x43\x61mp\x12)\n\tcamp_type\x18\x01 \x01(\x0e\x32\x16.arena.hok1v1.CampType\x12\x0b\n\x03\x65xp\x18\x02 \x01(\x05\x12\x10\n\x08kill_cnt\x18\x03 \x01(\x05\x12\x10\n\x08\x64\x65\x61\x64_cnt\x18\x04 \x01(\x05\x12\x12\n\nassist_cnt\x18\x05 \x01(\x05\x12\x12\n\ntotal_hurt\x18\x06 \x01(\x05\x12\x14\n\x0churt_by_hero\x18\x07 \x01(\x05\x12\x14\n\x0churt_to_hero\x18\x08 \x01(\x05\x12\r\n\x05money\x18\t \x01(\x05\x12+\n\nmoney_from\x18\n \x01(\x0b\x32\x17.arena.hok1v1.MoneyFrom\x12\x11\n\tmoney_cnt\x18\x0b \x01(\x05\x12\x19\n\x11\x64\x65stroy_tower_cnt\x18\x0c \x01(\x05\x12\x17\n\x0fkill_baojun_cnt\x18\r \x01(\x05\x12\x1b\n\x13kill_big_dragon_cnt\x18\x0e \x01(\x05\x12 \n\x18kill_dark_big_dragon_cnt\x18\x0f \x01(\x05\x12\x18\n\x10kill_monster_cnt\x18\x10 \x01(\x05\x12\x1a\n\x12kill_blue_buff_cnt\x18\x11 \x01(\x05\x12\x19\n\x11kill_red_buff_cnt\x18\x12 \x01(\x05\x12\x18\n\x10kill_soldier_cnt\x18\x13 \x01(\x05\x1a#\n\x0b\x43urPosition\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01z\x18\x02 \x01(\x05\x1a\xf4\x01\n\nSkillState\x12\x11\n\tconfig_id\x18\x01 \x01(\x05\x12\x0e\n\x06usable\x18\x02 \x01(\x08\x12\r\n\x05level\x18\x03 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x04 \x01(\x05\x12\x14\n\x0c\x63ooldown_max\x18\x05 \x01(\x05\x12\x1a\n\x12succ_used_in_frame\x18\x06 \x01(\x05\x12\x16\n\x0enext_config_id\x18\x07 \x01(\x05\x12\x16\n\x0ehit_hero_times\x18\x08 \x01(\x05\x12\x19\n\x11\x63ombo_effect_time\x18\t \x01(\x05\x12\x12\n\nused_times\x18\n \x01(\x05\x12\x11\n\tslot_type\x18\x0b \x01(\x05\x1a\x8c\x06\n\x04Hero\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12)\n\tcamp_type\x18\x02 \x01(\x0e\x32\x16.arena.hok1v1.CampType\x12\r\n\x05level\x18\x03 \x01(\x05\x12\r\n\x05money\x18\x04 \x01(\x05\x12\n\n\x02hp\x18\x05 \x01(\x05\x12\x0e\n\x06max_hp\x18\x06 \x01(\x05\x12\x0b\n\x03\x65xp\x18\x07 \x01(\x05\x12\x13\n\x0brevive_time\x18\x08 \x01(\x05\x12\x12\n\nruntime_id\x18\t \x01(\x05\x12\x10\n\x08kill_cnt\x18\n \x01(\x05\x12\x12\n\nassist_cnt\x18\x0b \x01(\x05\x12\x10\n\x08\x64\x65\x61\x64_cnt\x18\x0c \x01(\x05\x12\x12\n\ntotal_hurt\x18\r \x01(\x05\x12\x14\n\x0churt_by_hero\x18\x0e \x01(\x05\x12\x14\n\x0churt_to_hero\x18\x0f \x01(\x05\x12\x19\n\x11\x64\x65stroy_tower_cnt\x18\x10 \x01(\x05\x12\x17\n\x0fkill_baojun_cnt\x18\x11 \x01(\x05\x12\x1b\n\x13kill_big_dragon_cnt\x18\x12 \x01(\x05\x12 \n\x18kill_dark_big_dragon_cnt\x18\x13 \x01(\x05\x12\x18\n\x10kill_monster_cnt\x18\x14 \x01(\x05\x12\x1a\n\x12kill_blue_buff_cnt\x18\x15 \x01(\x05\x12\x19\n\x11kill_red_buff_cnt\x18\x16 \x01(\x05\x12\x18\n\x10kill_soldier_cnt\x18\x17 \x01(\x05\x12\x35\n\x0c\x63ur_position\x18\x18 \x01(\x0b\x32\x1f.arena.hok1v1.Frame.CurPosition\x12+\n\nmoney_from\x18\x19 \x01(\x0b\x32\x17.arena.hok1v1.MoneyFrom\x12\x34\n\x0cskill_states\x18\x1a \x03(\x0b\x32\x1e.arena.hok1v1.Frame.SkillState\x12\x11\n\tmoney_cnt\x18\x1b \x01(\x05\x12\n\n\x02\x65p\x18\x1c \x01(\x05\x12\x0e\n\x06max_ep\x18\x1d \x01(\x05\x12\x39\n\x10\x66orward_position\x18\x1e \x01(\x0b\x32\x1f.arena.hok1v1.Frame.CurPosition\x1a\xc0\x01\n\x07Monster\x12)\n\tcamp_type\x18\x01 \x01(\x0e\x32\x16.arena.hok1v1.CampType\x12\x11\n\tconfig_id\x18\x02 \x01(\x05\x12\n\n\x02hp\x18\x03 \x01(\x05\x12\x0e\n\x06max_hp\x18\x04 \x01(\x05\x12\x12\n\nruntime_id\x18\x05 \x01(\x05\x12\x10\n\x08sub_type\x18\x06 \x01(\x05\x12\x35\n\x0c\x63ur_position\x18\x07 \x01(\x0b\x32\x1f.arena.hok1v1.Frame.CurPosition\x1a\xbe\x01\n\x05Organ\x12)\n\tcamp_type\x18\x01 \x01(\x0e\x32\x16.arena.hok1v1.CampType\x12\x11\n\tconfig_id\x18\x02 \x01(\x05\x12\n\n\x02hp\x18\x03 \x01(\x05\x12\x0e\n\x06max_hp\x18\x04 \x01(\x05\x12\x12\n\nruntime_id\x18\x05 \x01(\x05\x12\x10\n\x08sub_type\x18\x06 \x01(\x05\x12\x35\n\x0c\x63ur_position\x18\x07 \x01(\x0b\x32\x1f.arena.hok1v1.Frame.CurPosition\x1at\n\x04\x43\x61ke\x12\x11\n\tconfig_id\x18\x01 \x01(\x05\x12\x35\n\x0c\x63ur_position\x18\x02 \x01(\x0b\x32\x1f.arena.hok1v1.Frame.CurPosition\x12\x0e\n\x06radius\x18\x03 \x01(\x05\x12\x12\n\nruntime_id\x18\x04 \x01(\x05\x1aq\n\x10MonsterSpawnInfo\x12\x11\n\tconfig_id\x18\x01 \x01(\x05\x12\x13\n\x0bspawn_timer\x18\x02 \x01(\x05\x12\x35\n\x0c\x63ur_position\x18\x03 \x01(\x0b\x32\x1f.arena.hok1v1.Frame.CurPosition\"-\n\x06\x46rames\x12#\n\x06\x66rames\x18\x02 \x03(\x0b\x32\x13.arena.hok1v1.Frame*\'\n\x08\x43\x61mpType\x12\x08\n\x04type\x10\x00\x12\x08\n\x04\x62lue\x10\x01\x12\x07\n\x03red\x10\x02\x42OZMgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/hok1v1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_eval_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZMgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/hok1v1'
  _globals['_CAMPTYPE']._serialized_start=3969
  _globals['_CAMPTYPE']._serialized_end=4008
  _globals['_MONEYFROM']._serialized_start=35
  _globals['_MONEYFROM']._serialized_end=109
  _globals['_STARTINFO']._serialized_start=112
  _globals['_STARTINFO']._serialized_end=352
  _globals['_STARTINFO_HEROSTARTCONFIG']._serialized_start=259
  _globals['_STARTINFO_HEROSTARTCONFIG']._serialized_end=352
  _globals['_ENDINFO']._serialized_start=355
  _globals['_ENDINFO']._serialized_end=1418
  _globals['_ENDINFO_HERODETAILHOK']._serialized_start=958
  _globals['_ENDINFO_HERODETAILHOK']._serialized_end=1418
  _globals['_FRAME']._serialized_start=1421
  _globals['_FRAME']._serialized_end=3920
  _globals['_FRAME_CAMP']._serialized_start=1749
  _globals['_FRAME_CAMP']._serialized_end=2232
  _globals['_FRAME_CURPOSITION']._serialized_start=2234
  _globals['_FRAME_CURPOSITION']._serialized_end=2269
  _globals['_FRAME_SKILLSTATE']._serialized_start=2272
  _globals['_FRAME_SKILLSTATE']._serialized_end=2516
  _globals['_FRAME_HERO']._serialized_start=2519
  _globals['_FRAME_HERO']._serialized_end=3299
  _globals['_FRAME_MONSTER']._serialized_start=3302
  _globals['_FRAME_MONSTER']._serialized_end=3494
  _globals['_FRAME_ORGAN']._serialized_start=3497
  _globals['_FRAME_ORGAN']._serialized_end=3687
  _globals['_FRAME_CAKE']._serialized_start=3689
  _globals['_FRAME_CAKE']._serialized_end=3805
  _globals['_FRAME_MONSTERSPAWNINFO']._serialized_start=3807
  _globals['_FRAME_MONSTERSPAWNINFO']._serialized_end=3920
  _globals['_FRAMES']._serialized_start=3922
  _globals['_FRAMES']._serialized_end=3967
# @@protoc_insertion_point(module_scope)
