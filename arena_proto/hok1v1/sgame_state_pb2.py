# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_state.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import hero_pb2 as hero__pb2
from . import scene_pb2 as scene__pb2
from . import sgame_action_pb2 as sgame__action__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11sgame_state.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\nhero.proto\x1a\x0bscene.proto\x1a\x12sgame_action.proto\"\xaf\x02\n\nFrameState\x12\x0f\n\x07\x66rameNo\x18\x01 \x02(\x05\x12+\n\x0bhero_states\x18\x02 \x03(\x0b\x32\x16.sgame_state.HeroState\x12+\n\nnpc_states\x18\x03 \x03(\x0b\x32\x17.sgame_state.ActorState\x12$\n\x07\x62ullets\x18\x04 \x03(\x0b\x32\x13.sgame_state.Bullet\x12 \n\x05\x63\x61kes\x18\x05 \x03(\x0b\x32\x11.sgame_state.Cake\x12+\n\x0b\x65quip_infos\x18\x06 \x03(\x0b\x32\x16.sgame_state.EquipInfo\x12.\n\x0c\x66rame_action\x18\n \x01(\x0b\x32\x18.sgame_state.FrameAction\x12\x11\n\tmap_state\x18\x0b \x01(\x08\"5\n\nFrameSlice\x12\'\n\x06\x66rames\x18\x01 \x03(\x0b\x32\x17.sgame_state.FrameState\"G\n\x0c\x43\x61mpStatInfo\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\x05\x12\r\n\x05score\x18\x02 \x02(\x05\x12\r\n\x05money\x18\x03 \x02(\x05\x12\x0b\n\x03\x65xp\x18\x04 \x02(\x05\"\xcb\x02\n\x0cHeroStatInfo\x12\x12\n\nruntime_id\x18\x01 \x02(\x05\x12\r\n\x05score\x18\x02 \x02(\x05\x12\r\n\x05money\x18\x03 \x02(\x05\x12\x1d\n\x15totalHurtToHeroOrigin\x18\x04 \x01(\x05\x12\x1f\n\x17totalBeHurtByHeroOrigin\x18\x05 \x01(\x05\x12\x17\n\x0f\x64\x65stroyTowerCnt\x18\x06 \x01(\x05\x12\x16\n\x0ekillSoidierCnt\x18\x07 \x01(\x05\x12\x16\n\x0eKillMonsterCnt\x18\x08 \x01(\x05\x12\x1b\n\x13KillLittleDragonCnt\x18\t \x01(\x05\x12\x1d\n\x15Hero1KillBigDragonCnt\x18\n \x01(\x05\x12\x17\n\x0fSelfKillDarkCnt\x18\x0b \x01(\x05\x12\x14\n\x0cTotalRedBuff\x18\x0c \x01(\x05\x12\x15\n\rTotalBlueBuff\x18\r \x01(\x05\"r\n\nCloseState\x12\x31\n\x0e\x63\x61mp_stat_info\x18\x01 \x03(\x0b\x32\x19.sgame_state.CampStatInfo\x12\x31\n\x0ehero_stat_info\x18\x02 \x03(\x0b\x32\x19.sgame_state.HeroStatInfo\"G\n\tEquipInfo\x12\x10\n\x08\x65quip_id\x18\x01 \x01(\x05\x12\x13\n\x0b\x65quip_price\x18\x02 \x01(\x05\x12\x13\n\x0b\x65quip_atoms\x18\x03 \x03(\x05\"c\n\x10MonsterSpawnInfo\x12\x12\n\nmonster_id\x18\x01 \x01(\x05\x12\x13\n\x0bspawn_timer\x18\x02 \x01(\x05\x12\x12\n\nborn_pos_x\x18\x03 \x01(\x05\x12\x12\n\nborn_pos_z\x18\x04 \x01(\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_state_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FRAMESTATE']._serialized_start=94
  _globals['_FRAMESTATE']._serialized_end=397
  _globals['_FRAMESLICE']._serialized_start=399
  _globals['_FRAMESLICE']._serialized_end=452
  _globals['_CAMPSTATINFO']._serialized_start=454
  _globals['_CAMPSTATINFO']._serialized_end=525
  _globals['_HEROSTATINFO']._serialized_start=528
  _globals['_HEROSTATINFO']._serialized_end=859
  _globals['_CLOSESTATE']._serialized_start=861
  _globals['_CLOSESTATE']._serialized_end=975
  _globals['_EQUIPINFO']._serialized_start=977
  _globals['_EQUIPINFO']._serialized_end=1048
  _globals['_MONSTERSPAWNINFO']._serialized_start=1050
  _globals['_MONSTERSPAWNINFO']._serialized_end=1149
# @@protoc_insertion_point(module_scope)
