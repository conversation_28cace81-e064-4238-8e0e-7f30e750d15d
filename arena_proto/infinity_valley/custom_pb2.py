# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x0finfinity_valley\"\x99\x01\n\x0bObservation\x12\x30\n\x0b\x66rame_state\x18\x01 \x01(\x0b\x32\x1b.infinity_valley.FrameState\x12,\n\tgame_info\x18\x02 \x01(\x0b\x32\x19.infinity_valley.GameInfo\x12*\n\x08map_info\x18\x03 \x03(\x0b\x32\x18.infinity_valley.MapInfo\"\x19\n\x07MapInfo\x12\x0e\n\x06values\x18\x01 \x03(\x05\"\xc0\x02\n\rValleyFeature\x12\x30\n\x08norm_pos\x18\x01 \x01(\x0b\x32\x1e.infinity_valley.FloatPosition\x12+\n\x08grid_pos\x18\x02 \x01(\x0b\x32\x19.infinity_valley.Position\x12*\n\x07monster\x18\x03 \x03(\x0b\x32\x19.infinity_valley.Monsters\x12\x37\n\x0ctreasure_pos\x18\x04 \x03(\x0b\x32!.infinity_valley.RelativePosition\x12\x16\n\x0etreasure_state\x18\x05 \x03(\x05\x12\x14\n\x0cobstacle_map\x18\x06 \x03(\x05\x12\x12\n\nmemory_map\x18\x07 \x03(\x02\x12\x14\n\x0ctreasure_map\x18\x08 \x03(\x05\x12\x13\n\x0bmonster_map\x18\t \x03(\x05\"R\n\x0bMonsterInfo\x12\x12\n\nmonster_id\x18\x01 \x01(\x05\x12\x15\n\rmonster_speed\x18\x02 \x01(\x05\x12\x18\n\x10monster_interval\x18\x03 \x01(\x05\"\xc1\x01\n\x07\x45ndInfo\x12\r\n\x05\x66rame\x18\x01 \x01(\x05\x12\x0c\n\x04step\x18\x02 \x01(\x02\x12\r\n\x05score\x18\x03 \x01(\x02\x12\x16\n\x0etreasure_count\x18\x04 \x01(\x02\x12\x16\n\x0etotal_treasure\x18\x05 \x01(\x05\x12\x13\n\x0btreasure_id\x18\x06 \x03(\x05\x12-\n\x07monster\x18\x07 \x03(\x0b\x32\x1c.infinity_valley.MonsterInfo\x12\x16\n\x0etreasure_score\x18\x08 \x01(\x02\"?\n\x0fMonsterStartPos\x12,\n\tstart_pos\x18\x01 \x01(\x0b\x32\x19.infinity_valley.Position\"2\n\x08OrganPos\x12&\n\x03pos\x18\x01 \x01(\x0b\x32\x19.infinity_valley.Position\"<\n\x0cHeroStartPos\x12,\n\tstart_pos\x18\x01 \x01(\x0b\x32\x19.infinity_valley.Position\"\xad\x01\n\tStartInfo\x12;\n\x11monster_start_pos\x18\x01 \x03(\x0b\x32 .infinity_valley.MonsterStartPos\x12,\n\torgan_pos\x18\x02 \x03(\x0b\x32\x19.infinity_valley.OrganPos\x12\x35\n\x0ehero_start_pos\x18\x03 \x01(\x0b\x32\x1d.infinity_valley.HeroStartPos\"L\n\tScoreInfo\x12\x12\n\nstep_score\x18\x01 \x01(\x02\x12\x13\n\x0btotal_score\x18\x02 \x01(\x02\x12\x16\n\x0etreasure_score\x18\x03 \x01(\x05\"\x07\n\x05State\"\x1a\n\x06\x41\x63tion\x12\x10\n\x08move_dir\x18\x01 \x01(\x05\"\xaf\x01\n\x10RelativePosition\x12\x35\n\tdirection\x18\x01 \x01(\x0e\x32\".infinity_valley.RelativeDirection\x12\x13\n\x0bl2_distance\x18\x02 \x01(\x02\x12\x38\n\rpath_distance\x18\x03 \x01(\x0e\x32!.infinity_valley.RelativeDistance\x12\x15\n\rgrid_distance\x18\x04 \x01(\x02\"l\n\x08Monsters\x12\x30\n\x08norm_pos\x18\x01 \x01(\x0b\x32\x1e.infinity_valley.FloatPosition\x12.\n\x03pos\x18\x02 \x01(\x0b\x32!.infinity_valley.RelativePosition\"\xc3\x01\n\nFrameState\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x33\n\x06heroes\x18\x02 \x03(\x0b\x32#.infinity_valley.InfinityValleyHero\x12\x38\n\x08monsters\x18\x03 \x03(\x0b\x32&.infinity_valley.InfinityValleyMonster\x12\x34\n\x06organs\x18\x04 \x03(\x0b\x32$.infinity_valley.InfinityValleyOrgan\"\x9b\x01\n\x12InfinityValleyHero\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12&\n\x03pos\x18\x02 \x01(\x0b\x32\x19.infinity_valley.Position\x12\x16\n\x0etreasure_score\x18\x03 \x01(\x02\x12\x12\n\nstep_score\x18\x04 \x01(\x02\x12 \n\x18treasure_collected_count\x18\x05 \x01(\x05\"\xb2\x01\n\x15InfinityValleyMonster\x12\x12\n\nmonster_id\x18\x01 \x01(\x05\x12&\n\x03pos\x18\x02 \x01(\x0b\x32\x19.infinity_valley.Position\x12,\n\tstart_pos\x18\x03 \x01(\x0b\x32\x19.infinity_valley.Position\x12\x15\n\rmonster_speed\x18\x04 \x01(\x05\x12\x18\n\x10monster_interval\x18\x05 \x01(\x05\"r\n\x13InfinityValleyOrgan\x12\x10\n\x08sub_type\x18\x01 \x01(\x05\x12\x11\n\tconfig_id\x18\x02 \x01(\x05\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12&\n\x03pos\x18\x04 \x01(\x0b\x32\x19.infinity_valley.Position\"E\n\x07\x43ommand\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12)\n\x08\x63md_info\x18\x02 \x01(\x0b\x32\x17.infinity_valley.CmdPkg\"0\n\x06\x43mdPkg\x12&\n\x03pos\x18\x01 \x01(\x0b\x32\x19.infinity_valley.Position\"%\n\rFloatPosition\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01z\x18\x02 \x01(\x02\" \n\x08Position\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01z\x18\x02 \x01(\x05\"\xa8\x02\n\x08GameInfo\x12\x13\n\x0btotal_score\x18\x01 \x01(\x02\x12\x0f\n\x07step_no\x18\x02 \x01(\x05\x12\x12\n\nstep_score\x18\x03 \x01(\x02\x12&\n\x03pos\x18\x04 \x01(\x0b\x32\x19.infinity_valley.Position\x12 \n\x18treasure_collected_count\x18\x05 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x06 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x07 \x01(\x05\x12\x13\n\x0btreasure_id\x18\x08 \x03(\x05\x12\x18\n\x10monster_interval\x18\t \x01(\x05\x12\x1b\n\x13\x66irst_monster_speed\x18\n \x01(\x05\x12\x1c\n\x14second_monster_speed\x18\x0b \x01(\x05\"0\n\x06\x46rames\x12&\n\x06\x66rames\x18\x01 \x03(\x0b\x32\x16.infinity_valley.Frame\"\xfb\x01\n\x05\x46rame\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x0f\n\x07step_no\x18\x02 \x01(\x05\x12\x31\n\x04hero\x18\x03 \x01(\x0b\x32#.infinity_valley.InfinityValleyHero\x12\x34\n\x06organs\x18\x04 \x03(\x0b\x32$.infinity_valley.InfinityValleyOrgan\x12\x38\n\x08monsters\x18\x05 \x03(\x0b\x32&.infinity_valley.InfinityValleyMonster\x12,\n\tgame_info\x18\x06 \x01(\x0b\x32\x19.infinity_valley.GameInfo*\x96\x01\n\x11RelativeDirection\x12\x1b\n\x17RELATIVE_DIRECTION_NONE\x10\x00\x12\x08\n\x04\x45\x41ST\x10\x01\x12\r\n\tNORTHEAST\x10\x02\x12\t\n\x05NORTH\x10\x03\x12\r\n\tNORTHWEST\x10\x04\x12\x08\n\x04WEST\x10\x05\x12\r\n\tSOUTHWEST\x10\x06\x12\t\n\x05SOUTH\x10\x07\x12\r\n\tSOUTHEAST\x10\x08*p\n\x10RelativeDistance\x12\x1a\n\x16RELATIVE_DISTANCE_NONE\x10\x00\x12\x0e\n\nVERY_SMALL\x10\x01\x12\t\n\x05SMALL\x10\x02\x12\n\n\x06MEDIUM\x10\x03\x12\t\n\x05LARGE\x10\x04\x12\x0e\n\nVERY_LARGE\x10\x05\x42XZVgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/infinity_valleyb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZVgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/infinity_valley'
  _globals['_RELATIVEDIRECTION']._serialized_start=3028
  _globals['_RELATIVEDIRECTION']._serialized_end=3178
  _globals['_RELATIVEDISTANCE']._serialized_start=3180
  _globals['_RELATIVEDISTANCE']._serialized_end=3292
  _globals['_OBSERVATION']._serialized_start=34
  _globals['_OBSERVATION']._serialized_end=187
  _globals['_MAPINFO']._serialized_start=189
  _globals['_MAPINFO']._serialized_end=214
  _globals['_VALLEYFEATURE']._serialized_start=217
  _globals['_VALLEYFEATURE']._serialized_end=537
  _globals['_MONSTERINFO']._serialized_start=539
  _globals['_MONSTERINFO']._serialized_end=621
  _globals['_ENDINFO']._serialized_start=624
  _globals['_ENDINFO']._serialized_end=817
  _globals['_MONSTERSTARTPOS']._serialized_start=819
  _globals['_MONSTERSTARTPOS']._serialized_end=882
  _globals['_ORGANPOS']._serialized_start=884
  _globals['_ORGANPOS']._serialized_end=934
  _globals['_HEROSTARTPOS']._serialized_start=936
  _globals['_HEROSTARTPOS']._serialized_end=996
  _globals['_STARTINFO']._serialized_start=999
  _globals['_STARTINFO']._serialized_end=1172
  _globals['_SCOREINFO']._serialized_start=1174
  _globals['_SCOREINFO']._serialized_end=1250
  _globals['_STATE']._serialized_start=1252
  _globals['_STATE']._serialized_end=1259
  _globals['_ACTION']._serialized_start=1261
  _globals['_ACTION']._serialized_end=1287
  _globals['_RELATIVEPOSITION']._serialized_start=1290
  _globals['_RELATIVEPOSITION']._serialized_end=1465
  _globals['_MONSTERS']._serialized_start=1467
  _globals['_MONSTERS']._serialized_end=1575
  _globals['_FRAMESTATE']._serialized_start=1578
  _globals['_FRAMESTATE']._serialized_end=1773
  _globals['_INFINITYVALLEYHERO']._serialized_start=1776
  _globals['_INFINITYVALLEYHERO']._serialized_end=1931
  _globals['_INFINITYVALLEYMONSTER']._serialized_start=1934
  _globals['_INFINITYVALLEYMONSTER']._serialized_end=2112
  _globals['_INFINITYVALLEYORGAN']._serialized_start=2114
  _globals['_INFINITYVALLEYORGAN']._serialized_end=2228
  _globals['_COMMAND']._serialized_start=2230
  _globals['_COMMAND']._serialized_end=2299
  _globals['_CMDPKG']._serialized_start=2301
  _globals['_CMDPKG']._serialized_end=2349
  _globals['_FLOATPOSITION']._serialized_start=2351
  _globals['_FLOATPOSITION']._serialized_end=2388
  _globals['_POSITION']._serialized_start=2390
  _globals['_POSITION']._serialized_end=2422
  _globals['_GAMEINFO']._serialized_start=2425
  _globals['_GAMEINFO']._serialized_end=2721
  _globals['_FRAMES']._serialized_start=2723
  _globals['_FRAMES']._serialized_end=2771
  _globals['_FRAME']._serialized_start=2774
  _globals['_FRAME']._serialized_end=3025
# @@protoc_insertion_point(module_scope)
