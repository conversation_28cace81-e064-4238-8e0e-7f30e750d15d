# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: scene.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import hero_pb2 as hero__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bscene.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\nhero.proto\"D\n\x04\x42ush\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12*\n\x08\x63ollider\x18\x02 \x02(\x0b\x32\x18.sgame_state.BoxCollider\"G\n\x04\x43\x61ke\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12-\n\x08\x63ollider\x18\x02 \x02(\x0b\x32\x1b.sgame_state.SphereCollider\"\xff\x02\n\x06\x42ullet\x12\x12\n\nruntime_id\x18\x01 \x02(\x05\x12)\n\x04\x63\x61mp\x18\x02 \x02(\x0e\x32\x1b.sgame_state.COM_PLAYERCAMP\x12\x14\n\x0csource_actor\x18\x03 \x02(\x05\x12-\n\tslot_type\x18\x04 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x10\n\x08skill_id\x18\x05 \x02(\x05\x12\x0e\n\x06\x61\x63tion\x18\x06 \x02(\t\x12$\n\x08location\x18\x07 \x02(\x0b\x32\x12.sgame_state.VInt3\x12$\n\x08\x62orn_pos\x18\x08 \x01(\x0b\x32\x12.sgame_state.VInt3\x12#\n\x07use_dir\x18\t \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x11\n\tuse_frame\x18\n \x01(\r\x12\x13\n\x0bsight_range\x18\x0b \x01(\x05\x12\x14\n\x0ctarget_actor\x18\x0c \x01(\x05\x12 \n\x04size\x18\r \x01(\x0b\x32\x12.sgame_state.VInt3\"\xf3\x01\n\x0b\x45xtraBullet\x12\x12\n\nruntime_id\x18\x01 \x01(\x05\x12)\n\x04\x63\x61mp\x18\x02 \x01(\x0e\x32\x1b.sgame_state.COM_PLAYERCAMP\x12\x14\n\x0csource_actor\x18\x03 \x01(\x05\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12$\n\x08location\x18\x05 \x01(\x0b\x32\x12.sgame_state.VInt3\x12$\n\x08\x62orn_pos\x18\x06 \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x11\n\tuse_frame\x18\x07 \x01(\r\x12 \n\x04size\x18\x08 \x01(\x0b\x32\x12.sgame_state.VInt3\"\x8a\x02\n\x05\x42lock\x12\x12\n\nruntime_id\x18\x01 \x01(\x05\x12)\n\x04\x63\x61mp\x18\x02 \x01(\x0e\x32\x1b.sgame_state.COM_PLAYERCAMP\x12\x14\n\x0csource_actor\x18\x03 \x01(\x05\x12-\n\tslot_type\x18\x04 \x01(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x10\n\x08skill_id\x18\x05 \x01(\x05\x12$\n\x08location\x18\x06 \x01(\x0b\x32\x12.sgame_state.VInt3\x12#\n\x07\x66orward\x18\x07 \x01(\x0b\x32\x12.sgame_state.VInt3\x12 \n\x04size\x18\x08 \x01(\x0b\x32\x12.sgame_state.VInt3\"{\n\x0bNavMeshData\x12!\n\x05verts\x18\x01 \x03(\x0b\x32\x12.sgame_state.VInt3\x12\x0c\n\x04tris\x18\x02 \x03(\x05\x12\'\n\x0boriginVerts\x18\x03 \x03(\x0b\x32\x12.sgame_state.VInt3\x12\x12\n\noriginTris\x18\x04 \x03(\x05\"\x9b\x02\n\x0eNavMeshCutData\x12\x0e\n\x06isDual\x18\x01 \x02(\x08\x12\x15\n\rcutsAddedGeom\x18\x02 \x02(\x08\x12\x13\n\x0buseRotation\x18\x03 \x02(\x08\x12)\n\x04type\x18\x04 \x02(\x0e\x32\x1b.sgame_state.NavMeshCutType\x12)\n\rrectangleSize\x18\x05 \x02(\x0b\x32\x12.sgame_state.VInt2\x12\x14\n\x0c\x63ircleRadius\x18\x06 \x02(\x05\x12\x18\n\x10\x63ircleResolution\x18\x07 \x02(\x05\x12\x0e\n\x06height\x18\x08 \x02(\x05\x12$\n\x08position\x18\t \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x11\n\tcampIndex\x18\n \x02(\x05\"f\n\x07NavData\x12)\n\x07navMesh\x18\x01 \x02(\x0b\x32\x18.sgame_state.NavMeshData\x12\x30\n\x0bnavMeshCuts\x18\x02 \x03(\x0b\x32\x1b.sgame_state.NavMeshCutData*h\n\x0eNavMeshCutType\x12\x1c\n\x18NavMeshCutType_Rectangle\x10\x00\x12\x19\n\x15NavMeshCutType_Circle\x10\x01\x12\x1d\n\x19NavMeshCutType_CustomMesh\x10\x02')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'scene_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_NAVMESHCUTTYPE']._serialized_start=1613
  _globals['_NAVMESHCUTTYPE']._serialized_end=1717
  _globals['_BUSH']._serialized_start=54
  _globals['_BUSH']._serialized_end=122
  _globals['_CAKE']._serialized_start=124
  _globals['_CAKE']._serialized_end=195
  _globals['_BULLET']._serialized_start=198
  _globals['_BULLET']._serialized_end=581
  _globals['_EXTRABULLET']._serialized_start=584
  _globals['_EXTRABULLET']._serialized_end=827
  _globals['_BLOCK']._serialized_start=830
  _globals['_BLOCK']._serialized_end=1096
  _globals['_NAVMESHDATA']._serialized_start=1098
  _globals['_NAVMESHDATA']._serialized_end=1221
  _globals['_NAVMESHCUTDATA']._serialized_start=1224
  _globals['_NAVMESHCUTDATA']._serialized_end=1507
  _globals['_NAVDATA']._serialized_start=1509
  _globals['_NAVDATA']._serialized_end=1611
# @@protoc_insertion_point(module_scope)
