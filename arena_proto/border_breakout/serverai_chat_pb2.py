# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: serverai_chat.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import command_pb2 as command__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13serverai_chat.proto\x12\x0fsgame_ai_server\x1a\x0c\x63ommon.proto\x1a\rcommand.proto\"\x93\x02\n\x14\x45ventChatRequestInfo\x12\x10\n\x08objectID\x18\x01 \x02(\r\x12\x0c\n\x04isAI\x18\x02 \x02(\x05\x12\x0b\n\x03uid\x18\x03 \x02(\x04\x12.\n\nevent_type\x18\x04 \x02(\x0e\x32\x1a.sgame_ai_server.EventType\x12\x11\n\tgame_type\x18\x05 \x02(\x05\x12\x10\n\x08\x61i_level\x18\x06 \x02(\x05\x12\x0f\n\x07hero_id\x18\x07 \x02(\x05\x12\x10\n\x08\x65quip_id\x18\x08 \x01(\x05\x12\x10\n\x08mon_type\x18\t \x01(\x05\x12\x14\n\x0c\x62\x65_killed_id\x18\n \x01(\x05\x12\x18\n\x10\x62\x65_kiilled_objid\x18\x0b \x01(\x05\x12\x14\n\x0ckiller_objid\x18\x0c \x01(\x05\"[\n\x0e\x43hatPlayerInfo\x12\r\n\x05objid\x18\x01 \x02(\r\x12\x0c\n\x04isai\x18\x02 \x02(\x05\x12\x0b\n\x03uid\x18\x03 \x02(\x04\x12\x0f\n\x07hero_id\x18\x04 \x01(\x05\x12\x0e\n\x06to_uid\x18\x05 \x01(\x04\"/\n\x08\x43hatText\x12\x12\n\nchat_limit\x18\x01 \x02(\r\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\"\xd2\x01\n\x0f\x43hatMsgInBattle\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\x11\n\tsignal_id\x18\x02 \x01(\r\x12,\n\tchat_text\x18\x03 \x01(\x0b\x32\x19.sgame_ai_server.ChatText\x12\x39\n\x10\x63hat_player_info\x18\x04 \x01(\x0b\x32\x1f.sgame_ai_server.ChatPlayerInfo\x12\x11\n\ttimestamp\x18\x05 \x01(\x04\x12\x0e\n\x06to_uid\x18\x06 \x01(\x04\x12\x12\n\next_params\x18\x07 \x03(\r\"r\n\rPosFightValue\x12\x13\n\x0b\x62ranch_type\x18\x01 \x01(\r\x12\x17\n\x0fpos_fight_value\x18\x02 \x01(\r\x12\x12\n\ntitle_type\x18\x03 \x01(\r\x12\x0f\n\x07rank_no\x18\x04 \x01(\r\x12\x0e\n\x06\x61\x64\x63ode\x18\x05 \x01(\r\"\xc7\x01\n\x0e\x43hatBranchInfo\x12\x13\n\x0b\x62ranch_type\x18\x01 \x01(\r\x12\x37\n\x0fpos_fight_value\x18\x02 \x01(\x0b\x32\x1e.sgame_ai_server.PosFightValue\x12\x1a\n\x12master_match_score\x18\x03 \x01(\x05\x12\x17\n\x0fold_branch_type\x18\x04 \x01(\r\x12\x18\n\x10is_first_perfect\x18\x05 \x01(\r\x12\x18\n\x10need_hide_bubble\x18\x06 \x01(\r\"\xd4\x01\n\rChatMsgBattle\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\x11\n\tsignal_id\x18\x02 \x01(\r\x12,\n\tchat_text\x18\x03 \x01(\x0b\x32\x19.sgame_ai_server.ChatText\x12\x39\n\x10\x63hat_player_info\x18\x04 \x01(\x0b\x32\x1f.sgame_ai_server.ChatPlayerInfo\x12\x39\n\x10\x63hat_branch_info\x18\x05 \x01(\x0b\x32\x1f.sgame_ai_server.ChatBranchInfo\"\x84\x01\n\x0b\x43hatMsgInfo\x12<\n\x12\x63hat_msg_in_battle\x18\x01 \x01(\x0b\x32 .sgame_ai_server.ChatMsgInBattle\x12\x37\n\x0f\x63hat_msg_battle\x18\x02 \x01(\x0b\x32\x1e.sgame_ai_server.ChatMsgBattle\"A\n\x0c\x45xpectBPInfo\x12\r\n\x05objid\x18\x01 \x02(\r\x12\x0f\n\x07hero_id\x18\x02 \x02(\r\x12\x11\n\toperation\x18\x03 \x02(\r\"\x82\x01\n\rHeroHighLight\x12\x11\n\tgame_type\x18\x01 \x01(\r\x12\x15\n\rlast_kill_cnt\x18\x02 \x01(\r\x12\x15\n\rlast_dead_cnt\x18\x03 \x01(\r\x12\x17\n\x0flast_assist_cnt\x18\x04 \x01(\r\x12\x17\n\x0flast_title_mask\x18\x05 \x01(\r\"\xf7\x01\n\rHeroStatistic\x12\x11\n\tgame_type\x18\x01 \x01(\r\x12\x14\n\x0cmap_acnt_num\x18\x02 \x01(\r\x12\x0f\n\x07win_num\x18\x03 \x01(\r\x12\x10\n\x08lose_num\x18\x04 \x01(\r\x12\x16\n\x0egold_medal_cnt\x18\x05 \x01(\r\x12\x18\n\x10silver_medal_cnt\x18\x06 \x01(\r\x12\x14\n\x0ctop_road_cnt\x18\x07 \x01(\r\x12\x14\n\x0cmid_road_cnt\x18\x08 \x01(\r\x12\x14\n\x0c\x62ot_road_cnt\x18\t \x01(\r\x12\x12\n\njungle_cnt\x18\n \x01(\r\x12\x12\n\nassist_cnt\x18\x0b \x01(\r\"\x8d\x02\n\x0cShowHeroInfo\x12\r\n\x05objid\x18\x01 \x02(\r\x12\x0f\n\x07hero_id\x18\x02 \x02(\r\x12\x16\n\x0eshow_info_type\x18\x03 \x01(\r\x12\x12\n\ntitle_type\x18\x04 \x01(\r\x12\x15\n\rtotle_ranking\x18\x05 \x01(\r\x12\x37\n\x0fhero_high_light\x18\x06 \x01(\x0b\x32\x1e.sgame_ai_server.HeroHighLight\x12/\n\x07history\x18\x07 \x01(\x0b\x32\x1e.sgame_ai_server.HeroStatistic\x12\x30\n\x08\x63ur_year\x18\x08 \x01(\x0b\x32\x1e.sgame_ai_server.HeroStatistic\"8\n\rSurrenderInfo\x12\r\n\x05objid\x18\x01 \x02(\r\x12\x18\n\x10surrender_result\x18\x02 \x02(\r\"g\n\x15\x45ventChatResponseType\x12;\n\x08hci_info\x18\x01 \x01(\x0b\x32).sgame_ai_server.EventChatResponseHCIInfo\x12\x11\n\tis_accept\x18\x02 \x01(\x05\"r\n\x15IntentionLocationType\x12\x10\n\x08objectID\x18\x01 \x02(\r\x12\x0b\n\x03uid\x18\x02 \x02(\x04\x12\x0f\n\x07hero_id\x18\x03 \x02(\x05\x12)\n\rintention_loc\x18\x04 \x01(\x0b\x32\x12.sgame_state.VInt3\"\xff\x03\n\x14IntentionMessageType\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12\x0f\n\x07hero_id\x18\x03 \x02(\x05\x12\x0c\n\x04\x63\x61mp\x18\x04 \x02(\x05\x12\x13\n\x0bintent_type\x18\x05 \x01(\x05\x12\x12\n\nscene_type\x18\x06 \x01(\x05\x12\x14\n\x0cto_object_id\x18\x07 \x01(\r\x12\x15\n\rto_runtime_id\x18\x08 \x01(\x05\x12\x12\n\nto_hero_id\x18\t \x01(\x05\x12\x0f\n\x07to_lane\x18\n \x01(\x05\x12\x13\n\x0btarget_type\x18\x0b \x01(\x05\x12\x13\n\x0btarget_camp\x18\x0c \x01(\x05\x12\x19\n\x11target_runtime_id\x18\r \x01(\x05\x12\x13\n\x0btarget_lane\x18\x0e \x01(\x05\x12+\n\x0ftarget_location\x18\x0f \x01(\x0b\x32\x12.sgame_state.VInt3\x12<\n\x13target_element_type\x18\x10 \x01(\x0e\x32\x1f.sgame_state.MiniMapElementType\x12\x11\n\ttimestamp\x18\x11 \x01(\x04\x12\x12\n\next_param1\x18\x12 \x01(\r\x12\x12\n\next_param2\x18\x13 \x01(\r\x12\x12\n\next_param3\x18\x14 \x01(\r\x12\x12\n\next_param4\x18\x15 \x01(\r\"|\n\rBPPreAllocMsg\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x0c\n\x04type\x18\x02 \x02(\r\x12\x0f\n\x07hero_id\x18\x03 \x01(\r\x12\x0c\n\x04lane\x18\x04 \x01(\r\x12\x0b\n\x03job\x18\x05 \x01(\r\x12\r\n\x05skill\x18\x06 \x01(\r\x12\x0f\n\x07symbols\x18\x07 \x03(\r\"\xa9\x01\n\x0e\x42PRecommendMsg\x12\x17\n\x0fhuman_object_id\x18\x01 \x02(\r\x12\x14\n\x0c\x61i_object_id\x18\x02 \x02(\r\x12.\n\x04type\x18\x03 \x02(\x0e\x32 .sgame_ai_server.BPRecommendType\x12\x0f\n\x07hero_id\x18\x04 \x01(\r\x12\x15\n\rinter_hero_id\x18\x05 \x01(\r\x12\x10\n\x08rec_info\x18\x06 \x01(\t\"S\n\x0b\x42PToChatMsg\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\x36\n\rrecommend_msg\x18\x02 \x02(\x0b\x32\x1f.sgame_ai_server.BPRecommendMsg\"!\n\x11\x43ollaBotToChatMsg\x12\x0c\n\x04type\x18\x01 \x02(\r\"s\n\x1cIntentionMessageResponseInfo\x12@\n\x11intention_message\x18\x01 \x01(\x0b\x32%.sgame_ai_server.IntentionMessageType\x12\x11\n\tis_accept\x18\x02 \x01(\x05\"y\n\x11\x43ollaChatToBotMsg\x12\x0c\n\x04type\x18\x01 \x02(\r\x12V\n\x1fintention_message_response_info\x18\x02 \x01(\x0b\x32-.sgame_ai_server.IntentionMessageResponseInfo\"\x84\x06\n\x0f\x43hatRequestInfo\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\x46\n\x17\x65vent_chat_request_info\x18\x02 \x01(\x0b\x32%.sgame_ai_server.EventChatRequestInfo\x12\x33\n\rchat_msg_info\x18\x03 \x01(\x0b\x32\x1c.sgame_ai_server.ChatMsgInfo\x12\x35\n\x0e\x65xpect_bp_info\x18\x04 \x01(\x0b\x32\x1d.sgame_ai_server.ExpectBPInfo\x12\x35\n\x0eshow_hero_info\x18\x05 \x01(\x0b\x32\x1d.sgame_ai_server.ShowHeroInfo\x12\x36\n\x0esurrender_info\x18\x06 \x01(\x0b\x32\x1e.sgame_ai_server.SurrenderInfo\x12H\n\x18\x65vent_chat_response_type\x18\x07 \x01(\x0b\x32&.sgame_ai_server.EventChatResponseType\x12G\n\x17intention_location_type\x18\x08 \x01(\x0b\x32&.sgame_ai_server.IntentionLocationType\x12\x45\n\x16intention_message_type\x18\t \x01(\x0b\x32%.sgame_ai_server.IntentionMessageType\x12\x35\n\rpre_alloc_msg\x18\n \x01(\x0b\x32\x1e.sgame_ai_server.BPPreAllocMsg\x12\x41\n\x15\x63olla_bot_to_chat_msg\x18\x0b \x01(\x0b\x32\".sgame_ai_server.CollaBotToChatMsg\x12\x34\n\x0e\x62p_to_chat_msg\x18\x0c \x01(\x0b\x32\x1c.sgame_ai_server.BPToChatMsg\x12\x36\n\x0e\x66ix_branch_msg\x18\r \x01(\x0b\x32\x1e.sgame_ai_server.BPPreAllocMsg\"\xce\x02\n\x15\x45ventChatResponseInfo\x12-\n\nerror_code\x18\x01 \x02(\x0e\x32\x19.sgame_ai_server.ErrorNum\x12\x10\n\x08objectID\x18\x02 \x02(\r\x12\x0b\n\x03uid\x18\x03 \x02(\x04\x12\x0f\n\x07hero_id\x18\x04 \x02(\x05\x12\x14\n\x0cmessage_type\x18\x05 \x01(\x05\x12\x14\n\x0c\x63hat_content\x18\x06 \x01(\t\x12\x11\n\tsignal_id\x18\x07 \x01(\x05\x12\x11\n\tdelay_sec\x18\x08 \x01(\r\x12\x12\n\nchat_limit\x18\t \x01(\r\x12\x11\n\tchat_type\x18\n \x01(\r\x12\x0e\n\x06to_uid\x18\x0b \x01(\x04\x12\x12\n\next_params\x18\x0c \x03(\r\x12\x11\n\ttimestamp\x18\r \x01(\x04\x12\x14\n\x0cvisible_uids\x18\x0e \x03(\x04\x12\x10\n\x08jsondata\x18\x0f \x01(\t\"\x95\x03\n\x18\x45ventChatResponseHCIInfo\x12\x10\n\x08objectID\x18\x01 \x02(\r\x12\x0b\n\x03uid\x18\x02 \x02(\x04\x12\x0f\n\x07hero_id\x18\x03 \x02(\x05\x12\x11\n\tgame_type\x18\x04 \x02(\x05\x12\x10\n\x08\x61i_level\x18\x05 \x02(\x05\x12\x14\n\x0cmessage_type\x18\x06 \x02(\x05\x12\x14\n\x0c\x63hat_content\x18\x07 \x01(\t\x12\x11\n\tsignal_id\x18\x08 \x01(\x05\x12\x11\n\ttarget_id\x18\t \x01(\x05\x12&\n\ntarget_pos\x18\n \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x35\n\x0c\x65lement_type\x18\x0b \x01(\x0e\x32\x1f.sgame_state.MiniMapElementType\x12\x10\n\x08\x63md_type\x18\x0c \x01(\x05\x12\x11\n\ttimeStamp\x18\r \x01(\r\x12\x12\n\nrumtime_id\x18\x0e \x01(\r\x12\x11\n\tsource_id\x18\x0f \x01(\x05\x12\x13\n\x0btarget_type\x18\x10 \x01(\x05\x12\x12\n\nscene_type\x18\x11 \x01(\x05\"h\n\x18\x45ventChatResponseCmdInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12%\n\x08\x63md_info\x18\x03 \x01(\x0b\x32\x13.sgame_state.CmdPkg\"!\n\x0e\x42PChatTextInfo\x12\x0f\n\x07\x63ontent\x18\x01 \x02(\t\"&\n\x13\x42PChatQuickTextInfo\x12\x0f\n\x07text_id\x18\x01 \x02(\r\",\n\x18\x42PChatSelectPositionInfo\x12\x10\n\x08position\x18\x01 \x02(\r\"\"\n\x0f\x42PChatEmojiInfo\x12\x0f\n\x07\x63ontent\x18\x01 \x02(\t\"L\n\x13\x42PChatExpectBopInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x11\n\toperation\x18\x02 \x02(\r\x12\x0f\n\x07hero_id\x18\x03 \x02(\r\"\x8d\x03\n\nBPChatInfo\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\x32\n\ttext_info\x18\x02 \x01(\x0b\x32\x1f.sgame_ai_server.BPChatTextInfo\x12=\n\x0fquick_text_info\x18\x03 \x01(\x0b\x32$.sgame_ai_server.BPChatQuickTextInfo\x12G\n\x14select_position_info\x18\x04 \x01(\x0b\x32).sgame_ai_server.BPChatSelectPositionInfo\x12\x34\n\nemoji_info\x18\x05 \x01(\x0b\x32 .sgame_ai_server.BPChatEmojiInfo\x12@\n\x18show_last_hero_highlight\x18\x06 \x01(\x0b\x32\x1e.sgame_ai_server.HeroHighLight\x12=\n\x0f\x65xpect_bop_info\x18\x07 \x01(\x0b\x32$.sgame_ai_server.BPChatExpectBopInfo\"w\n\x12\x42PChatResponseInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x0b\n\x03uid\x18\x02 \x02(\x04\x12\x11\n\tdelay_sec\x18\x03 \x01(\r\x12.\n\tchat_info\x18\x04 \x01(\x0b\x32\x1b.sgame_ai_server.BPChatInfo\"P\n\x1b\x42PChatFixBranchResponseInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x0b\n\x03uid\x18\x02 \x02(\x04\x12\x11\n\tdelay_sec\x18\x03 \x01(\r\"\xc7\x01\n\x13\x42PChatIntentionInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x0c\n\x04\x63\x61mp\x18\x02 \x02(\r\x12\x13\n\x0bintent_type\x18\x03 \x02(\r\x12\x14\n\x0cto_object_id\x18\x04 \x01(\r\x12\x10\n\x08to_floor\x18\x05 \x01(\r\x12\x13\n\x0btarget_lane\x18\x06 \x01(\r\x12\x12\n\ntarget_job\x18\x07 \x01(\r\x12\x16\n\x0etarget_hero_id\x18\x08 \x01(\r\x12\x11\n\ttimestamp\x18\t \x01(\x04\"\xa9\x04\n\x10\x43hatResponseInfo\x12\x0c\n\x04type\x18\x01 \x02(\r\x12H\n\x18\x65vent_chat_response_info\x18\x02 \x01(\x0b\x32&.sgame_ai_server.EventChatResponseInfo\x12O\n\x1c\x65vent_chat_response_hci_info\x18\x03 \x01(\x0b\x32).sgame_ai_server.EventChatResponseHCIInfo\x12O\n\x1c\x65vent_chat_response_cmd_info\x18\x04 \x01(\x0b\x32).sgame_ai_server.EventChatResponseCmdInfo\x12\x42\n\x15\x62p_chat_response_info\x18\x05 \x01(\x0b\x32#.sgame_ai_server.BPChatResponseInfo\x12\x44\n\x16\x62p_chat_intention_info\x18\x06 \x01(\x0b\x32$.sgame_ai_server.BPChatIntentionInfo\x12\x41\n\x15\x63olla_chat_to_bot_msg\x18\x07 \x01(\x0b\x32\".sgame_ai_server.CollaChatToBotMsg\x12N\n\x18\x66ix_branch_response_info\x18\x08 \x01(\x0b\x32,.sgame_ai_server.BPChatFixBranchResponseInfo\"\xe2\x03\n\x08HeroInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x10\n\x08is_alive\x18\x02 \x01(\x08\x12\x13\n\x0bis_serverai\x18\x03 \x01(\x08\x12\x0f\n\x07hero_id\x18\x04 \x01(\r\x12\x0c\n\x04\x63\x61mp\x18\x05 \x01(\r\x12\x13\n\x0brevive_time\x18\x06 \x01(\r\x12\x10\n\x08kill_cnt\x18\x07 \x01(\r\x12\x10\n\x08\x64\x65\x61\x64_cnt\x18\x08 \x01(\r\x12\x12\n\nassist_cnt\x18\t \x01(\r\x12\x11\n\tmoney_cnt\x18\n \x01(\r\x12\x12\n\nrank_grade\x18\x0b \x01(\r\x12\x0e\n\x06openid\x18\x0c \x01(\t\x12\x13\n\x0bprefer_hero\x18\r \x03(\r\x12\x0b\n\x03uid\x18\x0e \x01(\x04\x12\x12\n\nmatch_flag\x18\x0f \x01(\r\x12\x16\n\x0elogic_world_id\x18\x10 \x01(\x05\x12\x1a\n\x12master_match_score\x18\x11 \x01(\r\x12\r\n\x05\x66loor\x18\x12 \x01(\r\x12\x0f\n\x07skin_id\x18\x13 \x01(\r\x12\x10\n\x08\x62op_step\x18\x14 \x01(\r\x12\x12\n\nis_real_ai\x18\x15 \x01(\x08\x12\x0c\n\x04name\x18\x16 \x01(\t\x12\x11\n\tshow_lane\x18\x17 \x01(\r\x12\x0f\n\x07\x61i_lane\x18\x18 \x01(\r\x12\x17\n\x0fshow_country_id\x18\x19 \x01(\x05\"\x84\x01\n\x05\x42PEnv\x12\x0f\n\x07\x62p_type\x18\x01 \x01(\r\x12\x17\n\x0fround_total_sec\x18\x02 \x01(\r\x12\x18\n\x10round_remain_sec\x18\x03 \x01(\r\x12\x17\n\x0f\x62\x61nned_hero_ids\x18\x04 \x03(\r\x12\x1e\n\x16is_pre_selected_branch\x18\x05 \x01(\r\"\xf3\x01\n\x13ServerAIChatRequest\x12\x0e\n\x06\x64\x65skID\x18\x01 \x02(\x05\x12\x0f\n\x07\x64\x65skSeq\x18\x02 \x02(\x05\x12\x13\n\x0brelayentity\x18\x03 \x02(\x05\x12<\n\x12\x63hat_request_infos\x18\x04 \x03(\x0b\x32 .sgame_ai_server.ChatRequestInfo\x12-\n\nhero_infos\x18\x05 \x03(\x0b\x32\x19.sgame_ai_server.HeroInfo\x12\x11\n\tgame_type\x18\x06 \x01(\x05\x12&\n\x06\x62p_env\x18\x07 \x01(\x0b\x32\x16.sgame_ai_server.BPEnv\"\xbb\x01\n\x14ServerAIChatResponse\x12-\n\nerror_code\x18\x01 \x02(\x0e\x32\x19.sgame_ai_server.ErrorNum\x12\x0e\n\x06\x64\x65skID\x18\x02 \x02(\x05\x12\x0f\n\x07\x64\x65skSeq\x18\x03 \x02(\x05\x12\x13\n\x0brelayentity\x18\x04 \x02(\x05\x12>\n\x13\x63hat_response_infos\x18\x05 \x03(\x0b\x32!.sgame_ai_server.ChatResponseInfo*z\n\x08\x45rrorNum\x12\x10\n\x0c\x43ode_Success\x10\x00\x12\x1e\n\x11\x43ode_ReceiveError\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1e\n\x11\x43ode_ParsePbError\x10\xfe\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1c\n\x0f\x43ode_OtherError\x10\xfd\xff\xff\xff\xff\xff\xff\xff\xff\x01*\xd8\x03\n\tEventType\x12\x13\n\x0f\x45VENT_TYPE_NONE\x10\x00\x12\x15\n\x11\x45VENT_FIRST_BLOOD\x10\x01\x12\x15\n\x11\x45VENT_DOUBLE_KILL\x10\x02\x12\x15\n\x11\x45VENT_TRIPLE_KILL\x10\x03\x12\x15\n\x11\x45VENT_QUADRA_KILL\x10\x04\x12\x14\n\x10\x45VENT_PENTA_KILL\x10\x05\x12\r\n\tEVENT_ACE\x10\x06\x12\x14\n\x10\x45VENT_NINE_DEATH\x10\x07\x12\x13\n\x0f\x45VENT_TEN_DEATH\x10\x08\x12\x15\n\x11\x45VENT_FIRST_TOWER\x10\t\x12\x16\n\x12\x45VENT_SECOND_TOWER\x10\n\x12\x14\n\x10\x45VENT_HIGH_TOWER\x10\x0b\x12\x12\n\x0e\x45VENT_GOD_LIKE\x10\x0c\x12\x10\n\x0c\x45VENT_BE_ACE\x10\r\x12\x18\n\x14\x45VENT_BE_FIRST_BLOOD\x10\x0e\x12\x13\n\x0f\x45VENT_BUY_EQUIP\x10\x0f\x12\x14\n\x10\x45VENT_GAME_START\x10\x10\x12\x1a\n\x16\x45VENT_KILL_STORMDRAGON\x10\x11\x12\x15\n\x11\x45VENT_SINGLE_KILL\x10\x12\x12\x15\n\x11\x45VENT_MON_REFRESH\x10\x13\x12\x10\n\x0c\x45VENT_SIGNAL\x10\x14*q\n\x0b\x43hatMsgType\x12\x16\n\x12\x43HAT_MSG_TYPE_NONE\x10\x00\x12\x18\n\x14\x43HAT_MSG_TYPE_SIGNAL\x10\x01\x12\x16\n\x12\x43HAT_MSG_TYPE_TEXT\x10\x02\x12\x18\n\x14\x43HAT_MSG_TYPE_BRANCH\x10\x03*S\n\rChatLimitType\x12\"\n\x1e\x43HAT_LIMIT_TYPE_CAMP_UNLIMITED\x10\x00\x12\x1e\n\x1a\x43HAT_LIMIT_TYPE_CAMP_LIMIT\x10\x01*}\n\x08LaneType\x12\x12\n\x0eLANE_TYPE_NONE\x10\x00\x12\x11\n\rLANE_TYPE_TOP\x10\x01\x12\x11\n\rLANE_TYPE_MID\x10\x02\x12\x11\n\rLANE_TYPE_BOT\x10\x03\x12\x11\n\rLANE_TYPE_JUG\x10\x04\x12\x11\n\rLANE_TYPE_SUP\x10\x05*\xe7\x01\n\x11ResBranchRoadType\x12\x1d\n\x19RES_BRANCH_ROAD_TYPE_NULL\x10\x00\x12\x1b\n\x17RES_BRANCH_ROAD_TYPE_UP\x10\x01\x12\x1d\n\x19RES_BRANCH_ROAD_TYPE_WILD\x10\x02\x12\x1c\n\x18RES_BRANCH_ROAD_TYPE_MID\x10\x03\x12\x1c\n\x18RES_BRANCH_ROAD_TYPE_AID\x10\x04\x12\x1d\n\x19RES_BRANCH_ROAD_TYPE_DOWN\x10\x05\x12\x1c\n\x18RES_BRANCH_ROAD_TYPE_MAX\x10\x06*\x8a\x01\n\x07JobType\x12\x15\n\x11JOB_TYPE_ASSASSIN\x10\x00\x12\x14\n\x10JOB_TYPE_SHOOTER\x10\x01\x12\x14\n\x10JOB_TYPE_SOLDIER\x10\x02\x12\x14\n\x10JOB_TYPE_SUPPORT\x10\x03\x12\x11\n\rJOB_TYPE_TANK\x10\x04\x12\x13\n\x0fJOB_TYPE_WIZARD\x10\x05*\x7f\n\x0e\x42PPreAllocType\x12\x1a\n\x16\x42P_PRE_ALLOC_TYPE_NONE\x10\x00\x12\x1a\n\x16\x42P_PRE_ALLOC_TYPE_HERO\x10\x01\x12\x1a\n\x16\x42P_PRE_ALLOC_TYPE_LANE\x10\x02\x12\x19\n\x15\x42P_PRE_ALLOC_TYPE_JOB\x10\x03*\xe6\x03\n\x0b\x43hatReqType\x12\x16\n\x12\x43HAT_REQ_TYPE_NONE\x10\x00\x12)\n%CHAT_REQ_TYPE_EVENT_CHAT_REQUEST_INFO\x10\x01\x12\x1f\n\x1b\x43HAT_REQ_TYPE_CHAT_MSG_INFO\x10\x02\x12 \n\x1c\x43HAT_REQ_TYPE_EXPECT_BP_INFO\x10\x03\x12 \n\x1c\x43HAT_REQ_TYPE_SHOW_HERO_INFO\x10\x04\x12 \n\x1c\x43HAT_REQ_TYPE_SURRENDER_INFO\x10\x05\x12*\n&CHAT_REQ_TYPE_EVENT_CHAT_RESPONSE_TYPE\x10\x06\x12)\n%CHAT_REQ_TYPE_INTENTION_LOCATION_TYPE\x10\x07\x12(\n$CHAT_REQ_TYPE_INTENTION_MESSAGE_TYPE\x10\x08\x12\x1f\n\x1b\x43HAT_REQ_TYPE_PRE_ALLOC_MSG\x10\t\x12\'\n#CHAT_REQ_TYPE_COLLA_BOT_TO_CHAT_MSG\x10\n\x12 \n\x1c\x43HAT_REQ_TYPE_BP_TO_CHAT_MSG\x10\x0b\x12 \n\x1c\x43HAT_REQ_TYPE_FIX_BRANCH_MSG\x10\x0c*\xd3\x02\n\x0b\x43hatRspType\x12\x16\n\x12\x43HAT_RSP_TYPE_NONE\x10\x00\x12*\n&CHAT_RSP_TYPE_EVENT_CHAT_RESPONSE_INFO\x10\x01\x12.\n*CHAT_RSP_TYPE_EVENT_CHAT_RESPONSE_HCI_INFO\x10\x02\x12.\n*CHAT_RSP_TYPE_EVENT_CHAT_RESPONSE_CMD_INFO\x10\x03\x12\'\n#CHAT_RSP_TYPE_BP_CHAT_RESPONSE_INFO\x10\x04\x12(\n$CHAT_RSP_TYPE_BP_CHAT_INTENTION_INFO\x10\x05\x12\'\n#CHAT_RSP_TYPE_COLLA_CHAT_TO_BOT_MSG\x10\x06\x12$\n CHAT_RSP_TYPE_BP_FIX_BRANCH_INFO\x10\x07*\xe9\x01\n\x0c\x43hatInfoType\x12\x17\n\x13\x43HAT_INFO_TYPE_NONE\x10\x00\x12\x17\n\x13\x43HAT_INFO_TYPE_TEXT\x10\x01\x12\x1d\n\x19\x43HAT_INFO_TYPE_QUICK_TEXT\x10\x02\x12\"\n\x1e\x43HAT_INFO_TYPE_SELECT_POSITION\x10\x03\x12\x18\n\x14\x43HAT_INFO_TYPE_EMOJI\x10\x04\x12+\n\'CHAT_INFO_TYPE_SHOW_LAST_HERO_HIGHLIGHT\x10\x05\x12\x1d\n\x19\x43HAT_INFO_TYPE_EXPECT_BOP\x10\x06*\xab\x01\n\x0fRaOperationType\x12\x1b\n\x17RA_OPTERATION_TYPE_NONE\x10\x00\x12\x1b\n\x17RA_OPTERATION_TYPE_PICK\x10\x01\x12\x1a\n\x16RA_OPTERATION_TYPE_BAN\x10\x02\x12\x1b\n\x17RA_OPTERATION_TYPE_DONE\x10\x03\x12%\n!RA_OPTERATION_TYPE_PREPICK_BRANCH\x10\x04*\xa0\x01\n\x0f\x42PRecommendType\x12\x19\n\x15\x42PRECOMMEND_TYPE_NONE\x10\x00\x12\x1b\n\x17\x42PRECOMMEND_TYPE_NORMAL\x10\x01\x12\x1a\n\x16\x42PRECOMMEND_TYPE_COMBO\x10\x02\x12\x1c\n\x18\x42PRECOMMEND_TYPE_COUNTER\x10\x03\x12\x1b\n\x17\x42PRECOMMEND_TYPE_STRONG\x10\x04')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'serverai_chat_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ERRORNUM']._serialized_start=7821
  _globals['_ERRORNUM']._serialized_end=7943
  _globals['_EVENTTYPE']._serialized_start=7946
  _globals['_EVENTTYPE']._serialized_end=8418
  _globals['_CHATMSGTYPE']._serialized_start=8420
  _globals['_CHATMSGTYPE']._serialized_end=8533
  _globals['_CHATLIMITTYPE']._serialized_start=8535
  _globals['_CHATLIMITTYPE']._serialized_end=8618
  _globals['_LANETYPE']._serialized_start=8620
  _globals['_LANETYPE']._serialized_end=8745
  _globals['_RESBRANCHROADTYPE']._serialized_start=8748
  _globals['_RESBRANCHROADTYPE']._serialized_end=8979
  _globals['_JOBTYPE']._serialized_start=8982
  _globals['_JOBTYPE']._serialized_end=9120
  _globals['_BPPREALLOCTYPE']._serialized_start=9122
  _globals['_BPPREALLOCTYPE']._serialized_end=9249
  _globals['_CHATREQTYPE']._serialized_start=9252
  _globals['_CHATREQTYPE']._serialized_end=9738
  _globals['_CHATRSPTYPE']._serialized_start=9741
  _globals['_CHATRSPTYPE']._serialized_end=10080
  _globals['_CHATINFOTYPE']._serialized_start=10083
  _globals['_CHATINFOTYPE']._serialized_end=10316
  _globals['_RAOPERATIONTYPE']._serialized_start=10319
  _globals['_RAOPERATIONTYPE']._serialized_end=10490
  _globals['_BPRECOMMENDTYPE']._serialized_start=10493
  _globals['_BPRECOMMENDTYPE']._serialized_end=10653
  _globals['_EVENTCHATREQUESTINFO']._serialized_start=70
  _globals['_EVENTCHATREQUESTINFO']._serialized_end=345
  _globals['_CHATPLAYERINFO']._serialized_start=347
  _globals['_CHATPLAYERINFO']._serialized_end=438
  _globals['_CHATTEXT']._serialized_start=440
  _globals['_CHATTEXT']._serialized_end=487
  _globals['_CHATMSGINBATTLE']._serialized_start=490
  _globals['_CHATMSGINBATTLE']._serialized_end=700
  _globals['_POSFIGHTVALUE']._serialized_start=702
  _globals['_POSFIGHTVALUE']._serialized_end=816
  _globals['_CHATBRANCHINFO']._serialized_start=819
  _globals['_CHATBRANCHINFO']._serialized_end=1018
  _globals['_CHATMSGBATTLE']._serialized_start=1021
  _globals['_CHATMSGBATTLE']._serialized_end=1233
  _globals['_CHATMSGINFO']._serialized_start=1236
  _globals['_CHATMSGINFO']._serialized_end=1368
  _globals['_EXPECTBPINFO']._serialized_start=1370
  _globals['_EXPECTBPINFO']._serialized_end=1435
  _globals['_HEROHIGHLIGHT']._serialized_start=1438
  _globals['_HEROHIGHLIGHT']._serialized_end=1568
  _globals['_HEROSTATISTIC']._serialized_start=1571
  _globals['_HEROSTATISTIC']._serialized_end=1818
  _globals['_SHOWHEROINFO']._serialized_start=1821
  _globals['_SHOWHEROINFO']._serialized_end=2090
  _globals['_SURRENDERINFO']._serialized_start=2092
  _globals['_SURRENDERINFO']._serialized_end=2148
  _globals['_EVENTCHATRESPONSETYPE']._serialized_start=2150
  _globals['_EVENTCHATRESPONSETYPE']._serialized_end=2253
  _globals['_INTENTIONLOCATIONTYPE']._serialized_start=2255
  _globals['_INTENTIONLOCATIONTYPE']._serialized_end=2369
  _globals['_INTENTIONMESSAGETYPE']._serialized_start=2372
  _globals['_INTENTIONMESSAGETYPE']._serialized_end=2883
  _globals['_BPPREALLOCMSG']._serialized_start=2885
  _globals['_BPPREALLOCMSG']._serialized_end=3009
  _globals['_BPRECOMMENDMSG']._serialized_start=3012
  _globals['_BPRECOMMENDMSG']._serialized_end=3181
  _globals['_BPTOCHATMSG']._serialized_start=3183
  _globals['_BPTOCHATMSG']._serialized_end=3266
  _globals['_COLLABOTTOCHATMSG']._serialized_start=3268
  _globals['_COLLABOTTOCHATMSG']._serialized_end=3301
  _globals['_INTENTIONMESSAGERESPONSEINFO']._serialized_start=3303
  _globals['_INTENTIONMESSAGERESPONSEINFO']._serialized_end=3418
  _globals['_COLLACHATTOBOTMSG']._serialized_start=3420
  _globals['_COLLACHATTOBOTMSG']._serialized_end=3541
  _globals['_CHATREQUESTINFO']._serialized_start=3544
  _globals['_CHATREQUESTINFO']._serialized_end=4316
  _globals['_EVENTCHATRESPONSEINFO']._serialized_start=4319
  _globals['_EVENTCHATRESPONSEINFO']._serialized_end=4653
  _globals['_EVENTCHATRESPONSEHCIINFO']._serialized_start=4656
  _globals['_EVENTCHATRESPONSEHCIINFO']._serialized_end=5061
  _globals['_EVENTCHATRESPONSECMDINFO']._serialized_start=5063
  _globals['_EVENTCHATRESPONSECMDINFO']._serialized_end=5167
  _globals['_BPCHATTEXTINFO']._serialized_start=5169
  _globals['_BPCHATTEXTINFO']._serialized_end=5202
  _globals['_BPCHATQUICKTEXTINFO']._serialized_start=5204
  _globals['_BPCHATQUICKTEXTINFO']._serialized_end=5242
  _globals['_BPCHATSELECTPOSITIONINFO']._serialized_start=5244
  _globals['_BPCHATSELECTPOSITIONINFO']._serialized_end=5288
  _globals['_BPCHATEMOJIINFO']._serialized_start=5290
  _globals['_BPCHATEMOJIINFO']._serialized_end=5324
  _globals['_BPCHATEXPECTBOPINFO']._serialized_start=5326
  _globals['_BPCHATEXPECTBOPINFO']._serialized_end=5402
  _globals['_BPCHATINFO']._serialized_start=5405
  _globals['_BPCHATINFO']._serialized_end=5802
  _globals['_BPCHATRESPONSEINFO']._serialized_start=5804
  _globals['_BPCHATRESPONSEINFO']._serialized_end=5923
  _globals['_BPCHATFIXBRANCHRESPONSEINFO']._serialized_start=5925
  _globals['_BPCHATFIXBRANCHRESPONSEINFO']._serialized_end=6005
  _globals['_BPCHATINTENTIONINFO']._serialized_start=6008
  _globals['_BPCHATINTENTIONINFO']._serialized_end=6207
  _globals['_CHATRESPONSEINFO']._serialized_start=6210
  _globals['_CHATRESPONSEINFO']._serialized_end=6763
  _globals['_HEROINFO']._serialized_start=6766
  _globals['_HEROINFO']._serialized_end=7248
  _globals['_BPENV']._serialized_start=7251
  _globals['_BPENV']._serialized_end=7383
  _globals['_SERVERAICHATREQUEST']._serialized_start=7386
  _globals['_SERVERAICHATREQUEST']._serialized_end=7629
  _globals['_SERVERAICHATRESPONSE']._serialized_start=7632
  _globals['_SERVERAICHATRESPONSE']._serialized_end=7819
# @@protoc_insertion_point(module_scope)
