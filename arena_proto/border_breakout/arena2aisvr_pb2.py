# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: arena2aisvr.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import custom_pb2 as custom__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x61rena2aisvr.proto\x12\x0bsgame_state\x1a\x0c\x63ustom.proto\"\xd6\x01\n\x0f\x41IServerRequest\x12\x0f\n\x07game_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12%\n\x03obs\x18\x03 \x02(\x0b\x32\x18.sgame_state.Observation\x12*\n\nscore_info\x18\x04 \x02(\x0b\x32\x16.sgame_state.ScoreInfo\x12\x12\n\nterminated\x18\x05 \x02(\x05\x12\x11\n\ttruncated\x18\x06 \x02(\x05\x12&\n\x08\x65nv_info\x18\x07 \x02(\x0b\x32\x14.sgame_state.EnvInfo\"m\n\x10\x41IServerResponse\x12\x0f\n\x07game_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12#\n\x06\x61\x63tion\x18\x03 \x02(\x0b\x32\x13.sgame_state.Action\x12\x11\n\tstop_game\x18\x04 \x02(\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'arena2aisvr_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_AISERVERREQUEST']._serialized_start=49
  _globals['_AISERVERREQUEST']._serialized_end=263
  _globals['_AISERVERRESPONSE']._serialized_start=265
  _globals['_AISERVERRESPONSE']._serialized_end=374
# @@protoc_insertion_point(module_scope)
