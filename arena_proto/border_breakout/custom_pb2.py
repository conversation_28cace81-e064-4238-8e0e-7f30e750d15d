# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import sgame_state_pb2 as sgame__state__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x0bsgame_state\x1a\x11sgame_state.proto\"j\n\x0bObservation\x12\x38\n\x0bplayer_list\x18\x01 \x03(\x0b\x32#.sgame_state.Observation.EachPlayer\x1a!\n\nEachPlayer\x12\x13\n\x0b\x66rame_state\x18\x01 \x03(\x02\"`\n\tScoreInfo\x12\x36\n\x0bplayer_list\x18\x01 \x03(\x0b\x32!.sgame_state.ScoreInfo.EachPlayer\x1a\x1b\n\nEachPlayer\x12\r\n\x05score\x18\x01 \x03(\x02\"3\n\x07\x45nvInfo\x12(\n\tgame_info\x18\x01 \x02(\x0b\x32\x15.sgame_state.GameInfo\"\x96\x01\n\x06\x41\x63tion\x12\x33\n\x0bplayer_list\x18\x01 \x03(\x0b\x32\x1e.sgame_state.Action.EachPlayer\x1a\x18\n\tFloatList\x12\x0b\n\x03\x61\x63t\x18\x01 \x03(\x02\x1a=\n\nEachPlayer\x12/\n\x08\x61\x63t_list\x18\x01 \x03(\x0b\x32\x1d.sgame_state.Action.FloatList\"\x96\x02\n\x08GameInfo\x12\x35\n\x0bplayer_list\x18\x01 \x03(\x0b\x32 .sgame_state.GameInfo.EachPlayer\x12\x19\n\x11\x66rame_state_bytes\x18\x02 \x01(\t\x1a\x19\n\tFloatList\x12\x0c\n\x04mask\x18\x01 \x03(\x02\x1a\x9c\x01\n\nEachPlayer\x12\x14\n\x0clegal_action\x18\x01 \x03(\x02\x12\r\n\x05score\x18\x02 \x03(\x02\x12\x38\n\x0fsub_action_mask\x18\x03 \x03(\x0b\x32\x1f.sgame_state.GameInfo.FloatList\x12/\n\x0e\x66rame_state_pb\x18\x04 \x02(\x0b\x32\x17.sgame_state.FrameState\"\x99\x01\n\x07\x43ommand\x12\x34\n\x0bplayer_list\x18\x01 \x03(\x0b\x32\x1f.sgame_state.Command.EachPlayer\x1a\x18\n\tFloatList\x12\x0b\n\x03\x63md\x18\x01 \x03(\x02\x1a>\n\nEachPlayer\x12\x30\n\x08\x63md_list\x18\x01 \x03(\x0b\x32\x1e.sgame_state.Command.FloatList')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_OBSERVATION']._serialized_start=48
  _globals['_OBSERVATION']._serialized_end=154
  _globals['_OBSERVATION_EACHPLAYER']._serialized_start=121
  _globals['_OBSERVATION_EACHPLAYER']._serialized_end=154
  _globals['_SCOREINFO']._serialized_start=156
  _globals['_SCOREINFO']._serialized_end=252
  _globals['_SCOREINFO_EACHPLAYER']._serialized_start=225
  _globals['_SCOREINFO_EACHPLAYER']._serialized_end=252
  _globals['_ENVINFO']._serialized_start=254
  _globals['_ENVINFO']._serialized_end=305
  _globals['_ACTION']._serialized_start=308
  _globals['_ACTION']._serialized_end=458
  _globals['_ACTION_FLOATLIST']._serialized_start=371
  _globals['_ACTION_FLOATLIST']._serialized_end=395
  _globals['_ACTION_EACHPLAYER']._serialized_start=397
  _globals['_ACTION_EACHPLAYER']._serialized_end=458
  _globals['_GAMEINFO']._serialized_start=461
  _globals['_GAMEINFO']._serialized_end=739
  _globals['_GAMEINFO_FLOATLIST']._serialized_start=555
  _globals['_GAMEINFO_FLOATLIST']._serialized_end=580
  _globals['_GAMEINFO_EACHPLAYER']._serialized_start=583
  _globals['_GAMEINFO_EACHPLAYER']._serialized_end=739
  _globals['_COMMAND']._serialized_start=742
  _globals['_COMMAND']._serialized_end=895
  _globals['_COMMAND_FLOATLIST']._serialized_start=807
  _globals['_COMMAND_FLOATLIST']._serialized_end=831
  _globals['_COMMAND_EACHPLAYER']._serialized_start=833
  _globals['_COMMAND_EACHPLAYER']._serialized_end=895
# @@protoc_insertion_point(module_scope)
