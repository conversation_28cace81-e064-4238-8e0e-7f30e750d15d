# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: serverai_inbattle.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17serverai_inbattle.proto\x12\rSGameInBattle\"y\n\x0b\x41ILevelPara\x12\x10\n\x08model_id\x18\x01 \x02(\r\x12\x18\n\x10predict_interval\x18\x02 \x02(\r\x12\x15\n\rcommand_delay\x18\x03 \x02(\r\x12\x13\n\x0b\x65quip_delay\x18\x04 \x02(\r\x12\x12\n\ngod_vision\x18\x05 \x02(\r\"\x92\x02\n\nMemberInfo\x12\x0b\n\x03uid\x18\x01 \x02(\x04\x12\x13\n\x0bizoneareaid\x18\x02 \x02(\r\x12\x0f\n\x07vopenid\x18\x03 \x02(\t\x12\x0e\n\x06\x63\x61mpid\x18\x04 \x02(\r\x12\r\n\x05is_ai\x18\x05 \x02(\r\x12\r\n\x05grade\x18\x06 \x02(\r\x12\x17\n\x0fserver_ai_level\x18\x07 \x02(\r\x12\x10\n\x08juben_id\x18\x08 \x02(\r\x12\x0f\n\x07hero_id\x18\t \x02(\x04\x12\x12\n\nruntime_id\x18\n \x02(\x05\x12\x12\n\nis_real_ai\x18\x0b \x01(\r\x12\x0f\n\x07team_id\x18\x0c \x01(\r\x12\x1a\n\x12master_match_score\x18\r \x01(\r\x12\x12\n\ncountry_id\x18\x0e \x01(\x05\"\xa4\x04\n\x0eInBattleResult\x12\x0b\n\x03uid\x18\x01 \x02(\x04\x12\x13\n\x0bizoneareaid\x18\x02 \x02(\r\x12\x0f\n\x07vopenid\x18\x03 \x02(\t\x12\x0e\n\x06\x63\x61mpid\x18\x04 \x02(\r\x12\r\n\x05is_ai\x18\x05 \x02(\r\x12\x17\n\x0fserver_ai_level\x18\x06 \x02(\r\x12\x10\n\x08juben_id\x18\x07 \x02(\r\x12\x0f\n\x07hero_id\x18\x08 \x02(\x04\x12\x12\n\nruntime_id\x18\t \x02(\x05\x12\x11\n\tis_peiwan\x18\n \x01(\r\x12\x16\n\x0einstruction_cd\x18\x0b \x01(\r\x12#\n\x1binstruction_finish_cd_level\x18\x0c \x01(\r\x12!\n\x19instruction_trigger_count\x18\r \x01(\t\x12 \n\x18instruction_finish_count\x18\x0e \x01(\t\x12\x11\n\taction_id\x18\x0f \x03(\r\x12\x0f\n\x07team_id\x18\x10 \x01(\r\x12\x19\n\x11personal_colla_id\x18\x11 \x03(\r\x12\x15\n\rai_level_mode\x18\x12 \x01(\r\x12\x11\n\tmeta_info\x18\x13 \x01(\t\x12/\n\x0b\x61i_level_v2\x18\x14 \x01(\x0b\x32\x1a.SGameInBattle.AILevelPara\x12\x1e\n\x16\x61\x62ility_score_to_param\x18\x15 \x01(\t\x12!\n\x19win_rate_to_ability_score\x18\x16 \x01(\t\"\x8a\x02\n\x08\x45ventReq\x12\x12\n\nsession_id\x18\x01 \x02(\t\x12(\n\x08\x65vent_id\x18\x02 \x02(\x0e\x32\x16.SGameInBattle.EventID\x12.\n\x0bmember_info\x18\x03 \x03(\x0b\x32\x19.SGameInBattle.MemberInfo\x12\x13\n\x0broom_entity\x18\x04 \x01(\x05\x12\x0f\n\x07room_id\x18\x05 \x01(\r\x12\x10\n\x08room_seq\x18\x06 \x01(\r\x12\x10\n\x08map_type\x18\x07 \x01(\r\x12\x0e\n\x06map_id\x18\x08 \x01(\r\x12\x19\n\x11\x61i_server_version\x18\t \x01(\t\x12\x1b\n\x13valid_juben_id_list\x18\n \x03(\x05\"\xc0\x01\n\x08\x45ventRsp\x12\x12\n\nsession_id\x18\x01 \x02(\t\x12(\n\x08\x65vent_id\x18\x02 \x01(\x0e\x32\x16.SGameInBattle.EventID\x12,\n\nerror_code\x18\x03 \x02(\x0e\x32\x18.SGameInBattle.ErrorType\x12\x36\n\x0finbattle_result\x18\x04 \x03(\x0b\x32\x1d.SGameInBattle.InBattleResult\x12\x10\n\x08\x63olla_id\x18\x05 \x03(\r*\x17\n\x07\x45ventID\x12\x0c\n\x08INBATTLE\x10\x01*|\n\tErrorType\x12\x14\n\x10INBATTLE_SUCCESS\x10\x00\x12\x18\n\x14RECEIVE_DATA_INVALID\x10\x01\x12 \n\x1cRECEIVE_OFFLINE_FEATURE_FAIL\x10\x02\x12\r\n\tDEAL_FAIL\x10\x03\x12\x0e\n\nOTHER_FAIL\x10\x04')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'serverai_inbattle_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_EVENTID']._serialized_start=1457
  _globals['_EVENTID']._serialized_end=1480
  _globals['_ERRORTYPE']._serialized_start=1482
  _globals['_ERRORTYPE']._serialized_end=1606
  _globals['_AILEVELPARA']._serialized_start=42
  _globals['_AILEVELPARA']._serialized_end=163
  _globals['_MEMBERINFO']._serialized_start=166
  _globals['_MEMBERINFO']._serialized_end=440
  _globals['_INBATTLERESULT']._serialized_start=443
  _globals['_INBATTLERESULT']._serialized_end=991
  _globals['_EVENTREQ']._serialized_start=994
  _globals['_EVENTREQ']._serialized_end=1260
  _globals['_EVENTRSP']._serialized_start=1263
  _globals['_EVENTRSP']._serialized_end=1455
# @@protoc_insertion_point(module_scope)
