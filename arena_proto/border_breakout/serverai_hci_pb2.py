# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: serverai_hci.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import serverai_chat_pb2 as serverai__chat__pb2
from . import common_pb2 as common__pb2
from . import serverai_adjust_pb2 as serverai__adjust__pb2
from . import serverai_inbattle_pb2 as serverai__inbattle__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12serverai_hci.proto\x12\x0fsgame_ai_server\x1a\x13serverai_chat.proto\x1a\x0c\x63ommon.proto\x1a\x15serverai_adjust.proto\x1a\x17serverai_inbattle.proto\"\x8b\x03\n\x0eHCIRequestInfo\x12\x10\n\x08objectID\x18\x01 \x02(\r\x12\x0b\n\x03uid\x18\x02 \x02(\x04\x12\x0f\n\x07hero_id\x18\x03 \x02(\x05\x12\x11\n\tgame_type\x18\x04 \x02(\x05\x12\x10\n\x08\x61i_level\x18\x05 \x02(\x05\x12\x14\n\x0cmessage_type\x18\x06 \x02(\x05\x12\x14\n\x0c\x63hat_content\x18\x07 \x01(\t\x12\x11\n\tsignal_id\x18\x08 \x01(\x05\x12\x11\n\ttarget_id\x18\t \x01(\x05\x12&\n\ntarget_pos\x18\n \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x35\n\x0c\x65lement_type\x18\x0b \x01(\x0e\x32\x1f.sgame_state.MiniMapElementType\x12\x10\n\x08\x63md_type\x18\x0c \x01(\x05\x12\x11\n\ttimeStamp\x18\r \x01(\r\x12\x12\n\nrumtime_id\x18\x0e \x01(\r\x12\x11\n\tsource_id\x18\x0f \x01(\x05\x12\x13\n\x0btarget_type\x18\x10 \x01(\x05\x12\x12\n\nscene_type\x18\x11 \x01(\x05\"\xd5\x01\n\x0eHCIRequestType\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\x39\n\x10hci_request_info\x18\x02 \x01(\x0b\x32\x1f.sgame_ai_server.HCIRequestInfo\x12\x41\n\x15\x63olla_chat_to_bot_msg\x18\x03 \x01(\x0b\x32\".sgame_ai_server.CollaChatToBotMsg\x12\x37\n\x0e\x61\x64j_to_bot_msg\x18\x04 \x01(\x0b\x32\x1f.sgame_ai_server.AdjustToBotMsg\"\xb0\x02\n\x0fHCIResponseInfo\x12\x31\n\nerror_code\x18\x01 \x02(\x0e\x32\x1d.sgame_ai_server.HCIErrorCode\x12\x10\n\x08objectID\x18\x02 \x02(\r\x12\x0b\n\x03uid\x18\x03 \x02(\x04\x12\x0f\n\x07hero_id\x18\x04 \x02(\x05\x12\x14\n\x0cmessage_type\x18\x05 \x01(\x05\x12\x14\n\x0c\x63hat_content\x18\x06 \x01(\t\x12\x11\n\tsignal_id\x18\x07 \x01(\x05\x12\x11\n\tchat_camp\x18\x08 \x01(\x05\x12\x11\n\ttimeStamp\x18\t \x01(\r\x12\x19\n\x11target_player_uid\x18\n \x01(\x04\x12\x12\n\next_param1\x18\x0b \x01(\r\x12\x12\n\next_param2\x18\x0c \x01(\r\x12\x12\n\next_param3\x18\r \x01(\r\"\\\n\x0fHCIResponseType\x12\x0c\n\x04type\x18\x01 \x02(\r\x12;\n\x11hci_response_info\x18\x02 \x01(\x0b\x32 .sgame_ai_server.HCIResponseInfo\"\xbe\x01\n\x12ServerAIHCIRequest\x12\x0e\n\x06\x64\x65skID\x18\x01 \x02(\x05\x12\x0f\n\x07\x64\x65skSeq\x18\x02 \x02(\x05\x12\x13\n\x0brelayentity\x18\x03 \x02(\x05\x12:\n\x11hci_request_types\x18\x04 \x03(\x0b\x32\x1f.sgame_ai_server.HCIRequestType\x12\x36\n\x15serverbp_succ_fix_rsp\x18\x05 \x01(\x0b\x32\x17.SGameInBattle.EventRsp\"\x91\x04\n\x13ServerAIHCIResponse\x12\x31\n\nerror_code\x18\x01 \x02(\x0e\x32\x1d.sgame_ai_server.HCIErrorCode\x12\x0e\n\x06\x64\x65skID\x18\x02 \x02(\x05\x12\x0f\n\x07\x64\x65skSeq\x18\x03 \x02(\x05\x12\x13\n\x0brelayentity\x18\x04 \x02(\x05\x12;\n\x11hci_response_type\x18\x05 \x03(\x0b\x32 .sgame_ai_server.HCIResponseType\x12H\n\x18\x65vent_chat_response_type\x18\x06 \x03(\x0b\x32&.sgame_ai_server.EventChatResponseType\x12G\n\x17intention_location_type\x18\x07 \x03(\x0b\x32&.sgame_ai_server.IntentionLocationType\x12\x45\n\x16intention_message_type\x18\x08 \x03(\x0b\x32%.sgame_ai_server.IntentionMessageType\x12\x41\n\x15\x63olla_bot_to_chat_msg\x18\t \x03(\x0b\x32\".sgame_ai_server.CollaBotToChatMsg\x12\x37\n\x0e\x62ot_to_adj_msg\x18\n \x03(\x0b\x32\x1f.sgame_ai_server.BotToAdjustMsg*z\n\x0cHCIErrorCode\x12\x0f\n\x0bHCI_Success\x10\x00\x12\x1d\n\x10HCI_ReceiveError\x10\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1d\n\x10HCI_ParsePbError\x10\xfe\xff\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1b\n\x0eHCI_OtherError\x10\xfd\xff\xff\xff\xff\xff\xff\xff\xff\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'serverai_hci_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_HCIERRORCODE']._serialized_start=1862
  _globals['_HCIERRORCODE']._serialized_end=1984
  _globals['_HCIREQUESTINFO']._serialized_start=123
  _globals['_HCIREQUESTINFO']._serialized_end=518
  _globals['_HCIREQUESTTYPE']._serialized_start=521
  _globals['_HCIREQUESTTYPE']._serialized_end=734
  _globals['_HCIRESPONSEINFO']._serialized_start=737
  _globals['_HCIRESPONSEINFO']._serialized_end=1041
  _globals['_HCIRESPONSETYPE']._serialized_start=1043
  _globals['_HCIRESPONSETYPE']._serialized_end=1135
  _globals['_SERVERAIHCIREQUEST']._serialized_start=1138
  _globals['_SERVERAIHCIREQUEST']._serialized_end=1328
  _globals['_SERVERAIHCIRESPONSE']._serialized_start=1331
  _globals['_SERVERAIHCIRESPONSE']._serialized_end=1860
# @@protoc_insertion_point(module_scope)
