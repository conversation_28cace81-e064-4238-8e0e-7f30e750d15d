# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hero.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import command_pb2 as command__pb2
from . import event_pb2 as event__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nhero.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\rcommand.proto\x1a\x0b\x65vent.proto\"\xa5\x03\n\x0eSkillSlotState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12-\n\tslot_type\x18\x02 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05level\x18\x03 \x02(\x05\x12\x0e\n\x06usable\x18\x04 \x02(\x08\x12\x10\n\x08\x63ooldown\x18\x05 \x02(\x05\x12\x14\n\x0c\x63ooldown_max\x18\x06 \x02(\x05\x12\x11\n\tusedTimes\x18\x07 \x01(\x05\x12\x14\n\x0chitHeroTimes\x18\x08 \x01(\x05\x12\x17\n\x0fsuccUsedInFrame\x18\t \x01(\x05\x12\x14\n\x0cnextConfigID\x18\n \x01(\x05\x12\x17\n\x0f\x63omboEffectTime\x18\x0b \x01(\x05\x12\x17\n\x0fskillBeanAmount\x18\x0c \x01(\x05\x12\x36\n\nskill_type\x18\r \x01(\x0e\x32\".sgame_state.SkillRangeAppointType\x12\x16\n\x0ehitADHeroTimes\x18\x0e \x01(\x05\x12\x17\n\x0fhitMidHeroTimes\x18\x0f \x01(\x05\x12\x18\n\x10skill_runtime_id\x18\x10 \x01(\r\">\n\nSkillState\x12\x30\n\x0bslot_states\x18\x01 \x03(\x0b\x32\x1b.sgame_state.SkillSlotState\"\xb1\x02\n\x0e\x42uffSkillState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05times\x18\x02 \x02(\x05\x12\x11\n\tstartTime\x18\x03 \x02(\x04\x12\x12\n\neffectType\x18\x04 \x01(\x05\x12\x0f\n\x07skillId\x18\x05 \x01(\x05\x12\x10\n\x08leftTime\x18\x06 \x01(\x03\x12\x13\n\x0b\x63urDuration\x18\x07 \x01(\x05\x12\x16\n\x0eoriginDuration\x18\x08 \x01(\x05\x12\r\n\x05layer\x18\t \x01(\x05\x12\x16\n\x0eskillRuntimeId\x18\n \x01(\x05\x12-\n\tblockType\x18\x0b \x01(\x0e\x32\x1a.sgame_state.BuffBlockType\x12\x31\n\x0b\x63ontrolType\x18\x0c \x01(\x0e\x32\x1c.sgame_state.BuffControlType\"Z\n\rBuffMarkState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05layer\x18\x02 \x02(\x05\x12\x16\n\x0eorigin_actorId\x18\x03 \x02(\x05\x12\x10\n\x08leftTime\x18\x04 \x01(\x05\"\xbb\x01\n\x12\x42uffFakeBloodState\x12\x16\n\x0e\x66\x61keBloodValue\x18\x01 \x01(\x05\x12\x15\n\rfakeBloodRate\x18\x02 \x01(\x05\x12\x1b\n\x13\x66\x61keBloodReduceRate\x18\x03 \x01(\x05\x12\x19\n\x11\x66\x61keBloodToHPRate\x18\x04 \x01(\x05\x12 \n\x18\x66\x61keBloodToHPRateMonster\x18\x05 \x01(\x05\x12\x1c\n\x14\x64\x65ltaTimeBeginReduce\x18\x06 \x01(\x05\"\xa7\x01\n\tBuffState\x12\x30\n\x0b\x62uff_skills\x18\x01 \x03(\x0b\x32\x1b.sgame_state.BuffSkillState\x12.\n\nbuff_marks\x18\x02 \x03(\x0b\x32\x1a.sgame_state.BuffMarkState\x12\x38\n\x0f\x62uff_fake_blood\x18\x03 \x01(\x0b\x32\x1f.sgame_state.BuffFakeBloodState\"9\n\x0cPassiveSkill\x12\x17\n\x0fpassive_skillid\x18\x01 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x02 \x01(\x05\"7\n\x0b\x41\x63tiveSkill\x12\x16\n\x0e\x61\x63tive_skillid\x18\x01 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x02 \x01(\x05\"\xa1\x01\n\tEquipSlot\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\x0e\n\x06\x61mount\x18\x02 \x02(\x05\x12\x10\n\x08\x62uyPrice\x18\x03 \x02(\x05\x12\x30\n\rpassive_skill\x18\x04 \x03(\x0b\x32\x19.sgame_state.PassiveSkill\x12.\n\x0c\x61\x63tive_skill\x18\x05 \x03(\x0b\x32\x18.sgame_state.ActiveSkill\"4\n\nEquipState\x12&\n\x06\x65quips\x18\x01 \x03(\x0b\x32\x16.sgame_state.EquipSlot\"\x9a\x01\n\x13ReturnCityAbortInfo\x12\x10\n\x08isActive\x18\x01 \x01(\x08\x12.\n\tabortType\x18\x02 \x01(\x0e\x32\x1b.sgame_state.SkillAbortType\x12\x32\n\x0e\x61ttackSlotType\x18\x03 \x01(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05objID\x18\x04 \x01(\r\"\x83\x01\n\x0bProtectInfo\x12-\n\x0bprotectType\x18\x01 \x01(\x0e\x32\x18.sgame_state.ProtectType\x12\x14\n\x0cprotectValue\x18\x02 \x01(\r\x12\x16\n\x0eprotectSkillId\x18\x03 \x01(\x05\x12\x17\n\x0fprotectDuration\x18\x04 \x01(\x05\"B\n\nJointSkill\x12\x10\n\x08iconName\x18\x01 \x01(\t\x12\x10\n\x08leftTime\x18\x02 \x01(\x05\x12\x10\n\x08uniqueID\x18\x03 \x01(\x05\"\xef\x01\n\x07TTKInfo\x12\x10\n\x08\x64\x65\x61\x64Time\x18\x01 \x01(\r\x12\x14\n\x0cwinGoldCount\x18\x02 \x01(\x05\x12\x15\n\rloseGoldCount\x18\x03 \x01(\x05\x12\x12\n\nkillerGold\x18\x04 \x01(\x05\x12\x10\n\x08\x64\x65\x61\x64Gold\x18\x05 \x01(\x05\x12\x0e\n\x06winNum\x18\x06 \x01(\x05\x12\x0f\n\x07loseNum\x18\x07 \x01(\x05\x12\x14\n\x0cttkBeginTime\x18\x08 \x01(\r\x12\x1d\n\x15ttkBeginLoseHpPercent\x18\t \x01(\x05\x12\x13\n\x0bwinConfigId\x18\n \x01(\x05\x12\x14\n\x0closeConfigId\x18\x0b \x01(\x05\"\x82\x06\n\x08StatInfo\x12\x0c\n\x04heal\x18\x01 \x01(\x05\x12\x15\n\rhealForOthers\x18\x02 \x01(\x05\x12\x14\n\x0chorizonValue\x18\x03 \x01(\x05\x12\x1a\n\x12hardCtrlTimeToHero\x18\x04 \x01(\x05\x12\x1a\n\x12softCtrlTimeToHero\x18\x05 \x01(\x05\x12\x19\n\x11\x63oinNumFromJungle\x18\x06 \x01(\r\x12\x1e\n\x16\x63oinNumFromSoldierline\x18\x07 \x01(\r\x12\x1c\n\x14\x63oinNumFromEnemyHero\x18\x08 \x01(\r\x12\x19\n\x11\x63wjSkill2CtrlTime\x18\t \x01(\x05\x12\x1f\n\x17\x63wjSkill2CtrlToHeroTime\x18\n \x01(\x05\x12\x1a\n\x12\x63wjSkill1HealTotal\x18\x0b \x01(\x05\x12\x1e\n\x16\x63wjSkill1HealTeamMates\x18\x0c \x01(\x05\x12\x1a\n\x12\x63wjSkill3HealTotal\x18\r \x01(\x05\x12\x1e\n\x16\x63wjSkill3HealTeamMates\x18\x0e \x01(\x05\x12+\n#zfSkill2ProtectResistDamageTeamMate\x18\x0f \x01(\x05\x12!\n\x19\x63oinNumFromJungleSelfCamp\x18\x10 \x01(\r\x12\"\n\x1a\x63oinNumFromJungleEnemyCamp\x18\x11 \x01(\r\x12!\n\x19\x63oinNumFromSoldierlineMid\x18\x12 \x01(\r\x12\"\n\x1a\x63oinNumFromSoldierlineFayu\x18\x13 \x01(\r\x12%\n\x1d\x63oinNumFromSoldierlineDuiKang\x18\x14 \x01(\r\x12 \n\x18\x63oinNumFromKillEnemyHero\x18\x15 \x01(\r\x12\"\n\x1a\x63oinNumFromAssistEnemyHero\x18\x16 \x01(\r\x12\x1b\n\x13protectToFriendHero\x18\x17 \x01(\x05\x12\x1a\n\x12protectToHeroTotal\x18\x18 \x01(\x05\x12\x15\n\rlanSkill2Heal\x18\x19 \x01(\x05\"\xe4\x0b\n\tHeroState\x12\x11\n\tplayer_id\x18\x01 \x01(\r\x12,\n\x0b\x61\x63tor_state\x18\x02 \x02(\x0b\x32\x17.sgame_state.ActorState\x12,\n\x0bskill_state\x18\x03 \x02(\x0b\x32\x17.sgame_state.SkillState\x12,\n\x0b\x65quip_state\x18\x04 \x02(\x0b\x32\x17.sgame_state.EquipState\x12*\n\nbuff_state\x18\x05 \x02(\x0b\x32\x16.sgame_state.BuffState\x12\r\n\x05level\x18\x06 \x02(\x05\x12\x0b\n\x03\x65xp\x18\x07 \x02(\x05\x12\r\n\x05money\x18\x08 \x02(\x05\x12\x13\n\x0brevive_time\x18\t \x02(\x05\x12\x0f\n\x07killCnt\x18\n \x02(\x05\x12\x0f\n\x07\x64\x65\x61\x64\x43nt\x18\x0b \x02(\x05\x12\x11\n\tassistCnt\x18\x0c \x02(\x05\x12\x10\n\x08moneyCnt\x18\r \x02(\x05\x12\x11\n\ttotalHurt\x18\x0e \x02(\x05\x12\x17\n\x0ftotalHurtToHero\x18\x0f \x02(\x05\x12\x19\n\x11totalBeHurtByHero\x18\x10 \x02(\x05\x12\x30\n\rpassive_skill\x18\x11 \x03(\x0b\x32\x19.sgame_state.PassiveSkill\x12%\n\x08real_cmd\x18\x12 \x03(\x0b\x32\x13.sgame_state.CmdPkg\x12\x30\n\rtakeHurtInfos\x18\x13 \x03(\x0b\x32\x19.sgame_state.TakeHurtInfo\x12\x18\n\x10\x63\x61nAbortCurSkill\x18\x14 \x03(\x08\x12=\n\x13returnCityAbortInfo\x18\x15 \x03(\x0b\x32 .sgame_state.ReturnCityAbortInfo\x12\x11\n\tisInGrass\x18\x16 \x01(\x08\x12-\n\x0bprotectInfo\x18\x17 \x03(\x0b\x32\x18.sgame_state.ProtectInfo\x12\x13\n\x0b\x63\x61nBuyEquip\x18\x18 \x01(\x08\x12*\n\rcache_cmd_pkg\x18\x19 \x03(\x0b\x32\x13.sgame_state.CmdPkg\x12$\n\x07\x63md_pkg\x18\x1a \x03(\x0b\x32\x13.sgame_state.CmdPkg\x12,\n\x0bjoint_skill\x18\x1b \x01(\x0b\x32\x17.sgame_state.JointSkill\x12\x31\n\x10joint_skill_list\x18\x1c \x03(\x0b\x32\x17.sgame_state.JointSkill\x12&\n\x08ttk_info\x18\x1d \x01(\x0b\x32\x14.sgame_state.TTKInfo\x12\x14\n\x0cskinResCfgId\x18\x1e \x01(\x05\x12\x17\n\x0f\x64\x65stroyTowerCnt\x18\x1f \x01(\x05\x12\x1b\n\x13killLittleDragonCnt\x18  \x01(\x05\x12\x18\n\x10killBigDragonCnt\x18! \x01(\x05\x12\x15\n\rkillDragonCnt\x18\" \x01(\x05\x12\x13\n\x0bkillDarkCnt\x18# \x01(\x05\x12\x16\n\x0ekillOldDarkCnt\x18$ \x01(\x05\x12\x13\n\x0btotalBeHurt\x18% \x01(\x05\x12\x16\n\x0ekillSoldierCnt\x18& \x01(\x05\x12\x13\n\x0bhurtToOrgan\x18\' \x01(\x05\x12\x15\n\rbeHurtByOrgan\x18( \x01(\x05\x12(\n\tstat_info\x18) \x01(\x0b\x32\x15.sgame_state.StatInfo\x12\x1e\n\x16hurtToMageOrArcherHero\x18* \x01(\x05\x12%\n\x1dnumParticipateKillOrganorBase\x18+ \x01(\x05\x12#\n\x1bnumParticipateKillallDragon\x18, \x01(\x05\x12,\n\tevent_pkg\x18- \x03(\x0b\x32\x19.sgame_state.HeroEventPkg\x12\x31\n\rskillStatInfo\x18. \x03(\x0b\x32\x1a.sgame_state.SkillStatInfo\x12\x30\n\nerror_info\x18/ \x03(\x0b\x32\x1c.sgame_state.ClientErrorInfo*\x82\x01\n\x0e\x42uffEffectType\x12\x1b\n\x17\x42uffEffectType_POSITIVE\x10\x00\x12\x1b\n\x17\x42uffEffectType_NEGATIVE\x10\x01\x12\x1a\n\x16\x42uffEffectType_CONTROL\x10\x02\x12\x1a\n\x16\x42uffEffectType_PERSIST\x10\x03*\x94\x01\n\rBuffBlockType\x12\x16\n\x12\x42uffBlockType_NULL\x10\x00\x12\x18\n\x14\x42uffBlockType_NOHURT\x10\x01\x12\x19\n\x15\x42uffBlockType_CONTROL\x10\x02\x12\x18\n\x14\x42uffBlockType_IMMUNE\x10\x03\x12\x1c\n\x18\x42uffBlockType_CLEARFFECT\x10\x04*\x82\x03\n\x0f\x42uffControlType\x12\x18\n\x14\x42uffControlType_NULL\x10\x00\x12\x19\n\x15\x42uffControlType_Dizzy\x10\x01\x12\x1c\n\x18\x42uffControlType_SlowDown\x10\x02\x12\x19\n\x15\x42uffControlType_Taunt\x10\x03\x12\x18\n\x14\x42uffControlType_Fear\x10\x04\x12\x1a\n\x16\x42uffControlType_Frozen\x10\x05\x12\x1c\n\x18\x42uffControlType_Floating\x10\x06\x12\x1a\n\x16\x42uffControlType_Slient\x10\x07\x12\x19\n\x15\x42uffControlType_Stone\x10\x08\x12\x1e\n\x1a\x42uffControlType_Immobilize\x10\t\x12\x1e\n\x1a\x42uffControlType_Oppression\x10\n\x12\x1a\n\x16\x42uffControlType_Towing\x10\x0b\x12\x1a\n\x16\x42uffControlType_Disarm\x10\x0c')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'hero_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_BUFFEFFECTTYPE']._serialized_start=4537
  _globals['_BUFFEFFECTTYPE']._serialized_end=4667
  _globals['_BUFFBLOCKTYPE']._serialized_start=4670
  _globals['_BUFFBLOCKTYPE']._serialized_end=4818
  _globals['_BUFFCONTROLTYPE']._serialized_start=4821
  _globals['_BUFFCONTROLTYPE']._serialized_end=5207
  _globals['_SKILLSLOTSTATE']._serialized_start=70
  _globals['_SKILLSLOTSTATE']._serialized_end=491
  _globals['_SKILLSTATE']._serialized_start=493
  _globals['_SKILLSTATE']._serialized_end=555
  _globals['_BUFFSKILLSTATE']._serialized_start=558
  _globals['_BUFFSKILLSTATE']._serialized_end=863
  _globals['_BUFFMARKSTATE']._serialized_start=865
  _globals['_BUFFMARKSTATE']._serialized_end=955
  _globals['_BUFFFAKEBLOODSTATE']._serialized_start=958
  _globals['_BUFFFAKEBLOODSTATE']._serialized_end=1145
  _globals['_BUFFSTATE']._serialized_start=1148
  _globals['_BUFFSTATE']._serialized_end=1315
  _globals['_PASSIVESKILL']._serialized_start=1317
  _globals['_PASSIVESKILL']._serialized_end=1374
  _globals['_ACTIVESKILL']._serialized_start=1376
  _globals['_ACTIVESKILL']._serialized_end=1431
  _globals['_EQUIPSLOT']._serialized_start=1434
  _globals['_EQUIPSLOT']._serialized_end=1595
  _globals['_EQUIPSTATE']._serialized_start=1597
  _globals['_EQUIPSTATE']._serialized_end=1649
  _globals['_RETURNCITYABORTINFO']._serialized_start=1652
  _globals['_RETURNCITYABORTINFO']._serialized_end=1806
  _globals['_PROTECTINFO']._serialized_start=1809
  _globals['_PROTECTINFO']._serialized_end=1940
  _globals['_JOINTSKILL']._serialized_start=1942
  _globals['_JOINTSKILL']._serialized_end=2008
  _globals['_TTKINFO']._serialized_start=2011
  _globals['_TTKINFO']._serialized_end=2250
  _globals['_STATINFO']._serialized_start=2253
  _globals['_STATINFO']._serialized_end=3023
  _globals['_HEROSTATE']._serialized_start=3026
  _globals['_HEROSTATE']._serialized_end=4534
# @@protoc_insertion_point(module_scope)
