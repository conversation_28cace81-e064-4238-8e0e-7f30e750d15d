# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: game2arena.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import custom_pb2 as custom__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10game2arena.proto\x12\x0bsgame_state\x1a\x0c\x63ustom.proto\"\xb1\x01\n\x0cStepFrameReq\x12\x0f\n\x07game_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12-\n\x0b\x66rame_state\x18\x03 \x02(\x0b\x32\x18.sgame_state.Observation\x12\x12\n\nterminated\x18\x04 \x02(\x05\x12\x11\n\ttruncated\x18\x05 \x02(\x05\x12(\n\tgame_info\x18\x06 \x02(\x0b\x32\x15.sgame_state.GameInfo\"k\n\x0cStepFrameRsp\x12\x0f\n\x07game_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12%\n\x07\x63ommand\x18\x03 \x02(\x0b\x32\x14.sgame_state.Command\x12\x11\n\tstop_game\x18\x04 \x02(\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'game2arena_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_STEPFRAMEREQ']._serialized_start=48
  _globals['_STEPFRAMEREQ']._serialized_end=225
  _globals['_STEPFRAMERSP']._serialized_start=227
  _globals['_STEPFRAMERSP']._serialized_end=334
# @@protoc_insertion_point(module_scope)
