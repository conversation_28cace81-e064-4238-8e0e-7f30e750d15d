# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: kaiwu_aisvr.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import command_pb2 as command__pb2
from . import sgame_state_pb2 as sgame__state__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11kaiwu_aisvr.proto\x12\x0bkaiwu_aisvr\x1a\rcommand.proto\x1a\x11sgame_state.proto\"I\n\rAICommandInfo\x12\x11\n\tplayer_id\x18\x01 \x02(\x05\x12%\n\x08\x63md_info\x18\x02 \x01(\x0b\x32\x13.sgame_state.CmdPkg\"\xa3\x01\n\x0f\x41IServerRequest\x12\x10\n\x08sgame_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12\x10\n\x08gameover\x18\x03 \x01(\x08\x12,\n\x08\x63md_list\x18\x04 \x03(\x0b\x32\x1a.kaiwu_aisvr.AICommandInfo\x12,\n\x0b\x66rame_state\x18\x05 \x01(\x0b\x32\x17.sgame_state.FrameState\"h\n\tSingleReq\x12,\n\x06\x61i_req\x18\x01 \x02(\x0b\x32\x1c.kaiwu_aisvr.AIServerRequest\x12\x10\n\x08req_type\x18\x02 \x02(\x05\x12\x0e\n\x06seq_no\x18\x03 \x02(\x05\x12\x0b\n\x03pid\x18\x04 \x02(\x05\"\\\n\x10\x41IServerResponse\x12,\n\x08\x63md_list\x18\x01 \x03(\x0b\x32\x1a.kaiwu_aisvr.AICommandInfo\x12\x1a\n\x12gameover_ai_server\x18\x02 \x01(\x05\"\x1d\n\nPlayerInfo\x12\x0f\n\x07hero_id\x18\x01 \x02(\x05\"c\n\x08GameInfo\x12,\n\x0bplayer_info\x18\x01 \x03(\x0b\x32\x17.kaiwu_aisvr.PlayerInfo\x12\x11\n\tclient_id\x18\x02 \x01(\t\x12\x16\n\x0e\x63lient_version\x18\x03 \x01(\t\"\xce\x01\n\x14KaiwuAIServerRequest\x12(\n\x08req_list\x18\x01 \x03(\x0b\x32\x16.kaiwu_aisvr.SingleReq\x12\x12\n\nplayer_num\x18\x02 \x01(\x05\x12\x0f\n\x07game_id\x18\x03 \x02(\t\x12+\n\x08msg_type\x18\x04 \x02(\x0e\x32\x19.kaiwu_aisvr.KaiWuMsgType\x12(\n\tgame_info\x18\x05 \x01(\x0b\x32\x15.kaiwu_aisvr.GameInfo\x12\x10\n\x08\x61gent_id\x18\x06 \x01(\x05\"U\n\x15KaiwuAIServerResponse\x12.\n\x07rsp_pbs\x18\x01 \x03(\x0b\x32\x1d.kaiwu_aisvr.AIServerResponse\x12\x0c\n\x04\x63ode\x18\x02 \x02(\x05*e\n\x0cKaiWuMsgType\x12\r\n\tE_UNKNOWN\x10\x00\x12\n\n\x06\x45_INIT\x10\x01\x12\x0e\n\nE_EP_START\x10\x02\x12\x11\n\rE_AGENT_START\x10\x03\x12\x0c\n\x08\x45_UPDATE\x10\x04\x12\t\n\x05\x45_END\x10\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'kaiwu_aisvr_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_KAIWUMSGTYPE']._serialized_start=937
  _globals['_KAIWUMSGTYPE']._serialized_end=1038
  _globals['_AICOMMANDINFO']._serialized_start=68
  _globals['_AICOMMANDINFO']._serialized_end=141
  _globals['_AISERVERREQUEST']._serialized_start=144
  _globals['_AISERVERREQUEST']._serialized_end=307
  _globals['_SINGLEREQ']._serialized_start=309
  _globals['_SINGLEREQ']._serialized_end=413
  _globals['_AISERVERRESPONSE']._serialized_start=415
  _globals['_AISERVERRESPONSE']._serialized_end=507
  _globals['_PLAYERINFO']._serialized_start=509
  _globals['_PLAYERINFO']._serialized_end=538
  _globals['_GAMEINFO']._serialized_start=540
  _globals['_GAMEINFO']._serialized_end=639
  _globals['_KAIWUAISERVERREQUEST']._serialized_start=642
  _globals['_KAIWUAISERVERREQUEST']._serialized_end=848
  _globals['_KAIWUAISERVERRESPONSE']._serialized_start=850
  _globals['_KAIWUAISERVERRESPONSE']._serialized_end=935
# @@protoc_insertion_point(module_scope)
