# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_state.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import hero_pb2 as hero__pb2
from . import scene_pb2 as scene__pb2
from . import sgame_action_pb2 as sgame__action__pb2
from . import wz_highlight_pb2 as wz__highlight__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11sgame_state.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\nhero.proto\x1a\x0bscene.proto\x1a\x12sgame_action.proto\x1a\x12wz_highlight.proto\"\xf1\x01\n\x0cHeroPlayInfo\x12\x12\n\nruntime_id\x18\x01 \x02(\x05\x12\x12\n\nplayer_uid\x18\x02 \x02(\x04\x12\x14\n\x0cplayer_level\x18\x03 \x02(\x05\x12\x12\n\ngrade_rank\x18\x04 \x02(\x05\x12\x0f\n\x07open_id\x18\x05 \x02(\t\x12\x10\n\x08world_id\x18\x06 \x02(\x05\x12\x13\n\x0bproficiency\x18\x07 \x02(\x05\x12\x11\n\tplayer_id\x18\x08 \x02(\r\x12\x13\n\x0bis_computer\x18\t \x01(\x08\x12\x1c\n\x14is_serverai_computer\x18\n \x01(\x08\x12\x11\n\tnick_name\x18\x0b \x01(\t\"\x81\x01\n\nStartState\x12!\n\x06\x62ushes\x18\x01 \x03(\x0b\x32\x11.sgame_state.Bush\x12\x31\n\x0ehero_play_info\x18\x02 \x03(\x0b\x32\x19.sgame_state.HeroPlayInfo\x12\x0e\n\x06map_id\x18\x03 \x01(\x05\x12\r\n\x05level\x18\x04 \x01(\x05\"\xd0\x06\n\nFrameState\x12\x0f\n\x07\x66rameNo\x18\x01 \x02(\x05\x12+\n\x0bhero_states\x18\x02 \x03(\x0b\x32\x16.sgame_state.HeroState\x12+\n\nnpc_states\x18\x03 \x03(\x0b\x32\x17.sgame_state.ActorState\x12$\n\x07\x62ullets\x18\x04 \x03(\x0b\x32\x13.sgame_state.Bullet\x12 \n\x05\x63\x61kes\x18\x05 \x03(\x0b\x32\x11.sgame_state.Cake\x12+\n\x0b\x65quip_infos\x18\x06 \x03(\x0b\x32\x16.sgame_state.EquipInfo\x12&\n\x08nav_data\x18\x07 \x01(\x0b\x32\x14.sgame_state.NavData\x12:\n\x13monster_spawn_infos\x18\x08 \x03(\x0b\x32\x1d.sgame_state.MonsterSpawnInfo\x12\x13\n\x0b\x63\x61mp_scores\x18\t \x01(\x05\x12.\n\x0c\x66rame_action\x18\n \x01(\x0b\x32\x18.sgame_state.FrameAction\x12\x11\n\tmap_state\x18\x0b \x01(\x08\x12\"\n\x06\x62locks\x18\x0c \x03(\x0b\x32\x12.sgame_state.Block\x12/\n\rextra_bullets\x18\r \x03(\x0b\x32\x18.sgame_state.ExtraBullet\x12\x13\n\x0b\x66rame_delta\x18\x0e \x01(\x05\x12\x1c\n\x14originalBreathStates\x18\x0f \x01(\x05\x12\x0f\n\x07room_id\x18\x10 \x01(\t\x12\x1b\n\x13normal_soldier_wave\x18\x11 \x01(\x05\x12\x37\n\x11team_fight_action\x18\x12 \x03(\x0b\x32\x1c.sgame_state.TeamFightAction\x12\x13\n\x0bstorm_level\x18\x13 \x01(\x05\x12\x14\n\x0cstorm_radius\x18\x14 \x01(\x05\x12$\n\x08safe_pos\x18\x15 \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x13\n\x0bsafe_radius\x18\x16 \x01(\x05\x12\x1c\n\x14\x63ombat_effectiveness\x18\x17 \x03(\x05\x12\x33\n\x0f\x65quip_in_ground\x18\x18 \x03(\x0b\x32\x1a.sgame_state.EquipInGround\"5\n\nFrameSlice\x12\'\n\x06\x66rames\x18\x01 \x03(\x0b\x32\x17.sgame_state.FrameState\"G\n\x0c\x43\x61mpStatInfo\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\x05\x12\r\n\x05score\x18\x02 \x02(\x05\x12\r\n\x05money\x18\x03 \x02(\x05\x12\x0b\n\x03\x65xp\x18\x04 \x02(\x05\"\xfc\x03\n\x0cHeroStatInfo\x12\x12\n\nruntime_id\x18\x01 \x02(\x05\x12\r\n\x05score\x18\x02 \x02(\x05\x12\r\n\x05money\x18\x03 \x02(\x05\x12\x1d\n\x15totalHurtToHeroOrigin\x18\x04 \x01(\x05\x12\x1f\n\x17totalBeHurtByHeroOrigin\x18\x05 \x01(\x05\x12\x17\n\x0f\x64\x65stroyTowerCnt\x18\x06 \x01(\x05\x12\x16\n\x0ekillSoidierCnt\x18\x07 \x01(\x05\x12\x16\n\x0eKillMonsterCnt\x18\x08 \x01(\x05\x12\x1b\n\x13KillLittleDragonCnt\x18\t \x01(\x05\x12\x1d\n\x15Hero1KillBigDragonCnt\x18\n \x01(\x05\x12\x17\n\x0fSelfKillDarkCnt\x18\x0b \x01(\x05\x12\x14\n\x0cTotalRedBuff\x18\x0c \x01(\x05\x12\x15\n\rTotalBlueBuff\x18\r \x01(\x05\x12\x14\n\x0cOldMMROfRank\x18\x0e \x01(\x05\x12\x18\n\x10\x41\x63nt1MvpScoreTTH\x18\x0f \x01(\x05\x12\x31\n\rskillStatInfo\x18\x10 \x03(\x0b\x32\x1a.sgame_state.SkillStatInfo\x12\x0f\n\x07healCnt\x18\x11 \x01(\x05\x12\x10\n\x08\x63trlTime\x18\x12 \x01(\x05\x12\x13\n\x0bhurtToOrgan\x18\x13 \x01(\x05\x12\x14\n\x0chorizonValue\x18\x14 \x01(\x05\"\x85\x02\n\nCloseState\x12\x31\n\x0e\x63\x61mp_stat_info\x18\x01 \x03(\x0b\x32\x19.sgame_state.CampStatInfo\x12\x31\n\x0ehero_stat_info\x18\x02 \x03(\x0b\x32\x19.sgame_state.HeroStatInfo\x12<\n\x0ehighlight_info\x18\x03 \x01(\x0b\x32$.sgame_state_highlight.HighlightInfo\x12\x1a\n\x12hero_kill_frame_no\x18\x04 \x03(\x05\x12\x37\n\x11team_fight_action\x18\x05 \x03(\x0b\x32\x1c.sgame_state.TeamFightAction\"\x96\x01\n\nSGameState\x12,\n\x0bstart_state\x18\x01 \x02(\x0b\x32\x17.sgame_state.StartState\x12,\n\x0b\x66rame_state\x18\x02 \x03(\x0b\x32\x17.sgame_state.FrameState\x12,\n\x0b\x63lose_state\x18\x03 \x02(\x0b\x32\x17.sgame_state.CloseState\"G\n\tEquipInfo\x12\x10\n\x08\x65quip_id\x18\x01 \x01(\x05\x12\x13\n\x0b\x65quip_price\x18\x02 \x01(\x05\x12\x13\n\x0b\x65quip_atoms\x18\x03 \x03(\x05\"c\n\x10MonsterSpawnInfo\x12\x12\n\nmonster_id\x18\x01 \x01(\x05\x12\x13\n\x0bspawn_timer\x18\x02 \x01(\x05\x12\x12\n\nborn_pos_x\x18\x03 \x01(\x05\x12\x12\n\nborn_pos_z\x18\x04 \x01(\x05\"6\n\rEquipInGround\x12\x11\n\tshenfu_id\x18\x01 \x02(\x05\x12\x12\n\nshenfu_cnt\x18\x02 \x02(\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_state_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_HEROPLAYINFO']._serialized_start=114
  _globals['_HEROPLAYINFO']._serialized_end=355
  _globals['_STARTSTATE']._serialized_start=358
  _globals['_STARTSTATE']._serialized_end=487
  _globals['_FRAMESTATE']._serialized_start=490
  _globals['_FRAMESTATE']._serialized_end=1338
  _globals['_FRAMESLICE']._serialized_start=1340
  _globals['_FRAMESLICE']._serialized_end=1393
  _globals['_CAMPSTATINFO']._serialized_start=1395
  _globals['_CAMPSTATINFO']._serialized_end=1466
  _globals['_HEROSTATINFO']._serialized_start=1469
  _globals['_HEROSTATINFO']._serialized_end=1977
  _globals['_CLOSESTATE']._serialized_start=1980
  _globals['_CLOSESTATE']._serialized_end=2241
  _globals['_SGAMESTATE']._serialized_start=2244
  _globals['_SGAMESTATE']._serialized_end=2394
  _globals['_EQUIPINFO']._serialized_start=2396
  _globals['_EQUIPINFO']._serialized_end=2467
  _globals['_MONSTERSPAWNINFO']._serialized_start=2469
  _globals['_MONSTERSPAWNINFO']._serialized_end=2568
  _globals['_EQUIPINGROUND']._serialized_start=2570
  _globals['_EQUIPINGROUND']._serialized_end=2624
# @@protoc_insertion_point(module_scope)
