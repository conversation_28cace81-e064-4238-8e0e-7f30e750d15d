# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_rogue_state.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import sgame_state_pb2 as sgame__state__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17sgame_rogue_state.proto\x12\x11sgame_rogue_state\x1a\x0c\x63ommon.proto\x1a\x11sgame_state.proto\"?\n\x0eOneChestOpInfo\x12\x0e\n\x06itemId\x18\x01 \x01(\r\x12\r\n\x05price\x18\x02 \x01(\r\x12\x0e\n\x06opType\x18\x03 \x01(\r\"G\n\x12OneWaveChestOpInfo\x12\x31\n\x06opList\x18\x01 \x03(\x0b\x32!.sgame_rogue_state.OneChestOpInfo\"F\n\x0eOneMonsterInfo\x12\x11\n\tmonsterID\x18\x01 \x01(\r\x12\x10\n\x08totalNum\x18\x02 \x01(\r\x12\x0f\n\x07killNum\x18\x03 \x01(\r\"L\n\x12OneWaveMonsterInfo\x12\x36\n\x0bmonsterList\x18\x01 \x03(\x0b\x32!.sgame_rogue_state.OneMonsterInfo\":\n\rOneShopOpInfo\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05price\x18\x02 \x01(\r\x12\x0e\n\x06opType\x18\x03 \x01(\r\"C\n\x0fOneWaveShopInfo\x12\x30\n\x06opList\x18\x01 \x03(\x0b\x32 .sgame_rogue_state.OneShopOpInfo\"\xe5\x01\n\x13RogueMowPrepareInfo\x12\x12\n\nstartCoins\x18\x01 \x01(\r\x12\x13\n\x0b\x66inishCoins\x18\x02 \x01(\r\x12\x19\n\x11\x63hestRecycleCoins\x18\x03 \x01(\r\x12:\n\x0b\x63hestOpList\x18\x04 \x01(\x0b\x32%.sgame_rogue_state.OneWaveChestOpInfo\x12\x36\n\nshopOpList\x18\x05 \x01(\x0b\x32\".sgame_rogue_state.OneWaveShopInfo\x12\x16\n\x0e\x66inishAttrList\x18\x06 \x01(\t\"\xb8\x04\n\x1cRogueMowSettleOneWaveLogInfo\x12\x13\n\x0b\x64\x61mageValue\x18\x01 \x01(\r\x12\x11\n\tmaxDamage\x18\x02 \x01(\r\x12\x13\n\x0bhurtedValue\x18\x03 \x01(\r\x12\x13\n\x0bhuntedTimes\x18\x04 \x01(\r\x12\x12\n\ndodgeTimes\x18\x05 \x01(\r\x12\x10\n\x08hpBagNum\x18\x06 \x01(\r\x12\x0c\n\x04time\x18\x07 \x01(\r\x12:\n\x0bmonsterInfo\x18\x08 \x01(\x0b\x32%.sgame_rogue_state.OneWaveMonsterInfo\x12;\n\x0bprepareInfo\x18\t \x01(\x0b\x32&.sgame_rogue_state.RogueMowPrepareInfo\x12\x10\n\x08\x63oinsGet\x18\n \x01(\r\x12\x0e\n\x06\x65xpGet\x18\x0b \x01(\r\x12\x14\n\x0citemCoinsUse\x18\x0c \x01(\r\x12\x19\n\x11itemCoinsUseTimes\x18\r \x01(\r\x12\x16\n\x0eweaponCoinsUse\x18\x0e \x01(\r\x12\x1b\n\x13weaponCoinsUseTimes\x18\x0f \x01(\r\x12\x15\n\rpickCoinTimes\x18\x10 \x01(\r\x12\x11\n\tpickHpBag\x18\x11 \x01(\r\x12\x17\n\x0forganMonsterNum\x18\x12 \x01(\r\x12\x1b\n\x13killOrganMonsterNum\x18\x13 \x01(\r\x12\x16\n\x0enormalChestGet\x18\x14 \x01(\r\x12\x19\n\x11highLevelChestGet\x18\x15 \x01(\r\"\xeb\x01\n\x15RogueMowMilestoneData\x12\x1a\n\x12getFullWeaponWaves\x18\x01 \x01(\r\x12\x1c\n\x14\x66irstGetPurpleWeapon\x18\x02 \x01(\r\x12\x1c\n\x14\x66irstGetOrangeWeapon\x18\x03 \x01(\r\x12\x1f\n\x17\x66irstRefreshPurpleGoods\x18\x04 \x01(\r\x12\x1b\n\x13\x66irstBuyPurpleGoods\x18\x05 \x01(\r\x12\x1f\n\x17\x66irstRefreshOrangeGoods\x18\x06 \x01(\r\x12\x1b\n\x13\x66irstBuyOrangeGoods\x18\x07 \x01(\r\"f\n\x16RogueMowSettleDeckInfo\x12\x13\n\x0btotalDamage\x18\x01 \x01(\r\x12\x12\n\nuseCoinNum\x18\x02 \x01(\r\x12\x0f\n\x07\x62uyCoin\x18\x03 \x01(\r\x12\x12\n\nremainCoin\x18\x04 \x01(\r\"V\n RogueMowBossOneWaveChallengeInfo\x12\x0c\n\x04wave\x18\x01 \x01(\r\x12\x10\n\x08itemList\x18\x02 \x03(\r\x12\x12\n\nweaponList\x18\x03 \x03(\r\"g\n\x19RogueMowBossChallengeInfo\x12J\n\rchallengeInfo\x18\x01 \x03(\x0b\x32\x33.sgame_rogue_state.RogueMowBossOneWaveChallengeInfo\"\xa2\x03\n\x15RogueMowSettleLogInfo\x12\r\n\x05waves\x18\x01 \x01(\r\x12\x41\n\x08waveInfo\x18\x02 \x03(\x0b\x32/.sgame_rogue_state.RogueMowSettleOneWaveLogInfo\x12>\n\x08\x62ossInfo\x18\x03 \x01(\x0b\x32,.sgame_rogue_state.RogueMowBossChallengeInfo\x12\x39\n\tstartInfo\x18\x04 \x01(\x0b\x32&.sgame_rogue_state.RogueMowPrepareInfo\x12;\n\x08\x64\x65\x63kInfo\x18\x05 \x01(\x0b\x32).sgame_rogue_state.RogueMowSettleDeckInfo\x12?\n\rmileStoneInfo\x18\x06 \x01(\x0b\x32(.sgame_rogue_state.RogueMowMilestoneData\x12\x16\n\x0esettleAttrList\x18\x07 \x01(\t\x12\x17\n\x0f\x41\x63hieveMentList\x18\x08 \x03(\r\x12\r\n\x05score\x18\t \x01(\r\"w\n\x11RogueMowCheckData\x12\x10\n\x08playerID\x18\x01 \x01(\r\x12\x12\n\ndifficulty\x18\x02 \x01(\r\x12<\n\nsettleInfo\x18\x03 \x01(\x0b\x32(.sgame_rogue_state.RogueMowSettleLogInfo\"\x81\x01\n\x0fRogueCloseState\x12\x30\n\x0f\x63ommonCloseInfo\x18\x01 \x01(\x0b\x32\x17.sgame_state.CloseState\x12<\n\x0erogueCloseInfo\x18\x02 \x03(\x0b\x32$.sgame_rogue_state.RogueMowCheckData')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_rogue_state_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ONECHESTOPINFO']._serialized_start=79
  _globals['_ONECHESTOPINFO']._serialized_end=142
  _globals['_ONEWAVECHESTOPINFO']._serialized_start=144
  _globals['_ONEWAVECHESTOPINFO']._serialized_end=215
  _globals['_ONEMONSTERINFO']._serialized_start=217
  _globals['_ONEMONSTERINFO']._serialized_end=287
  _globals['_ONEWAVEMONSTERINFO']._serialized_start=289
  _globals['_ONEWAVEMONSTERINFO']._serialized_end=365
  _globals['_ONESHOPOPINFO']._serialized_start=367
  _globals['_ONESHOPOPINFO']._serialized_end=425
  _globals['_ONEWAVESHOPINFO']._serialized_start=427
  _globals['_ONEWAVESHOPINFO']._serialized_end=494
  _globals['_ROGUEMOWPREPAREINFO']._serialized_start=497
  _globals['_ROGUEMOWPREPAREINFO']._serialized_end=726
  _globals['_ROGUEMOWSETTLEONEWAVELOGINFO']._serialized_start=729
  _globals['_ROGUEMOWSETTLEONEWAVELOGINFO']._serialized_end=1297
  _globals['_ROGUEMOWMILESTONEDATA']._serialized_start=1300
  _globals['_ROGUEMOWMILESTONEDATA']._serialized_end=1535
  _globals['_ROGUEMOWSETTLEDECKINFO']._serialized_start=1537
  _globals['_ROGUEMOWSETTLEDECKINFO']._serialized_end=1639
  _globals['_ROGUEMOWBOSSONEWAVECHALLENGEINFO']._serialized_start=1641
  _globals['_ROGUEMOWBOSSONEWAVECHALLENGEINFO']._serialized_end=1727
  _globals['_ROGUEMOWBOSSCHALLENGEINFO']._serialized_start=1729
  _globals['_ROGUEMOWBOSSCHALLENGEINFO']._serialized_end=1832
  _globals['_ROGUEMOWSETTLELOGINFO']._serialized_start=1835
  _globals['_ROGUEMOWSETTLELOGINFO']._serialized_end=2253
  _globals['_ROGUEMOWCHECKDATA']._serialized_start=2255
  _globals['_ROGUEMOWCHECKDATA']._serialized_end=2374
  _globals['_ROGUECLOSESTATE']._serialized_start=2377
  _globals['_ROGUECLOSESTATE']._serialized_end=2506
# @@protoc_insertion_point(module_scope)
