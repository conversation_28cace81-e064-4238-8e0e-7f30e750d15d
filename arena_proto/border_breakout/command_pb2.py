# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: command.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rcommand.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\"1\n\nPrmMovePos\x12#\n\x07\x64\x65stPos\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\")\n\nPrmMoveDir\x12\x0e\n\x06\x64\x65gree\x18\x01 \x02(\x05\x12\x0b\n\x03seq\x18\x02 \x02(\x05\"H\n\x0fPrmAttackCommon\x12\r\n\x05start\x18\x01 \x02(\x05\x12\x0f\n\x07\x61\x63torID\x18\x02 \x02(\r\x12\x15\n\rsrcActorCfgId\x18\x03 \x01(\x05\"5\n\x0ePrmAttackToPos\x12#\n\x07\x64\x65stPos\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\"!\n\x0ePrmAttackActor\x12\x0f\n\x07\x61\x63torID\x18\x01 \x02(\r\"t\n\x0bPrmObjSkill\x12\x0f\n\x07\x61\x63torID\x18\x01 \x02(\r\x12\x0f\n\x07skillID\x18\x02 \x02(\r\x12,\n\x08slotType\x18\x03 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x15\n\rsrcActorCfgId\x18\x04 \x01(\x05\"\x84\x01\n\x0bPrmDirSkill\x12\x0f\n\x07\x61\x63torID\x18\x01 \x02(\r\x12\x0f\n\x07skillID\x18\x02 \x02(\r\x12,\n\x08slotType\x18\x03 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x0e\n\x06\x64\x65gree\x18\x04 \x02(\x05\x12\x15\n\rsrcActorCfgId\x18\x05 \x01(\x05\"\x88\x01\n\x0bPrmPosSkill\x12\x0f\n\x07skillID\x18\x01 \x02(\r\x12#\n\x07\x64\x65stPos\x18\x02 \x02(\x0b\x32\x12.sgame_state.VInt3\x12,\n\x08slotType\x18\x03 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x15\n\rsrcActorCfgId\x18\x04 \x01(\x05\"L\n\rPrmLearnSkill\x12,\n\x08slotType\x18\x01 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05level\x18\x02 \x02(\x05\".\n\x0bPrmBuyEquip\x12\x0f\n\x07\x65quipId\x18\x01 \x02(\x05\x12\x0e\n\x06obj_id\x18\x02 \x01(\x05\"\"\n\x0cPrmSellEquip\x12\x12\n\nequipIndex\x18\x01 \x02(\x05\",\n\x16PrmSetAttackTargetMode\x12\x12\n\natkTarMode\x18\x01 \x02(\x05\"E\n PrmPlayLastHitAndAttackOrganMode\x12!\n\x19lastHitAndAttackOrganMode\x18\x01 \x02(\r\")\n\x13PrmLockAttackTarget\x12\x12\n\nlockAtkTar\x18\x01 \x02(\r\",\n\x16PrmSetCommonAttackMode\x12\x12\n\ncomAtkMode\x18\x01 \x02(\x05\"$\n\x14PrmSwitchActorAutoAI\x12\x0c\n\x04isAI\x18\x01 \x02(\x05\"!\n\x10PrmSwitchCaptain\x12\r\n\x05objID\x18\x01 \x02(\r\".\n\x1bPrmGMSwitchActorSuperKiller\x12\x0f\n\x07isSuper\x18\x01 \x02(\x05\"+\n\x18PrmPlayerOneStepBuyEquip\x12\x0f\n\x07\x65quipId\x18\x01 \x03(\r\"T\n\x10PrmGMAddGoldCoin\x12\x10\n\x08\x61\x64\x64Value\x18\x01 \x02(\r\x12\x15\n\ruseGMPlayerID\x18\x02 \x02(\r\x12\x17\n\x0f\x61\x64\x64GoldPlayerID\x18\x03 \x02(\r\"/\n\x12PrmGMSetSkillLevel\x12\x0c\n\x04slot\x18\x01 \x02(\r\x12\x0b\n\x03lvl\x18\x02 \x02(\r\"7\n&PrmPlayerChangeUsedRecommendEquipGroup\x12\r\n\x05group\x18\x01 \x02(\r\"d\n\x18PrmPlayerChoseEquipSkill\x12\x18\n\x10\x65quipIndexInGrid\x18\x01 \x02(\r\x12\x0f\n\x07\x65quipID\x18\x02 \x01(\r\x12\x1d\n\x15\x65quipActiveSkillIndex\x18\x03 \x01(\r\"\x89\x01\n\x0ePrmPlayerCheat\x12\x14\n\x0c\x63trlPlayerID\x18\x01 \x02(\r\x12\x11\n\tcheatType\x18\x02 \x02(\r\x12\x0e\n\x06param1\x18\x03 \x02(\x05\x12\x0e\n\x06param2\x18\x04 \x02(\x05\x12\x0e\n\x06param3\x18\x05 \x02(\x05\x12\x0e\n\x06param4\x18\x06 \x01(\x05\x12\x0e\n\x06param5\x18\x07 \x01(\x05\"$\n\rPrmHeroSwitch\x12\x13\n\x0binHeroCfgId\x18\x01 \x02(\r\"_\n\x10PrmUseJointSkill\x12\r\n\x05objID\x18\x01 \x02(\r\x12\x10\n\x08uniqueID\x18\x02 \x01(\x05\x12\x15\n\rsourceActorID\x18\x03 \x01(\r\x12\x13\n\x0bsubTargetID\x18\x04 \x01(\x05\"M\n!PrmPlayerContinueCommonAttackMode\x12(\n byPlayerContinueCommonAttackMode\x18\x01 \x02(\r\"K\n\x11PrmAssistStateChg\x12\x0f\n\x07\x63hgType\x18\x01 \x02(\r\x12\x13\n\x0b\x61ssPlayerID\x18\x02 \x02(\r\x12\x10\n\x08masterID\x18\x03 \x02(\r\"7\n\x0fPrmSvrAutoAIChg\x12\x0f\n\x07\x63hgType\x18\x01 \x02(\r\x12\x13\n\x0b\x63hgPlayerID\x18\x02 \x02(\r\"#\n\x11PrmSvrNtfGameOver\x12\x0e\n\x06winCmp\x18\x01 \x02(\r\"a\n\x14PrmSignalBtnPosition\x12\x10\n\x08signalID\x18\x01 \x01(\r\x12$\n\x08worldPos\x18\x02 \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x11\n\ttimeStamp\x18\x03 \x01(\r\"\x8f\x01\n\x18PrmSignalMiniMapPosition\x12\x10\n\x08signalID\x18\x01 \x01(\r\x12$\n\x08worldPos\x18\x02 \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x13\n\x0b\x65lementType\x18\x03 \x01(\r\x12\x11\n\ttimeStamp\x18\x04 \x01(\r\x12\x13\n\x0bsenderObjID\x18\x05 \x01(\r\"d\n\x16PrmSignalMiniMapTarget\x12\x10\n\x08signalID\x18\x01 \x01(\r\x12\x10\n\x08targetID\x18\x02 \x01(\r\x12\x13\n\x0b\x65lementType\x18\x03 \x01(\r\x12\x11\n\ttimeStamp\x18\x04 \x01(\r\"]\n\x0ePrmChargeSkill\x12,\n\x08slotType\x18\x01 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05state\x18\x02 \x02(\r\x12\x0e\n\x06\x64\x65gree\x18\x03 \x02(\x05\"&\n\x14PrmSetSkillDirection\x12\x0e\n\x06\x64\x65gree\x18\x01 \x02(\x05\";\n\x13PrmSetSkillPosition\x12$\n\x08position\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\"W\n\x10PrmControlCamera\x12\x13\n\x0b\x63ontrolType\x18\x01 \x01(\x05\x12\x0e\n\x06param1\x18\x02 \x01(\x05\x12\x0e\n\x06param2\x18\x03 \x01(\x05\x12\x0e\n\x06param3\x18\x04 \x01(\x05\"(\n\tPrmRotate\x12\x0e\n\x06\x64\x65gree\x18\x01 \x01(\x05\x12\x0b\n\x03seq\x18\x02 \x01(\r\"\x99\x01\n\x1cPrmAIOverallSenseInstruction\x12\x11\n\tplayer_id\x18\x01 \x01(\r\x12\x0e\n\x06param1\x18\x02 \x01(\x05\x12\x0e\n\x06param2\x18\x03 \x01(\x05\x12\x0e\n\x06param3\x18\x04 \x01(\x05\x12\x10\n\x08kill_cnt\x18\x05 \x01(\x05\x12\x11\n\tkill_prob\x18\x06 \x01(\x05\x12\x11\n\tdead_prob\x18\x07 \x01(\x05\"2\n\x0cPrmPlayEmoji\x12\x0f\n\x07\x65mojiId\x18\x01 \x01(\r\x12\x11\n\ttextIndex\x18\x02 \x01(\r\"/\n\x1bPrmDimensionHeroChangeSkill\x12\x10\n\x08jobOrder\x18\x01 \x01(\r\"\xe3\x17\n\x06\x43mdPkg\x12.\n\x0c\x63ommand_type\x18\x01 \x02(\x0e\x32\x18.sgame_state.CommandType\x12-\n\x0cprm_move_pos\x18\x02 \x01(\x0b\x32\x17.sgame_state.PrmMovePos\x12-\n\x0cprm_move_dir\x18\x03 \x01(\x0b\x32\x17.sgame_state.PrmMoveDir\x12\x37\n\x11prm_attack_common\x18\x04 \x01(\x0b\x32\x1c.sgame_state.PrmAttackCommon\x12\x35\n\x10prm_attack_topos\x18\x05 \x01(\x0b\x32\x1b.sgame_state.PrmAttackToPos\x12\x35\n\x10prm_attack_actor\x18\x06 \x01(\x0b\x32\x1b.sgame_state.PrmAttackActor\x12/\n\rprm_obj_skill\x18\x07 \x01(\x0b\x32\x18.sgame_state.PrmObjSkill\x12/\n\rprm_dir_skill\x18\x08 \x01(\x0b\x32\x18.sgame_state.PrmDirSkill\x12/\n\rprm_pos_skill\x18\t \x01(\x0b\x32\x18.sgame_state.PrmPosSkill\x12\x33\n\x0fprm_learn_skill\x18\n \x01(\x0b\x32\x1a.sgame_state.PrmLearnSkill\x12/\n\rprm_buy_equip\x18\x0b \x01(\x0b\x32\x18.sgame_state.PrmBuyEquip\x12\x31\n\x0eprm_sell_equip\x18\x0c \x01(\x0b\x32\x19.sgame_state.PrmSellEquip\x12\x35\n\x10prm_charge_skill\x18\r \x01(\x0b\x32\x1b.sgame_state.PrmChargeSkill\x12G\n\x1aprm_set_attack_target_mode\x18\x0e \x01(\x0b\x32#.sgame_state.PrmSetAttackTargetMode\x12^\n\'prm_play_last_hit_and_attack_organ_mode\x18\x0f \x01(\x0b\x32-.sgame_state.PrmPlayLastHitAndAttackOrganMode\x12@\n\x16prm_lock_attack_target\x18\x10 \x01(\x0b\x32 .sgame_state.PrmLockAttackTarget\x12G\n\x1aprm_set_common_attack_mode\x18\x11 \x01(\x0b\x32#.sgame_state.PrmSetCommonAttackMode\x12\x42\n\x17prm_switch_actor_autoai\x18\x12 \x01(\x0b\x32!.sgame_state.PrmSwitchActorAutoAI\x12\x39\n\x12prm_switch_captain\x18\x13 \x01(\x0b\x32\x1d.sgame_state.PrmSwitchCaptain\x12R\n prm_gm_switch_actor_super_killer\x18\x14 \x01(\x0b\x32(.sgame_state.PrmGMSwitchActorSuperKiller\x12L\n\x1dprm_player_one_step_buy_equip\x18\x15 \x01(\x0b\x32%.sgame_state.PrmPlayerOneStepBuyEquip\x12;\n\x14prm_gm_add_gold_coin\x18\x16 \x01(\x0b\x32\x1d.sgame_state.PrmGMAddGoldCoin\x12?\n\x16prm_gm_set_skill_level\x18\x17 \x01(\x0b\x32\x1f.sgame_state.PrmGMSetSkillLevel\x12i\n,prm_player_change_used_recommend_equip_group\x18\x18 \x01(\x0b\x32\x33.sgame_state.PrmPlayerChangeUsedRecommendEquipGroup\x12K\n\x1cprm_player_chose_equip_skill\x18\x19 \x01(\x0b\x32%.sgame_state.PrmPlayerChoseEquipSkill\x12\x35\n\x10prm_player_cheat\x18\x1a \x01(\x0b\x32\x1b.sgame_state.PrmPlayerCheat\x12\x33\n\x0fprm_hero_switch\x18\x1b \x01(\x0b\x32\x1a.sgame_state.PrmHeroSwitch\x12:\n\x13prm_use_joint_skill\x18\x1c \x01(\x0b\x32\x1d.sgame_state.PrmUseJointSkill\x12^\n&prm_player_continue_common_attack_mode\x18\x1d \x01(\x0b\x32..sgame_state.PrmPlayerContinueCommonAttackMode\x12<\n\x14prm_assist_state_chg\x18\x1e \x01(\x0b\x32\x1e.sgame_state.PrmAssistStateChg\x12\x38\n\x12prm_svr_autoai_chg\x18\x1f \x01(\x0b\x32\x1c.sgame_state.PrmSvrAutoAIChg\x12<\n\x14prm_svr_ntf_gameover\x18  \x01(\x0b\x32\x1e.sgame_state.PrmSvrNtfGameOver\x12\x42\n\x17prm_signal_btn_position\x18! \x01(\x0b\x32!.sgame_state.PrmSignalBtnPosition\x12J\n\x1bprm_signal_minimap_position\x18\" \x01(\x0b\x32%.sgame_state.PrmSignalMiniMapPosition\x12\x46\n\x19prm_signal_minimap_target\x18# \x01(\x0b\x32#.sgame_state.PrmSignalMiniMapTarget\x12\x42\n\x17prm_set_skill_direction\x18$ \x01(\x0b\x32!.sgame_state.PrmSetSkillDirection\x12@\n\x16prm_set_skill_position\x18% \x01(\x0b\x32 .sgame_state.PrmSetSkillPosition\x12\x39\n\x12prm_control_camera\x18& \x01(\x0b\x32\x1d.sgame_state.PrmControlCamera\x12*\n\nprm_rotate\x18\' \x01(\x0b\x32\x16.sgame_state.PrmRotate\x12S\n prm_ai_overall_sense_instruction\x18( \x01(\x0b\x32).sgame_state.PrmAIOverallSenseInstruction\x12\x31\n\x0eprm_play_emoji\x18) \x01(\x0b\x32\x19.sgame_state.PrmPlayEmoji\x12Q\n\x1fprm_dimension_hero_change_skill\x18* \x01(\x0b\x32(.sgame_state.PrmDimensionHeroChangeSkill\x12\x37\n\x11prm_jump_umbrella\x18+ \x01(\x0b\x32\x1c.sgame_state.PrmJumpUmbrella\x12\x31\n\x0eprm_pick_equip\x18, \x01(\x0b\x32\x19.sgame_state.PrmPickEquip\x12>\n\x15prm_drop_shenfu_equip\x18- \x01(\x0b\x32\x1f.sgame_state.PrmDropShenfuEquip\x12:\n\x13prm_sell_pick_equip\x18. \x01(\x0b\x32\x1d.sgame_state.PrmSellPickEquip\x12>\n\x15prm_use_consume_equip\x18/ \x01(\x0b\x32\x1f.sgame_state.PrmUseConsumeEquip\x12\x30\n\rprm_transport\x18\x30 \x01(\x0b\x32\x19.sgame_state.PrmTransport\"D\n\x0fPrmJumpUmbrella\x12\x0e\n\x06isHalt\x18\x01 \x02(\r\x12\x0e\n\x06\x64\x65gree\x18\x02 \x02(\r\x12\x11\n\tjumpSpeed\x18\x03 \x02(\r\"4\n\x0cPrmPickEquip\x12\x10\n\x08shenfuId\x18\x01 \x02(\x05\x12\x12\n\nequipIndex\x18\x02 \x02(\x05\"9\n\x12PrmDropShenfuEquip\x12\x12\n\nequipIndex\x18\x01 \x02(\x05\x12\x0f\n\x07\x64ropnum\x18\x02 \x02(\x05\"$\n\x10PrmSellPickEquip\x12\x10\n\x08shenfuId\x18\x01 \x02(\x05\"(\n\x12PrmUseConsumeEquip\x12\x12\n\nequipIndex\x18\x01 \x02(\x05\"3\n\x0cPrmTransport\x12#\n\x07\x64\x65stPos\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3*\xbd\x01\n\nSignalType\x12\x14\n\x10SIGNAL_TYPE_None\x10\x00\x12\x19\n\x15SIGNAL_TYPE_Attention\x10\x01\x12\x16\n\x12SIGNAL_TYPE_Attack\x10\x02\x12\x17\n\x13SIGNAL_TYPE_Retreat\x10\x03\x12\x16\n\x12SIGNAL_TYPE_Gather\x10\x04\x12\x17\n\x13SIGNAL_TYPE_Protect\x10\x05\x12\x1c\n\x18SIGNAL_TYPE_BigDragonOut\x10\x06*\xb5\x0f\n\x0b\x43ommandType\x12\x15\n\x11\x43OMMAND_TYPE_None\x10\x00\x12\x18\n\x14\x43OMMAND_TYPE_MovePos\x10\x01\x12\x18\n\x14\x43OMMAND_TYPE_MoveDir\x10\x02\x12\x19\n\x15\x43OMMAND_TYPE_MoveStop\x10\x03\x12\x1d\n\x19\x43OMMAND_TYPE_AttackCommon\x10\x04\x12\x1c\n\x18\x43OMMAND_TYPE_AttackToPos\x10\x05\x12\x1c\n\x18\x43OMMAND_TYPE_AttackActor\x10\x06\x12\x19\n\x15\x43OMMAND_TYPE_ObjSkill\x10\x07\x12\x19\n\x15\x43OMMAND_TYPE_DirSkill\x10\x08\x12\x19\n\x15\x43OMMAND_TYPE_PosSkill\x10\t\x12\x1b\n\x17\x43OMMAND_TYPE_LearnSkill\x10\n\x12\x19\n\x15\x43OMMAND_TYPE_BuyEquip\x10\x0b\x12\x1a\n\x16\x43OMMAND_TYPE_SellEquip\x10\x0c\x12\x1c\n\x18\x43OMMAND_TYPE_ChargeSkill\x10\r\x12$\n COMMAND_TYPE_SetAttackTargetMode\x10\x0e\x12.\n*COMMAND_TYPE_PlayLastHitAndAttackOrganMode\x10\x0f\x12!\n\x1d\x43OMMAND_TYPE_LockAttackTarget\x10\x10\x12$\n COMMAND_TYPE_SetCommonAttackMode\x10\x11\x12\"\n\x1e\x43OMMAND_TYPE_SwitchActorAutoAI\x10\x12\x12\x1e\n\x1a\x43OMMAND_TYPE_SwitchCaptain\x10\x13\x12)\n%COMMAND_TYPE_GMSwitchActorSuperKiller\x10\x14\x12%\n!COMMAND_TYPE_GMSwitchActorGodMode\x10\x15\x12&\n\"COMMAND_TYPE_PlayerOneStepBuyEquip\x10\x16\x12\x1e\n\x1a\x43OMMAND_TYPE_GMAddGoldCoin\x10\x17\x12 \n\x1c\x43OMMAND_TYPE_GMSetSkillLevel\x10\x18\x12\x34\n0COMMAND_TYPE_PlayerChangeUsedRecommendEquipGroup\x10\x19\x12&\n\"COMMAND_TYPE_PlayerChoseEquipSkill\x10\x1a\x12\x1c\n\x18\x43OMMAND_TYPE_PlayerCheat\x10\x1b\x12\x1b\n\x17\x43OMMAND_TYPE_HeroSwitch\x10\x1c\x12\x1e\n\x1a\x43OMMAND_TYPE_UseJointSkill\x10\x1d\x12%\n!COMMAND_TYPE_PlayerInOutEquipShop\x10\x1e\x12\"\n\x1e\x43OMMAND_TYPE_SignalBtnPosition\x10\x1f\x12&\n\"COMMAND_TYPE_SignalMiniMapPosition\x10 \x12$\n COMMAND_TYPE_SignalMiniMapTarget\x10!\x12/\n+COMMAND_TYPE_PlayerContinueCommonAttackMode\x10\"\x12\x1e\n\x1a\x43OMMAND_TYPE_SvrDisconnect\x10#\x12\x1f\n\x1b\x43OMMAND_TYPE_SvrReconnected\x10$\x12\x1b\n\x17\x43OMMAND_TYPE_SvrRunaway\x10%\x12\x1f\n\x1b\x43OMMAND_TYPE_AssistStateChg\x10&\x12\x1d\n\x19\x43OMMAND_TYPE_SvrAutoAIChg\x10\'\x12\x1f\n\x1b\x43OMMAND_TYPE_SvrNtfGameOver\x10(\x12\x15\n\x11\x43OMMAND_TYPE_Dead\x10)\x12\x1c\n\x18\x43OMMAND_TYPE_InvalidHero\x10*\x12\"\n\x1e\x43OMMAND_TYPE_SetSkillDirection\x10+\x12!\n\x1d\x43OMMAND_TYPE_SetSkillPosition\x10,\x12\x1e\n\x1a\x43OMMAND_TYPE_ControlCamera\x10-\x12\x1c\n\x18\x43OMMAND_TYPE_InBattleMsg\x10.\x12#\n\x1f\x43OMMAND_TYPE_HciMinimapResponse\x10/\x12\x17\n\x13\x43OMMAND_TYPE_Rotate\x10\x30\x12\x1b\n\x17\x43OMMAND_TYPE_StopRotate\x10\x31\x12*\n&COMMAND_TYPE_AIOverallSenseInstruction\x10\x32\x12\x1a\n\x16\x43OMMAND_TYPE_PlayEmoji\x10\x33\x12)\n%COMMAND_TYPE_DimensionHeroChangeSkill\x10\x34\x12\x1d\n\x19\x43OMMAND_TYPE_JumpUmbrella\x10\x35\x12\x1a\n\x16\x43OMMAND_TYPE_PickEquip\x10\x36\x12 \n\x1c\x43OMMAND_TYPE_DropShenfuEquip\x10\x37\x12\x1e\n\x1a\x43OMMAND_TYPE_SellPickEquip\x10\x38\x12 \n\x1c\x43OMMAND_TYPE_UseConsumeEquip\x10\x39\x12\x1a\n\x16\x43OMMAND_TYPE_Transport\x10:')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'command_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_SIGNALTYPE']._serialized_start=6341
  _globals['_SIGNALTYPE']._serialized_end=6530
  _globals['_COMMANDTYPE']._serialized_start=6533
  _globals['_COMMANDTYPE']._serialized_end=8506
  _globals['_PRMMOVEPOS']._serialized_start=44
  _globals['_PRMMOVEPOS']._serialized_end=93
  _globals['_PRMMOVEDIR']._serialized_start=95
  _globals['_PRMMOVEDIR']._serialized_end=136
  _globals['_PRMATTACKCOMMON']._serialized_start=138
  _globals['_PRMATTACKCOMMON']._serialized_end=210
  _globals['_PRMATTACKTOPOS']._serialized_start=212
  _globals['_PRMATTACKTOPOS']._serialized_end=265
  _globals['_PRMATTACKACTOR']._serialized_start=267
  _globals['_PRMATTACKACTOR']._serialized_end=300
  _globals['_PRMOBJSKILL']._serialized_start=302
  _globals['_PRMOBJSKILL']._serialized_end=418
  _globals['_PRMDIRSKILL']._serialized_start=421
  _globals['_PRMDIRSKILL']._serialized_end=553
  _globals['_PRMPOSSKILL']._serialized_start=556
  _globals['_PRMPOSSKILL']._serialized_end=692
  _globals['_PRMLEARNSKILL']._serialized_start=694
  _globals['_PRMLEARNSKILL']._serialized_end=770
  _globals['_PRMBUYEQUIP']._serialized_start=772
  _globals['_PRMBUYEQUIP']._serialized_end=818
  _globals['_PRMSELLEQUIP']._serialized_start=820
  _globals['_PRMSELLEQUIP']._serialized_end=854
  _globals['_PRMSETATTACKTARGETMODE']._serialized_start=856
  _globals['_PRMSETATTACKTARGETMODE']._serialized_end=900
  _globals['_PRMPLAYLASTHITANDATTACKORGANMODE']._serialized_start=902
  _globals['_PRMPLAYLASTHITANDATTACKORGANMODE']._serialized_end=971
  _globals['_PRMLOCKATTACKTARGET']._serialized_start=973
  _globals['_PRMLOCKATTACKTARGET']._serialized_end=1014
  _globals['_PRMSETCOMMONATTACKMODE']._serialized_start=1016
  _globals['_PRMSETCOMMONATTACKMODE']._serialized_end=1060
  _globals['_PRMSWITCHACTORAUTOAI']._serialized_start=1062
  _globals['_PRMSWITCHACTORAUTOAI']._serialized_end=1098
  _globals['_PRMSWITCHCAPTAIN']._serialized_start=1100
  _globals['_PRMSWITCHCAPTAIN']._serialized_end=1133
  _globals['_PRMGMSWITCHACTORSUPERKILLER']._serialized_start=1135
  _globals['_PRMGMSWITCHACTORSUPERKILLER']._serialized_end=1181
  _globals['_PRMPLAYERONESTEPBUYEQUIP']._serialized_start=1183
  _globals['_PRMPLAYERONESTEPBUYEQUIP']._serialized_end=1226
  _globals['_PRMGMADDGOLDCOIN']._serialized_start=1228
  _globals['_PRMGMADDGOLDCOIN']._serialized_end=1312
  _globals['_PRMGMSETSKILLLEVEL']._serialized_start=1314
  _globals['_PRMGMSETSKILLLEVEL']._serialized_end=1361
  _globals['_PRMPLAYERCHANGEUSEDRECOMMENDEQUIPGROUP']._serialized_start=1363
  _globals['_PRMPLAYERCHANGEUSEDRECOMMENDEQUIPGROUP']._serialized_end=1418
  _globals['_PRMPLAYERCHOSEEQUIPSKILL']._serialized_start=1420
  _globals['_PRMPLAYERCHOSEEQUIPSKILL']._serialized_end=1520
  _globals['_PRMPLAYERCHEAT']._serialized_start=1523
  _globals['_PRMPLAYERCHEAT']._serialized_end=1660
  _globals['_PRMHEROSWITCH']._serialized_start=1662
  _globals['_PRMHEROSWITCH']._serialized_end=1698
  _globals['_PRMUSEJOINTSKILL']._serialized_start=1700
  _globals['_PRMUSEJOINTSKILL']._serialized_end=1795
  _globals['_PRMPLAYERCONTINUECOMMONATTACKMODE']._serialized_start=1797
  _globals['_PRMPLAYERCONTINUECOMMONATTACKMODE']._serialized_end=1874
  _globals['_PRMASSISTSTATECHG']._serialized_start=1876
  _globals['_PRMASSISTSTATECHG']._serialized_end=1951
  _globals['_PRMSVRAUTOAICHG']._serialized_start=1953
  _globals['_PRMSVRAUTOAICHG']._serialized_end=2008
  _globals['_PRMSVRNTFGAMEOVER']._serialized_start=2010
  _globals['_PRMSVRNTFGAMEOVER']._serialized_end=2045
  _globals['_PRMSIGNALBTNPOSITION']._serialized_start=2047
  _globals['_PRMSIGNALBTNPOSITION']._serialized_end=2144
  _globals['_PRMSIGNALMINIMAPPOSITION']._serialized_start=2147
  _globals['_PRMSIGNALMINIMAPPOSITION']._serialized_end=2290
  _globals['_PRMSIGNALMINIMAPTARGET']._serialized_start=2292
  _globals['_PRMSIGNALMINIMAPTARGET']._serialized_end=2392
  _globals['_PRMCHARGESKILL']._serialized_start=2394
  _globals['_PRMCHARGESKILL']._serialized_end=2487
  _globals['_PRMSETSKILLDIRECTION']._serialized_start=2489
  _globals['_PRMSETSKILLDIRECTION']._serialized_end=2527
  _globals['_PRMSETSKILLPOSITION']._serialized_start=2529
  _globals['_PRMSETSKILLPOSITION']._serialized_end=2588
  _globals['_PRMCONTROLCAMERA']._serialized_start=2590
  _globals['_PRMCONTROLCAMERA']._serialized_end=2677
  _globals['_PRMROTATE']._serialized_start=2679
  _globals['_PRMROTATE']._serialized_end=2719
  _globals['_PRMAIOVERALLSENSEINSTRUCTION']._serialized_start=2722
  _globals['_PRMAIOVERALLSENSEINSTRUCTION']._serialized_end=2875
  _globals['_PRMPLAYEMOJI']._serialized_start=2877
  _globals['_PRMPLAYEMOJI']._serialized_end=2927
  _globals['_PRMDIMENSIONHEROCHANGESKILL']._serialized_start=2929
  _globals['_PRMDIMENSIONHEROCHANGESKILL']._serialized_end=2976
  _globals['_CMDPKG']._serialized_start=2979
  _globals['_CMDPKG']._serialized_end=6022
  _globals['_PRMJUMPUMBRELLA']._serialized_start=6024
  _globals['_PRMJUMPUMBRELLA']._serialized_end=6092
  _globals['_PRMPICKEQUIP']._serialized_start=6094
  _globals['_PRMPICKEQUIP']._serialized_end=6146
  _globals['_PRMDROPSHENFUEQUIP']._serialized_start=6148
  _globals['_PRMDROPSHENFUEQUIP']._serialized_end=6205
  _globals['_PRMSELLPICKEQUIP']._serialized_start=6207
  _globals['_PRMSELLPICKEQUIP']._serialized_end=6243
  _globals['_PRMUSECONSUMEEQUIP']._serialized_start=6245
  _globals['_PRMUSECONSUMEEQUIP']._serialized_end=6285
  _globals['_PRMTRANSPORT']._serialized_start=6287
  _globals['_PRMTRANSPORT']._serialized_end=6338
# @@protoc_insertion_point(module_scope)
