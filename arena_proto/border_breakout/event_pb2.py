# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: event.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x65vent.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\"w\n\x15PrmCustomDefineEffect\x12\x11\n\teffect_id\x18\x01 \x01(\r\x12\x11\n\tint_param\x18\x02 \x01(\x05\x12\r\n\x05wx_id\x18\x03 \x01(\x04\x12\x10\n\x08gift_num\x18\x04 \x01(\r\x12\x17\n\x0fstart_frame_num\x18\x05 \x01(\r\"e\n\x1aPrmCustomDefineEffectLimit\x12\x11\n\teffect_id\x18\x01 \x01(\r\x12\x1b\n\x13\x61\x63hieved_limitation\x18\x02 \x01(\x08\x12\x17\n\x0fstart_frame_num\x18\x03 \x01(\r\"\xd5\x01\n\x0cHeroEventPkg\x12.\n\nevent_type\x18\x01 \x02(\x0e\x32\x1a.sgame_state.HeroEventType\x12\x44\n\x18prm_custom_define_effect\x18* \x01(\x0b\x32\".sgame_state.PrmCustomDefineEffect\x12O\n\x1eprm_custom_define_effect_limit\x18+ \x01(\x0b\x32\'.sgame_state.PrmCustomDefineEffectLimit*o\n\rHeroEventType\x12\x13\n\x0f\x45VENT_TYPE_None\x10\x00\x12!\n\x1d\x45VENT_TYPE_CustomDefineEffect\x10\x01\x12&\n\"EVENT_TYPE_CustomDefineLimitEffect\x10\x02')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'event_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_HEROEVENTTYPE']._serialized_start=482
  _globals['_HEROEVENTTYPE']._serialized_end=593
  _globals['_PRMCUSTOMDEFINEEFFECT']._serialized_start=42
  _globals['_PRMCUSTOMDEFINEEFFECT']._serialized_end=161
  _globals['_PRMCUSTOMDEFINEEFFECTLIMIT']._serialized_start=163
  _globals['_PRMCUSTOMDEFINEEFFECTLIMIT']._serialized_end=264
  _globals['_HEROEVENTPKG']._serialized_start=267
  _globals['_HEROEVENTPKG']._serialized_end=480
# @@protoc_insertion_point(module_scope)
