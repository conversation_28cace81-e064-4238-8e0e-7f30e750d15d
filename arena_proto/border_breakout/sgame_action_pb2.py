# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_action.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import hero_pb2 as hero__pb2
from . import wz_highlight_pb2 as wz__highlight__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12sgame_action.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\nhero.proto\x1a\x12wz_highlight.proto\".\n\x10\x41\x63tionIncomeInfo\x12\x0b\n\x03\x65xp\x18\x01 \x01(\x05\x12\r\n\x05money\x18\x02 \x01(\x05\"\x87\x01\n\x0e\x41\x63tionHurtInfo\x12(\n\thurt_type\x18\x01 \x02(\x0e\x32\x15.sgame_state.HurtType\x12\x10\n\x08hurt_val\x18\x02 \x02(\x05\x12\x11\n\ticon_name\x18\x03 \x01(\x0c\x12\x0c\n\x04name\x18\x04 \x01(\x0c\x12\x18\n\x10skill_runtime_id\x18\x05 \x01(\x05\"\xad\x01\n\x14\x41\x63tionSingleHurtInfo\x12\x0f\n\x07\x66rameNo\x18\x01 \x02(\x05\x12\x11\n\tconfig_id\x18\x02 \x02(\x05\x12\x12\n\nruntime_id\x18\x03 \x02(\x05\x12-\n\tslot_type\x18\x04 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12.\n\thurt_info\x18\x05 \x02(\x0b\x32\x1b.sgame_state.ActionHurtInfo\"}\n\x0e\x41\x63tionHealInfo\x12\x10\n\x08heal_val\x18\x01 \x02(\x05\x12-\n\tslot_type\x18\x02 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x10\n\x08skill_id\x18\x03 \x02(\x05\x12\x18\n\x10skill_runtime_id\x18\x04 \x01(\x05\"~\n\x14\x41\x63tionSingleHealInfo\x12\x0f\n\x07\x66rameNo\x18\x01 \x02(\x05\x12\x11\n\tconfig_id\x18\x02 \x02(\x05\x12\x12\n\nruntime_id\x18\x03 \x02(\x05\x12.\n\theal_info\x18\x04 \x02(\x0b\x32\x1b.sgame_state.ActionHealInfo\"\x80\x01\n\x15\x41\x63tionAchievementInfo\x12\x12\n\nmulti_kill\x18\x01 \x01(\x05\x12\x12\n\nconti_kill\x18\x02 \x01(\x05\x12\x12\n\nconti_dead\x18\x03 \x01(\x05\x12\x16\n\x0eis_first_blood\x18\x04 \x01(\x08\x12\x13\n\x0bis_all_dead\x18\x05 \x01(\x08\"\xdb\x03\n\x0f\x41\x63tionActorInfo\x12\x11\n\tconfig_id\x18\x01 \x02(\x05\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12-\n\nactor_type\x18\x03 \x02(\x0e\x32\x19.sgame_state.ActorTypeDef\x12+\n\x08sub_type\x18\x04 \x02(\x0e\x32\x19.sgame_state.ActorTypeSub\x12)\n\x04\x63\x61mp\x18\x05 \x02(\x0e\x32\x1b.sgame_state.COM_PLAYERCAMP\x12.\n\thurt_info\x18\x06 \x03(\x0b\x32\x1b.sgame_state.ActionHurtInfo\x12\x32\n\x0bincome_info\x18\x07 \x01(\x0b\x32\x1d.sgame_state.ActionIncomeInfo\x12<\n\x10\x61\x63hievement_info\x18\x08 \x01(\x0b\x32\".sgame_state.ActionAchievementInfo\x12;\n\x10single_hurt_list\x18\t \x03(\x0b\x32!.sgame_state.ActionSingleHurtInfo\x12;\n\x10single_heal_list\x18\n \x03(\x0b\x32!.sgame_state.ActionSingleHealInfo\"\x99\x01\n\nDeadAction\x12+\n\x05\x64\x65\x61th\x18\x01 \x02(\x0b\x32\x1c.sgame_state.ActionActorInfo\x12,\n\x06killer\x18\x02 \x02(\x0b\x32\x1c.sgame_state.ActionActorInfo\x12\x30\n\nassist_set\x18\x03 \x03(\x0b\x32\x1c.sgame_state.ActionActorInfo\"\xd0\x01\n\x0fTeamFightAction\x12\x11\n\tstartTime\x18\x01 \x02(\x05\x12\x0f\n\x07\x65ndTime\x18\x02 \x02(\x05\x12\x1b\n\x13joinFightRuntimeIds\x18\x03 \x03(\x05\x12\x16\n\x0e\x63\x61mp1StartCoin\x18\x04 \x01(\r\x12\x16\n\x0e\x63\x61mp2StartCoin\x18\x05 \x01(\r\x12\x14\n\x0c\x63\x61mp1EndCoin\x18\x06 \x01(\r\x12\x14\n\x0c\x63\x61mp2EndCoin\x18\x07 \x01(\r\x12 \n\x18\x66ightInstigatorRuntimeId\x18\x08 \x01(\x05\"\x80\x01\n\x0b\x46rameAction\x12,\n\x0b\x64\x65\x61\x64_action\x18\x01 \x03(\x0b\x32\x17.sgame_state.DeadAction\x12\x43\n\x10highlight_action\x18\x02 \x03(\x0b\x32).sgame_state_highlight.HighlightVideoInfo*\x95\x01\n\x08HurtType\x12\x16\n\x12HURT_TYPE_PhysHurt\x10\x00\x12\x17\n\x13HURT_TYPE_MagicHurt\x10\x01\x12\x16\n\x12HURT_TYPE_RealHurt\x10\x02\x12\x16\n\x12HURT_TYPE_Therapic\x10\x03\x12\x15\n\x11HURT_TYPE_Protect\x10\x04\x12\x11\n\rHURT_TYPE_Max\x10\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_action_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_HURTTYPE']._serialized_start=1806
  _globals['_HURTTYPE']._serialized_end=1955
  _globals['_ACTIONINCOMEINFO']._serialized_start=81
  _globals['_ACTIONINCOMEINFO']._serialized_end=127
  _globals['_ACTIONHURTINFO']._serialized_start=130
  _globals['_ACTIONHURTINFO']._serialized_end=265
  _globals['_ACTIONSINGLEHURTINFO']._serialized_start=268
  _globals['_ACTIONSINGLEHURTINFO']._serialized_end=441
  _globals['_ACTIONHEALINFO']._serialized_start=443
  _globals['_ACTIONHEALINFO']._serialized_end=568
  _globals['_ACTIONSINGLEHEALINFO']._serialized_start=570
  _globals['_ACTIONSINGLEHEALINFO']._serialized_end=696
  _globals['_ACTIONACHIEVEMENTINFO']._serialized_start=699
  _globals['_ACTIONACHIEVEMENTINFO']._serialized_end=827
  _globals['_ACTIONACTORINFO']._serialized_start=830
  _globals['_ACTIONACTORINFO']._serialized_end=1305
  _globals['_DEADACTION']._serialized_start=1308
  _globals['_DEADACTION']._serialized_end=1461
  _globals['_TEAMFIGHTACTION']._serialized_start=1464
  _globals['_TEAMFIGHTACTION']._serialized_end=1672
  _globals['_FRAMEACTION']._serialized_start=1675
  _globals['_FRAMEACTION']._serialized_end=1803
# @@protoc_insertion_point(module_scope)
