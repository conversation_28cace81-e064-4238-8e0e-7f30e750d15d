# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: serverai_banpick.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import serverai_chat_pb2 as serverai__chat__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16serverai_banpick.proto\x12\x0fsgame_ai_server\x1a\x13serverai_chat.proto\"\xf1\x05\n\x16ServerAIBanpickRequest\x12\x0e\n\x06\x64\x65skID\x18\x01 \x02(\x05\x12\x0f\n\x07\x64\x65skSeq\x18\x02 \x02(\x05\x12\x13\n\x0brelayentity\x18\x03 \x02(\x05\x12(\n\x07\x62p_type\x18\x04 \x02(\x0e\x32\x17.sgame_ai_server.BPType\x12\x18\n\x10\x63hoose_hero_type\x18\x05 \x02(\x05\x12\x1e\n\x16\x61i_forbidden_hero_list\x18\x06 \x03(\r\x12\x10\n\x08\x62\x61n_list\x18\x07 \x03(\r\x12;\n\x14\x61i_ban_pick_req_list\x18\x08 \x03(\x0b\x32\x1d.sgame_ai_server.AIBanPickReq\x12\x35\n\x10player_info_list\x18\t \x03(\x0b\x32\x1b.sgame_ai_server.PlayerInfo\x12*\n\x08map_type\x18\n \x01(\x0e\x32\x18.sgame_ai_server.MapType\x12\r\n\x05grade\x18\x0b \x01(\r\x12\x12\n\nmatch_flag\x18\x0c \x03(\r\x12\x11\n\taiversion\x18\r \x01(\t\x12*\n\x08\x62p_rules\x18\x0e \x01(\x0b\x32\x18.sgame_ai_server.BPRules\x12\x36\n\x0f\x65xpect_bp_infos\x18\x0f \x03(\x0b\x32\x1d.sgame_ai_server.ExpectBPInfo\x12\x45\n\x17\x62p_chat_intention_infos\x18\x10 \x03(\x0b\x32$.sgame_ai_server.BPChatIntentionInfo\x12\x12\n\nround_time\x18\x11 \x01(\r\x12\x13\n\x0bremain_time\x18\x12 \x01(\r\x12\x1e\n\x16is_pre_selected_branch\x18\x13 \x01(\r\x12:\n\x11\x65xpect_swap_infos\x18\x14 \x03(\x0b\x32\x1f.sgame_ai_server.ExpectSwapInfo\x12%\n\x1dis_desk_allow_hero_duplicated\x18\x15 \x01(\r\"l\n\x0f\x42PSwapBranchMsg\x12\x18\n\x10target_object_id\x18\x01 \x02(\r\x12\x18\n\x10source_object_id\x18\x02 \x02(\r\x12\x12\n\ndelay_time\x18\x03 \x02(\r\x12\x11\n\tis_accept\x18\x04 \x02(\r\"7\n\x0e\x45xpectSwapInfo\x12\x13\n\x0bhuman_objid\x18\x01 \x02(\r\x12\x10\n\x08\x61i_objid\x18\x02 \x02(\r\"\x99\x05\n\x17ServerAIBanpickResponse\x12.\n\nerror_code\x18\x01 \x02(\x0e\x32\x1a.sgame_ai_server.ErrorCode\x12\x0e\n\x06\x64\x65skID\x18\x02 \x02(\x05\x12\x0f\n\x07\x64\x65skSeq\x18\x03 \x02(\x05\x12\x13\n\x0brelayentity\x18\x04 \x02(\x05\x12(\n\x07\x62p_type\x18\x05 \x01(\x0e\x32\x17.sgame_ai_server.BPType\x12\x18\n\x10\x63hoose_hero_type\x18\x06 \x01(\x05\x12*\n\x08map_type\x18\x07 \x01(\x0e\x32\x18.sgame_ai_server.MapType\x12;\n\x14\x61i_ban_pick_rsp_list\x18\x08 \x03(\x0b\x32\x1d.sgame_ai_server.AIBanPickRSP\x12H\n\x18\x61i_help_choose_hero_msgs\x18\t \x03(\x0b\x32&.sgame_ai_server.BPAIHelpChooseHeroMsg\x12\x36\n\x0epre_alloc_msgs\x18\n \x03(\x0b\x32\x1e.sgame_ai_server.BPPreAllocMsg\x12\x35\n\x0f\x62p_to_chat_msgs\x18\x0b \x03(\x0b\x32\x1c.sgame_ai_server.BPToChatMsg\x12:\n\x10swap_branch_msgs\x18\x0c \x03(\x0b\x32 .sgame_ai_server.BPSwapBranchMsg\x12\x37\n\x0f\x66ix_branch_msgs\x18\r \x03(\x0b\x32\x1e.sgame_ai_server.BPPreAllocMsg\x12=\n\x13\x61i_swap_branch_msgs\x18\x0e \x03(\x0b\x32 .sgame_ai_server.BPSwapBranchMsg\"\xa7\x01\n\x0fPackageHeroInfo\x12\x0f\n\x07hero_id\x18\x01 \x02(\r\x12\x13\n\x0bproficiency\x18\x02 \x01(\r\x12\x11\n\ttop_count\x18\x03 \x01(\r\x12\x11\n\tmid_count\x18\x04 \x01(\r\x12\x11\n\tbtm_count\x18\x05 \x01(\r\x12\x11\n\tjgl_count\x18\x06 \x01(\r\x12\x11\n\tspl_count\x18\x07 \x01(\r\x12\x0f\n\x07skin_id\x18\x08 \x03(\r\"\xfa\x04\n\nPlayerInfo\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\r\x12\x10\n\x08objectid\x18\x02 \x02(\r\x12\x13\n\x0bgradeofrank\x18\x03 \x01(\r\x12\x14\n\x0cis_server_ai\x18\x04 \x02(\x08\x12\x18\n\x10has_choosed_hero\x18\x05 \x02(\x08\x12\x16\n\x0e\x63hoosed_heroid\x18\x06 \x01(\r\x12#\n\x1bpreset_wantplay_heroid_list\x18\x07 \x03(\r\x12\x30\n\x0bprefer_hero\x18\x08 \x01(\x0b\x32\x1b.sgame_ai_server.PreferHero\x12\x18\n\x10help_choose_hero\x18\t \x01(\r\x12\x0b\n\x03uid\x18\n \x01(\x04\x12\x16\n\x0eselected_skill\x18\x0b \x01(\r\x12\x16\n\x0e\x63hoosed_branch\x18\x0c \x01(\r\x12\x18\n\x10self_branch_prob\x18\r \x03(\x02\x12\x16\n\x0elogic_world_id\x18\x0e \x01(\x05\x12\x1a\n\x12master_match_score\x18\x0f \x01(\r\x12\x12\n\ncountry_id\x18\x10 \x01(\x05\x12\r\n\x05\x66loor\x18\x11 \x01(\r\x12\x17\n\x0f\x61ssigned_branch\x18\x12 \x01(\r\x12\x12\n\nis_real_ai\x18\x13 \x01(\x08\x12\x38\n\x0ehero_info_list\x18\x14 \x03(\x0b\x32 .sgame_ai_server.PackageHeroInfo\x12\x0f\n\x07open_id\x18\x15 \x01(\t\x12\x15\n\rscore_of_rank\x18\x16 \x01(\r\x12\x17\n\x0fshow_country_id\x18\x17 \x01(\x05\x12\x13\n\x0bgrade_10v10\x18\x18 \x01(\x05\x12\x13\n\x0bscore_10v10\x18\x19 \x01(\x05\"\xc3\x01\n\x0c\x41IBanPickReq\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\r\x12*\n\x08\x62op_step\x18\x02 \x02(\x0e\x32\x18.sgame_ai_server.BopStep\x12\x13\n\x0b\x61i_objectid\x18\x03 \x02(\r\x12\x31\n\x0f\x61i_fill_herojob\x18\x04 \x01(\x0e\x32\x18.sgame_ai_server.HeroJob\x12\x10\n\x08\x61i_level\x18\x05 \x01(\r\x12\x1f\n\x17is_pre_select_lock_pick\x18\x06 \x01(\r\"\x8e\x02\n\x0c\x41IBanPickRSP\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\r\x12*\n\x08\x62op_step\x18\x02 \x02(\x0e\x32\x18.sgame_ai_server.BopStep\x12\x13\n\x0b\x61i_objectid\x18\x03 \x02(\r\x12\x0e\n\x06heroid\x18\x04 \x01(\r\x12)\n\x07herojob\x18\x05 \x01(\x0e\x32\x18.sgame_ai_server.HeroJob\x12\x0e\n\x06skinid\x18\x06 \x01(\r\x12\x0f\n\x07skillid\x18\x07 \x01(\r\x12\x13\n\x0bsymbol_list\x18\x08 \x03(\r\x12\x0c\n\x04lane\x18\t \x01(\r\x12\x17\n\x0fshow_delay_time\x18\n \x01(\r\x12\x17\n\x0flock_delay_time\x18\x0b \x01(\r\"x\n\x15\x42PAIHelpChooseHeroMsg\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x1b\n\x13need_help_object_id\x18\x02 \x02(\r\x12\x1b\n\x13help_choose_hero_id\x18\x03 \x02(\r\x12\x12\n\ndelay_time\x18\x04 \x02(\r\"I\n\nPreferHero\x12*\n\x08\x62op_step\x18\x01 \x02(\x0e\x32\x18.sgame_ai_server.BopStep\x12\x0f\n\x07hero_id\x18\x02 \x02(\r\"/\n\nBPRuleHero\x12\x0f\n\x07hero_id\x18\x01 \x01(\r\x12\x10\n\x08skin_ids\x18\x02 \x03(\r\"\\\n\x06\x42PRule\x12\x0b\n\x03uid\x18\x01 \x01(\x04\x12\x32\n\rbp_rule_heros\x18\x02 \x03(\x0b\x32\x1b.sgame_ai_server.BPRuleHero\x12\x11\n\tcamp_type\x18\x03 \x01(\r\"\xa7\x01\n\x07\x42PRules\x12\x33\n\x12\x62lack_bp_rule_list\x18\x01 \x03(\x0b\x32\x17.sgame_ai_server.BPRule\x12\x33\n\x12white_bp_rule_list\x18\x02 \x03(\x0b\x32\x17.sgame_ai_server.BPRule\x12\x32\n\x11want_bp_rule_list\x18\x03 \x03(\x0b\x32\x17.sgame_ai_server.BPRule*\xfd\x01\n\tErrorCode\x12\x0b\n\x07Success\x10\x00\x12\x14\n\x10ReceiveDataError\x10\x01\x12\x10\n\x0cParsePbError\x10\x02\x12\x17\n\x13\x43hooseHeroTypeError\x10\x03\x12\x0f\n\x0b\x42PTypeError\x10\x04\x12\x12\n\x0e\x41nalyseReqFail\x10\x05\x12\x19\n\x15\x41nalysePlayerInfoFail\x10\x06\x12\x19\n\x15ProcessWarmBattleFail\x10\x07\x12\x18\n\x14ProcessReplenishFail\x10\x08\x12\x1e\n\x1aProcessFivefivepreteamFail\x10\t\x12\r\n\tOtherFail\x10\n*S\n\x07HeroJob\x12\x0c\n\x08\x41ssassin\x10\x00\x12\x0b\n\x07Shooter\x10\x01\x12\x0b\n\x07Soldier\x10\x02\x12\n\n\x06Suport\x10\x03\x12\x08\n\x04Tank\x10\x04\x12\n\n\x06Wizard\x10\x05*~\n\x07MapType\x12\x10\n\x0cMAPTYPE_NONE\x10\x00\x12\x10\n\x0cMAPTYPE_RANK\x10\x01\x12\x11\n\rMAPTYPE_MATCH\x10\x02\x12\x11\n\rMAPTYPE_CHAOS\x10\x03\x12\x12\n\x0eMAPTYPE_MASTER\x10\x04\x12\x15\n\x11MAPTYPE_RANK10v10\x10\x05*9\n\x06\x42PType\x12\x0f\n\x0b\x42PTYPE_NONE\x10\x00\x12\r\n\tBPTYPE_BP\x10\x01\x12\x0f\n\x0b\x42PTYPE_NOBP\x10\x02*\xd3\x01\n\x0e\x43hooseHeroTpye\x12\x19\n\x15\x43HOOSE_HERO_TYPE_NONE\x10\x00\x12\x1f\n\x1b\x43HOOSE_HERO_TYPE_WARMBATTLE\x10\x01\x12\x1e\n\x1a\x43HOOSE_HERP_TYPE_REPLENISH\x10\x02\x12%\n!CHOOSE_HERP_TYPE_FIVEFIVE_PRETEAM\x10\x03\x12 \n\x1c\x43HOOSE_HERO_TYPE_NEWBIE_WARM\x10\x04\x12\x1c\n\x18\x43HOOSE_HERO_TYPE_MINIMAP\x10\x05*\xe9\x01\n\x07\x42opStep\x12\x11\n\rBOP_STEP_NONE\x10\x00\x12\x11\n\rBOP_STEP_PICK\x10\x01\x12\x10\n\x0c\x42OP_STEP_BAN\x10\x02\x12\x11\n\rBOP_STEP_HELP\x10\x03\x12\x14\n\x10\x42OP_STEP_PREPICK\x10\x04\x12\x16\n\x12\x42OP_STEP_RECOMMEND\x10\x05\x12\x18\n\x14\x42OP_STEP_KEEPPREPICK\x10\x06\x12\x17\n\x13\x42OP_STEP_SWAPBRANCH\x10\x07\x12\x16\n\x12\x42OP_STEP_FIXBRANCH\x10\x08\x12\x1a\n\x16\x42OP_STEP_AI_SWAPBRANCH\x10\t')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'serverai_banpick_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ERRORCODE']._serialized_start=3444
  _globals['_ERRORCODE']._serialized_end=3697
  _globals['_HEROJOB']._serialized_start=3699
  _globals['_HEROJOB']._serialized_end=3782
  _globals['_MAPTYPE']._serialized_start=3784
  _globals['_MAPTYPE']._serialized_end=3910
  _globals['_BPTYPE']._serialized_start=3912
  _globals['_BPTYPE']._serialized_end=3969
  _globals['_CHOOSEHEROTPYE']._serialized_start=3972
  _globals['_CHOOSEHEROTPYE']._serialized_end=4183
  _globals['_BOPSTEP']._serialized_start=4186
  _globals['_BOPSTEP']._serialized_end=4419
  _globals['_SERVERAIBANPICKREQUEST']._serialized_start=65
  _globals['_SERVERAIBANPICKREQUEST']._serialized_end=818
  _globals['_BPSWAPBRANCHMSG']._serialized_start=820
  _globals['_BPSWAPBRANCHMSG']._serialized_end=928
  _globals['_EXPECTSWAPINFO']._serialized_start=930
  _globals['_EXPECTSWAPINFO']._serialized_end=985
  _globals['_SERVERAIBANPICKRESPONSE']._serialized_start=988
  _globals['_SERVERAIBANPICKRESPONSE']._serialized_end=1653
  _globals['_PACKAGEHEROINFO']._serialized_start=1656
  _globals['_PACKAGEHEROINFO']._serialized_end=1823
  _globals['_PLAYERINFO']._serialized_start=1826
  _globals['_PLAYERINFO']._serialized_end=2460
  _globals['_AIBANPICKREQ']._serialized_start=2463
  _globals['_AIBANPICKREQ']._serialized_end=2658
  _globals['_AIBANPICKRSP']._serialized_start=2661
  _globals['_AIBANPICKRSP']._serialized_end=2931
  _globals['_BPAIHELPCHOOSEHEROMSG']._serialized_start=2933
  _globals['_BPAIHELPCHOOSEHEROMSG']._serialized_end=3053
  _globals['_PREFERHERO']._serialized_start=3055
  _globals['_PREFERHERO']._serialized_end=3128
  _globals['_BPRULEHERO']._serialized_start=3130
  _globals['_BPRULEHERO']._serialized_end=3177
  _globals['_BPRULE']._serialized_start=3179
  _globals['_BPRULE']._serialized_end=3271
  _globals['_BPRULES']._serialized_start=3274
  _globals['_BPRULES']._serialized_end=3441
# @@protoc_insertion_point(module_scope)
