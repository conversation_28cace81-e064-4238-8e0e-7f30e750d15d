# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_gamecore_server.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import command_pb2 as command__pb2
from . import sgame_state_pb2 as sgame__state__pb2
from . import sgame_action_pb2 as sgame__action__pb2
from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1bsgame_gamecore_server.proto\x12\x15sgame_gamecore_server\x1a\rcommand.proto\x1a\x11sgame_state.proto\x1a\x12sgame_action.proto\x1a\x0c\x63ommon.proto\"G\n\x0b\x43ommandInfo\x12\x11\n\tplayer_id\x18\x01 \x02(\x05\x12%\n\x08\x63md_info\x18\x02 \x01(\x0b\x32\x13.sgame_state.CmdPkg\"\xd7\x01\n\x15GameCoreServerRequest\x12\x13\n\x0bres_version\x18\x01 \x02(\t\x12\x0f\n\x07game_id\x18\x02 \x02(\t\x12\x14\n\x0crelay_entity\x18\x03 \x02(\x05\x12\x0f\n\x07\x64\x65sk_id\x18\x04 \x02(\r\x12\x10\n\x08\x64\x65sk_seq\x18\x05 \x02(\r\x12\x34\n\ngame_state\x18\x06 \x02(\x0e\x32 .sgame_gamecore_server.GameState\x12\x12\n\nbegin_info\x18\x07 \x01(\x0c\x12\x15\n\rfrapboot_info\x18\x08 \x01(\x0c\"\x8e\x01\n\x10GameBeHurtInfors\x12\x12\n\nruntime_id\x18\x01 \x01(\x05\x12\x30\n\rtakeHurtInfos\x18\x02 \x03(\x0b\x32\x19.sgame_state.TakeHurtInfo\x12\x34\n\x10hit_target_infos\x18\x03 \x03(\x0b\x32\x1a.sgame_state.HitTargetInfo\"\xa3\x03\n\x16GameCoreServerResponse\x12\x10\n\x08ret_code\x18\x01 \x02(\x05\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12\x0e\n\x06seq_no\x18\x03 \x02(\x05\x12,\n\x0bstart_state\x18\x04 \x01(\x0b\x32\x17.sgame_state.StartState\x12,\n\x0b\x66rame_state\x18\x05 \x01(\x0b\x32\x17.sgame_state.FrameState\x12,\n\x0b\x63lose_state\x18\x06 \x01(\x0b\x32\x17.sgame_state.CloseState\x12.\n\x0c\x66rame_action\x18\x07 \x01(\x0b\x32\x18.sgame_state.FrameAction\x12\x13\n\x0breplay_file\x18\x08 \x01(\x0c\x12:\n\x18\x61ggregation_frame_action\x18\x64 \x01(\x0b\x32\x18.sgame_state.FrameAction\x12J\n\x19\x61ggregation_takeHurtInfos\x18\x65 \x03(\x0b\x32\'.sgame_gamecore_server.GameBeHurtInfors\"Q\n\x17GameCoreServerHeartBeat\x12\x0c\n\x04time\x18\x01 \x01(\r\x12\x13\n\x0b\x61pp_version\x18\x02 \x01(\x05\x12\x13\n\x0bres_version\x18\x03 \x01(\x05*_\n\tGameState\x12\x13\n\x0fGAME_STATE_None\x10\x00\x12\x14\n\x10GAME_STATE_START\x10\x01\x12\x13\n\x0fGAME_STATE_PLAY\x10\x02\x12\x12\n\x0eGAME_STATE_END\x10\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_gamecore_server_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_GAMESTATE']._serialized_start=1063
  _globals['_GAMESTATE']._serialized_end=1158
  _globals['_COMMANDINFO']._serialized_start=122
  _globals['_COMMANDINFO']._serialized_end=193
  _globals['_GAMECORESERVERREQUEST']._serialized_start=196
  _globals['_GAMECORESERVERREQUEST']._serialized_end=411
  _globals['_GAMEBEHURTINFORS']._serialized_start=414
  _globals['_GAMEBEHURTINFORS']._serialized_end=556
  _globals['_GAMECORESERVERRESPONSE']._serialized_start=559
  _globals['_GAMECORESERVERRESPONSE']._serialized_end=978
  _globals['_GAMECORESERVERHEARTBEAT']._serialized_start=980
  _globals['_GAMECORESERVERHEARTBEAT']._serialized_end=1061
# @@protoc_insertion_point(module_scope)
