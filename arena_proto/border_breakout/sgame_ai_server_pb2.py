# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_ai_server.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import command_pb2 as command__pb2
from . import sgame_state_pb2 as sgame__state__pb2
from . import sgame_action_pb2 as sgame__action__pb2
from . import serverai_banpick_pb2 as serverai__banpick__pb2
from . import serverai_chat_pb2 as serverai__chat__pb2
from . import serverai_hci_pb2 as serverai__hci__pb2
from . import serverai_adjust_pb2 as serverai__adjust__pb2
from . import serverai_inbattle_pb2 as serverai__inbattle__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15sgame_ai_server.proto\x12\x0fsgame_ai_server\x1a\rcommand.proto\x1a\x11sgame_state.proto\x1a\x12sgame_action.proto\x1a\x16serverai_banpick.proto\x1a\x13serverai_chat.proto\x1a\x12serverai_hci.proto\x1a\x15serverai_adjust.proto\x1a\x17serverai_inbattle.proto\"r\n\rExamBlockInfo\x12\x17\n\x0f\x65xam_block_type\x18\x01 \x02(\r\x12\x0f\n\x07\x61ilevel\x18\x02 \x02(\r\x12\x13\n\x0bstart_frame\x18\x03 \x02(\r\x12\x11\n\tend_frame\x18\x04 \x02(\r\x12\x0f\n\x07\x65xam_id\x18\x05 \x01(\r\"\xe1\x02\n\rAICommandInfo\x12\x11\n\tplayer_id\x18\x01 \x02(\x05\x12%\n\x08\x63md_info\x18\x02 \x01(\x0b\x32\x13.sgame_state.CmdPkg\x12\x11\n\tcmd_state\x18\x03 \x01(\x05\x12\x10\n\x08\x61i_level\x18\x04 \x01(\x05\x12\x18\n\x10\x64\x65lay_leave_home\x18\x05 \x01(\r\x12\x10\n\x08run_freq\x18\x06 \x01(\r\x12\x17\n\x0f\x64\x65lay_buy_equip\x18\x07 \x01(\r\x12\x12\n\nmatch_type\x18\x08 \x01(\r\x12\x0b\n\x03tag\x18\t \x01(\r\x12\x37\n\x0f\x65xam_block_info\x18\n \x03(\x0b\x32\x1e.sgame_ai_server.ExamBlockInfo\x12\x0f\n\x07team_id\x18\x0b \x01(\r\x12\x12\n\nis_real_ai\x18\x0c \x01(\r\x12\x1a\n\x12master_match_score\x18\r \x01(\r\x12\x11\n\thero_lane\x18\x0e \x01(\r\"\xa7\x01\n\x0cPlayAttrInfo\x12\r\n\x05objid\x18\x01 \x02(\r\x12\x12\n\nruntime_id\x18\x02 \x02(\r\x12\x0c\n\x04\x61ttr\x18\x03 \x01(\x04\x12\x13\n\x0b\x64\x61juguan_id\x18\x04 \x03(\r\x12\x0f\n\x07team_id\x18\x05 \x01(\r\x12\x1a\n\x12master_match_score\x18\x06 \x01(\r\x12\x10\n\x08\x61i_level\x18\x07 \x01(\r\x12\x12\n\ncountry_id\x18\x08 \x01(\x05\">\n\x0ePlayerRelation\x12\x0b\n\x03uid\x18\x01 \x02(\x04\x12\x10\n\x08relation\x18\x02 \x02(\r\x12\r\n\x05value\x18\x03 \x01(\r\"\x90\x04\n\x0cHeroAttrInfo\x12\x11\n\tobject_id\x18\x01 \x02(\r\x12\x12\n\nruntime_id\x18\x02 \x02(\r\x12\x0e\n\x06openid\x18\x03 \x01(\t\x12\x0e\n\x06zoneid\x18\x04 \x01(\x05\x12\x0b\n\x03uid\x18\x05 \x01(\x04\x12\r\n\x05grade\x18\x06 \x01(\r\x12\x0f\n\x07hero_id\x18\x07 \x01(\r\x12\x0c\n\x04\x63\x61mp\x18\x08 \x01(\r\x12\r\n\x05is_ai\x18\t \x01(\r\x12\x12\n\nis_real_ai\x18\n \x01(\r\x12\r\n\x05\x66loor\x18\x0b \x01(\r\x12\x10\n\x08\x62op_step\x18\x0c \x01(\r\x12\x1a\n\x12master_match_score\x18\r \x01(\r\x12\x0f\n\x07team_id\x18\x0e \x01(\r\x12\x11\n\tnick_name\x18\x0f \x01(\t\x12\x11\n\tshow_lane\x18\x10 \x01(\r\x12\x11\n\treal_lane\x18\x11 \x01(\r\x12\x10\n\x08\x61i_level\x18\x12 \x01(\r\x12\x10\n\x08juben_id\x18\x13 \x01(\r\x12\x13\n\x0bprefer_hero\x18\x14 \x03(\r\x12\x12\n\nmatch_flag\x18\x15 \x01(\r\x12\x12\n\ncountry_id\x18\x16 \x01(\x05\x12\x15\n\rscore_of_rank\x18\x17 \x01(\r\x12\x32\n\trelations\x18\x18 \x03(\x0b\x32\x1f.sgame_ai_server.PlayerRelation\x12\x13\n\x0bgrade_10v10\x18\x19 \x01(\r\x12\x13\n\x0bscore_10v10\x18\x1a \x01(\r\"\xb6\x02\n\nGlobalInfo\x12\x0e\n\x06map_id\x18\x01 \x01(\x05\x12\x14\n\x0c\x61itrain_mode\x18\x02 \x01(\x05\x12\x13\n\x0broom_entity\x18\x03 \x01(\x05\x12\x0f\n\x07room_id\x18\x04 \x01(\r\x12\x10\n\x08room_seq\x18\x05 \x01(\r\x12\x10\n\x08\x61i_level\x18\x06 \x01(\x05\x12\x10\n\x08gamecore\x18\x07 \x01(\r\x12\x0f\n\x07\x64\x65sk_id\x18\x08 \x01(\x05\x12\x10\n\x08\x64\x65sk_seq\x18\t \x01(\x05\x12\x14\n\x0crelay_entity\x18\n \x01(\x05\x12\x35\n\x0ehero_attr_info\x18\x0b \x03(\x0b\x32\x1d.sgame_ai_server.HeroAttrInfo\x12\x19\n\x11\x61i_server_version\x18\x0c \x01(\t\x12\x1b\n\x13valid_juben_id_list\x18\r \x03(\x05\"L\n\x12\x41IServerGlobalInfo\x12\x19\n\x11\x61i_server_version\x18\x01 \x01(\t\x12\x1b\n\x13valid_juben_id_list\x18\x02 \x03(\x05\"\xfe\x06\n\x0f\x41IServerRequest\x12\x10\n\x08sgame_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12\x10\n\x08gameover\x18\x03 \x01(\x08\x12\x30\n\x08\x63md_list\x18\x04 \x03(\x0b\x32\x1e.sgame_ai_server.AICommandInfo\x12,\n\x0b\x66rame_state\x18\x05 \x01(\x0b\x32\x17.sgame_state.FrameState\x12,\n\x0bstart_state\x18\x06 \x01(\x0b\x32\x17.sgame_state.StartState\x12\x0c\n\x04step\x18\x07 \x01(\x05\x12\x36\n\nbp_request\x18\x08 \x01(\x0b\x32\".sgame_ai_server.AIServerBPRequest\x12\x13\n\x0b\x63lient_type\x18\t \x01(\x05\x12.\n\x0c\x66rame_action\x18\n \x01(\x0b\x32\x18.sgame_state.FrameAction\x12\x30\n\x0fpre_frame_state\x18\x0b \x03(\x0b\x32\x17.sgame_state.FrameState\x12@\n\x0fserverai_bp_req\x18\x0c \x01(\x0b\x32\'.sgame_ai_server.ServerAIBanpickRequest\x12\x13\n\x0b\x66rame_delta\x18\r \x01(\x05\x12?\n\x11serverai_chat_req\x18\x0e \x01(\x0b\x32$.sgame_ai_server.ServerAIChatRequest\x12=\n\x10serverai_hci_req\x18\x0f \x01(\x0b\x32#.sgame_ai_server.ServerAIHCIRequest\x12\x10\n\x08map_type\x18\x10 \x01(\x05\x12\x35\n\x0eplay_attr_info\x18\x11 \x03(\x0b\x32\x1d.sgame_ai_server.PlayAttrInfo\x12\x1e\n\x16\x62reakpoint_player_list\x18\x12 \x03(\x05\x12\x30\n\x0bglobal_info\x18\x13 \x01(\x0b\x32\x1b.sgame_ai_server.GlobalInfo\x12@\n\x10serverai_adj_req\x18\x14 \x01(\x0b\x32&.sgame_ai_server.ServerAIAdjustRequest\x12\x36\n\x15serverbp_succ_fix_req\x18\x15 \x01(\x0b\x32\x17.SGameInBattle.EventReq\"\xe2\x04\n\x10\x41IServerResponse\x12\x30\n\x08\x63md_list\x18\x01 \x03(\x0b\x32\x1e.sgame_ai_server.AICommandInfo\x12\x1a\n\x12gameover_ai_server\x18\x02 \x01(\x05\x12\x0c\n\x04step\x18\x03 \x01(\x05\x12\x38\n\x0b\x62p_response\x18\x04 \x01(\x0b\x32#.sgame_ai_server.AIServerBPResponse\x12\x0e\n\x06seq_no\x18\x05 \x01(\x05\x12\x13\n\x0b\x63lient_type\x18\x06 \x01(\x05\x12\x41\n\x0fserverai_bp_rsp\x18\x07 \x01(\x0b\x32(.sgame_ai_server.ServerAIBanpickResponse\x12@\n\x11serverai_chat_rsp\x18\x08 \x01(\x0b\x32%.sgame_ai_server.ServerAIChatResponse\x12>\n\x10serverai_hci_rsp\x18\t \x01(\x0b\x32$.sgame_ai_server.ServerAIHCIResponse\x12\x10\n\x08ret_code\x18\n \x01(\x05\x12\x41\n\x10serverai_adj_rsp\x18\x0b \x01(\x0b\x32\'.sgame_ai_server.ServerAIAdjustResponse\x12\x36\n\x15serverbp_succ_fix_rsp\x18\x0c \x01(\x0b\x32\x17.SGameInBattle.EventRsp\x12\x41\n\x14\x61iserver_global_info\x18\r \x01(\x0b\x32#.sgame_ai_server.AIServerGlobalInfo\"?\n\x0c\x42\x61nPickState\x12\x0c\n\x04\x63\x61mp\x18\x01 \x01(\x05\x12\x0f\n\x07hero_id\x18\x02 \x01(\x05\x12\x10\n\x08hero_pro\x18\x03 \x01(\x05\"7\n\x11PlayerRencentHero\x12\x13\n\x0brecent_hero\x18\x01 \x03(\x05\x12\r\n\x05grade\x18\x02 \x01(\x05\"^\n\x0e\x43\x61mpRecentHero\x12\x0c\n\x04\x63\x61mp\x18\x01 \x01(\x05\x12>\n\x12player_recent_hero\x18\x02 \x03(\x0b\x32\".sgame_ai_server.PlayerRencentHero\"\xa3\x02\n\x11\x41IServerBPRequest\x12\x12\n\nbanpick_id\x18\x01 \x01(\t\x12\x0c\n\x04step\x18\x02 \x01(\x05\x12\x30\n\tban_state\x18\x03 \x03(\x0b\x32\x1d.sgame_ai_server.BanPickState\x12\x31\n\npick_state\x18\x04 \x03(\x0b\x32\x1d.sgame_ai_server.BanPickState\x12\x10\n\x08\x63ur_camp\x18\x05 \x01(\x05\x12\x39\n\x10\x63\x61mp_recent_hero\x18\x06 \x03(\x0b\x32\x1f.sgame_ai_server.CampRecentHero\x12\x0e\n\x06obj_id\x18\x07 \x01(\x05\x12\x14\n\x0c\x63\x61n_use_hero\x18\x08 \x03(\x05\x12\x14\n\x0chero_appoint\x18\t \x01(\x05\"r\n\x12\x41IServerBPResponse\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12\x0c\n\x04step\x18\x02 \x01(\x05\x12\x13\n\x0b\x61\x64\x64\x65\x64_skill\x18\x03 \x01(\x05\x12\x13\n\x0bsymbol_page\x18\x04 \x01(\x05\x12\x13\n\x0bsymbol_list\x18\x05 \x03(\x05\"|\n\nByPassInfo\x12\x0f\n\x07\x64\x65sk_id\x18\x01 \x02(\x05\x12\x10\n\x08\x64\x65sk_seq\x18\x02 \x02(\x05\x12\x14\n\x0crelay_entity\x18\x03 \x02(\x05\x12\x11\n\tgame_type\x18\x04 \x01(\x05\x12\x10\n\x08gamecore\x18\x05 \x01(\x05\x12\x10\n\x08\x62id_list\x18\x06 \x03(\x05*\x85\x01\n\x0f\x41IServerRetCode\x12\x11\n\rRetCodeNormal\x10\x00\x12\x17\n\x13RetCodeNoFrameState\x10\x01\x12\x15\n\x11RetCodeMcdTimeout\x10\x02\x12\x16\n\x12RetCodeSmcdTimeout\x10\x03\x12\x17\n\x13RetCodeReqRecommend\x10\x04*\xa1\x02\n\x12PlayerRelationType\x12\x1d\n\x19PLAYER_RELATION_TYPE_NULL\x10\x00\x12\x1f\n\x1bPLAYER_RELATION_TYPE_JI_YOU\x10\x01\x12!\n\x1dPLAYER_RELATION_TYPE_LIAN_REN\x10\x02\x12 \n\x1cPLAYER_RELATION_TYPE_SI_DANG\x10\x03\x12\x1f\n\x1bPLAYER_RELATION_TYPE_GUI_MI\x10\x04\x12\"\n\x1ePLAYER_RELATION_TYPE_XIONG_MEI\x10\x05\x12\x1f\n\x1bPLAYER_RELATION_TYPE_JIE_DI\x10\x06\x12 \n\x1cPLAYER_RELATION_TYPE_JIE_MEI\x10\x07')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_ai_server_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_AISERVERRETCODE']._serialized_start=4102
  _globals['_AISERVERRETCODE']._serialized_end=4235
  _globals['_PLAYERRELATIONTYPE']._serialized_start=4238
  _globals['_PLAYERRELATIONTYPE']._serialized_end=4527
  _globals['_EXAMBLOCKINFO']._serialized_start=209
  _globals['_EXAMBLOCKINFO']._serialized_end=323
  _globals['_AICOMMANDINFO']._serialized_start=326
  _globals['_AICOMMANDINFO']._serialized_end=679
  _globals['_PLAYATTRINFO']._serialized_start=682
  _globals['_PLAYATTRINFO']._serialized_end=849
  _globals['_PLAYERRELATION']._serialized_start=851
  _globals['_PLAYERRELATION']._serialized_end=913
  _globals['_HEROATTRINFO']._serialized_start=916
  _globals['_HEROATTRINFO']._serialized_end=1444
  _globals['_GLOBALINFO']._serialized_start=1447
  _globals['_GLOBALINFO']._serialized_end=1757
  _globals['_AISERVERGLOBALINFO']._serialized_start=1759
  _globals['_AISERVERGLOBALINFO']._serialized_end=1835
  _globals['_AISERVERREQUEST']._serialized_start=1838
  _globals['_AISERVERREQUEST']._serialized_end=2732
  _globals['_AISERVERRESPONSE']._serialized_start=2735
  _globals['_AISERVERRESPONSE']._serialized_end=3345
  _globals['_BANPICKSTATE']._serialized_start=3347
  _globals['_BANPICKSTATE']._serialized_end=3410
  _globals['_PLAYERRENCENTHERO']._serialized_start=3412
  _globals['_PLAYERRENCENTHERO']._serialized_end=3467
  _globals['_CAMPRECENTHERO']._serialized_start=3469
  _globals['_CAMPRECENTHERO']._serialized_end=3563
  _globals['_AISERVERBPREQUEST']._serialized_start=3566
  _globals['_AISERVERBPREQUEST']._serialized_end=3857
  _globals['_AISERVERBPRESPONSE']._serialized_start=3859
  _globals['_AISERVERBPRESPONSE']._serialized_end=3973
  _globals['_BYPASSINFO']._serialized_start=3975
  _globals['_BYPASSINFO']._serialized_end=4099
# @@protoc_insertion_point(module_scope)
