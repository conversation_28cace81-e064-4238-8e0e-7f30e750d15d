# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: common.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ommon.proto\x12\x0bsgame_state\"\x1d\n\x05VInt2\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01y\x18\x02 \x02(\x05\"(\n\x05VInt3\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01y\x18\x02 \x02(\x05\x12\t\n\x01z\x18\x03 \x02(\x05\"d\n\x05TInt3\x12\x1d\n\x01x\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x1d\n\x01y\x18\x02 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x1d\n\x01z\x18\x03 \x02(\x0b\x32\x12.sgame_state.VInt3\"|\n\x0b\x42oxCollider\x12$\n\x08location\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\x12 \n\x04size\x18\x02 \x02(\x0b\x32\x12.sgame_state.VInt3\x12%\n\ttransform\x18\x03 \x02(\x0b\x32\x12.sgame_state.TInt3\"F\n\x0eSphereCollider\x12$\n\x08location\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x0e\n\x06radius\x18\x02 \x02(\x05\"\x8f\x03\n\nActorValue\x12\x0f\n\x07phy_atk\x18\x01 \x02(\x05\x12\x0f\n\x07phy_def\x18\x02 \x02(\x05\x12\x0f\n\x07mgc_atk\x18\x03 \x02(\x05\x12\x0f\n\x07mgc_def\x18\x04 \x02(\x05\x12\x0f\n\x07mov_spd\x18\x05 \x02(\x05\x12\x0f\n\x07\x61tk_spd\x18\x06 \x02(\x05\x12\n\n\x02\x65p\x18\x07 \x02(\x05\x12\x0e\n\x06max_ep\x18\x08 \x02(\x05\x12\x12\n\nhp_recover\x18\t \x02(\x05\x12\x12\n\nep_recover\x18\n \x02(\x05\x12\x16\n\x0ephy_armor_hurt\x18\x0b \x02(\x05\x12\x16\n\x0emgc_armor_hurt\x18\x0c \x02(\x05\x12\x11\n\tcrit_rate\x18\r \x02(\x05\x12\x11\n\tcrit_effe\x18\x0e \x02(\x05\x12\x10\n\x08phy_vamp\x18\x0f \x02(\x05\x12\x10\n\x08mgc_vamp\x18\x10 \x02(\x05\x12\x11\n\tcd_reduce\x18\x11 \x02(\x05\x12\x13\n\x0b\x63trl_reduce\x18\x12 \x02(\x05\x12\x19\n\x11monster_endurance\x18\x13 \x01(\x05\x12\x1a\n\x12organ_conti_attack\x18\x14 \x01(\x05\"\"\n\nMethodCall\x12\x14\n\x0c\x63\x61ll_methods\x18\x01 \x03(\t\"\xdd\x01\n\x13\x41\x63torBuffSkillState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05times\x18\x02 \x02(\x05\x12\x11\n\tstartTime\x18\x03 \x02(\x04\x12\x12\n\neffectType\x18\x04 \x01(\x05\x12\x0f\n\x07skillId\x18\x05 \x01(\x05\x12\x10\n\x08leftTime\x18\x06 \x01(\x03\x12\x13\n\x0b\x63urDuration\x18\x07 \x01(\x05\x12\x16\n\x0eoriginDuration\x18\x08 \x01(\x05\x12\r\n\x05layer\x18\t \x01(\x05\x12\x1f\n\x17\x62uffOriginatorRuntimeId\x18\n \x01(\x05\"_\n\x12\x41\x63torBuffMarkState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05layer\x18\x02 \x02(\x05\x12\x16\n\x0eorigin_actorId\x18\x03 \x02(\x05\x12\x10\n\x08leftTime\x18\x04 \x01(\x05\"|\n\x0e\x41\x63torBuffState\x12\x35\n\x0b\x62uff_skills\x18\x01 \x03(\x0b\x32 .sgame_state.ActorBuffSkillState\x12\x33\n\nbuff_marks\x18\x02 \x03(\x0b\x32\x1f.sgame_state.ActorBuffMarkState\"Y\n\x11\x43lientErrorDetail\x12\x14\n\x0cuseSkillSlot\x18\x01 \x01(\x05\x12\x16\n\x0e\x65quipmentBuyId\x18\x02 \x01(\x05\x12\x16\n\x0elearnSkillSlot\x18\x03 \x01(\x05\"\xd4\x05\n\nActorState\x12\x11\n\tconfig_id\x18\x01 \x02(\x05\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12-\n\nactor_type\x18\x03 \x02(\x0e\x32\x19.sgame_state.ActorTypeDef\x12+\n\x08sub_type\x18\x04 \x02(\x0e\x32\x19.sgame_state.ActorTypeSub\x12)\n\x04\x63\x61mp\x18\x05 \x02(\x0e\x32\x1b.sgame_state.COM_PLAYERCAMP\x12.\n\nbehav_mode\x18\x06 \x02(\x0e\x32\x1a.sgame_state.ObjBehaviMode\x12$\n\x08location\x18\x07 \x02(\x0b\x32\x12.sgame_state.VInt3\x12#\n\x07\x66orward\x18\x08 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\n\n\x02hp\x18\t \x02(\x05\x12\x0e\n\x06max_hp\x18\n \x02(\x05\x12\'\n\x06values\x18\x0b \x01(\x0b\x32\x17.sgame_state.ActorValue\x12\x11\n\tabilities\x18\x0c \x03(\x08\x12\x14\n\x0c\x61ttack_range\x18\r \x02(\x05\x12\x15\n\rattack_target\x18\x0e \x02(\x05\x12\x13\n\x0bkill_income\x18\x0f \x01(\x05\x12\x33\n\x0fhit_target_info\x18\x10 \x03(\x0b\x32\x1a.sgame_state.HitTargetInfo\x12\x14\n\x0c\x63\x61mp_visible\x18\x11 \x03(\x08\x12\x12\n\nsight_area\x18\x12 \x01(\x05\x12/\n\nbuff_state\x18\x13 \x01(\x0b\x32\x1b.sgame_state.ActorBuffState\x12\x31\n\x0ehurt_hero_info\x18\x14 \x03(\x0b\x32\x19.sgame_state.HurtHeroInfo\x12\x12\n\nhost_actor\x18\x15 \x01(\x05\x12,\n\x0bmethod_call\x18\x16 \x01(\x0b\x32\x17.sgame_state.MethodCall\"\x8c\x01\n\rHitTargetInfo\x12\x12\n\nhit_target\x18\x01 \x01(\x05\x12\x10\n\x08skill_id\x18\x02 \x01(\r\x12-\n\tslot_type\x18\x03 \x01(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x17\n\x0f\x63onti_hit_count\x18\x04 \x01(\x05\x12\r\n\x05value\x18\x05 \x01(\x05\"1\n\x0cHurtHeroInfo\x12\x13\n\x0bhurt_target\x18\x01 \x01(\x05\x12\x0c\n\x04hurt\x18\x02 \x01(\r\"\xbb\x01\n\x0cTakeHurtInfo\x12\r\n\x05\x61tker\x18\x01 \x02(\x05\x12\x11\n\thurtValue\x18\x02 \x02(\x05\x12\x11\n\tskillSlot\x18\x03 \x02(\x05\x12\x34\n\nsourceType\x18\x04 \x02(\x0e\x32 .sgame_state.SKILL_USE_FROM_TYPE\x12\x10\n\x08sourceID\x18\x05 \x02(\x05\x12\x14\n\x0crealHurtInfo\x18\x06 \x01(\x05\x12\x18\n\x10skill_runtime_id\x18\x07 \x01(\r\"\xab\x01\n\rSkillStatInfo\x12-\n\tslot_type\x18\x01 \x01(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x15\n\rkillHeroTimes\x18\x02 \x01(\x05\x12\x11\n\ttotalHurt\x18\x03 \x01(\x05\x12\x15\n\rtotalHurtHero\x18\x04 \x01(\x05\x12\x14\n\x0chardCtrlTime\x18\x05 \x01(\x05\x12\x14\n\x0csoftCtrlTime\x18\x06 \x01(\x05\"\x91\x01\n\x0f\x43lientErrorInfo\x12\x30\n\nerror_code\x18\x01 \x01(\x0e\x32\x1c.sgame_state.ClientErrorCode\x12;\n\x13\x63lient_error_detail\x18\x02 \x01(\x0b\x32\x1e.sgame_state.ClientErrorDetail\x12\x0f\n\x07\x66rameNo\x18\x03 \x01(\x05*}\n\x15SkillRangeAppointType\x12\x08\n\x04\x41uto\x10\x00\x12\n\n\x06Target\x10\x01\x12\x07\n\x03Pos\x10\x02\x12\x0f\n\x0b\x44irectional\x10\x03\x12\t\n\x05Track\x10\x04\x12\x18\n\x14Project8Directionnal\x10\x05\x12\x0f\n\x0bProject8Pos\x10\x06*\x9f\x02\n\x0c\x41\x63torTypeDef\x12\x13\n\x0f\x41\x43TOR_TYPE_HERO\x10\x00\x12\x16\n\x12\x41\x43TOR_TYPE_MONSTER\x10\x01\x12\x14\n\x10\x41\x43TOR_TYPE_ORGAN\x10\x02\x12\x12\n\x0e\x41\x43TOR_TYPE_EYE\x10\x03\x12\x13\n\x0f\x41\x43TOR_TYPE_CALL\x10\x04\x12\x15\n\x11\x41\x43TOR_TYPE_BULLET\x10\x05\x12\x14\n\x10\x41\x43TOR_TYPE_BLOCK\x10\x06\x12\x1b\n\x17\x41\x43TOR_TYPE_INTERACTITEM\x10\x07\x12\x15\n\x11\x41\x43TOR_TYPE_SHENFU\x10\x08\x12\x16\n\x12\x41\x43TOR_TYPE_VEHICLE\x10\t\x12\x12\n\x0e\x41\x43TOR_TYPE_ALL\x10\n\x12\x16\n\x12\x41\x43TOR_TYPE_INVALID\x10\x0b*\xde\x02\n\x0c\x41\x63torTypeSub\x12\x12\n\x0e\x41\x43TOR_SUB_NONE\x10\x00\x12\x15\n\x11\x41\x43TOR_SUB_SOLDIER\x10\x0b\x12\x14\n\x10\x41\x43TOR_SUB_BAOJUN\x10\x0c\x12\x13\n\x0f\x41\x43TOR_SUB_BARON\x10\r\x12\x14\n\x10\x41\x43TOR_SUB_BLUEBA\x10\x0e\x12\x13\n\x0f\x41\x43TOR_SUB_REDBA\x10\x0f\x12\x17\n\x13\x41\x43TOR_SUB_BIGDRAGON\x10\x10\x12\x1c\n\x18\x41\x43TOR_SUB_DARK_BIGDRAGON\x10\x11\x12\x18\n\x14\x41\x43TOR_SUB_XIAOZHUZAI\x10\x12\x12\x1a\n\x16\x41\x43TOR_SUB_YUANGUBAOJUN\x10\x13\x12\x13\n\x0f\x41\x43TOR_SUB_TOWER\x10\x15\x12\x18\n\x14\x41\x43TOR_SUB_TOWER_HIGH\x10\x16\x12\x1a\n\x16\x41\x43TOR_SUB_TOWER_SPRING\x10\x17\x12\x15\n\x11\x41\x43TOR_SUB_CRYSTAL\x10\x18*\x82\x0f\n\x0e\x43OM_PLAYERCAMP\x12\x16\n\x12\x43OM_PLAYERCAMP_MID\x10\x00\x12\x14\n\x10\x43OM_PLAYERCAMP_1\x10\x01\x12\x14\n\x10\x43OM_PLAYERCAMP_2\x10\x02\x12\x14\n\x10\x43OM_PLAYERCAMP_3\x10\x03\x12\x14\n\x10\x43OM_PLAYERCAMP_4\x10\x04\x12\x14\n\x10\x43OM_PLAYERCAMP_5\x10\x05\x12\x14\n\x10\x43OM_PLAYERCAMP_6\x10\x06\x12\x14\n\x10\x43OM_PLAYERCAMP_7\x10\x07\x12\x14\n\x10\x43OM_PLAYERCAMP_8\x10\x08\x12\x14\n\x10\x43OM_PLAYERCAMP_9\x10\t\x12\x15\n\x11\x43OM_PLAYERCAMP_10\x10\n\x12\x15\n\x11\x43OM_PLAYERCAMP_11\x10\x0b\x12\x15\n\x11\x43OM_PLAYERCAMP_12\x10\x0c\x12\x15\n\x11\x43OM_PLAYERCAMP_13\x10\r\x12\x15\n\x11\x43OM_PLAYERCAMP_14\x10\x0e\x12\x15\n\x11\x43OM_PLAYERCAMP_15\x10\x0f\x12\x15\n\x11\x43OM_PLAYERCAMP_16\x10\x10\x12\x15\n\x11\x43OM_PLAYERCAMP_17\x10\x11\x12\x15\n\x11\x43OM_PLAYERCAMP_18\x10\x12\x12\x15\n\x11\x43OM_PLAYERCAMP_19\x10\x13\x12\x15\n\x11\x43OM_PLAYERCAMP_20\x10\x14\x12\x15\n\x11\x43OM_PLAYERCAMP_21\x10\x15\x12\x15\n\x11\x43OM_PLAYERCAMP_22\x10\x16\x12\x15\n\x11\x43OM_PLAYERCAMP_23\x10\x17\x12\x15\n\x11\x43OM_PLAYERCAMP_24\x10\x18\x12\x15\n\x11\x43OM_PLAYERCAMP_25\x10\x19\x12\x15\n\x11\x43OM_PLAYERCAMP_26\x10\x1a\x12\x15\n\x11\x43OM_PLAYERCAMP_27\x10\x1b\x12\x15\n\x11\x43OM_PLAYERCAMP_28\x10\x1c\x12\x15\n\x11\x43OM_PLAYERCAMP_29\x10\x1d\x12\x15\n\x11\x43OM_PLAYERCAMP_30\x10\x1e\x12\x15\n\x11\x43OM_PLAYERCAMP_31\x10\x1f\x12\x15\n\x11\x43OM_PLAYERCAMP_32\x10 \x12\x15\n\x11\x43OM_PLAYERCAMP_33\x10!\x12\x15\n\x11\x43OM_PLAYERCAMP_34\x10\"\x12\x15\n\x11\x43OM_PLAYERCAMP_35\x10#\x12\x15\n\x11\x43OM_PLAYERCAMP_36\x10$\x12\x15\n\x11\x43OM_PLAYERCAMP_37\x10%\x12\x15\n\x11\x43OM_PLAYERCAMP_38\x10&\x12\x15\n\x11\x43OM_PLAYERCAMP_39\x10\'\x12\x15\n\x11\x43OM_PLAYERCAMP_40\x10(\x12\x15\n\x11\x43OM_PLAYERCAMP_41\x10)\x12\x15\n\x11\x43OM_PLAYERCAMP_42\x10*\x12\x15\n\x11\x43OM_PLAYERCAMP_43\x10+\x12\x15\n\x11\x43OM_PLAYERCAMP_44\x10,\x12\x15\n\x11\x43OM_PLAYERCAMP_45\x10-\x12\x15\n\x11\x43OM_PLAYERCAMP_46\x10.\x12\x15\n\x11\x43OM_PLAYERCAMP_47\x10/\x12\x15\n\x11\x43OM_PLAYERCAMP_48\x10\x30\x12\x15\n\x11\x43OM_PLAYERCAMP_49\x10\x31\x12\x15\n\x11\x43OM_PLAYERCAMP_50\x10\x32\x12\x15\n\x11\x43OM_PLAYERCAMP_51\x10\x33\x12\x15\n\x11\x43OM_PLAYERCAMP_52\x10\x34\x12\x15\n\x11\x43OM_PLAYERCAMP_53\x10\x35\x12\x15\n\x11\x43OM_PLAYERCAMP_54\x10\x36\x12\x15\n\x11\x43OM_PLAYERCAMP_55\x10\x37\x12\x15\n\x11\x43OM_PLAYERCAMP_56\x10\x38\x12\x15\n\x11\x43OM_PLAYERCAMP_57\x10\x39\x12\x15\n\x11\x43OM_PLAYERCAMP_58\x10:\x12\x15\n\x11\x43OM_PLAYERCAMP_59\x10;\x12\x15\n\x11\x43OM_PLAYERCAMP_60\x10<\x12\x15\n\x11\x43OM_PLAYERCAMP_61\x10=\x12\x15\n\x11\x43OM_PLAYERCAMP_62\x10>\x12\x15\n\x11\x43OM_PLAYERCAMP_63\x10?\x12\x15\n\x11\x43OM_PLAYERCAMP_64\x10@\x12\x15\n\x11\x43OM_PLAYERCAMP_65\x10\x41\x12\x15\n\x11\x43OM_PLAYERCAMP_66\x10\x42\x12\x15\n\x11\x43OM_PLAYERCAMP_67\x10\x43\x12\x15\n\x11\x43OM_PLAYERCAMP_68\x10\x44\x12\x15\n\x11\x43OM_PLAYERCAMP_69\x10\x45\x12\x15\n\x11\x43OM_PLAYERCAMP_70\x10\x46\x12\x15\n\x11\x43OM_PLAYERCAMP_71\x10G\x12\x15\n\x11\x43OM_PLAYERCAMP_72\x10H\x12\x15\n\x11\x43OM_PLAYERCAMP_73\x10I\x12\x15\n\x11\x43OM_PLAYERCAMP_74\x10J\x12\x15\n\x11\x43OM_PLAYERCAMP_75\x10K\x12\x15\n\x11\x43OM_PLAYERCAMP_76\x10L\x12\x15\n\x11\x43OM_PLAYERCAMP_77\x10M\x12\x15\n\x11\x43OM_PLAYERCAMP_78\x10N\x12\x15\n\x11\x43OM_PLAYERCAMP_79\x10O\x12\x15\n\x11\x43OM_PLAYERCAMP_80\x10P\x12\x18\n\x14\x43OM_PLAYERCAMP_COUNT\x10Q\x12\x17\n\x12\x43OM_PLAYERCAMP_ALL\x10\xff\x01*\xd2\x04\n\rObjBehaviMode\x12\x0e\n\nState_Idle\x10\x00\x12\x0e\n\nState_Dead\x10\x01\x12\x12\n\x0e\x44irection_Move\x10\x02\x12\x14\n\x10\x44\x65stination_Move\x10\x03\x12\x11\n\rNormal_Attack\x10\x04\x12\x0f\n\x0b\x41ttack_Move\x10\x05\x12\x0f\n\x0b\x41ttack_Path\x10\x06\x12\x0f\n\x0b\x41ttack_Lock\x10\x07\x12\x0e\n\nUseSkill_0\x10\x08\x12\x0e\n\nUseSkill_1\x10\t\x12\x0e\n\nUseSkill_2\x10\n\x12\x0e\n\nUseSkill_3\x10\x0b\x12\x10\n\x0cUseSkill_EX3\x10\x0c\x12\x0e\n\nUseSkill_4\x10\r\x12\x0e\n\nUseSkill_5\x10\x0e\x12\x0e\n\nUseSkill_6\x10\x0f\x12\x0e\n\nUseSkill_7\x10\x10\x12\x0e\n\nUseSkill_9\x10\x11\x12\x0f\n\x0bUseSkill_10\x10\x12\x12\x0f\n\x0bUseSkill_11\x10\x13\x12\x0f\n\x0bUseSkill_12\x10\x14\x12\x0f\n\x0bUseSkill_13\x10\x15\x12\x0f\n\x0bUseSkill_14\x10\x16\x12\x10\n\x0cState_AutoAI\x10\x17\x12\x12\n\x0eState_GameOver\x10\x18\x12\x16\n\x12State_OutOfControl\x10\x19\x12\x0e\n\nState_Born\x10\x1a\x12\x10\n\x0cState_Revive\x10\x1b\x12\x0f\n\x0bState_Dying\x10\x1c\x12\x12\n\x0eState_StayBush\x10\x1d\x12\x11\n\rState_Retreat\x10\x1e\x12\x12\n\x0eState_Standing\x10\x1f\x12\x0e\n\nState_Null\x10 *\xd6\x05\n\x0eObjAbilityType\x12\x18\n\x14ObjAbility_NoControl\x10\x00\x12\x15\n\x11ObjAbility_NoMove\x10\x01\x12\x16\n\x12ObjAbility_NoSkill\x10\x02\x12\x1d\n\x19ObjAbility_ImmuneNegative\x10\x03\x12\x1c\n\x18ObjAbility_ImmuneControl\x10\x04\x12\x1b\n\x17ObjAbility_NoMoveRotate\x10\x05\x12\x19\n\x15ObjAbility_ImmuneCrit\x10\x06\x12\x18\n\x14ObjAbility_Blindness\x10\x07\x12\x1a\n\x16ObjAbility_MoveProtect\x10\x08\x12\x1e\n\x1aObjAbility_NoRecoverEnergy\x10\t\x12\x15\n\x11ObjAbility_Freeze\x10\n\x12\x1a\n\x16ObjAbility_DeadControl\x10\x0b\x12#\n\x1fObjAbility_NoCollisionDetection\x10\x0c\x12\x1b\n\x17ObjAbility_NoJointSkill\x10\r\x12\x18\n\x14ObjAbility_AbortMove\x10\x0e\x12\x1b\n\x17ObjAbility_ForbidSelect\x10\x0f\x12\x16\n\x12ObjAbility_Renewal\x10\x10\x12\x15\n\x11ObjAbility_Sprint\x10\x11\x12!\n\x1dObjAbility_NoMoveButCanRatate\x10\x12\x12%\n!ObjAbility_ForbidSelectBySkillOrg\x10\x13\x12;\n7ObjAbility_ImmunePositiveAndPersistFromOtherOriginators\x10\x14\x12\x18\n\x14ObjAbility_Repressed\x10\x15\x12 \n\x1cObjAbility_ImmuneDeMoveSpeed\x10\x16\x12\x12\n\x0eObjAbility_Max\x10\x17*\xd0\x02\n\rSkillSlotType\x12\x10\n\x0cSLOT_SKILL_0\x10\x00\x12\x10\n\x0cSLOT_SKILL_1\x10\x01\x12\x10\n\x0cSLOT_SKILL_2\x10\x02\x12\x10\n\x0cSLOT_SKILL_3\x10\x03\x12\x12\n\x0eSLOT_SKILL_EX3\x10\x04\x12\x10\n\x0cSLOT_SKILL_4\x10\x05\x12\x10\n\x0cSLOT_SKILL_5\x10\x06\x12\x10\n\x0cSLOT_SKILL_6\x10\x07\x12\x10\n\x0cSLOT_SKILL_7\x10\x08\x12\x10\n\x0cSLOT_SKILL_9\x10\t\x12\x11\n\rSLOT_SKILL_10\x10\n\x12\x11\n\rSLOT_SKILL_11\x10\x0b\x12\x11\n\rSLOT_SKILL_12\x10\x0c\x12\x11\n\rSLOT_SKILL_13\x10\r\x12\x11\n\rSLOT_SKILL_14\x10\x0e\x12\x14\n\x10SLOT_SKILL_COUNT\x10\x0f\x12\x14\n\x10SLOT_SKILL_VALID\x10\x10*\xc6\x02\n\x0eSkillAbortType\x12\x10\n\x0cTYPE_SKILL_0\x10\x00\x12\x10\n\x0cTYPE_SKILL_1\x10\x01\x12\x10\n\x0cTYPE_SKILL_2\x10\x02\x12\x10\n\x0cTYPE_SKILL_3\x10\x03\x12\x12\n\x0eTYPE_SKILL_EX3\x10\x04\x12\x10\n\x0cTYPE_SKILL_4\x10\x05\x12\x10\n\x0cTYPE_SKILL_5\x10\x06\x12\x10\n\x0cTYPE_SKILL_6\x10\x07\x12\x10\n\x0cTYPE_SKILL_7\x10\x08\x12\x10\n\x0cTYPE_SKILL_9\x10\t\x12\x11\n\rTYPE_SKILL_10\x10\n\x12\x11\n\rTYPE_SKILL_11\x10\x0b\x12\x14\n\x10TYPE_SKILL_COUNT\x10\x0c\x12\x14\n\x10TYPE_SKILL_VALID\x10\r\x12\r\n\tTYPE_MOVE\x10\x0e\x12\x0f\n\x0bTYPE_DAMAGE\x10\x0f\x12\x0c\n\x08TYPE_MAX\x10\x10*\xbd\x01\n\x13SKILL_USE_FROM_TYPE\x12\x1d\n\x19SKILL_USE_FROM_TYPE_SKILL\x10\x00\x12\x1d\n\x19SKILL_USE_FROM_TYPE_EQUIP\x10\x01\x12#\n\x1fSKILL_USE_FROM_TYPE_AREATRIGGER\x10\x02\x12$\n SKILL_USE_FROM_TYPE_PASSIVESKILL\x10\x03\x12\x1d\n\x19SKILL_USE_FROM_TYPE_COUNT\x10\x04*\xed\x02\n\x12MiniMapElementType\x12\x14\n\x10\x45lementType_None\x10\x00\x12\x15\n\x11\x45lementType_Tower\x10\x01\x12\x14\n\x10\x45lementType_Base\x10\x02\x12\x14\n\x10\x45lementType_Hero\x10\x03\x12\x1c\n\x18\x45lementType_Dragon_5_big\x10\x04\x12\x1e\n\x1a\x45lementType_Dragon_5_small\x10\x05\x12\x18\n\x14\x45lementType_Dragon_3\x10\x06\x12\x13\n\x0f\x45lementType_Eye\x10\x07\x12\x17\n\x13\x45lementType_Solider\x10\x08\x12\x16\n\x12\x45lementType_Signal\x10\t\x12#\n\x1f\x45lementType_Dragon_5_XiaoZhuZai\x10\n\x12%\n!ElementType_Dragon_5_YuanGuBaoJun\x10\x0b\x12\x14\n\x10\x45lementType_Buff\x10\x0c*\xd2\x01\n\x0bProtectType\x12\x10\n\x0cPROTECT_NONE\x10\x00\x12\x14\n\x10PROTECT_PHYSHURT\x10\x01\x12\x15\n\x11PROTECT_MAGICHURT\x10\x02\x12\x0f\n\x0bPROTECT_ALL\x10\x03\x12 \n\x1cPROTECT_ALL_INCLUDE_REALHURT\x10\x04\x12\x13\n\x0fPROTECT_CONVERT\x10\x05\x12\x12\n\x0ePROTECT_EFFECT\x10\x06\x12\x14\n\x10PROTECT_PROPERTY\x10\x07\x12\x12\n\x0ePROTECT_ENERGY\x10\x08*\xab\x01\n\x0f\x43lientErrorCode\x12\x1a\n\x16\x43LIENT_ERROR_CODE_NONE\x10\x00\x12&\n\"CLIENT_ERROR_CODE_USE_SKILL_FAILED\x10\x01\x12*\n&CLIENT_ERROR_CODE_BUY_EQUIPMENT_FAILED\x10\x02\x12(\n$CLIENT_ERROR_CODE_LERAN_SKILL_FAILED\x10\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_SKILLRANGEAPPOINTTYPE']._serialized_start=2811
  _globals['_SKILLRANGEAPPOINTTYPE']._serialized_end=2936
  _globals['_ACTORTYPEDEF']._serialized_start=2939
  _globals['_ACTORTYPEDEF']._serialized_end=3226
  _globals['_ACTORTYPESUB']._serialized_start=3229
  _globals['_ACTORTYPESUB']._serialized_end=3579
  _globals['_COM_PLAYERCAMP']._serialized_start=3582
  _globals['_COM_PLAYERCAMP']._serialized_end=5504
  _globals['_OBJBEHAVIMODE']._serialized_start=5507
  _globals['_OBJBEHAVIMODE']._serialized_end=6101
  _globals['_OBJABILITYTYPE']._serialized_start=6104
  _globals['_OBJABILITYTYPE']._serialized_end=6830
  _globals['_SKILLSLOTTYPE']._serialized_start=6833
  _globals['_SKILLSLOTTYPE']._serialized_end=7169
  _globals['_SKILLABORTTYPE']._serialized_start=7172
  _globals['_SKILLABORTTYPE']._serialized_end=7498
  _globals['_SKILL_USE_FROM_TYPE']._serialized_start=7501
  _globals['_SKILL_USE_FROM_TYPE']._serialized_end=7690
  _globals['_MINIMAPELEMENTTYPE']._serialized_start=7693
  _globals['_MINIMAPELEMENTTYPE']._serialized_end=8058
  _globals['_PROTECTTYPE']._serialized_start=8061
  _globals['_PROTECTTYPE']._serialized_end=8271
  _globals['_CLIENTERRORCODE']._serialized_start=8274
  _globals['_CLIENTERRORCODE']._serialized_end=8445
  _globals['_VINT2']._serialized_start=29
  _globals['_VINT2']._serialized_end=58
  _globals['_VINT3']._serialized_start=60
  _globals['_VINT3']._serialized_end=100
  _globals['_TINT3']._serialized_start=102
  _globals['_TINT3']._serialized_end=202
  _globals['_BOXCOLLIDER']._serialized_start=204
  _globals['_BOXCOLLIDER']._serialized_end=328
  _globals['_SPHERECOLLIDER']._serialized_start=330
  _globals['_SPHERECOLLIDER']._serialized_end=400
  _globals['_ACTORVALUE']._serialized_start=403
  _globals['_ACTORVALUE']._serialized_end=802
  _globals['_METHODCALL']._serialized_start=804
  _globals['_METHODCALL']._serialized_end=838
  _globals['_ACTORBUFFSKILLSTATE']._serialized_start=841
  _globals['_ACTORBUFFSKILLSTATE']._serialized_end=1062
  _globals['_ACTORBUFFMARKSTATE']._serialized_start=1064
  _globals['_ACTORBUFFMARKSTATE']._serialized_end=1159
  _globals['_ACTORBUFFSTATE']._serialized_start=1161
  _globals['_ACTORBUFFSTATE']._serialized_end=1285
  _globals['_CLIENTERRORDETAIL']._serialized_start=1287
  _globals['_CLIENTERRORDETAIL']._serialized_end=1376
  _globals['_ACTORSTATE']._serialized_start=1379
  _globals['_ACTORSTATE']._serialized_end=2103
  _globals['_HITTARGETINFO']._serialized_start=2106
  _globals['_HITTARGETINFO']._serialized_end=2246
  _globals['_HURTHEROINFO']._serialized_start=2248
  _globals['_HURTHEROINFO']._serialized_end=2297
  _globals['_TAKEHURTINFO']._serialized_start=2300
  _globals['_TAKEHURTINFO']._serialized_end=2487
  _globals['_SKILLSTATINFO']._serialized_start=2490
  _globals['_SKILLSTATINFO']._serialized_end=2661
  _globals['_CLIENTERRORINFO']._serialized_start=2664
  _globals['_CLIENTERRORINFO']._serialized_end=2809
# @@protoc_insertion_point(module_scope)
