# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: serverai_adjust.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15serverai_adjust.proto\x12\x0fsgame_ai_server\"l\n\x06\x41\x63tion\x12\x11\n\taction_id\x18\x01 \x02(\r\x12\x12\n\nruntime_id\x18\x02 \x02(\r\x12\x13\n\x0b\x61\x63tion_name\x18\x03 \x02(\t\x12\x14\n\x0c\x61\x63tion_param\x18\x04 \x02(\t\x12\x10\n\x08\x64uration\x18\x05 \x02(\r\"G\n\x0e\x41\x64justToBotMsg\x12\x0c\n\x04type\x18\x01 \x02(\r\x12\'\n\x06\x61\x63tion\x18\x02 \x01(\x0b\x32\x17.sgame_ai_server.Action\"\x1e\n\x0e\x42otToAdjustMsg\x12\x0c\n\x04type\x18\x01 \x02(\r\"T\n\x15ServerAIAdjustRequest\x12;\n\x12\x62ot_to_adjust_msgs\x18\x01 \x03(\x0b\x32\x1f.sgame_ai_server.BotToAdjustMsg\"U\n\x16ServerAIAdjustResponse\x12;\n\x12\x61\x64just_to_bot_msgs\x18\x01 \x03(\x0b\x32\x1f.sgame_ai_server.AdjustToBotMsg*P\n\x12\x41\x64justToBotMsgType\x12\x1b\n\x17\x41\x64justToBotMsgType_None\x10\x00\x12\x1d\n\x19\x41\x64justToBotMsgType_Action\x10\x01*1\n\x12\x42otToAdjustMsgType\x12\x1b\n\x17\x42otToAdjustMsgType_None\x10\x00')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'serverai_adjust_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ADJUSTTOBOTMSGTYPE']._serialized_start=430
  _globals['_ADJUSTTOBOTMSGTYPE']._serialized_end=510
  _globals['_BOTTOADJUSTMSGTYPE']._serialized_start=512
  _globals['_BOTTOADJUSTMSGTYPE']._serialized_end=561
  _globals['_ACTION']._serialized_start=42
  _globals['_ACTION']._serialized_end=150
  _globals['_ADJUSTTOBOTMSG']._serialized_start=152
  _globals['_ADJUSTTOBOTMSG']._serialized_end=223
  _globals['_BOTTOADJUSTMSG']._serialized_start=225
  _globals['_BOTTOADJUSTMSG']._serialized_end=255
  _globals['_SERVERAIADJUSTREQUEST']._serialized_start=257
  _globals['_SERVERAIADJUSTREQUEST']._serialized_end=341
  _globals['_SERVERAIADJUSTRESPONSE']._serialized_start=343
  _globals['_SERVERAIADJUSTRESPONSE']._serialized_end=428
# @@protoc_insertion_point(module_scope)
