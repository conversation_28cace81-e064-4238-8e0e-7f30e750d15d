# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: wz_highlight.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12wz_highlight.proto\x12\x15sgame_state_highlight\x1a\x0c\x63ommon.proto\"\x89\x01\n\x07NpcInfo\x12\x11\n\trunTimeId\x18\x01 \x01(\x05\x12,\n\tactorType\x18\x02 \x01(\x0e\x32\x19.sgame_state.ActorTypeDef\x12/\n\x0c\x61\x63torSubType\x18\x03 \x01(\x0e\x32\x19.sgame_state.ActorTypeSub\x12\x0c\n\x04\x63\x61mp\x18\x04 \x01(\x05\"o\n\x0cSkillHitInfo\x12\x14\n\x0chitTimestamp\x18\x01 \x01(\x03\x12\x10\n\x08hitHeros\x18\x02 \x03(\x05\x12\x11\n\tdistances\x18\x03 \x03(\x05\x12\x15\n\risHardControl\x18\x04 \x01(\x08\x12\r\n\x05\x65xtra\x18\x05 \x03(\x05\"\xcb\x01\n\rCastSkillInfo\x12\x0f\n\x07skillId\x18\x01 \x01(\x05\x12\x15\n\rcastTimestamp\x18\x02 \x01(\x03\x12\x35\n\x08hitInfos\x18\x03 \x03(\x0b\x32#.sgame_state_highlight.SkillHitInfo\x12\x10\n\x08\x64uration\x18\x05 \x01(\x05\x12\x15\n\rhighlightFlag\x18\x06 \x01(\x05\x12\x32\n\tskillTags\x18\x07 \x01(\x0e\x32\x1f.sgame_state_highlight.SkillTag\"X\n\rHeroSkillInfo\x12\x11\n\trunTimeId\x18\x01 \x01(\x05\x12\x34\n\x06skills\x18\x02 \x03(\x0b\x32$.sgame_state_highlight.CastSkillInfo\"T\n\nReviveInfo\x12\x11\n\trunTimeId\x18\x01 \x01(\x05\x12\x0f\n\x07skillId\x18\x02 \x01(\x05\x12\x0f\n\x07\x65quipId\x18\x03 \x01(\x05\x12\x11\n\ttimestamp\x18\x04 \x01(\x03\"g\n\x12HighlightEquipInfo\x12\x11\n\trunTimeId\x18\x01 \x01(\x05\x12\x0f\n\x07\x65quipId\x18\x02 \x01(\x05\x12\x14\n\x0c\x62uyTimestamp\x18\x03 \x01(\x03\x12\x17\n\x0f\x61\x63tiveTimestamp\x18\x04 \x01(\x03\"G\n\x08KillInfo\x12\x11\n\trunTimeId\x18\x01 \x01(\x05\x12\x15\n\rdeadRunTimeId\x18\x02 \x01(\x05\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\"d\n\x0bKillNpcInfo\x12\x11\n\trunTimeId\x18\x01 \x01(\x05\x12/\n\x07npcInfo\x18\x02 \x01(\x0b\x32\x1e.sgame_state_highlight.NpcInfo\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\"\xdd\x02\n\rEventBaseInfo\x12\x0e\n\x06\x64\x61mage\x18\x01 \x01(\x03\x12\x13\n\x0b\x64\x61mageRatio\x18\x02 \x01(\x02\x12\x18\n\x10\x64\x61mageBloodRatio\x18\x03 \x01(\x02\x12\x0e\n\x06injury\x18\x04 \x01(\x03\x12\x13\n\x0binjuryRatio\x18\x05 \x01(\x02\x12\x18\n\x10injuryBloodRatio\x18\x06 \x01(\x02\x12\x0c\n\x04\x63ure\x18\x07 \x01(\x03\x12\x11\n\tcureRatio\x18\x08 \x01(\x02\x12\x0e\n\x06shield\x18\t \x01(\x03\x12\x13\n\x0bshieldRatio\x18\n \x01(\x02\x12\r\n\x05\x64odge\x18\x0b \x01(\x03\x12\x17\n\x0f\x66irstDamageTime\x18\x0c \x01(\x03\x12\x17\n\x0f\x66irstInjureTime\x18\r \x01(\x03\x12\x15\n\rfirstCureTime\x18\x0e \x01(\x03\x12\x17\n\x0f\x66irstShieldTime\x18\x0f \x01(\x03\x12\x17\n\x0f\x66inalBloodRatio\x18\x10 \x01(\x02\"0\n\x12HighLightExtraInfo\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xb0\x02\n\x0e\x45ventExtraInfo\x12\x38\n\x05\x65xtra\x18\x01 \x03(\x0b\x32).sgame_state_highlight.HighLightExtraInfo\x12\x14\n\x0c\x64\x65\x66\x65ndHeroes\x18\x02 \x03(\x05\x12\x42\n\x14interactiveSkillInfo\x18\x03 \x01(\x0b\x32$.sgame_state_highlight.CastSkillInfo\x12\x13\n\x0bUltTargetId\x18\x04 \x01(\x05\x12\x13\n\x0bUltDistance\x18\x05 \x01(\x05\x12\x18\n\x10hitNpcEnemyHeros\x18\x06 \x03(\x05\x12\x18\n\x10towerDamageRatio\x18\x07 \x01(\x02\x12\x17\n\x0frefreshSkillCnt\x18\x08 \x01(\x05\x12\x13\n\x0b\x61liveHeroes\x18\t \x03(\x05\"\xdb\x04\n\tEventInfo\x12\x33\n\teventType\x18\x01 \x01(\x0e\x32 .sgame_state_highlight.EventType\x12\x0f\n\x07\x65ventId\x18\x02 \x01(\x05\x12\x11\n\trunTimeId\x18\x03 \x01(\x05\x12\x11\n\ttimestamp\x18\x04 \x01(\x03\x12;\n\reventBaseInfo\x18\x05 \x01(\x0b\x32$.sgame_state_highlight.EventBaseInfo\x12=\n\x0e\x65ventExtraInfo\x18\x06 \x01(\x0b\x32%.sgame_state_highlight.EventExtraInfo\x12=\n\nheroEquips\x18\x07 \x03(\x0b\x32).sgame_state_highlight.HighlightEquipInfo\x12\x38\n\nheroSkills\x18\x08 \x03(\x0b\x32$.sgame_state_highlight.HeroSkillInfo\x12\x36\n\rkillHeroInfos\x18\t \x03(\x0b\x32\x1f.sgame_state_highlight.KillInfo\x12\x38\n\x0ckillNpcInfos\x18\n \x03(\x0b\x32\".sgame_state_highlight.KillNpcInfo\x12\x36\n\x0breviveInfos\x18\x0b \x03(\x0b\x32!.sgame_state_highlight.ReviveInfo\x12\x11\n\tstartTime\x18\x0c \x01(\x03\x12\x10\n\x08stopTime\x18\r \x01(\x03\x12\x1e\n\x16primaryTargetRuntimeId\x18\x0e \x01(\x05\"D\n\rHighlightInfo\x12\x33\n\teventList\x18\x01 \x03(\x0b\x32 .sgame_state_highlight.EventInfo\"\xf8\x01\n\x12HighlightVideoInfo\x12\x1c\n\x14highlighterRunTimeId\x18\x01 \x01(\r\x12\x33\n\tvideoType\x18\x02 \x01(\x0e\x32 .sgame_state_highlight.VideoType\x12\x12\n\nvideoScore\x18\x03 \x01(\r\x12\x11\n\tstartTime\x18\x04 \x01(\r\x12\x0f\n\x07\x65ndTime\x18\x05 \x01(\r\x12\x41\n\x10videoSpecialType\x18\x06 \x01(\x0e\x32\'.sgame_state_highlight.VideoSpecialType\x12\x14\n\x0chightlightId\x18\x07 \x01(\r*\xc3\x06\n\tEventType\x12\x0b\n\x07\x45T_NONE\x10\x00\x12\x14\n\x10\x45T_MULTI_CONTROL\x10\x01\x12\x14\n\x10\x45T_FIRST_CONTROL\x10\x02\x12\x11\n\rET_REMOTE_HIT\x10\x03\x12\x0b\n\x07\x45T_HURT\x10\x04\x12\r\n\tET_BEHURT\x10\x05\x12\x0b\n\x07\x45T_HEAL\x10\x06\x12\x0e\n\nET_PROTECT\x10\x07\x12\x0f\n\x0b\x45T_TELEPORT\x10\x08\x12\r\n\tET_REVIVE\x10\t\x12\x15\n\x11\x45T_IMMUNE_CONTROL\x10\n\x12$\n ET_NO_SELF_SOLDIER_DESTROY_TOWER\x10\x0b\x12$\n ET_HAVE_ENEMY_HERO_DESTROY_TOWER\x10\x0c\x12\x12\n\x0e\x45T_STEAL_TOWER\x10\r\x12\x12\n\x0e\x45T_GRAB_DRAGON\x10\x0e\x12\x12\n\x0e\x45T_JOINT_SKILL\x10\x0f\x12\x12\n\x0e\x45T_REMOTE_KILL\x10\x10\x12\x12\n\x0e\x45T_LOW_HP_KILL\x10\x11\x12\x17\n\x13\x45T_SKILL_FLASH_KILL\x10\x12\x12\x1a\n\x16\x45T_SKILL_FLASH_CONTROL\x10\x13\x12\x17\n\x13\x45T_KILL_UNDER_TOWER\x10\x14\x12\x13\n\x0f\x45T_INSTANT_KILL\x10\x15\x12\x18\n\x14\x45T_CHANGE_EQUIP_KILL\x10\x16\x12\x1a\n\x16\x45T_USE_SAME_SKILL_KILL\x10\x17\x12\x11\n\rET_BLIND_KILL\x10\x18\x12\x16\n\x12\x45T_SKILL_HIT_COUNT\x10\x19\x12\x1a\n\x16\x45T_KILL_WITHOUT_BEHURT\x10\x1a\x12\x16\n\x12\x45T_INVINCIBLE_TIME\x10\x1b\x12\x15\n\x11\x45T_DOUBLE_PROTECT\x10\x1c\x12\x13\n\x0f\x45T_LIMIT_RESIST\x10\x1d\x12\x13\n\x0f\x45T_LIMIT_RESCUE\x10\x1e\x12\x13\n\x0f\x45T_ALL_ASSEMBLE\x10\x1f\x12\x16\n\x12\x45T_SMALL_COME_BACK\x10 \x12\x14\n\x10\x45T_BIG_COME_BACK\x10!\x12\x19\n\x15\x45T_BUFF_TRIGGER_COUNT\x10\"\x12\x17\n\x13\x45T_GET_KEY_RESOURCE\x10#\x12\x1a\n\x16\x45T_BLOCK_LETHAL_DAMAGE\x10$*\xaa\x01\n\x08SkillTag\x12\x0b\n\x07ST_NONE\x10\x00\x12\n\n\x06ST_ANY\x10\x01\x12\x13\n\x0fST_HARD_CONTROL\x10\x02\x12\r\n\tST_SILENT\x10\x03\x12\r\n\tST_DISARM\x10\x04\x12\r\n\tST_REMOTE\x10\x05\x12\x0f\n\x0bST_TELEPORT\x10\x06\x12\r\n\tST_REVIVE\x10\x07\x12\x15\n\x11ST_IMMUNE_CONTROL\x10\x08\x12\x0c\n\x08ST_JOINT\x10\t*\x92\x05\n\tVideoType\x12\x0b\n\x07VT_NONE\x10\x00\x12\x12\n\x0eVT_SINGLE_KILL\x10\x01\x12\x12\n\x0eVT_DOUBLE_KILL\x10\x02\x12\x12\n\x0eVT_TRIPLE_KILL\x10\x03\x12\x13\n\x0fVT_QUATARY_KILL\x10\x04\x12\x11\n\rVT_PENTA_KILL\x10\x05\x12\x14\n\x10VT_MULTI_CONTROL\x10\x06\x12\x1c\n\x18VT_PERFECT_MULTI_CONTROL\x10\x07\x12\x14\n\x10VT_FIRST_CONTROL\x10\x08\x12\x15\n\x11VT_TEAMFIGHT_HURT\x10\t\x12\x1d\n\x19VT_PERFECT_TEAMFIGHT_HURT\x10\n\x12\x17\n\x13VT_TEAMFIGHT_BEHURT\x10\x0b\x12\x1f\n\x1bVT_PERFECT_TEAMFIGHT_BEHURT\x10\x0c\x12\x12\n\x0eVT_GRAB_DRAGON\x10\r\x12\x12\n\x0eVT_REMOTE_KILL\x10\x0e\x12\x12\n\x0eVT_LOW_HP_KILL\x10\x0f\x12\x17\n\x13VT_SKILL_FLASH_KILL\x10\x10\x12\x17\n\x13VT_KILL_UNDER_TOWER\x10\x11\x12\x13\n\x0fVT_INSTANT_KILL\x10\x12\x12\x18\n\x14VT_CHANGE_EQUIP_KILL\x10\x13\x12\x14\n\x10VT_JUESHIFENGHUA\x10\x14\x12\x13\n\x0fVT_LEITINGZHILI\x10\x15\x12\x15\n\x11VT_NIZHUANQIANKUN\x10\x16\x12\x14\n\x10VT_SHOULIEZHIREN\x10\x17\x12\x17\n\x13VT_YUEXIAWUXIANLIAN\x10\x18\x12\x13\n\x0fVT_POJINGZHIREN\x10\x19\x12\x14\n\x10VT_ANYESHOUGEZHE\x10\x1a\x12\x13\n\x0fVT_HUOLIQUANKAI\x10\x1b\x12\x0c\n\x08VT_COUNT\x10\x1c*\xc8\x01\n\x10VideoSpecialType\x12\x0c\n\x08VST_NONE\x10\x00\x12\x14\n\x10VST_MONSTER_KILL\x10\x01\x12\x17\n\x13VST_DOMINATE_BATTLE\x10\x02\x12\x11\n\rVST_LEGENDARY\x10\x03\x12\x17\n\x13VST_TOTAL_ANNIHILAT\x10\x04\x12\x0f\n\x0bVST_ODYSSEY\x10\x05\x12\x0f\n\x0bVST_ALLDEAD\x10\x06\x12\x15\n\x11VST_STOPMULTIKILL\x10\x07\x12\x12\n\x0eVST_FRIST_Kill\x10\x08\x42\x02H\x02')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'wz_highlight_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'H\002'
  _globals['_EVENTTYPE']._serialized_start=2611
  _globals['_EVENTTYPE']._serialized_end=3446
  _globals['_SKILLTAG']._serialized_start=3449
  _globals['_SKILLTAG']._serialized_end=3619
  _globals['_VIDEOTYPE']._serialized_start=3622
  _globals['_VIDEOTYPE']._serialized_end=4280
  _globals['_VIDEOSPECIALTYPE']._serialized_start=4283
  _globals['_VIDEOSPECIALTYPE']._serialized_end=4483
  _globals['_NPCINFO']._serialized_start=60
  _globals['_NPCINFO']._serialized_end=197
  _globals['_SKILLHITINFO']._serialized_start=199
  _globals['_SKILLHITINFO']._serialized_end=310
  _globals['_CASTSKILLINFO']._serialized_start=313
  _globals['_CASTSKILLINFO']._serialized_end=516
  _globals['_HEROSKILLINFO']._serialized_start=518
  _globals['_HEROSKILLINFO']._serialized_end=606
  _globals['_REVIVEINFO']._serialized_start=608
  _globals['_REVIVEINFO']._serialized_end=692
  _globals['_HIGHLIGHTEQUIPINFO']._serialized_start=694
  _globals['_HIGHLIGHTEQUIPINFO']._serialized_end=797
  _globals['_KILLINFO']._serialized_start=799
  _globals['_KILLINFO']._serialized_end=870
  _globals['_KILLNPCINFO']._serialized_start=872
  _globals['_KILLNPCINFO']._serialized_end=972
  _globals['_EVENTBASEINFO']._serialized_start=975
  _globals['_EVENTBASEINFO']._serialized_end=1324
  _globals['_HIGHLIGHTEXTRAINFO']._serialized_start=1326
  _globals['_HIGHLIGHTEXTRAINFO']._serialized_end=1374
  _globals['_EVENTEXTRAINFO']._serialized_start=1377
  _globals['_EVENTEXTRAINFO']._serialized_end=1681
  _globals['_EVENTINFO']._serialized_start=1684
  _globals['_EVENTINFO']._serialized_end=2287
  _globals['_HIGHLIGHTINFO']._serialized_start=2289
  _globals['_HIGHLIGHTINFO']._serialized_end=2357
  _globals['_HIGHLIGHTVIDEOINFO']._serialized_start=2360
  _globals['_HIGHLIGHTVIDEOINFO']._serialized_end=2608
# @@protoc_insertion_point(module_scope)
