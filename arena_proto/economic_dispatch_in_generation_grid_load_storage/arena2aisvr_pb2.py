# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: arena2aisvr.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import custom_pb2 as custom__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x61rena2aisvr.proto\x12\x37\x61rena.economic_dispatch_in_generation_grid_load_storage\x1a\x0c\x63ustom.proto\"\xda\x02\n\x0f\x41IServerRequest\x12\x0f\n\x07game_id\x18\x01 \x01(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x01(\x05\x12Q\n\x03obs\x18\x03 \x01(\x0b\x32\x44.arena.economic_dispatch_in_generation_grid_load_storage.Observation\x12V\n\nscore_info\x18\x04 \x01(\x0b\x32\x42.arena.economic_dispatch_in_generation_grid_load_storage.ScoreInfo\x12\x12\n\nterminated\x18\x05 \x01(\x05\x12\x11\n\ttruncated\x18\x06 \x01(\x05\x12R\n\x08\x65nv_info\x18\x07 \x01(\x0b\x32@.arena.economic_dispatch_in_generation_grid_load_storage.EnvInfo\"\x99\x01\n\x10\x41IServerResponse\x12\x0f\n\x07game_id\x18\x01 \x01(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x01(\x05\x12O\n\x06\x61\x63tion\x18\x03 \x01(\x0b\x32?.arena.economic_dispatch_in_generation_grid_load_storage.Action\x12\x11\n\tstop_game\x18\x04 \x01(\x05\x42zZxgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/economic_dispatch_in_generation_grid_load_storageb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'arena2aisvr_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Zxgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/economic_dispatch_in_generation_grid_load_storage'
  _globals['_AISERVERREQUEST']._serialized_start=93
  _globals['_AISERVERREQUEST']._serialized_end=439
  _globals['_AISERVERRESPONSE']._serialized_start=442
  _globals['_AISERVERRESPONSE']._serialized_end=595
# @@protoc_insertion_point(module_scope)
