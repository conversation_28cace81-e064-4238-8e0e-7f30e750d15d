# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x37\x61rena.economic_dispatch_in_generation_grid_load_storage\"\x1a\n\x07\x43ommand\x12\x0f\n\x07\x61\x63tions\x18\x01 \x03(\x02\"D\n\x0bNetTopology\x12\x13\n\x0b\x66latten_net\x18\x01 \x03(\x05\x12\x0f\n\x07shape_x\x18\x02 \x01(\x05\x12\x0f\n\x07shape_y\x18\x03 \x01(\x05\"\xd3\x03\n\nFrameState\x12\r\n\x05vm_pu\x18\x01 \x03(\x02\x12\x11\n\tva_degree\x18\x02 \x03(\x02\x12\x0e\n\x06load_p\x18\x03 \x03(\x02\x12\x0e\n\x06load_q\x18\x04 \x03(\x02\x12\r\n\x05ren_p\x18\x05 \x03(\x02\x12\r\n\x05ren_q\x18\x06 \x03(\x02\x12\r\n\x05gen_p\x18\x07 \x03(\x02\x12\r\n\x05gen_q\x18\x08 \x03(\x02\x12\x13\n\x0bstorage_soc\x18\t \x03(\x02\x12\x14\n\x0cstorage_maxe\x18\n \x03(\x02\x12\r\n\x05price\x18\x0b \x01(\x02\x12\x0e\n\x06line_p\x18\x0c \x03(\x02\x12\x0e\n\x06line_q\x18\r \x03(\x02\x12\x0e\n\x06switch\x18\x0e \x03(\x05\x12Z\n\x0cnet_topology\x18\x0f \x01(\x0b\x32\x44.arena.economic_dispatch_in_generation_grid_load_storage.NetTopology\x12\x10\n\x08\x62us_zone\x18\x10 \x03(\x05\x12\x11\n\tload_zone\x18\x11 \x03(\x05\x12\x11\n\tsgen_zone\x18\x12 \x03(\x05\x12\x10\n\x08gen_zone\x18\x13 \x03(\x05\x12\x14\n\x0cstorage_zone\x18\x14 \x03(\x05\x12\x10\n\x08load_all\x18\x15 \x03(\x02\x12\x0f\n\x07ren_all\x18\x16 \x03(\x02\"\xaf\x02\n\x08GameInfo\x12\x0e\n\x06reward\x18\x01 \x01(\x02\x12\x10\n\x08gen_cost\x18\x02 \x01(\x02\x12\x11\n\tsgen_cost\x18\x03 \x01(\x02\x12\x14\n\x0c\x65xtgrid_cost\x18\x04 \x01(\x02\x12\x0c\n\x04\x63ost\x18\x05 \x01(\x02\x12\r\n\x05price\x18\x06 \x01(\x02\x12\x11\n\tep_reward\x18\x07 \x01(\x02\x12\x13\n\x0b\x65p_gen_cost\x18\x08 \x01(\x02\x12\x14\n\x0c\x65p_sgen_cost\x18\t \x01(\x02\x12\x17\n\x0f\x65p_extgrid_cost\x18\n \x01(\x02\x12\x0f\n\x07\x65p_cost\x18\x0b \x01(\x02\x12\r\n\x05\x63rash\x18\x0c \x01(\x05\x12\x17\n\x0fv_control_ratio\x18\r \x01(\x02\x12\x18\n\x10\x65xtgrid_overflow\x18\x0e \x01(\x02\x12\x11\n\tline_loss\x18\x0f \x01(\x02\"\x19\n\x06\x41\x63tion\x12\x0f\n\x07\x61\x63tions\x18\x01 \x03(\x02\"a\n\x0bObservation\x12R\n\x05\x66rame\x18\x01 \x01(\x0b\x32\x43.arena.economic_dispatch_in_generation_grid_load_storage.FrameState\"\xdb\x01\n\tScoreInfo\x12\x0e\n\x06reward\x18\x01 \x01(\x02\x12\x10\n\x08gen_cost\x18\x02 \x01(\x02\x12\x11\n\tsgen_cost\x18\x03 \x01(\x02\x12\x14\n\x0c\x65xtgrid_cost\x18\x04 \x01(\x02\x12\x0c\n\x04\x63ost\x18\x05 \x01(\x02\x12\r\n\x05price\x18\x06 \x01(\x02\x12\x11\n\tep_reward\x18\x07 \x01(\x02\x12\x13\n\x0b\x65p_gen_cost\x18\x08 \x01(\x02\x12\x14\n\x0c\x65p_sgen_cost\x18\t \x01(\x02\x12\x17\n\x0f\x65p_extgrid_cost\x18\n \x01(\x02\x12\x0f\n\x07\x65p_cost\x18\x0b \x01(\x02\"^\n\x07\x45nvInfo\x12\r\n\x05\x63rash\x18\x01 \x01(\x05\x12\x17\n\x0fv_control_ratio\x18\x02 \x01(\x02\x12\x18\n\x10\x65xtgrid_overflow\x18\x03 \x01(\x02\x12\x11\n\tline_loss\x18\x04 \x01(\x02\x42zZxgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/economic_dispatch_in_generation_grid_load_storageb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Zxgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/economic_dispatch_in_generation_grid_load_storage'
  _globals['_COMMAND']._serialized_start=73
  _globals['_COMMAND']._serialized_end=99
  _globals['_NETTOPOLOGY']._serialized_start=101
  _globals['_NETTOPOLOGY']._serialized_end=169
  _globals['_FRAMESTATE']._serialized_start=172
  _globals['_FRAMESTATE']._serialized_end=639
  _globals['_GAMEINFO']._serialized_start=642
  _globals['_GAMEINFO']._serialized_end=945
  _globals['_ACTION']._serialized_start=947
  _globals['_ACTION']._serialized_end=972
  _globals['_OBSERVATION']._serialized_start=974
  _globals['_OBSERVATION']._serialized_end=1071
  _globals['_SCOREINFO']._serialized_start=1074
  _globals['_SCOREINFO']._serialized_end=1293
  _globals['_ENVINFO']._serialized_start=1295
  _globals['_ENVINFO']._serialized_end=1389
# @@protoc_insertion_point(module_scope)
