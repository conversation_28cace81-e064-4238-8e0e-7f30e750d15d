# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: common.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ommon.proto\x12\x1aintelligent_traffic_lights\"G\n\rAICommandInfo\x12\x0c\n\x04s_id\x18\x01 \x02(\r\x12\x16\n\x0enext_phase_idx\x18\x02 \x02(\r\x12\x10\n\x08\x64uration\x18\x03 \x02(\r\"\xe5\x01\n\x08Junction\x12\x0c\n\x04j_id\x18\x01 \x02(\r\x12\x0e\n\x06signal\x18\x02 \x02(\r\x12O\n\x19\x65nter_lanes_on_directions\x18\x03 \x03(\x0b\x32,.intelligent_traffic_lights.LanesOnDirection\x12N\n\x18\x65xit_lanes_on_directions\x18\x04 \x03(\x0b\x32,.intelligent_traffic_lights.LanesOnDirection\x12\x1a\n\x12neighbor_junctions\x18\x05 \x03(\r\"!\n\x10LanesOnDirection\x12\r\n\x05lanes\x18\x01 \x03(\r\"n\n\x0eLightsOnConfig\x12\x0f\n\x07\x66rom_id\x18\x01 \x02(\x03\x12\x10\n\x08\x65nter_id\x18\x02 \x02(\x03\x12\x12\n\ngreen_mask\x18\x03 \x02(\r\x12\x13\n\x0byellow_mask\x18\x04 \x02(\r\x12\x10\n\x08red_mask\x18\x05 \x02(\r\"Y\n\x10LightPhaseConfig\x12\x45\n\x11lights_on_configs\x18\x01 \x03(\x0b\x32*.intelligent_traffic_lights.LightsOnConfig\"\xc1\x01\n\x06Signal\x12\x0c\n\x04s_id\x18\x01 \x02(\r\x12<\n\x06phases\x18\x02 \x03(\x0b\x32,.intelligent_traffic_lights.LightPhaseConfig\x12\x11\n\tphase_idx\x18\x03 \x02(\r\x12\x32\n\x06scheme\x18\x04 \x01(\x0e\x32\".intelligent_traffic_lights.Scheme\x12\x10\n\x08\x64uration\x18\x05 \x02(\r\x12\x12\n\nstart_time\x18\x06 \x02(\r\"#\n\x04\x45\x64ge\x12\x0c\n\x04\x65_id\x18\x01 \x02(\r\x12\r\n\x05lanes\x18\x02 \x03(\r\"\\\n\nLaneConfig\x12\x0c\n\x04l_id\x18\x01 \x02(\r\x12\x0f\n\x07\x65\x64ge_id\x18\x02 \x02(\r\x12\x10\n\x08\x64ir_mask\x18\x03 \x02(\r\x12\x0e\n\x06length\x18\x04 \x02(\r\x12\r\n\x05width\x18\x05 \x02(\r\"\x80\x01\n\rVehicleConfig\x12\x13\n\x0bv_config_id\x18\x01 \x02(\r\x12\x37\n\x06v_type\x18\x02 \x02(\x0e\x32\'.intelligent_traffic_lights.VehicleType\x12\x0e\n\x06length\x18\x03 \x02(\r\x12\x11\n\tmax_speed\x18\x04 \x02(\r\"\xf6\x01\n\x07Vehicle\x12\x0c\n\x04v_id\x18\x01 \x02(\r\x12\x13\n\x0bv_config_id\x18\x02 \x02(\r\x12\x10\n\x04\x65\x64ge\x18\x03 \x01(\x05:\x02-1\x12\x10\n\x04lane\x18\x04 \x01(\x05:\x02-1\x12\x14\n\x08junction\x18\x05 \x01(\x05:\x02-1\x12\x1b\n\x0ftarget_junction\x18\x06 \x01(\x05:\x02-1\x12\x44\n\x10position_in_lane\x18\x07 \x01(\x0b\x32*.intelligent_traffic_lights.PositionInLane\x12\r\n\x05speed\x18\x08 \x02(\x05\x12\r\n\x05\x61\x63\x63\x65l\x18\t \x02(\x05\x12\r\n\x05\x61ngle\x18\n \x02(\x05\"&\n\x0ePositionInLane\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01y\x18\x02 \x02(\x05\",\n\rJunctionDelay\x12\x0c\n\x04j_id\x18\x01 \x02(\r\x12\r\n\x05\x64\x65lay\x18\x02 \x02(\x02\"9\n\x13JunctionQueueLength\x12\x0c\n\x04j_id\x18\x01 \x02(\r\x12\x14\n\x0cqueue_length\x18\x02 \x02(\x05\"0\n\x0fJunctionTraffic\x12\x0c\n\x04j_id\x18\x01 \x02(\r\x12\x0f\n\x07traffic\x18\x02 \x02(\x05*\'\n\x06Scheme\x12\x0b\n\x07WEBSTER\x10\x01\x12\x07\n\x03\x44QN\x10\x02\x12\x07\n\x03PPO\x10\x03*G\n\x0bVehicleType\x12\x07\n\x03\x43\x41R\x10\x01\x12\x07\n\x03\x42US\x10\x02\x12\t\n\x05TRUCK\x10\x03\x12\x0e\n\nMOTORCYCLE\x10\x04\x12\x0b\n\x07\x42ICYCLE\x10\x05*=\n\rDirectionMask\x12\x0c\n\x08Straight\x10\x01\x12\x08\n\x04Left\x10\x02\x12\t\n\x05Right\x10\x04\x12\t\n\x05UTurn\x10\x08')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_SCHEME']._serialized_start=1489
  _globals['_SCHEME']._serialized_end=1528
  _globals['_VEHICLETYPE']._serialized_start=1530
  _globals['_VEHICLETYPE']._serialized_end=1601
  _globals['_DIRECTIONMASK']._serialized_start=1603
  _globals['_DIRECTIONMASK']._serialized_end=1664
  _globals['_AICOMMANDINFO']._serialized_start=44
  _globals['_AICOMMANDINFO']._serialized_end=115
  _globals['_JUNCTION']._serialized_start=118
  _globals['_JUNCTION']._serialized_end=347
  _globals['_LANESONDIRECTION']._serialized_start=349
  _globals['_LANESONDIRECTION']._serialized_end=382
  _globals['_LIGHTSONCONFIG']._serialized_start=384
  _globals['_LIGHTSONCONFIG']._serialized_end=494
  _globals['_LIGHTPHASECONFIG']._serialized_start=496
  _globals['_LIGHTPHASECONFIG']._serialized_end=585
  _globals['_SIGNAL']._serialized_start=588
  _globals['_SIGNAL']._serialized_end=781
  _globals['_EDGE']._serialized_start=783
  _globals['_EDGE']._serialized_end=818
  _globals['_LANECONFIG']._serialized_start=820
  _globals['_LANECONFIG']._serialized_end=912
  _globals['_VEHICLECONFIG']._serialized_start=915
  _globals['_VEHICLECONFIG']._serialized_end=1043
  _globals['_VEHICLE']._serialized_start=1046
  _globals['_VEHICLE']._serialized_end=1292
  _globals['_POSITIONINLANE']._serialized_start=1294
  _globals['_POSITIONINLANE']._serialized_end=1332
  _globals['_JUNCTIONDELAY']._serialized_start=1334
  _globals['_JUNCTIONDELAY']._serialized_end=1378
  _globals['_JUNCTIONQUEUELENGTH']._serialized_start=1380
  _globals['_JUNCTIONQUEUELENGTH']._serialized_end=1437
  _globals['_JUNCTIONTRAFFIC']._serialized_start=1439
  _globals['_JUNCTIONTRAFFIC']._serialized_end=1487
# @@protoc_insertion_point(module_scope)
