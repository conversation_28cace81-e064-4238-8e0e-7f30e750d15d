# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: game2arena.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import custom_pb2 as custom__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10game2arena.proto\x12 arena.intelligent_traffic_lights\x1a\x0c\x63ustom.proto\"\x87\x02\n\x0cStepFrameReq\x12\x0f\n\x07game_id\x18\x01 \x01(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x01(\x05\x12\x41\n\x0b\x66rame_state\x18\x03 \x01(\x0b\x32,.arena.intelligent_traffic_lights.FrameState\x12\x12\n\nterminated\x18\x04 \x01(\x05\x12\x11\n\ttruncated\x18\x05 \x01(\x05\x12=\n\tgame_info\x18\x06 \x01(\x0b\x32*.arena.intelligent_traffic_lights.GameInfo\x12\x13\n\x0bresult_code\x18\x07 \x01(\x05\x12\x16\n\x0eresult_message\x18\x08 \x01(\t\"\x80\x01\n\x0cStepFrameRsp\x12\x0f\n\x07game_id\x18\x01 \x01(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x01(\x05\x12:\n\x07\x63ommand\x18\x03 \x01(\x0b\x32).arena.intelligent_traffic_lights.Command\x12\x11\n\tstop_game\x18\x04 \x01(\x05\x42\x63Zagit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/intelligent_traffic_lightsb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'game2arena_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Zagit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/intelligent_traffic_lights'
  _globals['_STEPFRAMEREQ']._serialized_start=69
  _globals['_STEPFRAMEREQ']._serialized_end=332
  _globals['_STEPFRAMERSP']._serialized_start=335
  _globals['_STEPFRAMERSP']._serialized_end=463
# @@protoc_insertion_point(module_scope)
