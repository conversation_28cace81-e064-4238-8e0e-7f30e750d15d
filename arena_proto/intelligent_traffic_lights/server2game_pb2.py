# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: server2game.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11server2game.proto\x12\x1aintelligent_traffic_lights\x1a\x0c\x63ommon.proto\"\xc4\x02\n\rFightStartReq\x12\x12\n\nlevel_name\x18\x01 \x02(\t\x12\x37\n\tjunctions\x18\x02 \x03(\x0b\x32$.intelligent_traffic_lights.Junction\x12\x33\n\x07signals\x18\x03 \x03(\x0b\x32\".intelligent_traffic_lights.Signal\x12/\n\x05\x65\x64ges\x18\x04 \x03(\x0b\x32 .intelligent_traffic_lights.Edge\x12<\n\x0clane_configs\x18\x05 \x03(\x0b\x32&.intelligent_traffic_lights.LaneConfig\x12\x42\n\x0fvehicle_configs\x18\x06 \x03(\x0b\x32).intelligent_traffic_lights.VehicleConfig\"\x0f\n\rFightStartRsp\"e\n\x0cStepFrameReq\x12\x41\n\x0b\x66rame_state\x18\x01 \x02(\x0b\x32,.intelligent_traffic_lights.FrameStateNature\x12\x12\n\nterminated\x18\x02 \x02(\x05\"^\n\x0cStepFrameRsp\x12;\n\x08\x63md_list\x18\x01 \x03(\x0b\x32).intelligent_traffic_lights.AICommandInfo\x12\x11\n\tgame_over\x18\x02 \x01(\x05\"\x0b\n\tFinishReq\"\x0b\n\tFinishRsp\"o\n\x10\x46rameStateNature\x12\x10\n\x08\x66rame_no\x18\x01 \x02(\r\x12\x12\n\nframe_time\x18\x02 \x02(\r\x12\x35\n\x08vehicles\x18\x03 \x03(\x0b\x32#.intelligent_traffic_lights.Vehicle\"\xd1\x01\n\tGameFrame\x12\x0f\n\x07game_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\r\x12\x41\n\x0b\x66rame_state\x18\x03 \x02(\x0b\x32,.intelligent_traffic_lights.FrameStateNature\x12\x12\n\nterminated\x18\x04 \x02(\x08\x12\x11\n\ttruncated\x18\x05 \x02(\x08\x12\x37\n\tgame_info\x18\x06 \x02(\x0b\x32$.intelligent_traffic_lights.GameInfo\"\xab\x02\n\x08GameInfo\x12\x37\n\tjunctions\x18\x01 \x03(\x0b\x32$.intelligent_traffic_lights.Junction\x12\x33\n\x07signals\x18\x02 \x03(\x0b\x32\".intelligent_traffic_lights.Signal\x12/\n\x05\x65\x64ges\x18\x03 \x03(\x0b\x32 .intelligent_traffic_lights.Edge\x12<\n\x0clane_configs\x18\x04 \x03(\x0b\x32&.intelligent_traffic_lights.LaneConfig\x12\x42\n\x0fvehicle_configs\x18\x05 \x03(\x0b\x32).intelligent_traffic_lights.VehicleConfig')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'server2game_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FIGHTSTARTREQ']._serialized_start=64
  _globals['_FIGHTSTARTREQ']._serialized_end=388
  _globals['_FIGHTSTARTRSP']._serialized_start=390
  _globals['_FIGHTSTARTRSP']._serialized_end=405
  _globals['_STEPFRAMEREQ']._serialized_start=407
  _globals['_STEPFRAMEREQ']._serialized_end=508
  _globals['_STEPFRAMERSP']._serialized_start=510
  _globals['_STEPFRAMERSP']._serialized_end=604
  _globals['_FINISHREQ']._serialized_start=606
  _globals['_FINISHREQ']._serialized_end=617
  _globals['_FINISHRSP']._serialized_start=619
  _globals['_FINISHRSP']._serialized_end=630
  _globals['_FRAMESTATENATURE']._serialized_start=632
  _globals['_FRAMESTATENATURE']._serialized_end=743
  _globals['_GAMEFRAME']._serialized_start=746
  _globals['_GAMEFRAME']._serialized_end=955
  _globals['_GAMEINFO']._serialized_start=958
  _globals['_GAMEINFO']._serialized_end=1257
# @@protoc_insertion_point(module_scope)
