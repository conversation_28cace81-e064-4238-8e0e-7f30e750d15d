# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_state.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import hero_pb2 as hero__pb2
from . import scene_pb2 as scene__pb2
from . import sgame_action_pb2 as sgame__action__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11sgame_state.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\nhero.proto\x1a\x0bscene.proto\x1a\x12sgame_action.proto\"\xa9\x03\n\nFrameState\x12\x0f\n\x07\x66rameNo\x18\x01 \x02(\x05\x12+\n\x0bhero_states\x18\x02 \x03(\x0b\x32\x16.sgame_state.HeroState\x12+\n\nnpc_states\x18\x03 \x03(\x0b\x32\x17.sgame_state.ActorState\x12$\n\x07\x62ullets\x18\x04 \x03(\x0b\x32\x13.sgame_state.Bullet\x12 \n\x05\x63\x61kes\x18\x05 \x03(\x0b\x32\x11.sgame_state.Cake\x12+\n\x0b\x65quip_infos\x18\x06 \x03(\x0b\x32\x16.sgame_state.EquipInfo\x12:\n\x13monster_spawn_infos\x18\x08 \x03(\x0b\x32\x1d.sgame_state.MonsterSpawnInfo\x12.\n\x0c\x66rame_action\x18\n \x01(\x0b\x32\x18.sgame_state.FrameAction\x12\x11\n\tmap_state\x18\x0b \x01(\x08\x12<\n\x13\x63ollaboration_state\x18\x1e \x01(\x0b\x32\x1f.sgame_state.CollaborationState\"\x96\x01\n\x17\x43ollaborationMicroState\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08receiver\x18\x02 \x02(\x05\x12\x11\n\tend_frame\x18\x03 \x02(\x05\x12+\n\x0ftarget_position\x18\x04 \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x19\n\x11target_runtime_id\x18\x05 \x01(\x05\"h\n\x17\x43ollaborationMacroState\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08receiver\x18\x02 \x02(\x05\x12\x11\n\tend_frame\x18\x03 \x02(\x05\x12\x18\n\x10target_bot_state\x18\x04 \x01(\x05\"9\n\x15\x43ollaborationLastSend\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\"\xb4\x02\n\x12\x43ollaborationState\x12\x14\n\x0cmax_duration\x18\x01 \x02(\x05\x12\x10\n\x08micro_cd\x18\x02 \x02(\x05\x12\x10\n\x08macro_cd\x18\x03 \x02(\x05\x12\x39\n\x0bmicro_state\x18\x04 \x03(\x0b\x32$.sgame_state.CollaborationMicroState\x12\x39\n\x0bmacro_state\x18\x05 \x03(\x0b\x32$.sgame_state.CollaborationMacroState\x12\x36\n\nmicro_last\x18\x06 \x03(\x0b\x32\".sgame_state.CollaborationLastSend\x12\x36\n\nmacro_last\x18\x07 \x03(\x0b\x32\".sgame_state.CollaborationLastSend\"5\n\nFrameSlice\x12\'\n\x06\x66rames\x18\x01 \x03(\x0b\x32\x17.sgame_state.FrameState\"G\n\x0c\x43\x61mpStatInfo\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\x05\x12\r\n\x05score\x18\x02 \x02(\x05\x12\r\n\x05money\x18\x03 \x02(\x05\x12\x0b\n\x03\x65xp\x18\x04 \x02(\x05\"\xcb\x02\n\x0cHeroStatInfo\x12\x12\n\nruntime_id\x18\x01 \x02(\x05\x12\r\n\x05score\x18\x02 \x02(\x05\x12\r\n\x05money\x18\x03 \x02(\x05\x12\x1d\n\x15totalHurtToHeroOrigin\x18\x04 \x01(\x05\x12\x1f\n\x17totalBeHurtByHeroOrigin\x18\x05 \x01(\x05\x12\x17\n\x0f\x64\x65stroyTowerCnt\x18\x06 \x01(\x05\x12\x16\n\x0ekillSoidierCnt\x18\x07 \x01(\x05\x12\x16\n\x0eKillMonsterCnt\x18\x08 \x01(\x05\x12\x1b\n\x13KillLittleDragonCnt\x18\t \x01(\x05\x12\x1d\n\x15Hero1KillBigDragonCnt\x18\n \x01(\x05\x12\x17\n\x0fSelfKillDarkCnt\x18\x0b \x01(\x05\x12\x14\n\x0cTotalRedBuff\x18\x0c \x01(\x05\x12\x15\n\rTotalBlueBuff\x18\r \x01(\x05\"r\n\nCloseState\x12\x31\n\x0e\x63\x61mp_stat_info\x18\x01 \x03(\x0b\x32\x19.sgame_state.CampStatInfo\x12\x31\n\x0ehero_stat_info\x18\x02 \x03(\x0b\x32\x19.sgame_state.HeroStatInfo\"G\n\tEquipInfo\x12\x10\n\x08\x65quip_id\x18\x01 \x01(\x05\x12\x13\n\x0b\x65quip_price\x18\x02 \x01(\x05\x12\x13\n\x0b\x65quip_atoms\x18\x03 \x03(\x05\"c\n\x10MonsterSpawnInfo\x12\x12\n\nmonster_id\x18\x01 \x01(\x05\x12\x13\n\x0bspawn_timer\x18\x02 \x01(\x05\x12\x12\n\nborn_pos_x\x18\x03 \x01(\x05\x12\x12\n\nborn_pos_z\x18\x04 \x01(\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_state_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FRAMESTATE']._serialized_start=94
  _globals['_FRAMESTATE']._serialized_end=519
  _globals['_COLLABORATIONMICROSTATE']._serialized_start=522
  _globals['_COLLABORATIONMICROSTATE']._serialized_end=672
  _globals['_COLLABORATIONMACROSTATE']._serialized_start=674
  _globals['_COLLABORATIONMACROSTATE']._serialized_end=778
  _globals['_COLLABORATIONLASTSEND']._serialized_start=780
  _globals['_COLLABORATIONLASTSEND']._serialized_end=837
  _globals['_COLLABORATIONSTATE']._serialized_start=840
  _globals['_COLLABORATIONSTATE']._serialized_end=1148
  _globals['_FRAMESLICE']._serialized_start=1150
  _globals['_FRAMESLICE']._serialized_end=1203
  _globals['_CAMPSTATINFO']._serialized_start=1205
  _globals['_CAMPSTATINFO']._serialized_end=1276
  _globals['_HEROSTATINFO']._serialized_start=1279
  _globals['_HEROSTATINFO']._serialized_end=1610
  _globals['_CLOSESTATE']._serialized_start=1612
  _globals['_CLOSESTATE']._serialized_end=1726
  _globals['_EQUIPINFO']._serialized_start=1728
  _globals['_EQUIPINFO']._serialized_end=1799
  _globals['_MONSTERSPAWNINFO']._serialized_start=1801
  _globals['_MONSTERSPAWNINFO']._serialized_end=1900
# @@protoc_insertion_point(module_scope)
