# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import sgame_state_pb2 as sgame__state__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x0bsgame_state\x1a\x11sgame_state.proto\"\xed\x02\n\x0bObservation\x12,\n\x05\x63\x61mps\x18\x01 \x03(\x0b\x32\x1d.sgame_state.Observation.Camp\x1a\x19\n\tFloatList\x12\x0c\n\x04mask\x18\x01 \x03(\x02\x1a\x63\n\x04\x43\x61mp\x12,\n\x0b\x66rame_state\x18\x01 \x02(\x0b\x32\x17.sgame_state.FrameState\x12-\n\x06heroes\x18\x02 \x03(\x0b\x32\x1d.sgame_state.Observation.Hero\x1a\xaf\x01\n\x04Hero\x12\x0c\n\x04\x63\x61mp\x18\x01 \x02(\x05\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12\x11\n\tconfig_id\x18\x03 \x02(\x05\x12\x0f\n\x07\x66\x65\x61ture\x18\x04 \x03(\x02\x12\x14\n\x0clegal_action\x18\x05 \x03(\x02\x12;\n\x0fsub_action_mask\x18\x06 \x03(\x0b\x32\".sgame_state.Observation.FloatList\x12\x0e\n\x06reward\x18\x07 \x03(\x02\"j\n\x05State\x12\x33\n\x12global_frame_state\x18\x01 \x01(\x0b\x32\x17.sgame_state.FrameState\x12,\n\x0b\x63lose_state\x18\x02 \x01(\x0b\x32\x17.sgame_state.CloseState\"`\n\tScoreInfo\x12\x36\n\x0bplayer_list\x18\x01 \x03(\x0b\x32!.sgame_state.ScoreInfo.EachPlayer\x1a\x1b\n\nEachPlayer\x12\r\n\x05score\x18\x01 \x03(\x02\"\xe5\x01\n\x06\x41\x63tion\x12\x33\n\x0bplayer_list\x18\x01 \x03(\x0b\x32\x1e.sgame_state.Action.EachPlayer\x12:\n\x12\x63ollaboration_list\x18\x02 \x03(\x0b\x32\x1e.sgame_state.Action.EachPlayer\x1a+\n\tFloatList\x12\x11\n\tconfig_id\x18\x01 \x02(\x05\x12\x0b\n\x03\x61\x63t\x18\x02 \x03(\x02\x1a=\n\nEachPlayer\x12/\n\x08\x61\x63t_list\x18\x01 \x03(\x0b\x32\x1d.sgame_state.Action.FloatList\"\xd6\x01\n\x07\x43ommand\x12\x34\n\x0bplayer_list\x18\x01 \x03(\x0b\x32\x1f.sgame_state.Command.EachPlayer\x12;\n\x12\x63ollaboration_list\x18\x02 \x03(\x0b\x32\x1f.sgame_state.Command.EachPlayer\x1a\x18\n\tFloatList\x12\x0b\n\x03\x63md\x18\x01 \x03(\x02\x1a>\n\nEachPlayer\x12\x30\n\x08\x63md_list\x18\x01 \x03(\x0b\x32\x1e.sgame_state.Command.FloatList')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_OBSERVATION']._serialized_start=49
  _globals['_OBSERVATION']._serialized_end=414
  _globals['_OBSERVATION_FLOATLIST']._serialized_start=110
  _globals['_OBSERVATION_FLOATLIST']._serialized_end=135
  _globals['_OBSERVATION_CAMP']._serialized_start=137
  _globals['_OBSERVATION_CAMP']._serialized_end=236
  _globals['_OBSERVATION_HERO']._serialized_start=239
  _globals['_OBSERVATION_HERO']._serialized_end=414
  _globals['_STATE']._serialized_start=416
  _globals['_STATE']._serialized_end=522
  _globals['_SCOREINFO']._serialized_start=524
  _globals['_SCOREINFO']._serialized_end=620
  _globals['_SCOREINFO_EACHPLAYER']._serialized_start=593
  _globals['_SCOREINFO_EACHPLAYER']._serialized_end=620
  _globals['_ACTION']._serialized_start=623
  _globals['_ACTION']._serialized_end=852
  _globals['_ACTION_FLOATLIST']._serialized_start=746
  _globals['_ACTION_FLOATLIST']._serialized_end=789
  _globals['_ACTION_EACHPLAYER']._serialized_start=791
  _globals['_ACTION_EACHPLAYER']._serialized_end=852
  _globals['_COMMAND']._serialized_start=855
  _globals['_COMMAND']._serialized_end=1069
  _globals['_COMMAND_FLOATLIST']._serialized_start=981
  _globals['_COMMAND_FLOATLIST']._serialized_end=1005
  _globals['_COMMAND_EACHPLAYER']._serialized_start=1007
  _globals['_COMMAND_EACHPLAYER']._serialized_end=1069
# @@protoc_insertion_point(module_scope)
