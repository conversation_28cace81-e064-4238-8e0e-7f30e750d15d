# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: common.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ommon.proto\x12\x0bsgame_state\"\x1d\n\x05VInt2\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01y\x18\x02 \x02(\x05\"(\n\x05VInt3\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01y\x18\x02 \x02(\x05\x12\t\n\x01z\x18\x03 \x02(\x05\"d\n\x05TInt3\x12\x1d\n\x01x\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x1d\n\x01y\x18\x02 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x1d\n\x01z\x18\x03 \x02(\x0b\x32\x12.sgame_state.VInt3\"|\n\x0b\x42oxCollider\x12$\n\x08location\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\x12 \n\x04size\x18\x02 \x02(\x0b\x32\x12.sgame_state.VInt3\x12%\n\ttransform\x18\x03 \x02(\x0b\x32\x12.sgame_state.TInt3\"F\n\x0eSphereCollider\x12$\n\x08location\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\x0e\n\x06radius\x18\x02 \x02(\x05\"\x8f\x03\n\nActorValue\x12\x0f\n\x07phy_atk\x18\x01 \x02(\x05\x12\x0f\n\x07phy_def\x18\x02 \x02(\x05\x12\x0f\n\x07mgc_atk\x18\x03 \x02(\x05\x12\x0f\n\x07mgc_def\x18\x04 \x02(\x05\x12\x0f\n\x07mov_spd\x18\x05 \x02(\x05\x12\x0f\n\x07\x61tk_spd\x18\x06 \x02(\x05\x12\n\n\x02\x65p\x18\x07 \x02(\x05\x12\x0e\n\x06max_ep\x18\x08 \x02(\x05\x12\x12\n\nhp_recover\x18\t \x02(\x05\x12\x12\n\nep_recover\x18\n \x02(\x05\x12\x16\n\x0ephy_armor_hurt\x18\x0b \x02(\x05\x12\x16\n\x0emgc_armor_hurt\x18\x0c \x02(\x05\x12\x11\n\tcrit_rate\x18\r \x02(\x05\x12\x11\n\tcrit_effe\x18\x0e \x02(\x05\x12\x10\n\x08phy_vamp\x18\x0f \x02(\x05\x12\x10\n\x08mgc_vamp\x18\x10 \x02(\x05\x12\x11\n\tcd_reduce\x18\x11 \x02(\x05\x12\x13\n\x0b\x63trl_reduce\x18\x12 \x02(\x05\x12\x19\n\x11monster_endurance\x18\x13 \x01(\x05\x12\x1a\n\x12organ_conti_attack\x18\x14 \x01(\x05\"I\n\x13\x41\x63torBuffSkillState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05times\x18\x02 \x02(\x05\x12\x11\n\tstartTime\x18\x03 \x02(\x04\"M\n\x12\x41\x63torBuffMarkState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05layer\x18\x02 \x02(\x05\x12\x16\n\x0eorigin_actorId\x18\x03 \x02(\x05\"|\n\x0e\x41\x63torBuffState\x12\x35\n\x0b\x62uff_skills\x18\x01 \x03(\x0b\x32 .sgame_state.ActorBuffSkillState\x12\x33\n\nbuff_marks\x18\x02 \x03(\x0b\x32\x1f.sgame_state.ActorBuffMarkState\"\x8b\x05\n\nActorState\x12\x11\n\tconfig_id\x18\x01 \x02(\x05\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12*\n\nactor_type\x18\x03 \x02(\x0e\x32\x16.sgame_state.ActorType\x12+\n\x08sub_type\x18\x04 \x02(\x0e\x32\x19.sgame_state.ActorSubType\x12%\n\x04\x63\x61mp\x18\x05 \x02(\x0e\x32\x17.sgame_state.PLAYERCAMP\x12.\n\nbehav_mode\x18\x06 \x02(\x0e\x32\x1a.sgame_state.ObjBehaviMode\x12$\n\x08location\x18\x07 \x02(\x0b\x32\x12.sgame_state.VInt3\x12#\n\x07\x66orward\x18\x08 \x02(\x0b\x32\x12.sgame_state.VInt3\x12\n\n\x02hp\x18\t \x02(\x05\x12\x0e\n\x06max_hp\x18\n \x02(\x05\x12\'\n\x06values\x18\x0b \x01(\x0b\x32\x17.sgame_state.ActorValue\x12\x11\n\tabilities\x18\x0c \x03(\x08\x12\x14\n\x0c\x61ttack_range\x18\r \x02(\x05\x12\x15\n\rattack_target\x18\x0e \x02(\x05\x12\x13\n\x0bkill_income\x18\x0f \x01(\x05\x12\x33\n\x0fhit_target_info\x18\x10 \x03(\x0b\x32\x1a.sgame_state.HitTargetInfo\x12\x14\n\x0c\x63\x61mp_visible\x18\x11 \x03(\x08\x12\x12\n\nsight_area\x18\x12 \x01(\x05\x12/\n\nbuff_state\x18\x13 \x01(\x0b\x32\x1b.sgame_state.ActorBuffState\x12\x31\n\x0ehurt_hero_info\x18\x14 \x03(\x0b\x32\x19.sgame_state.HurtHeroInfo\"}\n\rHitTargetInfo\x12\x12\n\nhit_target\x18\x01 \x01(\x05\x12\x10\n\x08skill_id\x18\x02 \x01(\r\x12-\n\tslot_type\x18\x03 \x01(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x17\n\x0f\x63onti_hit_count\x18\x04 \x01(\x05\"1\n\x0cHurtHeroInfo\x12\x13\n\x0bhurt_target\x18\x01 \x01(\x05\x12\x0c\n\x04hurt\x18\x02 \x01(\r\"\x8b\x01\n\x0cTakeHurtInfo\x12\r\n\x05\x61tker\x18\x01 \x02(\x05\x12\x11\n\thurtValue\x18\x02 \x02(\x05\x12\x11\n\tskillSlot\x18\x03 \x02(\x05\x12\x34\n\nsourceType\x18\x04 \x02(\x0e\x32 .sgame_state.SKILL_USE_FROM_TYPE\x12\x10\n\x08sourceID\x18\x05 \x02(\x05*\xe0\x01\n\tActorType\x12\x0e\n\nACTOR_HERO\x10\x00\x12\x11\n\rACTOR_MONSTER\x10\x01\x12\x0f\n\x0b\x41\x43TOR_ORGAN\x10\x02\x12\r\n\tACTOR_EYE\x10\x03\x12\x0e\n\nACTOR_CALL\x10\x04\x12\x10\n\x0c\x41\x43TOR_BULLET\x10\x05\x12\x0f\n\x0b\x41\x43TOR_BLOCK\x10\x06\x12\x16\n\x12\x41\x43TOR_INTERACTITEM\x10\x07\x12\x10\n\x0c\x41\x43TOR_SHENFU\x10\x08\x12\x11\n\rACTOR_VEHICLE\x10\t\x12\r\n\tACTOR_ALL\x10\n\x12\x11\n\rACTOR_INVALID\x10\x0b*\xa8\x02\n\x0c\x41\x63torSubType\x12\x12\n\x0e\x41\x43TOR_SUB_NONE\x10\x00\x12\x15\n\x11\x41\x43TOR_SUB_SOLDIER\x10\x0b\x12\x14\n\x10\x41\x43TOR_SUB_BAOJUN\x10\x0c\x12\x13\n\x0f\x41\x43TOR_SUB_BARON\x10\r\x12\x14\n\x10\x41\x43TOR_SUB_BLUEBA\x10\x0e\x12\x13\n\x0f\x41\x43TOR_SUB_REDBA\x10\x0f\x12\x17\n\x13\x41\x43TOR_SUB_BIGDRAGON\x10\x10\x12\x1c\n\x18\x41\x43TOR_SUB_DARK_BIGDRAGON\x10\x11\x12\x13\n\x0f\x41\x43TOR_SUB_TOWER\x10\x15\x12\x18\n\x14\x41\x43TOR_SUB_TOWER_HIGH\x10\x16\x12\x1a\n\x16\x41\x43TOR_SUB_TOWER_SPRING\x10\x17\x12\x15\n\x11\x41\x43TOR_SUB_CRYSTAL\x10\x18*D\n\nPLAYERCAMP\x12\x12\n\x0ePLAYERCAMP_MID\x10\x00\x12\x10\n\x0cPLAYERCAMP_1\x10\x01\x12\x10\n\x0cPLAYERCAMP_2\x10\x02*\x95\x04\n\rObjBehaviMode\x12\x0e\n\nState_Idle\x10\x00\x12\x0e\n\nState_Dead\x10\x01\x12\x12\n\x0e\x44irection_Move\x10\x02\x12\x14\n\x10\x44\x65stination_Move\x10\x03\x12\x11\n\rNormal_Attack\x10\x04\x12\x0f\n\x0b\x41ttack_Move\x10\x05\x12\x0f\n\x0b\x41ttack_Path\x10\x06\x12\x0f\n\x0b\x41ttack_Lock\x10\x07\x12\x0e\n\nUseSkill_0\x10\x08\x12\x0e\n\nUseSkill_1\x10\t\x12\x0e\n\nUseSkill_2\x10\n\x12\x0e\n\nUseSkill_3\x10\x0b\x12\x10\n\x0cUseSkill_EX3\x10\x0c\x12\x0e\n\nUseSkill_4\x10\r\x12\x0e\n\nUseSkill_5\x10\x0e\x12\x0e\n\nUseSkill_6\x10\x0f\x12\x0e\n\nUseSkill_7\x10\x10\x12\x0e\n\nUseSkill_9\x10\x11\x12\x0f\n\x0bUseSkill_10\x10\x12\x12\x0f\n\x0bUseSkill_11\x10\x13\x12\x0f\n\x0bUseSkill_12\x10\x14\x12\x0f\n\x0bUseSkill_13\x10\x15\x12\x0f\n\x0bUseSkill_14\x10\x16\x12\x0e\n\nState_Auto\x10\x17\x12\x12\n\x0eState_GameOver\x10\x18\x12\x16\n\x12State_OutOfControl\x10\x19\x12\x0e\n\nState_Born\x10\x1a\x12\x10\n\x0cState_Revive\x10\x1b\x12\x0f\n\x0bState_Dying\x10\x1c\x12\x0e\n\nState_Null\x10\x1d*\xd6\x05\n\x0eObjAbilityType\x12\x18\n\x14ObjAbility_NoControl\x10\x00\x12\x15\n\x11ObjAbility_NoMove\x10\x01\x12\x16\n\x12ObjAbility_NoSkill\x10\x02\x12\x1d\n\x19ObjAbility_ImmuneNegative\x10\x03\x12\x1c\n\x18ObjAbility_ImmuneControl\x10\x04\x12\x1b\n\x17ObjAbility_NoMoveRotate\x10\x05\x12\x19\n\x15ObjAbility_ImmuneCrit\x10\x06\x12\x18\n\x14ObjAbility_Blindness\x10\x07\x12\x1a\n\x16ObjAbility_MoveProtect\x10\x08\x12\x1e\n\x1aObjAbility_NoRecoverEnergy\x10\t\x12\x15\n\x11ObjAbility_Freeze\x10\n\x12\x1a\n\x16ObjAbility_DeadControl\x10\x0b\x12#\n\x1fObjAbility_NoCollisionDetection\x10\x0c\x12\x1b\n\x17ObjAbility_NoJointSkill\x10\r\x12\x18\n\x14ObjAbility_AbortMove\x10\x0e\x12\x1b\n\x17ObjAbility_ForbidSelect\x10\x0f\x12\x16\n\x12ObjAbility_Renewal\x10\x10\x12\x15\n\x11ObjAbility_Sprint\x10\x11\x12!\n\x1dObjAbility_NoMoveButCanRatate\x10\x12\x12%\n!ObjAbility_ForbidSelectBySkillOrg\x10\x13\x12;\n7ObjAbility_ImmunePositiveAndPersistFromOtherOriginators\x10\x14\x12\x18\n\x14ObjAbility_Repressed\x10\x15\x12 \n\x1cObjAbility_ImmuneDeMoveSpeed\x10\x16\x12\x12\n\x0eObjAbility_Max\x10\x17*\xd0\x02\n\rSkillSlotType\x12\x10\n\x0cSLOT_SKILL_0\x10\x00\x12\x10\n\x0cSLOT_SKILL_1\x10\x01\x12\x10\n\x0cSLOT_SKILL_2\x10\x02\x12\x10\n\x0cSLOT_SKILL_3\x10\x03\x12\x12\n\x0eSLOT_SKILL_EX3\x10\x04\x12\x10\n\x0cSLOT_SKILL_4\x10\x05\x12\x10\n\x0cSLOT_SKILL_5\x10\x06\x12\x10\n\x0cSLOT_SKILL_6\x10\x07\x12\x10\n\x0cSLOT_SKILL_7\x10\x08\x12\x10\n\x0cSLOT_SKILL_9\x10\t\x12\x11\n\rSLOT_SKILL_10\x10\n\x12\x11\n\rSLOT_SKILL_11\x10\x0b\x12\x11\n\rSLOT_SKILL_12\x10\x0c\x12\x11\n\rSLOT_SKILL_13\x10\r\x12\x11\n\rSLOT_SKILL_14\x10\x0e\x12\x14\n\x10SLOT_SKILL_COUNT\x10\x0f\x12\x14\n\x10SLOT_SKILL_VALID\x10\x10*\xc6\x02\n\x0eSkillAbortType\x12\x10\n\x0cTYPE_SKILL_0\x10\x00\x12\x10\n\x0cTYPE_SKILL_1\x10\x01\x12\x10\n\x0cTYPE_SKILL_2\x10\x02\x12\x10\n\x0cTYPE_SKILL_3\x10\x03\x12\x12\n\x0eTYPE_SKILL_EX3\x10\x04\x12\x10\n\x0cTYPE_SKILL_4\x10\x05\x12\x10\n\x0cTYPE_SKILL_5\x10\x06\x12\x10\n\x0cTYPE_SKILL_6\x10\x07\x12\x10\n\x0cTYPE_SKILL_7\x10\x08\x12\x10\n\x0cTYPE_SKILL_9\x10\t\x12\x11\n\rTYPE_SKILL_10\x10\n\x12\x11\n\rTYPE_SKILL_11\x10\x0b\x12\x14\n\x10TYPE_SKILL_COUNT\x10\x0c\x12\x14\n\x10TYPE_SKILL_VALID\x10\r\x12\r\n\tTYPE_MOVE\x10\x0e\x12\x0f\n\x0bTYPE_DAMAGE\x10\x0f\x12\x0c\n\x08TYPE_MAX\x10\x10*\xbd\x01\n\x13SKILL_USE_FROM_TYPE\x12\x1d\n\x19SKILL_USE_FROM_TYPE_SKILL\x10\x00\x12\x1d\n\x19SKILL_USE_FROM_TYPE_EQUIP\x10\x01\x12#\n\x1fSKILL_USE_FROM_TYPE_AREATRIGGER\x10\x02\x12$\n SKILL_USE_FROM_TYPE_PASSIVESKILL\x10\x03\x12\x1d\n\x19SKILL_USE_FROM_TYPE_COUNT\x10\x04*\xd2\x01\n\x0bProtectType\x12\x10\n\x0cPROTECT_NONE\x10\x00\x12\x14\n\x10PROTECT_PHYSHURT\x10\x01\x12\x15\n\x11PROTECT_MAGICHURT\x10\x02\x12\x0f\n\x0bPROTECT_ALL\x10\x03\x12 \n\x1cPROTECT_ALL_INCLUDE_REALHURT\x10\x04\x12\x13\n\x0fPROTECT_CONVERT\x10\x05\x12\x12\n\x0ePROTECT_EFFECT\x10\x06\x12\x14\n\x10PROTECT_PROPERTY\x10\x07\x12\x12\n\x0ePROTECT_ENERGY\x10\x08')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ACTORTYPE']._serialized_start=2059
  _globals['_ACTORTYPE']._serialized_end=2283
  _globals['_ACTORSUBTYPE']._serialized_start=2286
  _globals['_ACTORSUBTYPE']._serialized_end=2582
  _globals['_PLAYERCAMP']._serialized_start=2584
  _globals['_PLAYERCAMP']._serialized_end=2652
  _globals['_OBJBEHAVIMODE']._serialized_start=2655
  _globals['_OBJBEHAVIMODE']._serialized_end=3188
  _globals['_OBJABILITYTYPE']._serialized_start=3191
  _globals['_OBJABILITYTYPE']._serialized_end=3917
  _globals['_SKILLSLOTTYPE']._serialized_start=3920
  _globals['_SKILLSLOTTYPE']._serialized_end=4256
  _globals['_SKILLABORTTYPE']._serialized_start=4259
  _globals['_SKILLABORTTYPE']._serialized_end=4585
  _globals['_SKILL_USE_FROM_TYPE']._serialized_start=4588
  _globals['_SKILL_USE_FROM_TYPE']._serialized_end=4777
  _globals['_PROTECTTYPE']._serialized_start=4780
  _globals['_PROTECTTYPE']._serialized_end=4990
  _globals['_VINT2']._serialized_start=29
  _globals['_VINT2']._serialized_end=58
  _globals['_VINT3']._serialized_start=60
  _globals['_VINT3']._serialized_end=100
  _globals['_TINT3']._serialized_start=102
  _globals['_TINT3']._serialized_end=202
  _globals['_BOXCOLLIDER']._serialized_start=204
  _globals['_BOXCOLLIDER']._serialized_end=328
  _globals['_SPHERECOLLIDER']._serialized_start=330
  _globals['_SPHERECOLLIDER']._serialized_end=400
  _globals['_ACTORVALUE']._serialized_start=403
  _globals['_ACTORVALUE']._serialized_end=802
  _globals['_ACTORBUFFSKILLSTATE']._serialized_start=804
  _globals['_ACTORBUFFSKILLSTATE']._serialized_end=877
  _globals['_ACTORBUFFMARKSTATE']._serialized_start=879
  _globals['_ACTORBUFFMARKSTATE']._serialized_end=956
  _globals['_ACTORBUFFSTATE']._serialized_start=958
  _globals['_ACTORBUFFSTATE']._serialized_end=1082
  _globals['_ACTORSTATE']._serialized_start=1085
  _globals['_ACTORSTATE']._serialized_end=1736
  _globals['_HITTARGETINFO']._serialized_start=1738
  _globals['_HITTARGETINFO']._serialized_end=1863
  _globals['_HURTHEROINFO']._serialized_start=1865
  _globals['_HURTHEROINFO']._serialized_end=1914
  _globals['_TAKEHURTINFO']._serialized_start=1917
  _globals['_TAKEHURTINFO']._serialized_end=2056
# @@protoc_insertion_point(module_scope)
