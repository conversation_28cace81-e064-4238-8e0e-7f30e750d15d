# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: command.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rcommand.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\"0\n\tMoveToPos\x12#\n\x07\x64\x65stPos\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\"\x19\n\x07MoveDir\x12\x0e\n\x06\x64\x65gree\x18\x01 \x02(\x05\".\n\x0c\x41ttackCommon\x12\x0f\n\x07\x61\x63torID\x18\x01 \x02(\r\x12\r\n\x05start\x18\x02 \x02(\x05\"2\n\x0b\x41ttackToPos\x12#\n\x07\x64\x65stPos\x18\x01 \x02(\x0b\x32\x12.sgame_state.VInt3\"\x1e\n\x0b\x41ttackActor\x12\x0f\n\x07\x61\x63torID\x18\x01 \x02(\r\"Z\n\x08ObjSkill\x12\x0f\n\x07skillID\x18\x01 \x02(\r\x12\x0f\n\x07\x61\x63torID\x18\x02 \x02(\r\x12,\n\x08slotType\x18\x03 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\"j\n\x08\x44irSkill\x12\x0f\n\x07skillID\x18\x01 \x02(\r\x12\x0f\n\x07\x61\x63torID\x18\x02 \x02(\r\x12,\n\x08slotType\x18\x03 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\x0e\n\x06\x64\x65gree\x18\x04 \x02(\x05\"n\n\x08PosSkill\x12\x0f\n\x07skillID\x18\x01 \x02(\r\x12#\n\x07\x64\x65stPos\x18\x02 \x02(\x0b\x32\x12.sgame_state.VInt3\x12,\n\x08slotType\x18\x03 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\"I\n\nLearnSkill\x12,\n\x08slotType\x18\x01 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05level\x18\x02 \x02(\x05\"+\n\x08\x42uyEquip\x12\x0f\n\x07\x65quipId\x18\x01 \x02(\x05\x12\x0e\n\x06obj_id\x18\x02 \x01(\x05\"\x1f\n\tSellEquip\x12\x12\n\nequipIndex\x18\x01 \x02(\x05\"Z\n\x0b\x43hargeSkill\x12,\n\x08slotType\x18\x01 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05state\x18\x02 \x02(\r\x12\x0e\n\x06\x64\x65gree\x18\x03 \x02(\x05\"t\n\x08SetMicro\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08receiver\x18\x02 \x02(\x05\x12+\n\x0ftarget_position\x18\x03 \x01(\x0b\x32\x12.sgame_state.VInt3\x12\x19\n\x11target_runtime_id\x18\x04 \x01(\x05\".\n\nUnsetMicro\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08receiver\x18\x02 \x02(\x05\"c\n\x08SetMacro\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08receiver\x18\x02 \x02(\x05\x12\x35\n\x10target_bot_state\x18\x03 \x02(\x0e\x32\x1b.sgame_state.TargetBotState\".\n\nUnsetMacro\x12\x0e\n\x06sender\x18\x01 \x02(\x05\x12\x10\n\x08receiver\x18\x02 \x02(\x05\"\xf6\x05\n\x06\x43mdPkg\x12.\n\x0c\x63ommand_type\x18\x01 \x02(\x0e\x32\x18.sgame_state.CommandType\x12(\n\x08move_pos\x18\x02 \x01(\x0b\x32\x16.sgame_state.MoveToPos\x12&\n\x08move_dir\x18\x03 \x01(\x0b\x32\x14.sgame_state.MoveDir\x12\x30\n\rattack_common\x18\x04 \x01(\x0b\x32\x19.sgame_state.AttackCommon\x12.\n\x0c\x61ttack_topos\x18\x05 \x01(\x0b\x32\x18.sgame_state.AttackToPos\x12.\n\x0c\x61ttack_actor\x18\x06 \x01(\x0b\x32\x18.sgame_state.AttackActor\x12(\n\tobj_skill\x18\x07 \x01(\x0b\x32\x15.sgame_state.ObjSkill\x12(\n\tdir_skill\x18\x08 \x01(\x0b\x32\x15.sgame_state.DirSkill\x12(\n\tpos_skill\x18\t \x01(\x0b\x32\x15.sgame_state.PosSkill\x12,\n\x0blearn_skill\x18\n \x01(\x0b\x32\x17.sgame_state.LearnSkill\x12(\n\tbuy_equip\x18\x0b \x01(\x0b\x32\x15.sgame_state.BuyEquip\x12*\n\nsell_equip\x18\x0c \x01(\x0b\x32\x16.sgame_state.SellEquip\x12.\n\x0c\x63harge_skill\x18\r \x01(\x0b\x32\x18.sgame_state.ChargeSkill\x12$\n\x05micro\x18\x64 \x01(\x0b\x32\x15.sgame_state.SetMicro\x12,\n\x0bunset_micro\x18\x65 \x01(\x0b\x32\x17.sgame_state.UnsetMicro\x12$\n\x05macro\x18\x66 \x01(\x0b\x32\x15.sgame_state.SetMacro\x12,\n\x0bunset_macro\x18g \x01(\x0b\x32\x17.sgame_state.UnsetMacro*\x81\x04\n\x0b\x43ommandType\x12\x15\n\x11\x43OMMAND_TYPE_None\x10\x00\x12\x18\n\x14\x43OMMAND_TYPE_MovePos\x10\x01\x12\x18\n\x14\x43OMMAND_TYPE_MoveDir\x10\x02\x12\x19\n\x15\x43OMMAND_TYPE_MoveStop\x10\x03\x12\x1d\n\x19\x43OMMAND_TYPE_AttackCommon\x10\x04\x12\x1c\n\x18\x43OMMAND_TYPE_AttackToPos\x10\x05\x12\x1c\n\x18\x43OMMAND_TYPE_AttackActor\x10\x06\x12\x19\n\x15\x43OMMAND_TYPE_ObjSkill\x10\x07\x12\x19\n\x15\x43OMMAND_TYPE_DirSkill\x10\x08\x12\x19\n\x15\x43OMMAND_TYPE_PosSkill\x10\t\x12\x1b\n\x17\x43OMMAND_TYPE_LearnSkill\x10\n\x12\x19\n\x15\x43OMMAND_TYPE_BuyEquip\x10\x0b\x12\x1a\n\x16\x43OMMAND_TYPE_SellEquip\x10\x0c\x12\x1c\n\x18\x43OMMAND_TYPE_ChargeSkill\x10\r\x12\x19\n\x15\x43OMMAND_TYPE_SetMicro\x10\x64\x12\x1b\n\x17\x43OMMAND_TYPE_UnsetMicro\x10\x65\x12\x19\n\x15\x43OMMAND_TYPE_SetMacro\x10\x66\x12\x1b\n\x17\x43OMMAND_TYPE_UnsetMacro\x10g*7\n\x0eTargetBotState\x12\x0b\n\x07General\x10\x00\x12\x0b\n\x07Offense\x10\x01\x12\x0b\n\x07\x44\x65\x66\x65nse\x10\x02')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'command_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_COMMANDTYPE']._serialized_start=1887
  _globals['_COMMANDTYPE']._serialized_end=2400
  _globals['_TARGETBOTSTATE']._serialized_start=2402
  _globals['_TARGETBOTSTATE']._serialized_end=2457
  _globals['_MOVETOPOS']._serialized_start=44
  _globals['_MOVETOPOS']._serialized_end=92
  _globals['_MOVEDIR']._serialized_start=94
  _globals['_MOVEDIR']._serialized_end=119
  _globals['_ATTACKCOMMON']._serialized_start=121
  _globals['_ATTACKCOMMON']._serialized_end=167
  _globals['_ATTACKTOPOS']._serialized_start=169
  _globals['_ATTACKTOPOS']._serialized_end=219
  _globals['_ATTACKACTOR']._serialized_start=221
  _globals['_ATTACKACTOR']._serialized_end=251
  _globals['_OBJSKILL']._serialized_start=253
  _globals['_OBJSKILL']._serialized_end=343
  _globals['_DIRSKILL']._serialized_start=345
  _globals['_DIRSKILL']._serialized_end=451
  _globals['_POSSKILL']._serialized_start=453
  _globals['_POSSKILL']._serialized_end=563
  _globals['_LEARNSKILL']._serialized_start=565
  _globals['_LEARNSKILL']._serialized_end=638
  _globals['_BUYEQUIP']._serialized_start=640
  _globals['_BUYEQUIP']._serialized_end=683
  _globals['_SELLEQUIP']._serialized_start=685
  _globals['_SELLEQUIP']._serialized_end=716
  _globals['_CHARGESKILL']._serialized_start=718
  _globals['_CHARGESKILL']._serialized_end=808
  _globals['_SETMICRO']._serialized_start=810
  _globals['_SETMICRO']._serialized_end=926
  _globals['_UNSETMICRO']._serialized_start=928
  _globals['_UNSETMICRO']._serialized_end=974
  _globals['_SETMACRO']._serialized_start=976
  _globals['_SETMACRO']._serialized_end=1075
  _globals['_UNSETMACRO']._serialized_start=1077
  _globals['_UNSETMACRO']._serialized_end=1123
  _globals['_CMDPKG']._serialized_start=1126
  _globals['_CMDPKG']._serialized_end=1884
# @@protoc_insertion_point(module_scope)
