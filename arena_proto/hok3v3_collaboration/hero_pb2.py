# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hero.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import command_pb2 as command__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nhero.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\rcommand.proto\"\x89\x02\n\x0eSkillSlotState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12-\n\tslot_type\x18\x02 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05level\x18\x03 \x02(\x05\x12\x0e\n\x06usable\x18\x04 \x02(\x08\x12\x10\n\x08\x63ooldown\x18\x05 \x02(\x05\x12\x14\n\x0c\x63ooldown_max\x18\x06 \x02(\x05\x12\x11\n\tusedTimes\x18\x07 \x01(\x05\x12\x14\n\x0chitHeroTimes\x18\x08 \x01(\x05\x12\x17\n\x0fsuccUsedInFrame\x18\t \x01(\x05\x12\x14\n\x0cnextConfigID\x18\n \x01(\x05\x12\x17\n\x0f\x63omboEffectTime\x18\x0b \x01(\x05\">\n\nSkillState\x12\x30\n\x0bslot_states\x18\x01 \x03(\x0b\x32\x1b.sgame_state.SkillSlotState\"m\n\x0e\x42uffSkillState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05times\x18\x02 \x02(\x05\x12\x11\n\tstartTime\x18\x03 \x02(\x04\x12\x12\n\neffectType\x18\x04 \x01(\x05\x12\x13\n\x0b\x63urDuration\x18\x07 \x01(\x05\"Z\n\rBuffMarkState\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\r\n\x05layer\x18\x02 \x02(\x05\x12\x16\n\x0eorigin_actorId\x18\x03 \x02(\x05\x12\x10\n\x08leftTime\x18\x04 \x01(\x05\"m\n\tBuffState\x12\x30\n\x0b\x62uff_skills\x18\x01 \x03(\x0b\x32\x1b.sgame_state.BuffSkillState\x12.\n\nbuff_marks\x18\x02 \x03(\x0b\x32\x1a.sgame_state.BuffMarkState\"9\n\x0cPassiveSkill\x12\x17\n\x0fpassive_skillid\x18\x01 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x02 \x01(\x05\"7\n\x0b\x41\x63tiveSkill\x12\x16\n\x0e\x61\x63tive_skillid\x18\x01 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x02 \x01(\x05\"\xa1\x01\n\tEquipSlot\x12\x10\n\x08\x63onfigId\x18\x01 \x02(\x05\x12\x0e\n\x06\x61mount\x18\x02 \x02(\x05\x12\x10\n\x08\x62uyPrice\x18\x03 \x02(\x05\x12\x30\n\rpassive_skill\x18\x04 \x03(\x0b\x32\x19.sgame_state.PassiveSkill\x12.\n\x0c\x61\x63tive_skill\x18\x05 \x03(\x0b\x32\x18.sgame_state.ActiveSkill\"4\n\nEquipState\x12&\n\x06\x65quips\x18\x01 \x03(\x0b\x32\x16.sgame_state.EquipSlot\"\x9a\x01\n\x13ReturnCityAbortInfo\x12\x10\n\x08isActive\x18\x01 \x01(\x08\x12.\n\tabortType\x18\x02 \x01(\x0e\x32\x1b.sgame_state.SkillAbortType\x12\x32\n\x0e\x61ttackSlotType\x18\x03 \x01(\x0e\x32\x1a.sgame_state.SkillSlotType\x12\r\n\x05objID\x18\x04 \x01(\r\"R\n\x0bProtectInfo\x12-\n\x0bprotectType\x18\x01 \x01(\x0e\x32\x18.sgame_state.ProtectType\x12\x14\n\x0cprotectValue\x18\x02 \x01(\r\"\xdd\x05\n\tHeroState\x12\x11\n\tplayer_id\x18\x01 \x02(\r\x12,\n\x0b\x61\x63tor_state\x18\x02 \x02(\x0b\x32\x17.sgame_state.ActorState\x12,\n\x0bskill_state\x18\x03 \x02(\x0b\x32\x17.sgame_state.SkillState\x12,\n\x0b\x65quip_state\x18\x04 \x02(\x0b\x32\x17.sgame_state.EquipState\x12*\n\nbuff_state\x18\x05 \x02(\x0b\x32\x16.sgame_state.BuffState\x12\r\n\x05level\x18\x06 \x02(\x05\x12\x0b\n\x03\x65xp\x18\x07 \x02(\x05\x12\r\n\x05money\x18\x08 \x02(\x05\x12\x13\n\x0brevive_time\x18\t \x02(\x05\x12\x0f\n\x07killCnt\x18\n \x02(\x05\x12\x0f\n\x07\x64\x65\x61\x64\x43nt\x18\x0b \x02(\x05\x12\x11\n\tassistCnt\x18\x0c \x02(\x05\x12\x10\n\x08moneyCnt\x18\r \x02(\x05\x12\x11\n\ttotalHurt\x18\x0e \x02(\x05\x12\x17\n\x0ftotalHurtToHero\x18\x0f \x02(\x05\x12\x19\n\x11totalBeHurtByHero\x18\x10 \x02(\x05\x12\x30\n\rpassive_skill\x18\x11 \x03(\x0b\x32\x19.sgame_state.PassiveSkill\x12%\n\x08real_cmd\x18\x12 \x03(\x0b\x32\x13.sgame_state.CmdPkg\x12\x30\n\rtakeHurtInfos\x18\x13 \x03(\x0b\x32\x19.sgame_state.TakeHurtInfo\x12\x18\n\x10\x63\x61nAbortCurSkill\x18\x14 \x03(\x08\x12=\n\x13returnCityAbortInfo\x18\x15 \x03(\x0b\x32 .sgame_state.ReturnCityAbortInfo\x12\x11\n\tisInGrass\x18\x16 \x01(\x08\x12-\n\x0bprotectInfo\x18\x17 \x03(\x0b\x32\x18.sgame_state.ProtectInfo\x12\x13\n\x0b\x63\x61nBuyEquip\x18\x18 \x01(\x08')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'hero_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_SKILLSLOTSTATE']._serialized_start=57
  _globals['_SKILLSLOTSTATE']._serialized_end=322
  _globals['_SKILLSTATE']._serialized_start=324
  _globals['_SKILLSTATE']._serialized_end=386
  _globals['_BUFFSKILLSTATE']._serialized_start=388
  _globals['_BUFFSKILLSTATE']._serialized_end=497
  _globals['_BUFFMARKSTATE']._serialized_start=499
  _globals['_BUFFMARKSTATE']._serialized_end=589
  _globals['_BUFFSTATE']._serialized_start=591
  _globals['_BUFFSTATE']._serialized_end=700
  _globals['_PASSIVESKILL']._serialized_start=702
  _globals['_PASSIVESKILL']._serialized_end=759
  _globals['_ACTIVESKILL']._serialized_start=761
  _globals['_ACTIVESKILL']._serialized_end=816
  _globals['_EQUIPSLOT']._serialized_start=819
  _globals['_EQUIPSLOT']._serialized_end=980
  _globals['_EQUIPSTATE']._serialized_start=982
  _globals['_EQUIPSTATE']._serialized_end=1034
  _globals['_RETURNCITYABORTINFO']._serialized_start=1037
  _globals['_RETURNCITYABORTINFO']._serialized_end=1191
  _globals['_PROTECTINFO']._serialized_start=1193
  _globals['_PROTECTINFO']._serialized_end=1275
  _globals['_HEROSTATE']._serialized_start=1278
  _globals['_HEROSTATE']._serialized_end=2011
# @@protoc_insertion_point(module_scope)
