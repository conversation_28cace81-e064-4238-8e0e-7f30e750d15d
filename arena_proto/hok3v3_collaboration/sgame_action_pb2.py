# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sgame_action.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import hero_pb2 as hero__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12sgame_action.proto\x12\x0bsgame_state\x1a\x0c\x63ommon.proto\x1a\nhero.proto\".\n\x10\x41\x63tionIncomeInfo\x12\x0b\n\x03\x65xp\x18\x01 \x01(\x05\x12\r\n\x05money\x18\x02 \x01(\x05\"m\n\x0e\x41\x63tionHurtInfo\x12(\n\thurt_type\x18\x01 \x02(\x0e\x32\x15.sgame_state.HurtType\x12\x10\n\x08hurt_val\x18\x02 \x02(\x05\x12\x11\n\ticon_name\x18\x03 \x01(\x0c\x12\x0c\n\x04name\x18\x04 \x01(\x0c\"\xad\x01\n\x14\x41\x63tionSingleHurtInfo\x12\x0f\n\x07\x66rameNo\x18\x01 \x02(\x05\x12\x11\n\tconfig_id\x18\x02 \x02(\x05\x12\x12\n\nruntime_id\x18\x03 \x02(\x05\x12-\n\tslot_type\x18\x04 \x02(\x0e\x32\x1a.sgame_state.SkillSlotType\x12.\n\thurt_info\x18\x05 \x02(\x0b\x32\x1b.sgame_state.ActionHurtInfo\"S\n\x15\x41\x63tionAchievementInfo\x12\x12\n\nmulti_kill\x18\x01 \x01(\x05\x12\x12\n\nconti_kill\x18\x02 \x01(\x05\x12\x12\n\nconti_dead\x18\x03 \x01(\x05\"\x97\x03\n\x0f\x41\x63tionActorInfo\x12\x11\n\tconfig_id\x18\x01 \x02(\x05\x12\x12\n\nruntime_id\x18\x02 \x02(\x05\x12*\n\nactor_type\x18\x03 \x02(\x0e\x32\x16.sgame_state.ActorType\x12+\n\x08sub_type\x18\x04 \x02(\x0e\x32\x19.sgame_state.ActorSubType\x12%\n\x04\x63\x61mp\x18\x05 \x02(\x0e\x32\x17.sgame_state.PLAYERCAMP\x12.\n\thurt_info\x18\x06 \x03(\x0b\x32\x1b.sgame_state.ActionHurtInfo\x12\x32\n\x0bincome_info\x18\x07 \x01(\x0b\x32\x1d.sgame_state.ActionIncomeInfo\x12<\n\x10\x61\x63hievement_info\x18\x08 \x01(\x0b\x32\".sgame_state.ActionAchievementInfo\x12;\n\x10single_hurt_list\x18\t \x03(\x0b\x32!.sgame_state.ActionSingleHurtInfo\"\x99\x01\n\nDeadAction\x12+\n\x05\x64\x65\x61th\x18\x01 \x02(\x0b\x32\x1c.sgame_state.ActionActorInfo\x12,\n\x06killer\x18\x02 \x02(\x0b\x32\x1c.sgame_state.ActionActorInfo\x12\x30\n\nassist_set\x18\x03 \x03(\x0b\x32\x1c.sgame_state.ActionActorInfo\";\n\x0b\x46rameAction\x12,\n\x0b\x64\x65\x61\x64_action\x18\x01 \x03(\x0b\x32\x17.sgame_state.DeadAction*\x95\x01\n\x08HurtType\x12\x16\n\x12HURT_TYPE_PhysHurt\x10\x00\x12\x17\n\x13HURT_TYPE_MagicHurt\x10\x01\x12\x16\n\x12HURT_TYPE_RealHurt\x10\x02\x12\x16\n\x12HURT_TYPE_Therapic\x10\x03\x12\x15\n\x11HURT_TYPE_Protect\x10\x04\x12\x11\n\rHURT_TYPE_Max\x10\x05')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sgame_action_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_HURTTYPE']._serialized_start=1109
  _globals['_HURTTYPE']._serialized_end=1258
  _globals['_ACTIONINCOMEINFO']._serialized_start=61
  _globals['_ACTIONINCOMEINFO']._serialized_end=107
  _globals['_ACTIONHURTINFO']._serialized_start=109
  _globals['_ACTIONHURTINFO']._serialized_end=218
  _globals['_ACTIONSINGLEHURTINFO']._serialized_start=221
  _globals['_ACTIONSINGLEHURTINFO']._serialized_end=394
  _globals['_ACTIONACHIEVEMENTINFO']._serialized_start=396
  _globals['_ACTIONACHIEVEMENTINFO']._serialized_end=479
  _globals['_ACTIONACTORINFO']._serialized_start=482
  _globals['_ACTIONACTORINFO']._serialized_end=889
  _globals['_DEADACTION']._serialized_start=892
  _globals['_DEADACTION']._serialized_end=1045
  _globals['_FRAMEACTION']._serialized_start=1047
  _globals['_FRAMEACTION']._serialized_end=1106
# @@protoc_insertion_point(module_scope)
