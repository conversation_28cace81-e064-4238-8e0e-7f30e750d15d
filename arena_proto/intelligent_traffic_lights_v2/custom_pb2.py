# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12#arena.intelligent_traffic_lights_v2\"h\n\x0bObservation\x12\x43\n\nframestate\x18\x01 \x01(\x0b\x32/.arena.intelligent_traffic_lights_v2.FrameState\x12\x14\n\x0clegal_action\x18\x02 \x03(\x05\"\xee\x02\n\tExtraInfo\x12?\n\x08gameinfo\x18\x01 \x01(\x0b\x32-.arena.intelligent_traffic_lights_v2.GameInfo\x12\x43\n\nframestate\x18\x02 \x01(\x0b\x32/.arena.intelligent_traffic_lights_v2.FrameState\x12\x42\n\nscore_info\x18\x03 \x01(\x0b\x32..arena.intelligent_traffic_lights_v2.ScoreInfo\x12=\n\x07usrconf\x18\x04 \x01(\x0b\x32,.arena.intelligent_traffic_lights_v2.UsrConf\x12\x18\n\x0bresult_code\x18\x05 \x01(\x05H\x00\x88\x01\x01\x12\x1b\n\x0eresult_message\x18\x06 \x01(\tH\x01\x88\x01\x01\x42\x0e\n\x0c_result_codeB\x11\n\x0f_result_message\":\n\tScoreInfo\x12\x14\n\x0cphase_reward\x18\x01 \x01(\x02\x12\x17\n\x0f\x64uration_reward\x18\x02 \x01(\x02\"N\n\x06\x41\x63tion\x12\x44\n\x08\x63md_list\x18\x01 \x03(\x0b\x32\x32.arena.intelligent_traffic_lights_v2.AICommandInfo\"O\n\x07\x43ommand\x12\x44\n\x08\x63md_list\x18\x01 \x03(\x0b\x32\x32.arena.intelligent_traffic_lights_v2.AICommandInfo\"G\n\rAICommandInfo\x12\x0c\n\x04s_id\x18\x01 \x01(\r\x12\x16\n\x0enext_phase_idx\x18\x02 \x01(\r\x12\x10\n\x08\x64uration\x18\x03 \x01(\r\",\n\rJunctionDelay\x12\x0c\n\x04j_id\x18\x01 \x01(\r\x12\r\n\x05\x64\x65lay\x18\x02 \x01(\x02\"9\n\x13JunctionQueueLength\x12\x0c\n\x04j_id\x18\x01 \x01(\r\x12\x14\n\x0cqueue_length\x18\x02 \x01(\x05\"0\n\x0fJunctionTraffic\x12\x0c\n\x04j_id\x18\x01 \x01(\r\x12\x0f\n\x07traffic\x18\x02 \x01(\x05\"\xe8\x01\n\nFrameState\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\r\x12\x12\n\nframe_time\x18\x02 \x01(\r\x12>\n\x08vehicles\x18\x03 \x03(\x0b\x32,.arena.intelligent_traffic_lights_v2.Vehicle\x12:\n\x06phases\x18\x04 \x03(\x0b\x32*.arena.intelligent_traffic_lights_v2.Phase\x12\x38\n\x05lanes\x18\x05 \x03(\x0b\x32).arena.intelligent_traffic_lights_v2.Lane\"\x81\x02\n\x07Vehicle\x12\x0c\n\x04v_id\x18\x01 \x01(\r\x12\x13\n\x0bv_config_id\x18\x02 \x01(\r\x12\x0c\n\x04\x65\x64ge\x18\x03 \x01(\x05\x12\x0c\n\x04lane\x18\x04 \x01(\x05\x12\x10\n\x08junction\x18\x05 \x01(\x05\x12\x17\n\x0ftarget_junction\x18\x06 \x01(\x05\x12M\n\x10position_in_lane\x18\x07 \x01(\x0b\x32\x33.arena.intelligent_traffic_lights_v2.PositionInLane\x12\r\n\x05speed\x18\x08 \x01(\x05\x12\r\n\x05\x61\x63\x63\x65l\x18\t \x01(\x05\x12\r\n\x05\x61ngle\x18\n \x01(\x05\x12\x10\n\x08v_status\x18\x0b \x01(\x05\"D\n\x05Phase\x12\x0c\n\x04s_id\x18\x01 \x01(\r\x12\x11\n\tphase_idx\x18\x02 \x01(\r\x12\x1a\n\x12remaining_duration\x18\x03 \x01(\r\"<\n\x04Lane\x12\x0f\n\x07lane_id\x18\x01 \x01(\r\x12\x0f\n\x07v_count\x18\x02 \x01(\r\x12\x12\n\ncongestion\x18\x03 \x01(\x05\"&\n\x0ePositionInLane\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01y\x18\x02 \x01(\x05\"\xd8\x02\n\x08GameInfo\x12@\n\tjunctions\x18\x01 \x03(\x0b\x32-.arena.intelligent_traffic_lights_v2.Junction\x12<\n\x07signals\x18\x02 \x03(\x0b\x32+.arena.intelligent_traffic_lights_v2.Signal\x12\x38\n\x05\x65\x64ges\x18\x03 \x03(\x0b\x32).arena.intelligent_traffic_lights_v2.Edge\x12\x45\n\x0clane_configs\x18\x04 \x03(\x0b\x32/.arena.intelligent_traffic_lights_v2.LaneConfig\x12K\n\x0fvehicle_configs\x18\x05 \x03(\x0b\x32\x32.arena.intelligent_traffic_lights_v2.VehicleConfig\"\xf7\x01\n\x08Junction\x12\x0c\n\x04j_id\x18\x01 \x01(\r\x12\x0e\n\x06signal\x18\x02 \x01(\r\x12X\n\x19\x65nter_lanes_on_directions\x18\x03 \x03(\x0b\x32\x35.arena.intelligent_traffic_lights_v2.LanesOnDirection\x12W\n\x18\x65xit_lanes_on_directions\x18\x04 \x03(\x0b\x32\x35.arena.intelligent_traffic_lights_v2.LanesOnDirection\x12\x1a\n\x12neighbor_junctions\x18\x05 \x03(\r\"!\n\x10LanesOnDirection\x12\r\n\x05lanes\x18\x01 \x03(\r\"\x96\x01\n\x06Signal\x12\x0c\n\x04s_id\x18\x01 \x01(\r\x12\x45\n\x06phases\x18\x02 \x03(\x0b\x32\x35.arena.intelligent_traffic_lights_v2.LightPhaseConfig\x12\x11\n\tphase_idx\x18\x03 \x01(\r\x12\x10\n\x08\x64uration\x18\x04 \x01(\r\x12\x12\n\nstart_time\x18\x05 \x01(\r\"b\n\x10LightPhaseConfig\x12N\n\x11lights_on_configs\x18\x01 \x03(\x0b\x32\x33.arena.intelligent_traffic_lights_v2.LightsOnConfig\"n\n\x0eLightsOnConfig\x12\x0f\n\x07\x66rom_id\x18\x01 \x01(\x03\x12\x10\n\x08\x65nter_id\x18\x02 \x01(\x03\x12\x12\n\ngreen_mask\x18\x03 \x01(\r\x12\x13\n\x0byellow_mask\x18\x04 \x01(\r\x12\x10\n\x08red_mask\x18\x05 \x01(\r\"#\n\x04\x45\x64ge\x12\x0c\n\x04\x65_id\x18\x01 \x01(\r\x12\r\n\x05lanes\x18\x02 \x03(\r\"\\\n\nLaneConfig\x12\x0c\n\x04l_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65\x64ge_id\x18\x02 \x01(\r\x12\x10\n\x08\x64ir_mask\x18\x03 \x01(\r\x12\x0e\n\x06length\x18\x04 \x01(\r\x12\r\n\x05width\x18\x05 \x01(\r\"\x89\x01\n\rVehicleConfig\x12\x13\n\x0bv_config_id\x18\x01 \x01(\r\x12@\n\x06v_type\x18\x02 \x01(\x0e\x32\x30.arena.intelligent_traffic_lights_v2.VehicleType\x12\x0e\n\x06length\x18\x03 \x01(\r\x12\x11\n\tmax_speed\x18\x04 \x01(\r\"c\n\x07\x45ndInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x12I\n\rjunctionscore\x18\x02 \x03(\x0b\x32\x32.arena.intelligent_traffic_lights_v2.JuncTionScore\"\x90\x01\n\rJuncTionScore\x12\x0c\n\x04j_id\x18\x01 \x01(\r\x12\x0f\n\x07j_score\x18\x02 \x01(\x02\x12\x1a\n\x12\x61vg_junction_delay\x18\x03 \x01(\x02\x12!\n\x19\x61vg_junction_queue_length\x18\x04 \x01(\x02\x12!\n\x19\x61vg_junction_waiting_time\x18\x05 \x01(\x02\"\xae\x02\n\x07UsrConf\x12\x0f\n\x07weather\x18\x01 \x01(\x05\x12\x11\n\trush_hour\x18\x02 \x01(\x05\x12\x13\n\x0bspeed_limit\x18\x03 \x01(\x05\x12\x1a\n\x12speeding_cars_rate\x18\x04 \x01(\x05\x12\x18\n\x10max_waiting_cars\x18\x05 \x01(\x05\x12!\n\x19max_waiting_cars_duration\x18\x06 \x01(\x05\x12\x46\n\x0c\x63\x61r_accident\x18\x07 \x03(\x0b\x32\x30.arena.intelligent_traffic_lights_v2.CarAccident\x12I\n\rlanes_disable\x18\x08 \x03(\x0b\x32\x32.arena.intelligent_traffic_lights_v2.LanesDisabled\"Y\n\x0b\x43\x61rAccident\x12\x12\n\nlane_index\x18\x01 \x01(\x05\x12\x1b\n\x13\x61\x63\x63ident_start_time\x18\x02 \x01(\x05\x12\x19\n\x11\x61\x63\x63ident_end_time\x18\x03 \x01(\x05\"Y\n\rLanesDisabled\x12\x12\n\nlane_index\x18\x01 \x01(\x05\x12\x1a\n\x12\x64isable_start_time\x18\x02 \x01(\x05\x12\x18\n\x10\x64isable_end_time\x18\x03 \x01(\x05\"D\n\x06\x46rames\x12:\n\x06\x66rames\x18\x01 \x03(\x0b\x32*.arena.intelligent_traffic_lights_v2.Frame\"\xae\x01\n\x05\x46rame\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x0f\n\x07step_no\x18\x02 \x01(\x05\x12=\n\x07usrconf\x18\x03 \x01(\x0b\x32,.arena.intelligent_traffic_lights_v2.UsrConf\x12\x43\n\nframestate\x18\x04 \x01(\x0b\x32/.arena.intelligent_traffic_lights_v2.FrameState*T\n\x0bVehicleType\x12\x0b\n\x07Unknown\x10\x00\x12\x07\n\x03\x43\x41R\x10\x01\x12\x07\n\x03\x42US\x10\x02\x12\t\n\x05TRUCK\x10\x03\x12\x0e\n\nMOTORCYCLE\x10\x04\x12\x0b\n\x07\x42ICYCLE\x10\x05*G\n\rDirectionMask\x12\x08\n\x04None\x10\x00\x12\x0c\n\x08Straight\x10\x01\x12\x08\n\x04Left\x10\x02\x12\t\n\x05Right\x10\x04\x12\t\n\x05UTurn\x10\x08\x42\x66Zdgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/intelligent_traffic_lights_v2b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Zdgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/intelligent_traffic_lights_v2'
  _globals['_VEHICLETYPE']._serialized_start=3894
  _globals['_VEHICLETYPE']._serialized_end=3978
  _globals['_DIRECTIONMASK']._serialized_start=3980
  _globals['_DIRECTIONMASK']._serialized_end=4051
  _globals['_OBSERVATION']._serialized_start=53
  _globals['_OBSERVATION']._serialized_end=157
  _globals['_EXTRAINFO']._serialized_start=160
  _globals['_EXTRAINFO']._serialized_end=526
  _globals['_SCOREINFO']._serialized_start=528
  _globals['_SCOREINFO']._serialized_end=586
  _globals['_ACTION']._serialized_start=588
  _globals['_ACTION']._serialized_end=666
  _globals['_COMMAND']._serialized_start=668
  _globals['_COMMAND']._serialized_end=747
  _globals['_AICOMMANDINFO']._serialized_start=749
  _globals['_AICOMMANDINFO']._serialized_end=820
  _globals['_JUNCTIONDELAY']._serialized_start=822
  _globals['_JUNCTIONDELAY']._serialized_end=866
  _globals['_JUNCTIONQUEUELENGTH']._serialized_start=868
  _globals['_JUNCTIONQUEUELENGTH']._serialized_end=925
  _globals['_JUNCTIONTRAFFIC']._serialized_start=927
  _globals['_JUNCTIONTRAFFIC']._serialized_end=975
  _globals['_FRAMESTATE']._serialized_start=978
  _globals['_FRAMESTATE']._serialized_end=1210
  _globals['_VEHICLE']._serialized_start=1213
  _globals['_VEHICLE']._serialized_end=1470
  _globals['_PHASE']._serialized_start=1472
  _globals['_PHASE']._serialized_end=1540
  _globals['_LANE']._serialized_start=1542
  _globals['_LANE']._serialized_end=1602
  _globals['_POSITIONINLANE']._serialized_start=1604
  _globals['_POSITIONINLANE']._serialized_end=1642
  _globals['_GAMEINFO']._serialized_start=1645
  _globals['_GAMEINFO']._serialized_end=1989
  _globals['_JUNCTION']._serialized_start=1992
  _globals['_JUNCTION']._serialized_end=2239
  _globals['_LANESONDIRECTION']._serialized_start=2241
  _globals['_LANESONDIRECTION']._serialized_end=2274
  _globals['_SIGNAL']._serialized_start=2277
  _globals['_SIGNAL']._serialized_end=2427
  _globals['_LIGHTPHASECONFIG']._serialized_start=2429
  _globals['_LIGHTPHASECONFIG']._serialized_end=2527
  _globals['_LIGHTSONCONFIG']._serialized_start=2529
  _globals['_LIGHTSONCONFIG']._serialized_end=2639
  _globals['_EDGE']._serialized_start=2641
  _globals['_EDGE']._serialized_end=2676
  _globals['_LANECONFIG']._serialized_start=2678
  _globals['_LANECONFIG']._serialized_end=2770
  _globals['_VEHICLECONFIG']._serialized_start=2773
  _globals['_VEHICLECONFIG']._serialized_end=2910
  _globals['_ENDINFO']._serialized_start=2912
  _globals['_ENDINFO']._serialized_end=3011
  _globals['_JUNCTIONSCORE']._serialized_start=3014
  _globals['_JUNCTIONSCORE']._serialized_end=3158
  _globals['_USRCONF']._serialized_start=3161
  _globals['_USRCONF']._serialized_end=3463
  _globals['_CARACCIDENT']._serialized_start=3465
  _globals['_CARACCIDENT']._serialized_end=3554
  _globals['_LANESDISABLED']._serialized_start=3556
  _globals['_LANESDISABLED']._serialized_end=3645
  _globals['_FRAMES']._serialized_start=3647
  _globals['_FRAMES']._serialized_end=3715
  _globals['_FRAME']._serialized_start=3718
  _globals['_FRAME']._serialized_end=3892
# @@protoc_insertion_point(module_scope)
