# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: server2game.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11server2game.proto\x12\x11\x62\x61\x63k_to_the_realm\"\x83\x01\n\nFrameState\x12\x10\n\x08\x66rame_no\x18\x01 \x02(\x05\x12\x30\n\x06heroes\x18\x02 \x03(\x0b\x32 .back_to_the_realm.GorgeWalkHero\x12\x31\n\x06organs\x18\x03 \x03(\x0b\x32!.back_to_the_realm.GorgeWalkOrgan\"\xf6\x01\n\rGorgeWalkHero\x12\x0f\n\x07hero_id\x18\x01 \x02(\x05\x12\x31\n\x03pos\x18\x02 \x02(\x0b\x32$.back_to_the_realm.GorgeWalkPosition\x12\x10\n\x08speed_up\x18\x03 \x02(\x05\x12)\n\x06talent\x18\x04 \x02(\x0b\x32\x19.back_to_the_realm.Talent\x12\r\n\x05score\x18\x05 \x01(\x05\x12\x13\n\x0btotal_score\x18\x06 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x07 \x01(\x05\x12\x12\n\nbuff_count\x18\x08 \x01(\x05\x12\x14\n\x0ctalent_count\x18\t \x01(\x05\"\xce\x01\n\x0eGorgeWalkOrgan\x12\x10\n\x08sub_type\x18\x01 \x02(\x05\x12\x11\n\tconfig_id\x18\x02 \x02(\x05\x12\x0e\n\x06status\x18\x03 \x02(\x05\x12\x31\n\x03pos\x18\x04 \x01(\x0b\x32$.back_to_the_realm.GorgeWalkPosition\x12\x32\n\x05r_pos\x18\x05 \x01(\x0b\x32#.back_to_the_realm.RelativePosition\x12\x0e\n\x06reward\x18\x06 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x07 \x01(\x05\"?\n\x06Talent\x12\x13\n\x0btalent_type\x18\x01 \x02(\x05\x12\x0e\n\x06status\x18\x02 \x02(\x05\x12\x10\n\x08\x63ooldown\x18\x03 \x02(\x05\"M\n\rAICommandInfo\x12\x0f\n\x07hero_id\x18\x01 \x02(\x05\x12+\n\x08\x63md_info\x18\x02 \x01(\x0b\x32\x19.back_to_the_realm.CmdPkg\"\x88\x01\n\x06\x43mdPkg\x12.\n\x08move_dir\x18\x01 \x01(\x0e\x32\x1c.back_to_the_realm.Direction\x12\x13\n\x0btalent_type\x18\x02 \x01(\x05\x12\x39\n\x0bmove_to_pos\x18\x03 \x01(\x0b\x32$.back_to_the_realm.GorgeWalkPosition\")\n\x11GorgeWalkPosition\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01z\x18\x02 \x02(\x05\"$\n\x0cGridPosition\x12\t\n\x01x\x18\x01 \x02(\x05\x12\t\n\x01z\x18\x02 \x02(\x05\"\x85\x01\n\x10RelativePosition\x12\x37\n\tdirection\x18\x01 \x02(\x0e\x32$.back_to_the_realm.RelativeDirection\x12\x38\n\x0bl2_distance\x18\x02 \x02(\x0e\x32#.back_to_the_realm.RelativeDistance\"%\n\rFloatPosition\x12\t\n\x01x\x18\x01 \x02(\x02\x12\t\n\x01z\x18\x02 \x02(\x02\"\xa0\x01\n\x06Reward\x12\x10\n\x08\x65nd_dist\x18\x01 \x02(\x02\x12\x11\n\tbuff_dist\x18\x02 \x02(\x02\x12\x15\n\rtreasure_dist\x18\x03 \x02(\x02\x12\x10\n\x08treasure\x18\x04 \x02(\x02\x12\x0c\n\x04step\x18\x05 \x02(\x02\x12\x0b\n\x03win\x18\x06 \x02(\x02\x12\x0c\n\x04\x62ump\x18\x07 \x02(\x02\x12\x0e\n\x06memory\x18\x08 \x02(\x02\x12\x0f\n\x07\x66licker\x18\t \x02(\x02\"\xb9\x01\n\tGameFrame\x12\x0f\n\x07game_id\x18\x01 \x02(\t\x12\x10\n\x08\x66rame_no\x18\x02 \x02(\x05\x12\x32\n\x0b\x66rame_state\x18\x03 \x02(\x0b\x32\x1d.back_to_the_realm.FrameState\x12\x12\n\nterminated\x18\x04 \x02(\x08\x12\x11\n\ttruncated\x18\x05 \x02(\x08\x12.\n\tgame_info\x18\x06 \x02(\x0b\x32\x1b.back_to_the_realm.GameInfo\"\x9b\x03\n\x08GameInfo\x12\r\n\x05score\x18\x01 \x02(\x02\x12\x13\n\x0btotal_score\x18\x02 \x02(\x02\x12\x0f\n\x07step_no\x18\x03 \x02(\x02\x12\x12\n\nhero_pos_x\x18\x04 \x02(\x02\x12\x12\n\nhero_pos_z\x18\x05 \x02(\x02\x12\x16\n\x0etreasure_count\x18\x06 \x02(\x05\x12\x16\n\x0etreasure_score\x18\x07 \x02(\x05\x12\x1c\n\x14total_treasure_count\x18\x08 \x02(\x05\x12\x12\n\nbuff_count\x18\t \x02(\x05\x12\x14\n\x0ctalent_count\x18\n \x02(\x05\x12\x18\n\x10\x62uff_remain_time\x18\x0b \x02(\x02\x12\x15\n\rbuff_duration\x18\x0c \x02(\x05\x12\x31\n\x08start_id\x18\r \x02(\x0b\x32\x1f.back_to_the_realm.GridPosition\x12/\n\x06\x65nd_id\x18\x0e \x02(\x0b\x32\x1f.back_to_the_realm.GridPosition\x12\x10\n\x08map_name\x18\x0f \x02(\t\x12\x13\n\x0bobstacle_id\x18\x10 \x03(\x05*;\n\nTalentType\x12\x14\n\x10TALENT_TYPE_None\x10\x00\x12\x17\n\x13TALENT_TYPE_Flicker\x10\x01*\x96\x01\n\x11RelativeDirection\x12\x1b\n\x17RELATIVE_DIRECTION_NONE\x10\x00\x12\x08\n\x04\x45\x61st\x10\x01\x12\r\n\tNorthEast\x10\x02\x12\t\n\x05North\x10\x03\x12\r\n\tNorthWest\x10\x04\x12\x08\n\x04West\x10\x05\x12\r\n\tSouthWest\x10\x06\x12\t\n\x05South\x10\x07\x12\r\n\tSouthEast\x10\x08*n\n\x10RelativeDistance\x12\x1a\n\x16RELATIVE_DISTANCE_NONE\x10\x00\x12\r\n\tVerySmall\x10\x01\x12\t\n\x05Small\x10\x02\x12\n\n\x06Medium\x10\x03\x12\t\n\x05Large\x10\x04\x12\r\n\tVeryLarge\x10\x05*f\n\x07MapName\x12\t\n\x05Map_1\x10\x00\x12\t\n\x05Map_2\x10\x01\x12\t\n\x05Map_3\x10\x02\x12\t\n\x05Map_4\x10\x03\x12\x0c\n\x08Map_fish\x10\x04\x12\x0e\n\nMap_Cherry\x10\x05\x12\x11\n\rMap_butterfly\x10\x06*L\n\nGameStatus\x12\x0b\n\x07success\x10\x00\x12\t\n\x05\x65rror\x10\x01\x12\x0b\n\x07running\x10\x02\x12\x0b\n\x07pending\x10\x03\x12\x0c\n\x08overtime\x10\x04*\x7f\n\tDirection\x12\x0b\n\x07\x41ngle_0\x10\x00\x12\x0c\n\x08\x41ngle_45\x10\x01\x12\x0c\n\x08\x41ngle_90\x10\x02\x12\r\n\tAngle_135\x10\x03\x12\r\n\tAngle_180\x10\x04\x12\r\n\tAngle_225\x10\x05\x12\r\n\tAngle_270\x10\x06\x12\r\n\tAngle_315\x10\x07')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'server2game_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_TALENTTYPE']._serialized_start=1936
  _globals['_TALENTTYPE']._serialized_end=1995
  _globals['_RELATIVEDIRECTION']._serialized_start=1998
  _globals['_RELATIVEDIRECTION']._serialized_end=2148
  _globals['_RELATIVEDISTANCE']._serialized_start=2150
  _globals['_RELATIVEDISTANCE']._serialized_end=2260
  _globals['_MAPNAME']._serialized_start=2262
  _globals['_MAPNAME']._serialized_end=2364
  _globals['_GAMESTATUS']._serialized_start=2366
  _globals['_GAMESTATUS']._serialized_end=2442
  _globals['_DIRECTION']._serialized_start=2444
  _globals['_DIRECTION']._serialized_end=2571
  _globals['_FRAMESTATE']._serialized_start=41
  _globals['_FRAMESTATE']._serialized_end=172
  _globals['_GORGEWALKHERO']._serialized_start=175
  _globals['_GORGEWALKHERO']._serialized_end=421
  _globals['_GORGEWALKORGAN']._serialized_start=424
  _globals['_GORGEWALKORGAN']._serialized_end=630
  _globals['_TALENT']._serialized_start=632
  _globals['_TALENT']._serialized_end=695
  _globals['_AICOMMANDINFO']._serialized_start=697
  _globals['_AICOMMANDINFO']._serialized_end=774
  _globals['_CMDPKG']._serialized_start=777
  _globals['_CMDPKG']._serialized_end=913
  _globals['_GORGEWALKPOSITION']._serialized_start=915
  _globals['_GORGEWALKPOSITION']._serialized_end=956
  _globals['_GRIDPOSITION']._serialized_start=958
  _globals['_GRIDPOSITION']._serialized_end=994
  _globals['_RELATIVEPOSITION']._serialized_start=997
  _globals['_RELATIVEPOSITION']._serialized_end=1130
  _globals['_FLOATPOSITION']._serialized_start=1132
  _globals['_FLOATPOSITION']._serialized_end=1169
  _globals['_REWARD']._serialized_start=1172
  _globals['_REWARD']._serialized_end=1332
  _globals['_GAMEFRAME']._serialized_start=1335
  _globals['_GAMEFRAME']._serialized_end=1520
  _globals['_GAMEINFO']._serialized_start=1523
  _globals['_GAMEINFO']._serialized_end=1934
# @@protoc_insertion_point(module_scope)
