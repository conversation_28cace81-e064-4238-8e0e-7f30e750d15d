# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x1a\x61rena.back_to_the_realm_v2\"\xcf\x01\n\x0bObservation\x12;\n\x0b\x66rame_state\x18\x01 \x01(\x0b\x32&.arena.back_to_the_realm_v2.FrameState\x12\x39\n\nscore_info\x18\x02 \x01(\x0b\x32%.arena.back_to_the_realm_v2.ScoreInfo\x12\x35\n\x08map_info\x18\x03 \x03(\x0b\x32#.arena.back_to_the_realm_v2.MapInfo\x12\x11\n\tlegal_act\x18\x04 \x03(\x05\"\x19\n\x07MapInfo\x12\x0e\n\x06values\x18\x01 \x03(\x05\"\xae\x01\n\tExtraInfo\x12\x13\n\x0bresult_code\x18\x01 \x01(\x05\x12\x16\n\x0eresult_message\x18\x02 \x01(\t\x12;\n\x0b\x66rame_state\x18\x03 \x01(\x0b\x32&.arena.back_to_the_realm_v2.FrameState\x12\x37\n\tgame_info\x18\x04 \x01(\x0b\x32$.arena.back_to_the_realm_v2.GameInfo\".\n\x06\x41\x63tion\x12\x10\n\x08move_dir\x18\x01 \x01(\x05\x12\x12\n\nuse_talent\x18\x02 \x01(\x05\"%\n\rFloatPosition\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01z\x18\x02 \x01(\x02\"\x97\x01\n\x10RelativePosition\x12@\n\tdirection\x18\x01 \x01(\x0e\x32-.arena.back_to_the_realm_v2.RelativeDirection\x12\x41\n\x0bl2_distance\x18\x02 \x01(\x0e\x32,.arena.back_to_the_realm_v2.RelativeDistance\"\x8c\x01\n\nFrameState\x12\x0f\n\x07step_no\x18\x01 \x01(\x05\x12\x35\n\x06heroes\x18\x02 \x03(\x0b\x32%.arena.back_to_the_realm_v2.RealmHero\x12\x36\n\x06organs\x18\x03 \x03(\x0b\x32&.arena.back_to_the_realm_v2.RealmOrgan\"\xa4\x01\n\tScoreInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x12\x13\n\x0btotal_score\x18\x02 \x01(\x02\x12\x0f\n\x07step_no\x18\x03 \x01(\x05\x12 \n\x18treasure_collected_count\x18\x04 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x05 \x01(\x05\x12\x12\n\nbuff_count\x18\x06 \x01(\x05\x12\x14\n\x0ctalent_count\x18\x07 \x01(\x05\"\xdb\x03\n\x08GameInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x12\x13\n\x0btotal_score\x18\x02 \x01(\x02\x12\x0f\n\x07step_no\x18\x03 \x01(\x05\x12\x31\n\x03pos\x18\x04 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12\x37\n\tstart_pos\x18\x05 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12\x35\n\x07\x65nd_pos\x18\x06 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12 \n\x18treasure_collected_count\x18\x07 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x08 \x01(\x05\x12\x16\n\x0etreasure_count\x18\t \x01(\x05\x12\x12\n\nbuff_count\x18\n \x01(\x05\x12\x14\n\x0ctalent_count\x18\x0b \x01(\x05\x12\x18\n\x10\x62uff_remain_time\x18\x0c \x01(\x05\x12\x15\n\rbuff_duration\x18\r \x01(\x05\x12\x35\n\x08map_info\x18\x0e \x03(\x0b\x32#.arena.back_to_the_realm_v2.MapInfo\x12\x13\n\x0bobstacle_id\x18\x0f \x03(\x05\"y\n\x07\x43ommand\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12\x10\n\x08move_dir\x18\x02 \x01(\x05\x12\x13\n\x0btalent_type\x18\x03 \x01(\x05\x12\x36\n\x08move_pos\x18\x04 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\"\xaf\x01\n\tRealmHero\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12\x31\n\x03pos\x18\x02 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12\x10\n\x08speed_up\x18\x03 \x01(\x05\x12\x32\n\x06talent\x18\x04 \x01(\x0b\x32\".arena.back_to_the_realm_v2.Talent\x12\x18\n\x10\x62uff_remain_time\x18\x05 \x01(\x05\"?\n\x06Talent\x12\x13\n\x0btalent_type\x18\x01 \x01(\x05\x12\x0e\n\x06status\x18\x02 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x03 \x01(\x05\"\xca\x01\n\nRealmOrgan\x12\x10\n\x08sub_type\x18\x01 \x01(\x05\x12\x11\n\tconfig_id\x18\x02 \x01(\x05\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12\x31\n\x03pos\x18\x04 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12\x10\n\x08\x63ooldown\x18\x05 \x01(\x05\x12\x42\n\x0crelative_pos\x18\x06 \x01(\x0b\x32,.arena.back_to_the_realm_v2.RelativePosition\"\xcc\x02\n\tStartInfo\x12\x33\n\x05start\x18\x01 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12\x31\n\x03\x65nd\x18\x02 \x01(\x0b\x32$.arena.back_to_the_realm_v2.Position\x12\x36\n\x06organs\x18\x03 \x03(\x0b\x32&.arena.back_to_the_realm_v2.RealmOrgan\x12<\n\tobstacles\x18\x04 \x03(\x0b\x32).arena.back_to_the_realm_v2.RealmObstacle\x12\x31\n\x08map_info\x18\x05 \x01(\x0b\x32\x1f.arena.back_to_the_realm_v2.Map\x12\x15\n\rbuff_cooldown\x18\x06 \x01(\x05\x12\x17\n\x0ftalent_cooldown\x18\x07 \x01(\x05\"S\n\x03Map\x12\x10\n\x08map_name\x18\x01 \x01(\t\x12\r\n\x05x_max\x18\x02 \x01(\x05\x12\r\n\x05z_max\x18\x03 \x01(\x05\x12\r\n\x05x_min\x18\x04 \x01(\x05\x12\r\n\x05z_min\x18\x05 \x01(\x05\"\xd0\x01\n\x05\x46rame\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x0f\n\x07step_no\x18\x02 \x01(\x05\x12\x33\n\x04hero\x18\x03 \x01(\x0b\x32%.arena.back_to_the_realm_v2.RealmHero\x12\x36\n\x06organs\x18\x04 \x03(\x0b\x32&.arena.back_to_the_realm_v2.RealmOrgan\x12\x37\n\tgame_info\x18\x05 \x01(\x0b\x32$.arena.back_to_the_realm_v2.GameInfo\"<\n\rRealmObstacle\x12\x13\n\x0bobstacle_id\x18\x01 \x01(\x05\x12\x16\n\x0eoutline_points\x18\x02 \x03(\x05\";\n\x06\x46rames\x12\x31\n\x06\x66rames\x18\x01 \x03(\x0b\x32!.arena.back_to_the_realm_v2.Frame\"\xb7\x01\n\x07\x45ndInfo\x12\r\n\x05\x66rame\x18\x01 \x01(\x02\x12\x0c\n\x04step\x18\x02 \x01(\x02\x12\x13\n\x0btotal_score\x18\x03 \x01(\x02\x12 \n\x18treasure_collected_count\x18\x04 \x01(\x02\x12\x16\n\x0etreasure_score\x18\x05 \x01(\x02\x12\x12\n\nbuff_count\x18\x06 \x01(\x02\x12\x14\n\x0ctalent_count\x18\x07 \x01(\x02\x12\x16\n\x0eobstacle_count\x18\x08 \x01(\x02\"x\n\x07UsrConf\x12\x14\n\x0cstart_pos_id\x18\x01 \x01(\x05\x12\x12\n\nend_pos_id\x18\x02 \x01(\x05\x12\x17\n\x0ftreasure_pos_id\x18\x03 \x01(\x05\x12\x11\n\ttalent_id\x18\x04 \x01(\x05\x12\x17\n\x0ftreasure_random\x18\x05 \x01(\x05\" \n\x08Position\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01z\x18\x02 \x01(\x05*\x96\x01\n\x11RelativeDirection\x12\x1b\n\x17RELATIVE_DIRECTION_NONE\x10\x00\x12\x08\n\x04\x45\x61st\x10\x01\x12\r\n\tNorthEast\x10\x02\x12\t\n\x05North\x10\x03\x12\r\n\tNorthWest\x10\x04\x12\x08\n\x04West\x10\x05\x12\r\n\tSouthWest\x10\x06\x12\t\n\x05South\x10\x07\x12\r\n\tSouthEast\x10\x08*n\n\x10RelativeDistance\x12\x1a\n\x16RELATIVE_DISTANCE_NONE\x10\x00\x12\r\n\tVerySmall\x10\x01\x12\t\n\x05Small\x10\x02\x12\n\n\x06Medium\x10\x03\x12\t\n\x05Large\x10\x04\x12\r\n\tVeryLarge\x10\x05\x42]Z[git.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/back_to_the_realm_v2b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z[git.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/back_to_the_realm_v2'
  _globals['_RELATIVEDIRECTION']._serialized_start=3155
  _globals['_RELATIVEDIRECTION']._serialized_end=3305
  _globals['_RELATIVEDISTANCE']._serialized_start=3307
  _globals['_RELATIVEDISTANCE']._serialized_end=3417
  _globals['_OBSERVATION']._serialized_start=45
  _globals['_OBSERVATION']._serialized_end=252
  _globals['_MAPINFO']._serialized_start=254
  _globals['_MAPINFO']._serialized_end=279
  _globals['_EXTRAINFO']._serialized_start=282
  _globals['_EXTRAINFO']._serialized_end=456
  _globals['_ACTION']._serialized_start=458
  _globals['_ACTION']._serialized_end=504
  _globals['_FLOATPOSITION']._serialized_start=506
  _globals['_FLOATPOSITION']._serialized_end=543
  _globals['_RELATIVEPOSITION']._serialized_start=546
  _globals['_RELATIVEPOSITION']._serialized_end=697
  _globals['_FRAMESTATE']._serialized_start=700
  _globals['_FRAMESTATE']._serialized_end=840
  _globals['_SCOREINFO']._serialized_start=843
  _globals['_SCOREINFO']._serialized_end=1007
  _globals['_GAMEINFO']._serialized_start=1010
  _globals['_GAMEINFO']._serialized_end=1485
  _globals['_COMMAND']._serialized_start=1487
  _globals['_COMMAND']._serialized_end=1608
  _globals['_REALMHERO']._serialized_start=1611
  _globals['_REALMHERO']._serialized_end=1786
  _globals['_TALENT']._serialized_start=1788
  _globals['_TALENT']._serialized_end=1851
  _globals['_REALMORGAN']._serialized_start=1854
  _globals['_REALMORGAN']._serialized_end=2056
  _globals['_STARTINFO']._serialized_start=2059
  _globals['_STARTINFO']._serialized_end=2391
  _globals['_MAP']._serialized_start=2393
  _globals['_MAP']._serialized_end=2476
  _globals['_FRAME']._serialized_start=2479
  _globals['_FRAME']._serialized_end=2687
  _globals['_REALMOBSTACLE']._serialized_start=2689
  _globals['_REALMOBSTACLE']._serialized_end=2749
  _globals['_FRAMES']._serialized_start=2751
  _globals['_FRAMES']._serialized_end=2810
  _globals['_ENDINFO']._serialized_start=2813
  _globals['_ENDINFO']._serialized_end=2996
  _globals['_USRCONF']._serialized_start=2998
  _globals['_USRCONF']._serialized_end=3118
  _globals['_POSITION']._serialized_start=3120
  _globals['_POSITION']._serialized_end=3152
# @@protoc_insertion_point(module_scope)
