# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12%arena.legged_robot_locomotion_control\"\x18\n\x07\x45ndInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x42hZfgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/legged_robot_locomotion_controlb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Zfgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/legged_robot_locomotion_control'
  _globals['_ENDINFO']._serialized_start=55
  _globals['_ENDINFO']._serialized_end=79
# @@protoc_insertion_point(module_scope)
