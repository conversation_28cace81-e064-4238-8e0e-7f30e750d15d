# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x17\x61rena.back_to_the_realm\"\xc4\x01\n\x0bObservation\x12\x38\n\x0b\x66rame_state\x18\x01 \x01(\x0b\x32#.arena.back_to_the_realm.FrameState\x12\x34\n\tgame_info\x18\x02 \x01(\x0b\x32!.arena.back_to_the_realm.GameInfo\x12\x32\n\x08map_info\x18\x03 \x03(\x0b\x32 .arena.back_to_the_realm.MapInfo\x12\x11\n\tlegal_act\x18\x04 \x03(\x05\"\x19\n\x07MapInfo\x12\x0e\n\x06values\x18\x01 \x03(\x05\"q\n\tScoreInfo\x12\r\n\x05score\x18\x01 \x01(\x05\x12\x13\n\x0btotal_score\x18\x02 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x03 \x01(\x05\x12\x12\n\nbuff_count\x18\x04 \x01(\x05\x12\x14\n\x0ctalent_count\x18\x05 \x01(\x05\"r\n\tExtraInfo\x12\x13\n\x0bresult_code\x18\x01 \x01(\x05\x12\x16\n\x0eresult_message\x18\x02 \x01(\t\x12\x38\n\x0b\x66rame_state\x18\x03 \x01(\x0b\x32#.arena.back_to_the_realm.FrameState\".\n\x06\x41\x63tion\x12\x10\n\x08move_dir\x18\x01 \x01(\x05\x12\x12\n\nuse_talent\x18\x02 \x01(\x05\"\xc6\x03\n\x0cRealmFeature\x12\x38\n\x08norm_pos\x18\x01 \x01(\x0b\x32&.arena.back_to_the_realm.FloatPosition\x12\x33\n\x08grid_pos\x18\x02 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12<\n\tstart_pos\x18\x03 \x01(\x0b\x32).arena.back_to_the_realm.RelativePosition\x12:\n\x07\x65nd_pos\x18\x04 \x01(\x0b\x32).arena.back_to_the_realm.RelativePosition\x12;\n\x08\x62uff_pos\x18\x05 \x01(\x0b\x32).arena.back_to_the_realm.RelativePosition\x12?\n\x0ctreasure_pos\x18\x06 \x03(\x0b\x32).arena.back_to_the_realm.RelativePosition\x12\x14\n\x0cobstacle_map\x18\x07 \x03(\x05\x12\x12\n\nmemory_map\x18\x08 \x03(\x02\x12\x14\n\x0ctreasure_map\x18\t \x03(\x05\x12\x0f\n\x07\x65nd_map\x18\n \x03(\x05\"%\n\rFloatPosition\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01z\x18\x02 \x01(\x02\"\xbf\x01\n\x10RelativePosition\x12=\n\tdirection\x18\x01 \x01(\x0e\x32*.arena.back_to_the_realm.RelativeDirection\x12\x13\n\x0bl2_distance\x18\x02 \x01(\x02\x12@\n\rpath_distance\x18\x03 \x01(\x0e\x32).arena.back_to_the_realm.RelativeDistance\x12\x15\n\rgrid_distance\x18\x04 \x01(\x02\"\x87\x01\n\nFrameState\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x32\n\x06heroes\x18\x02 \x03(\x0b\x32\".arena.back_to_the_realm.RealmHero\x12\x33\n\x06organs\x18\x03 \x03(\x0b\x32#.arena.back_to_the_realm.RealmOrgan\"\xba\x03\n\x08GameInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x12\x13\n\x0btotal_score\x18\x02 \x01(\x02\x12\x0f\n\x07step_no\x18\x03 \x01(\x05\x12.\n\x03pos\x18\x04 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12\x34\n\tstart_pos\x18\x05 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12\x32\n\x07\x65nd_pos\x18\x06 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12 \n\x18treasure_collected_count\x18\x07 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x08 \x01(\x05\x12\x16\n\x0etreasure_count\x18\t \x01(\x05\x12\x12\n\nbuff_count\x18\n \x01(\x05\x12\x14\n\x0ctalent_count\x18\x0b \x01(\x05\x12\x18\n\x10\x62uff_remain_time\x18\x0c \x01(\x05\x12\x15\n\rbuff_duration\x18\r \x01(\x05\x12\x32\n\x08map_info\x18\x0e \x03(\x0b\x32 .arena.back_to_the_realm.MapInfo\"v\n\x07\x43ommand\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12\x10\n\x08move_dir\x18\x02 \x01(\x05\x12\x13\n\x0btalent_type\x18\x03 \x01(\x05\x12\x33\n\x08move_pos\x18\x04 \x01(\x0b\x32!.arena.back_to_the_realm.Position\"\x8f\x01\n\tRealmHero\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12.\n\x03pos\x18\x02 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12\x10\n\x08speed_up\x18\x03 \x01(\x05\x12/\n\x06talent\x18\x04 \x01(\x0b\x32\x1f.arena.back_to_the_realm.Talent\"?\n\x06Talent\x12\x13\n\x0btalent_type\x18\x01 \x01(\x05\x12\x0e\n\x06status\x18\x02 \x01(\x05\x12\x10\n\x08\x63ooldown\x18\x03 \x01(\x05\"\x83\x01\n\nRealmOrgan\x12\x10\n\x08sub_type\x18\x01 \x01(\x05\x12\x11\n\tconfig_id\x18\x02 \x01(\x05\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12.\n\x03pos\x18\x04 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12\x10\n\x08\x63ooldown\x18\x05 \x01(\x05\"\xa2\x01\n\tStartInfo\x12\x30\n\x05start\x18\x01 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12.\n\x03\x65nd\x18\x02 \x01(\x0b\x32!.arena.back_to_the_realm.Position\x12\x33\n\x06organs\x18\x03 \x03(\x0b\x32#.arena.back_to_the_realm.RealmOrgan\"\x82\x02\n\x05\x46rame\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x0f\n\x07step_no\x18\x02 \x01(\x05\x12\x30\n\x04hero\x18\x03 \x01(\x0b\x32\".arena.back_to_the_realm.RealmHero\x12\x33\n\x06organs\x18\x04 \x03(\x0b\x32#.arena.back_to_the_realm.RealmOrgan\x12\x34\n\tgame_info\x18\x05 \x01(\x0b\x32!.arena.back_to_the_realm.GameInfo\x12\x39\n\tobstacles\x18\x06 \x03(\x0b\x32&.arena.back_to_the_realm.RealmObstacle\"?\n\rRealmObstacle\x12\x13\n\x0bobstacle_id\x18\x01 \x01(\x05\x12\x19\n\x11obstacle_grid_pos\x18\x02 \x03(\x05\"8\n\x06\x46rames\x12.\n\x06\x66rames\x18\x01 \x03(\x0b\x32\x1e.arena.back_to_the_realm.Frame\"\x95\x01\n\x07\x45ndInfo\x12\r\n\x05\x66rame\x18\x01 \x01(\x05\x12\x0c\n\x04step\x18\x02 \x01(\x05\x12\x13\n\x0btotal_score\x18\x03 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x04 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x05 \x01(\x05\x12\x12\n\nbuff_count\x18\x06 \x01(\x05\x12\x14\n\x0ctalent_count\x18\x07 \x01(\x05\"x\n\x07UsrConf\x12\x14\n\x0cstart_pos_id\x18\x01 \x01(\x05\x12\x12\n\nend_pos_id\x18\x02 \x01(\x05\x12\x17\n\x0ftreasure_pos_id\x18\x03 \x01(\x05\x12\x11\n\ttalent_id\x18\x04 \x01(\x05\x12\x17\n\x0ftreasure_random\x18\x05 \x01(\x05\" \n\x08Position\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01z\x18\x02 \x01(\x05*\x96\x01\n\x11RelativeDirection\x12\x1b\n\x17RELATIVE_DIRECTION_NONE\x10\x00\x12\x08\n\x04\x45\x61st\x10\x01\x12\r\n\tNorthEast\x10\x02\x12\t\n\x05North\x10\x03\x12\r\n\tNorthWest\x10\x04\x12\x08\n\x04West\x10\x05\x12\r\n\tSouthWest\x10\x06\x12\t\n\x05South\x10\x07\x12\r\n\tSouthEast\x10\x08*n\n\x10RelativeDistance\x12\x1a\n\x16RELATIVE_DISTANCE_NONE\x10\x00\x12\r\n\tVerySmall\x10\x01\x12\t\n\x05Small\x10\x02\x12\n\n\x06Medium\x10\x03\x12\t\n\x05Large\x10\x04\x12\r\n\tVeryLarge\x10\x05\x42ZZXgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/back_to_the_realmb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZXgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/back_to_the_realm'
  _globals['_RELATIVEDIRECTION']._serialized_start=3142
  _globals['_RELATIVEDIRECTION']._serialized_end=3292
  _globals['_RELATIVEDISTANCE']._serialized_start=3294
  _globals['_RELATIVEDISTANCE']._serialized_end=3404
  _globals['_OBSERVATION']._serialized_start=42
  _globals['_OBSERVATION']._serialized_end=238
  _globals['_MAPINFO']._serialized_start=240
  _globals['_MAPINFO']._serialized_end=265
  _globals['_SCOREINFO']._serialized_start=267
  _globals['_SCOREINFO']._serialized_end=380
  _globals['_EXTRAINFO']._serialized_start=382
  _globals['_EXTRAINFO']._serialized_end=496
  _globals['_ACTION']._serialized_start=498
  _globals['_ACTION']._serialized_end=544
  _globals['_REALMFEATURE']._serialized_start=547
  _globals['_REALMFEATURE']._serialized_end=1001
  _globals['_FLOATPOSITION']._serialized_start=1003
  _globals['_FLOATPOSITION']._serialized_end=1040
  _globals['_RELATIVEPOSITION']._serialized_start=1043
  _globals['_RELATIVEPOSITION']._serialized_end=1234
  _globals['_FRAMESTATE']._serialized_start=1237
  _globals['_FRAMESTATE']._serialized_end=1372
  _globals['_GAMEINFO']._serialized_start=1375
  _globals['_GAMEINFO']._serialized_end=1817
  _globals['_COMMAND']._serialized_start=1819
  _globals['_COMMAND']._serialized_end=1937
  _globals['_REALMHERO']._serialized_start=1940
  _globals['_REALMHERO']._serialized_end=2083
  _globals['_TALENT']._serialized_start=2085
  _globals['_TALENT']._serialized_end=2148
  _globals['_REALMORGAN']._serialized_start=2151
  _globals['_REALMORGAN']._serialized_end=2282
  _globals['_STARTINFO']._serialized_start=2285
  _globals['_STARTINFO']._serialized_end=2447
  _globals['_FRAME']._serialized_start=2450
  _globals['_FRAME']._serialized_end=2708
  _globals['_REALMOBSTACLE']._serialized_start=2710
  _globals['_REALMOBSTACLE']._serialized_end=2773
  _globals['_FRAMES']._serialized_start=2775
  _globals['_FRAMES']._serialized_end=2831
  _globals['_ENDINFO']._serialized_start=2834
  _globals['_ENDINFO']._serialized_end=2983
  _globals['_USRCONF']._serialized_start=2985
  _globals['_USRCONF']._serialized_end=3105
  _globals['_POSITION']._serialized_start=3107
  _globals['_POSITION']._serialized_end=3139
# @@protoc_insertion_point(module_scope)
