# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: custom.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63ustom.proto\x12\x10\x61rena.gorge_walk\"1\n\x0bObservation\x12\x0f\n\x07\x66\x65\x61ture\x18\x01 \x03(\x02\x12\x11\n\tlegal_act\x18\x02 \x03(\x05\"/\n\tScoreInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x12\x13\n\x0btotal_score\x18\x02 \x01(\x02\"i\n\x05State\x12\x31\n\x0b\x66rame_state\x18\x01 \x01(\x0b\x32\x1c.arena.gorge_walk.FrameState\x12-\n\tgame_info\x18\x02 \x01(\x0b\x32\x1a.arena.gorge_walk.GameInfo\"\x15\n\x06\x41\x63tion\x12\x0b\n\x03\x61\x63t\x18\x01 \x01(\x05\"3\n\nFrameState\x12\x12\n\ngame_state\x18\x01 \x03(\x02\x12\x11\n\tlegal_act\x18\x02 \x03(\x05\"\xe1\x01\n\x08GameInfo\x12\r\n\x05score\x18\x01 \x01(\x02\x12\x13\n\x0btotal_score\x18\x02 \x01(\x02\x12\x0f\n\x07step_no\x18\x03 \x01(\x05\x12\r\n\x05pos_x\x18\x04 \x01(\x05\x12\r\n\x05pos_z\x18\x05 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x06 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x07 \x01(\x05\x12\x17\n\x0ftreasure_status\x18\x08 \x03(\x05\x12\x0c\n\x04view\x18\t \x01(\x05\x12\x12\n\nlocal_view\x18\n \x03(\x05\x12\x17\n\x0flocation_memory\x18\x0b \x03(\x02\"\x16\n\x07\x43ommand\x12\x0b\n\x03\x63md\x18\x01 \x01(\x05\"\xa3\x01\n\tStartInfo\x12\x32\n\x05start\x18\x01 \x01(\x0b\x32#.arena.gorge_walk.GorgeWalkPosition\x12\x30\n\x03\x65nd\x18\x02 \x01(\x0b\x32#.arena.gorge_walk.GorgeWalkPosition\x12\x30\n\x06organs\x18\x03 \x03(\x0b\x32 .arena.gorge_walk.GorgeWalkOrgan\"\x8b\x01\n\x05\x46rame\x12\x10\n\x08\x66rame_no\x18\x01 \x01(\x05\x12\x0f\n\x07step_no\x18\x02 \x01(\x05\x12-\n\x04hero\x18\x03 \x01(\x0b\x32\x1f.arena.gorge_walk.GorgeWalkHero\x12\x30\n\x06organs\x18\x04 \x03(\x0b\x32 .arena.gorge_walk.GorgeWalkOrgan\"1\n\x06\x46rames\x12\'\n\x06\x66rames\x18\x01 \x03(\x0b\x32\x17.arena.gorge_walk.Frame\"k\n\x07\x45ndInfo\x12\r\n\x05\x66rame\x18\x01 \x01(\x05\x12\x0c\n\x04step\x18\x02 \x01(\x05\x12\x13\n\x0btotal_score\x18\x03 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x04 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x05 \x01(\x05\")\n\x11GorgeWalkPosition\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01z\x18\x02 \x01(\x05\"\xa6\x01\n\rGorgeWalkHero\x12\x0f\n\x07hero_id\x18\x01 \x01(\x05\x12\x30\n\x03pos\x18\x02 \x01(\x0b\x32#.arena.gorge_walk.GorgeWalkPosition\x12\r\n\x05score\x18\x03 \x01(\x05\x12\x13\n\x0btotal_score\x18\x04 \x01(\x05\x12\x16\n\x0etreasure_count\x18\x05 \x01(\x05\x12\x16\n\x0etreasure_score\x18\x06 \x01(\x05\"w\n\x0eGorgeWalkOrgan\x12\x10\n\x08sub_type\x18\x01 \x01(\x05\x12\x11\n\tconfig_id\x18\x02 \x01(\x05\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12\x30\n\x03pos\x18\x04 \x01(\x0b\x32#.arena.gorge_walk.GorgeWalkPositionBSZQgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/gorge_walkb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'custom_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZQgit.woa.com/king-kaiwu/framework/kaiwu_env/protocol/golang/arena_proto/gorge_walk'
  _globals['_OBSERVATION']._serialized_start=34
  _globals['_OBSERVATION']._serialized_end=83
  _globals['_SCOREINFO']._serialized_start=85
  _globals['_SCOREINFO']._serialized_end=132
  _globals['_STATE']._serialized_start=134
  _globals['_STATE']._serialized_end=239
  _globals['_ACTION']._serialized_start=241
  _globals['_ACTION']._serialized_end=262
  _globals['_FRAMESTATE']._serialized_start=264
  _globals['_FRAMESTATE']._serialized_end=315
  _globals['_GAMEINFO']._serialized_start=318
  _globals['_GAMEINFO']._serialized_end=543
  _globals['_COMMAND']._serialized_start=545
  _globals['_COMMAND']._serialized_end=567
  _globals['_STARTINFO']._serialized_start=570
  _globals['_STARTINFO']._serialized_end=733
  _globals['_FRAME']._serialized_start=736
  _globals['_FRAME']._serialized_end=875
  _globals['_FRAMES']._serialized_start=877
  _globals['_FRAMES']._serialized_end=926
  _globals['_ENDINFO']._serialized_start=928
  _globals['_ENDINFO']._serialized_end=1035
  _globals['_GORGEWALKPOSITION']._serialized_start=1037
  _globals['_GORGEWALKPOSITION']._serialized_end=1078
  _globals['_GORGEWALKHERO']._serialized_start=1081
  _globals['_GORGEWALKHERO']._serialized_end=1247
  _globals['_GORGEWALKORGAN']._serialized_start=1249
  _globals['_GORGEWALKORGAN']._serialized_end=1368
# @@protoc_insertion_point(module_scope)
