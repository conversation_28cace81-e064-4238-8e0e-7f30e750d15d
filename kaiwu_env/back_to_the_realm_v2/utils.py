import json
import math
import numpy as np
import logging
from arena_proto.back_to_the_realm_v2.custom_pb2 import (
    <PERSON>Hero,
    RealmOrgan,
    RelativePosition,
    Position,
    GameInfo,
    Talent,
    FrameState,
)

# from kaiwu_env.conf import yaml_back_to_the_realm_v2_treasure_path_fish as treasure_data
from kaiwu_env.conf import yaml_back_to_the_realm_v2_treasure_path_crab as treasure_data
from kaiwu_env.conf import yaml_back_to_the_realm_v2_game as GW2_CONFIG
from common_python.utils.singleton import Singleton


def _print_debug_log(self, freq):
    """
    Debug时调用, 打印游戏状态信息, 每freq步打印一次
    """
    if self.frame_no % freq == 0:
        print(f"--------------------frame_no is {self.frame_no}----------------------")
        print(f"Total score is [{self.total_score}], Colleted treasure is {self.treasure_collected_count}")
        print("### Hero ###")
        print(f"- position = [{self.hero_pos.x}, {self.hero_pos.z}]")
        # printug(f"- delta distance = {delta_distance}")
        print(f"- speed_up = {self.speed_up}")
        print(f"- talent_cnt = {self.talent_count}")
        print(f"- treasure_cnt = {self.treasure_collected_count}")
        print(f"- treasure_score = {self.treasure_score}")
        print(f"- score = {self.score}")
        print("### Buff ###")
        print(f"- buff_cnt = {self.buff_count}")
        # self.logger.debug(f"- buff_remain_time = {self.buff_remain_time/ 1000}")
        print("### Talent ###")
        print(f"- talent_type = {self.telent_tpye}")
        print(f"- status = {self.telent_status}")
        print(f"- cooldown = {self.telent_cooldown / 1000}")

        print("### Organ ###")
        for organ in self.organs:
            print(f"- sub_type = {organ.sub_type}   config_id = {organ.config_id}")
            print(f"- status = {organ.status},    pos = {organ.pos}")


def map_init(scene):
    """
    初始化地图, 读取天工生成的地图数据, 包含障碍物和可通行区域的信息 \n
    返回一个 2D numpy 数组以及数组的高度和宽度 \n
    其中 0 表示障碍物, 1 表示可通行
    """
    # TODO:修改地图数据
    # 读取地图数据
    from kaiwu_env.conf import json_back_to_the_realm_v2_map_path_fish as map_data

    width = map_data["Width"]
    height = map_data["Height"]
    flags = map_data["Flags"]

    # 注意：gird 的 shape 是 (height, width), 第一维对应的是 z 坐标, 第二维对应的是 x 坐标
    # 所以要进行一个转置的操作
    grid = np.array(flags).reshape(height, width)

    return grid.T, height, width


# 初始化memory_map
def init_memory_map(map_data):
    height = map_data["Height"]
    width = map_data["Width"]

    return np.zeros((height, width))


def get_legal_pos(grid):
    """
    通过地图数据获取所有可通行的坐标 \n
    """
    legal_pos = list()
    for i in range(len(grid)):
        for j in range(len(grid[0])):
            flag = grid[i, j]
            if flag:
                legal_pos.append((i, j))

    return legal_pos


def show_map(grid):
    height = len(grid)
    width = len(grid[0])

    # 为了和评估时看到的视角一样，这里将地图进行了一个转置和翻转
    grid = grid.T
    for i in reversed(range(height)):
        for j in range(width):
            if grid[i, j] == 0:
                item = "x"
            elif grid[i, j] == 1:
                item = " "
            elif grid[i, j] == 2:
                item = "S"
            elif grid[i, j] == 3:
                item = "E"
            elif grid[i, j] == 4:
                item = "T"
            print(item, end=" ")
        print()
    print("---------------------------------------------")


def show_local_view(grid, pos, view):
    """
    显示智能体的局部视野 \n
    输入参数:
        - grid: 2D numpy 数组, 地图数据
        - pos: 当前位置
        - view: 局部视野的大小
    """
    height = len(grid)
    width = len(grid[0])

    # 为了和评估时看到的视角一样，这里将地图进行了一个转置和翻转
    grid = grid.T
    print("----------------------")
    for i in reversed(range(pos[1] - view, pos[1] + view + 1)):
        print("|", end="")
        for j in range(pos[0] - view, pos[0] + view + 1):
            if i < 0 or i >= height or j < 0 or j >= width:
                item = "x"
            elif grid[i, j] == 0:
                item = "x"
            elif grid[i, j] == 1:
                item = " "
            elif grid[i, j] == 2:
                item = "S"
            elif grid[i, j] == 3:
                item = "E"
            elif grid[i, j] == 4:
                item = "T"
            if i == pos[1] and j == pos[0]:
                item = "A"
            print(item, end=" ")
        print("|")
    print("----------------------")


def bump(grid, pos):
    """
    判断当前位置是否是障碍物 \n
    输入参数:
        - grid: 2D numpy 数组, 地图数据
        - pos: 当前位置
    返回值:
        - bump(bool): True 表示当前位置是障碍物, False 表示当前位置不是障碍物
    """
    bump = False

    if grid[pos[0], pos[1]] == 0:
        bump = True

    return bump


def find_treasure(grid, pos):
    """
    判断当前位置是否是宝藏 \n
    输入参数:
        - grid: 2D numpy 数组, 地图数据
        - pos: 当前位置
    返回值:
        - find(bool): True 表示当前位置是宝箱, False 表示当前位置不是宝箱
    """
    find = False

    if grid[pos[0], pos[1]] == 4:
        find = True

    return find


def convert_pos_to_grid_pos(map_name, x, z):
    """将pos转换为珊格化后坐标

    Args:
        map_name (str): 地图名称
        x (float): x
        z (float): z

    Returns:
        _type_: tuple
    """
    # map_fish
    if map_name == "map_fish":
        x = (x + 4500) // 500
        z = (z + 63000) // 500

    # map_cherry
    if map_name == "map_cherry":
        x = (x + 5000) // 500
        z = (z + 60000) // 500

    # map_butterfly
    if map_name == "map_butterfly":
        x = (x + 2332) // 500
        z = (z + 7103) // 500

    return x, z


def convert_grid_pos_to_pos(map_name, x, z):
    """将珊格化坐标转换回原始pos坐标

    Args:
        map_name (str): 地图名称
        x (int): 网格x坐标
        z (int): 网格z坐标

    Returns:
        tuple: (x, z) 原始坐标
    """
    # 首先交换x和z坐标，以匹配json文件的顺序
    # 这一步是必要的，用于与 json 文件的顺序保持一致
    # x, z = z, x
    # map_fish
    if map_name == "map_fish":
        x = x * 500 - 4500 + 250  # 加250是为了取网格中心点
        z = z * 500 - 63000 + 250
    # map_cherry
    if map_name == "map_cherry":
        x = x * 500 - 5000 + 250  # 加250是为了取网格中心点
        z = z * 500 - 60000 + 250
    # map_butterfly
    if map_name == "map_butterfly":
        x = x * 500 - 2332 + 250  # 加250是为了取网格中心点
        z = z * 500 - 7103 + 250

    return Position(x=x, z=z)


def generate_F(env, game_conf):
    # F dict initialization
    F = {}
    for pos in env.legal_pos:
        if pos == game_conf.end:
            continue
        s = int(pos[0] * 64 + pos[1])
        F[s] = {}

    for pos in env.legal_pos:
        if pos == game_conf.end:
            continue
        for action in range(4):
            _ = env.reset(start=pos, end=game_conf.end, treasure_id=range(10))
            score, terminated = env._move(action)
            new_s = env.pos[0] * 64 + env.pos[1]
            s = int(pos[0] * 64 + pos[1])
            F[s][action] = [int(new_s), score, terminated]

    with open("F.json", "w") as f:
        json.dump(F, f)


def get_F(path="arena/conf/gorge_walk/F_level_0.json"):
    with open(path, "r") as f:
        F = json.load(f)
    return F


def get_logger(level=logging.INFO):
    # Create a logger
    logger = logging.getLogger("game")
    logger.setLevel(level)

    # Create a formatter
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    # Create a console handler and set the formatter
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # Add the console handler to the logger
    logger.addHandler(console_handler)

    return logger


def bfs_distance(map, start, end):
    """
    用 BFS 搜索算法计算最短路径
    """
    a1, b1 = start
    a2, b2 = end

    if map[a1][b1] == 0 or map[a2][b2] == 0:
        return None

    start, end = (a1, b1), (a2, b2)
    queue = [start]
    visited = {start}
    dis = 0

    while queue:
        dis += 1
        length = len(queue)

        for i in range(length):
            x, y = queue[i]

            def help(x, y):
                if (x, y) not in visited and map[x][y] != 0:
                    queue.append((x, y))
                    visited.add((x, y))

            if end in [(x + 1, y), (x - 1, y), (x, y + 1), (x, y - 1)]:
                return dis

            help(x + 1, y)
            help(x - 1, y), help(x, y + 1), help(x, y - 1)

        queue = queue[length:]


def discrete_bfs_dist(pos1, pos2):
    """
    输入任意两个位置坐标, 返回离散化后的两个位置之间的最短路径距离: \n
    输入参数:
        - pos1: 位置坐标1
        - pos2: 位置坐标2
    返回值:
        - 0: 非常近
        - 1: 很近
        - 2: 近
        - 3: 中等
        - 4: 远
        - 5: 很远
        - 6: 非常远
    """
    from kaiwu_env.conf import json_back_to_the_realm_v2_map_path_bfs_dist as bfs_dist

    state1 = pos1[0] * 64 + pos1[1]
    state2 = pos2[0] * 64 + pos2[1]
    try:
        dist = bfs_dist[str(state1)][str(state2)]
    except KeyError:
        raise KeyError(f"KeyError: {state1}, {state2}")

    if dist <= 20:
        return 0
    elif dist <= 35:
        return 1
    elif dist <= 45:
        return 2
    elif dist <= 52:
        return 3
    elif dist <= 60:
        return 4
    elif dist <= 70:
        return 5
    else:
        return 6


# 通过配置来生成起点、终点、宝箱位置
def get_nature_pos(game_index):
    return Position(x=int(treasure_data.get(game_index)[0]), z=int(treasure_data.get(game_index)[1]))


# 更新英雄信息
def get_hero_info(map_name, hero, need_nature_pos=False):
    return RealmHero(
        hero_id=hero.hero_id,
        pos=(
            convert_grid_pos_to_pos(map_name, hero.pos.x, hero.pos.z)
            if need_nature_pos
            else Position(x=int(hero.pos.x), z=int(hero.pos.z))
        ),
        speed_up=hero.speed_up,
        talent=Talent(
            talent_type=hero.talent.talent_type,
            status=hero.talent.status,
            cooldown=hero.talent.cooldown,
        ),
    )


def get_game_info(map_name, hero, game_info, frame_no, need_nature_pos=False):

    return GameInfo(
        score=0 if frame_no == 0 else int(game_info.score),
        total_score=0 if frame_no == 0 else int(game_info.total_score),
        step_no=int(frame_no / 3),
        pos=(
            convert_grid_pos_to_pos(map_name, hero.pos.x, hero.pos.z)
            if need_nature_pos
            else Position(x=int(hero.pos.x), z=int(hero.pos.z))
        ),
        treasure_count=game_info.treasure_count,
        treasure_score=0 if frame_no == 0 else game_info.treasure_score,
        treasure_collected_count=(0 if frame_no == 0 else game_info.treasure_collected_count),
        buff_count=0 if frame_no == 0 else game_info.buff_count,
        talent_count=0 if frame_no == 0 else game_info.talent_count,
        buff_remain_time=0 if frame_no == 0 else game_info.buff_remain_time,
        buff_duration=game_info.buff_duration,
    )


def get_organ_info(
    map_name, frame_state, view_size=50, need_nature_pos=False, is_local_view=True, is_first_frame=False
):
    hero = frame_state.heroes[0]
    organs = list()

    # 地图难度
    # 0: 宝箱、buff、起点在视野外提供绝对和相对位置, 终点在视野外提供相对位置
    # 1: 宝箱、buff、起点、终点在视野外提供相对位置
    # 2: 宝箱、buff、起点、终点在视野外不提供信息
    map_difficulty = GW2_CONFIG.map_difficulty

    for id, organ in enumerate(frame_state.organs):
        config_id = organ.config_id
        temp_organ = RealmOrgan(
            sub_type=organ.sub_type,
            config_id=config_id,
            status=1 if is_first_frame else organ.status,
            pos=convert_grid_pos_to_pos(map_name, organ.pos.x, organ.pos.z) if need_nature_pos else organ.pos,
            cooldown=organ.cooldown,
            relative_pos=RelativePosition(
                direction=organ.relative_pos.direction,
                l2_distance=organ.relative_pos.l2_distance,
            ),
        )

        in_hero_view = False
        hero_grid_x, hero_grid_z = hero.pos.x, hero.pos.z
        organ_grid_x, organ_grid_z = organ.pos.x, organ.pos.z
        if abs(hero_grid_x - organ_grid_x) <= view_size and abs(hero_grid_z - organ_grid_z) <= view_size:
            in_hero_view = True
        # 是否是局部视野(obs)
        if is_local_view:
            # 终点在视野外屏蔽具体坐标, 保留相对位置
            if map_difficulty == 0:
                if config_id == 22 and not in_hero_view:
                    temp_organ.pos.x = -1
                    temp_organ.pos.z = -1
                    temp_organ.status = -1
            # 宝箱、buff、起点、终点在视野外屏蔽具体坐标, 保留相对位置
            elif map_difficulty == 1:
                if not in_hero_view:
                    temp_organ.pos.x = -1
                    temp_organ.pos.z = -1
                    temp_organ.status = -1
            # 宝箱、buff、起点、终点在视野外不提供信息
            elif map_difficulty == 2:
                if not in_hero_view:
                    continue
        organs.append(temp_organ)

    return organs


def get_legal_act(talent_status):
    return [1, 1] if bool(talent_status) else [1, 0]


# 针对坐标的float, 直接调整成整数
def float_to_int(coord):
    if not isinstance(coord, list):
        return coord

    if len(coord) == 0:
        return coord

    if isinstance(coord[0], float):
        coord[0] = int(coord[0])
    if isinstance(coord[1], float):
        coord[1] = int(coord[1])
    return coord


# 判断坐标的输入是否有效
def check_coord_valid(coord):
    # 检查输入是否非空且为可迭代对象（列表/元组）
    if not coord or not isinstance(coord, (list, tuple)):
        return False

    # 检查坐标长度是否为2
    if len(coord) != 2:
        return False

    # 检查每个元素的类型是否为 float 或 int
    for item in coord:
        if not isinstance(item, (float, int)):
            return False

    return True


# 通过方向和距离计算目标位置
def polar_to_pos(pos, dir, use_talent, speed_up, talent_status):
    if use_talent and talent_status:
        r = GW2_CONFIG.talent.distance
    elif speed_up:
        r = GW2_CONFIG.buff.speed + 200
    else:
        r = GW2_CONFIG.step_distance + 200
    theta = math.radians(dir * (360 / GW2_CONFIG.direction_num))
    delta_x = r * math.cos(theta)
    delta_z = r * math.sin(theta)

    move_to_pos = Position(x=pos.x + int(delta_x), z=pos.z + int(delta_z))
    return move_to_pos


def get_local_frame_state(map_name, frame_state, view_size):
    heroes = []
    organs = []

    heroes.append(get_hero_info(map_name, frame_state.heroes[0]))
    organs = get_organ_info(map_name, frame_state, view_size, need_nature_pos=False, is_local_view=True)

    return FrameState(
        step_no=frame_state.step_no,
        heroes=heroes,
        organs=organs,
    )


def get_local_grid_info(grid, center_pos, view_size):
    """
    获取局部地图信息。

    :param grid: 整个地图的网格信息, numpy 数组。转置后的gird
    :param center_pos: 中心点的坐标 (x, z)。
    :param view_size: 视野大小。
    :return: 局部地图信息, numpy 数组。
    """
    x, z = center_pos
    height, width = grid.shape

    # 初始化局部地图
    local_grid = np.zeros((2 * view_size + 1, 2 * view_size + 1), dtype=grid.dtype)

    # 计算局部地图的边界
    x_min = max(0, x - view_size)
    x_max = min(width, x + view_size + 1)
    z_min = max(0, z - view_size)
    z_max = min(height, z + view_size + 1)

    # 计算局部地图在全局地图中的位置
    local_x_min = max(0, view_size - x)
    local_x_max = local_x_min + (x_max - x_min)
    local_z_min = max(0, view_size - z)
    local_z_max = local_z_min + (z_max - z_min)

    # 填充局部地图
    local_grid[local_x_min:local_x_max, local_z_min:local_z_max] = grid[x_min:x_max, z_min:z_max]

    return local_grid


# 生成一个map类，初始化的时候保存一个网格信息，提供获取网格和更新网格数据的方法
@Singleton
class Map(object):
    def __init__(self):
        self.hero_pos = (0, 0)
        self.local_grid = None
        self.map_name = ""
        self.view_size = 0

        pass

    def reset_data(self, map_name):
        """
        重置地图数据。
        """
        """获取地图的网格信息(已经转置)。"""
        from kaiwu_env.conf import (
            json_back_to_the_realm_v2_map_data_obstacle_pool as obstacle_area,
        )

        obstacle_pool = obstacle_area[map_name]
        if map_name == "map_fish":
            from kaiwu_env.conf import json_back_to_the_realm_v2_map_data_fish as map_data

        elif map_name == "map_cherry":
            from kaiwu_env.conf import json_back_to_the_realm_v2_map_data_cherry as map_data

        elif map_name == "map_butterfly":
            from kaiwu_env.conf import json_back_to_the_realm_v2_map_data_butterfly as map_data

        self.grid = None
        grid = np.array(map_data["Flags"]).reshape(map_data["Height"], map_data["Width"])
        self.map_name = map_name
        self.grid, self.obstacle_pool = grid.T, obstacle_pool

    def set_view_size(self, view_size):
        self.view_size = view_size

    def get_grid(self):
        return self.grid

    def get_obstacle_pool(self):
        return self.obstacle_pool

    def get_pos(self, n, flag):
        """
        在迷宫中均匀分布宝箱
        :param maze: 迷宫矩阵
        :param n: 目标点位
        :param flag: Start 2 End 3 宝箱 4 英雄 5 buff 6
        :return: 实际放置的点位
        """
        from kaiwu_env import np_random_generator

        if n == 0:
            return []

        treasure_id = []
        # 计算区块划分维度（向上取整的平方根）
        k = math.ceil(math.sqrt(n))
        block_size = 128 // k  # 计算基础区块尺寸

        # 收集所有包含可通行区域的区块
        valid_blocks = []
        for i in range(k):
            for j in range(k):
                # 计算区块边界（处理边缘余数）
                x_start = i * block_size
                x_end = x_start + block_size if i < k - 1 else 128
                y_start = j * block_size
                y_end = y_start + block_size if j < k - 1 else 128

                # 提取当前区块的可通行坐标
                block = self.grid[x_start:x_end, y_start:y_end]
                passable = np.argwhere(block == 1)
                if passable.size > 0:
                    valid_blocks.append((x_start, y_start, passable))

        # 在选定区块中放置宝箱
        all_valid_pos = []
        for x_base, y_base, positions in valid_blocks:
            for dx, dy in positions:
                x = x_base + dx
                y = y_base + dy
                valid = True
                for i in range(-1, 2):
                    for j in range(-1, 2):
                        if i == 0 and j == 0:
                            continue
                        nx, ny = x + i, y + j
                        if nx < 0 or ny < 0 or nx >= 128 or ny >= 128 or self.grid[nx][ny] != 1:
                            valid = False
                            break
                    if not valid:
                        break
                if valid and self.grid[x][y] == 1:
                    all_valid_pos.append([x, y])

        # 随机选择n个点
        if len(all_valid_pos) <= n:
            treasure_id = all_valid_pos
        else:
            indices = np_random_generator.choice(len(all_valid_pos), n, replace=False)
            treasure_id = [all_valid_pos[i] for i in indices]

        # for pos in treasure_id:
        #     self.update_grid(pos[0], pos[1], flag)
        return treasure_id

    def update_grid(self, x, z, flag):
        self.grid[x][z] = flag

    def update_grid_end(self, pos):
        self.update_grid(pos[0], pos[1], 3)

    def update_grid_start(self, pos):
        self.update_grid(pos[0], pos[1], 2)

    def update_grid_treasure(self, pos):
        self.update_grid(pos[0], pos[1], 4)

    def update_grid_hero(self, pos_x, pos_z):
        self.hero_pos = convert_pos_to_grid_pos(self.map_name, pos_x, pos_z)

        # x, z = self.hero_pos
        # self.update_grid(x, z, 5)

    def update_grid_buff(self, pos):
        self.update_grid(pos[0], pos[1], 6)

    def update_obstacle(self, obstacle):
        """
        更新障碍物位置。
        """
        if obstacle is not None and len(obstacle) > 0:
            for index in range(len(obstacle)):
                obstacle_pos = self.obstacle_pool[f"{obstacle[index]}"]
                for i in range(0, len(obstacle_pos), 2):
                    pair = obstacle_pos[i : i + 2]
                    # 障碍物本身就是x,z
                    self.grid[pair[0]][pair[1]] = 0
        return self.grid

    def get_local_grid_info(self):
        """
        获取局部地图信息。

        :param grid: 整个地图的网格信息, numpy 数组。转置后的gird
        :param center_pos: 中心点的坐标 (x, z)。
        :param view_size: 视野大小。
        :return: 局部地图信息, numpy 数组。
        """
        x, z = self.hero_pos
        height, width = self.grid.shape

        # 初始化局部地图
        self.local_grid = np.zeros((2 * self.view_size + 1, 2 * self.view_size + 1), dtype=self.grid.dtype)

        # 计算局部地图的边界
        x_min = max(0, x - self.view_size)
        x_max = min(width, x + self.view_size + 1)
        z_min = max(0, z - self.view_size)
        z_max = min(height, z + self.view_size + 1)

        # 计算局部地图在全局地图中的位置
        local_x_min = max(0, self.view_size - x)
        local_x_max = local_x_min + (x_max - x_min)
        local_z_min = max(0, self.view_size - z)
        local_z_max = local_z_min + (z_max - z_min)

        # 填充局部地图
        self.local_grid[local_x_min:local_x_max, local_z_min:local_z_max] = self.grid[x_min:x_max, z_min:z_max]

        return self.local_grid

    def show_map(self):
        height = len(self.grid)
        width = len(self.grid[0])

        # 为了和评估时看到的视角一样，这里将地图进行了一个转置和翻转
        grid = self.grid.T
        for i in reversed(range(height)):
            for j in range(width):
                if grid[i, j] == 0:
                    item = "x"
                elif grid[i, j] == 1:
                    item = " "
                elif grid[i, j] == 2:
                    item = "S"
                elif grid[i, j] == 3:
                    item = "E"
                elif grid[i, j] == 4:
                    item = "T"
                elif grid[i, j] == 5:
                    item = "H"
                elif grid[i, j] == 6:
                    item = "B"
                print(item, end=" ")
            print()
        print("------------------------------------------------------------")

    def show_local_map(self):
        height = len(self.local_grid)
        width = len(self.local_grid[0])

        # 为了和评估时看到的视角一样，这里将地图进行了一个转置和翻转
        grid = self.local_grid.T
        for i in reversed(range(height)):
            for j in range(width):
                if grid[i, j] == 0:
                    item = "x"
                elif grid[i, j] == 1:
                    item = " "
                elif grid[i, j] == 2:
                    item = "S"
                elif grid[i, j] == 3:
                    item = "E"
                elif grid[i, j] == 4:
                    item = "T"
                elif grid[i, j] == 5:
                    item = "H"
                elif grid[i, j] == 6:
                    item = "B"
                print(item, end=" ")
            print()
        print("------------------------------------------------------------")


if __name__ == "__main__":
    # grid, _, _ = map_init("arena/conf/back_to_the_realm_v2/map_path/fish.json")
    # legal_pos = get_legal_pos(grid)
    # show_map(grid)
    # show_local_view(grid, (29, 9), 7)

    # dist = []
    # for pos in legal_pos:
    #     dist.append(discrete_bfs_dist(pos, (29, 9)))

    # print(np.max(dist))
    """
    # 输出地图所有legal_pos相对于所有legal_pos最短路径距离
    i = 0
    all_dist_dict = {}
    for pos in legal_pos:
        if i % 10 == 0:
            print(f"############ i = {i} ############")
        state = pos[0] * 64 + pos[1]
        state_dist_dict = {}
        for pos2 in legal_pos:
            if pos2 == pos:
                state_dist_dict[state] = 0
            else:
                state2 = pos2[0] * 64 + pos2[1]
                dist = bfs_distance(grid, pos, pos2)
                state_dist_dict[state2] = dist
        all_dist_dict[state] = state_dist_dict
        i += 1

    with open("bfs_dist.json", "w") as f:
        json.dump(all_dist_dict, f)
    """

    map = Map("map_fish")
    map.update_obstacle([1])
    map.get_pos(2, 2)
    map.show_map()
