import datetime
import json
import os
import time

from arena_proto.arena2plat_pb2 import CampInfo, GameData, GameStatus
from arena_proto.back_to_the_realm_v2.arena2aisvr_pb2 import (
    AIServerRequest,
    AIServerResponse,
)
from arena_proto.back_to_the_realm_v2.custom_pb2 import (
    Action,
    Command,
    EndInfo,
    ExtraInfo,
    Frame,
    Frames,
    FrameState,
    GameInfo,
    Observation,
    Position,
    RealmObstacle,
    RealmHero,
    RealmOrgan,
    ScoreInfo,
    StartInfo,
    Map,
    MapInfo,
)
from arena_proto.back_to_the_realm_v2.game2arena_pb2 import StepFrameReq, StepFrameRsp
from google.protobuf.json_format import MessageToJson
from google.protobuf.timestamp_pb2 import Timestamp
import numpy as np

from kaiwu_env.back_to_the_realm_v2.utils import (
    _print_debug_log,
    convert_grid_pos_to_pos,
    get_game_info,
    get_hero_info,
    get_local_frame_state,
    get_organ_info,
    get_legal_act,
    polar_to_pos,
)
from kaiwu_env.conf import json_back_to_the_realm_v2_map_data_fish as map_data
from kaiwu_env.conf import yaml_arena
from kaiwu_env.conf import yaml_back_to_the_realm_v2_game as game_conf
from kaiwu_env.conf import yaml_back_to_the_realm_v2_treasure_path_crab as treasure_data
from kaiwu_env.env.protocol import BaseSkylarenaDataHandler
from kaiwu_env.conf import (
    json_back_to_the_realm_v2_map_data_obstacle_pool as obstacle_area,
)
from kaiwu_env.conf import (
    json_back_to_the_realm_v2_map_data_map_area as map_area,
)
from kaiwu_env.conf import (
    json_back_to_the_realm_v2_map_data_obstacle_outline as obstacle_outline,
)
from kaiwu_env.back_to_the_realm_v2.utils import float_to_int, check_coord_valid


class SkylarenaDataHandler(BaseSkylarenaDataHandler):
    def __init__(self, logger, monitor) -> None:
        self.logger = logger
        self.monitor = monitor

        # 下面是监控相关的变量
        self.last_time = 0
        self.total_treasures = None
        self.buff_cooldown = 0
        self.talent_cooldown = 0
        self.episode_cnt = 0
        self.last_episode_cnt = 0
        self.env_id = None

        # 评估或者测评下有些操作可以不做, 有利于提升性能
        self.is_eval_or_exam_mode = (yaml_arena.train_or_eval == "eval") or (yaml_arena.train_or_eval == "exam")

    def reset(self, usr_conf):
        if self.logger:
            self.logger.info(f"reset usr_conf is {usr_conf}")

        # 对局相关数据初始化
        self.game_status = 0
        self.frame_no = 0
        self.step_no = 0
        self.score = 0
        self.map_name = usr_conf["env_conf"]["map_name"]
        # 需要先校验地图名字, 否则下面的操作会因为获取地图名而失败
        if self.map_name not in game_conf.valid_map_name:
            self.logger.error(f"map_name error,  is {self.map_name}")
            return

        # 需要根据实际配置的map_name再确认下配置的map是不是正确的
        if self.map_name not in usr_conf["env_conf"]:
            self.logger.error(f"map_name {self.map_name} error, not in usr_conf")
            return

        self.max_steps = usr_conf["env_conf"][self.map_name]["max_step"]
        if not isinstance(self.max_steps, int):
            self.logger.error(f"max_steps字段必须是整数")
            return

        self.total_score = 0
        self.buff_count = 0
        # 障碍物
        self.obstacle_random = 1 if usr_conf["env_conf"][self.map_name]["obstacle_random"] == 1 else 0
        self.obstacle_count = (
            1 if self.obstacle_random == 1 else len(usr_conf["env_conf"][self.map_name]["obstacle_id"])
        )
        self.buff_random = 1 if usr_conf["env_conf"][self.map_name]["buff_random"] == 1 else 0
        self.obstacles = []

        # 已收集到的宝箱个数
        self.treasure_collected_count = 0
        self.treasure_score = 0
        self.treasure_data = treasure_data
        if self.is_eval_or_exam_mode:
            self.frames = Frames()

        self.speed_up = 0
        self.talent_count = 0

        self.hero_pos = None
        self.telent_tpye = 0
        self.telent_status = 0
        self.telent_cooldown = 0
        self.organs = list()

        self.treasure_random = 1 if usr_conf["env_conf"][self.map_name]["treasure_random"] == 1 else 0

        self.buff_cooldown = usr_conf["env_conf"][self.map_name]["buff_cooldown"]
        self.talent_cooldown = usr_conf["env_conf"][self.map_name]["talent_cooldown"]

        if not isinstance(self.buff_cooldown, int) or not isinstance(self.talent_cooldown, int):
            self.logger.error(f"buff_cooldown和talent_cooldown字段必须是整数")
            return

        # 获取当前的UTC时间
        now = datetime.datetime.utcnow()
        self.start_timestamp = Timestamp()
        self.start_timestamp.FromDatetime(now)

    def step(self, pb_stepframe_req, pb_aisvr_rsp):
        self.frame_no = pb_stepframe_req.game_info.step_no * 3 + 1
        self.step_no = pb_stepframe_req.game_info.step_no
        self.score = pb_stepframe_req.game_info.score
        self.total_score = pb_stepframe_req.game_info.total_score
        self.treasure_collected_count = pb_stepframe_req.game_info.treasure_collected_count
        self.treasure_score = pb_stepframe_req.game_info.treasure_score
        self.hero_pos = convert_grid_pos_to_pos(
            self.map_name,
            pb_stepframe_req.frame_state.heroes[0].pos.x,
            pb_stepframe_req.frame_state.heroes[0].pos.z,
        )
        self.speed_up = pb_stepframe_req.frame_state.heroes[0].speed_up
        self.buff_count = pb_stepframe_req.game_info.buff_count
        self.talent_count = pb_stepframe_req.game_info.talent_count
        self.telent_tpye = pb_stepframe_req.frame_state.heroes[0].talent.talent_type
        self.telent_status = pb_stepframe_req.frame_state.heroes[0].talent.status
        self.telent_cooldown = pb_stepframe_req.frame_state.heroes[0].talent.cooldown
        # self.organs = get_organ_info(
        #     self.map_name, pb_stepframe_req.frame_state, need_nature_pos=False
        # )

        if self.step_no == 1:
            self.env_id = pb_stepframe_req.env_id
            init_organs = get_organ_info(
                self.map_name, pb_stepframe_req.frame_state, need_nature_pos=True, is_local_view=False
            )
            self.start_info.organs.extend(init_organs)

            # 只有评估和测评模式下需要收集帧数据信息
            if self.is_eval_or_exam_mode:
                # 如果是第一步，构造第0步的初始数据
                import copy

                hero = copy.deepcopy(pb_stepframe_req.frame_state.heroes[0])
                game_info = copy.deepcopy(pb_stepframe_req.game_info)
                # hero坐标已经是原始坐标
                hero.pos.x = self.start_info.start.x
                hero.pos.z = self.start_info.start.z
                hero.speed_up = 0
                hero.talent.status = 1
                hero.talent.cooldown = 0
                game_info.talent_count = 0
                frame = Frame(
                    frame_no=1,
                    step_no=0,
                    hero=get_hero_info(self.map_name, hero),
                    organs=get_organ_info(
                        self.map_name,
                        pb_stepframe_req.frame_state,
                        need_nature_pos=True,
                        is_local_view=False,
                        is_first_frame=True,
                    ),
                    game_info=get_game_info(self.map_name, hero, game_info, 0),
                )
                self.frames.frames.append(frame)

        # 只有评估和测评模式下需要收集帧数据信息，需要场景真实坐标
        if self.is_eval_or_exam_mode:
            frame = Frame(
                frame_no=self.frame_no,
                step_no=self.step_no + 1,
                hero=get_hero_info(
                    self.map_name,
                    pb_stepframe_req.frame_state.heroes[0],
                    need_nature_pos=True,
                ),
                organs=get_organ_info(
                    self.map_name, pb_stepframe_req.frame_state, need_nature_pos=True, is_local_view=False
                ),
                game_info=get_game_info(
                    self.map_name,
                    pb_stepframe_req.frame_state.heroes[0],
                    pb_stepframe_req.game_info,
                    pb_stepframe_req.step_no,
                    need_nature_pos=True,
                ),
            )
            self.frames.frames.append(frame)

        # _print_debug_log(self=self, freq=1)

    def finish(self):
        # 存在容灾情况下会调用到finish函数, 打印错误日志, 故调整为warning级别
        if self.env_id == None:
            self.logger.warning("finish enter no env_id, so return")
            return

        self.episode_cnt += 1
        # 如果超过最大步数，需要额外处理
        self.game_status = 3 if (self.step_no + 1) >= self.max_steps else 1
        self.total_score = 0 if (self.step_no + 1) >= self.max_steps else self.total_score
        # step_no 截断
        self.step_no = min(self.step_no + 1, self.max_steps)

        if self.monitor:
            now = time.time()
            if now - self.last_time > game_conf.TIME_WINDOW and self.episode_cnt > self.last_episode_cnt:
                monitor_data = {
                    "finished_steps": self.step_no,
                    "score": int(self.score),
                    "total_score": int(self.total_score),
                    "collected_treasures": int(self.treasure_collected_count),
                    "treasure_score": int(self.treasure_score),
                    "buff_cnt": int(self.buff_count),
                    "talent_cnt": int(self.talent_count),
                    "max_steps": int(self.max_steps),
                    "treasure_random": self.treasure_random,
                    "total_treasures": int(self.total_treasures),
                    "obstacle_count": int(self.obstacle_count),
                    "obstacle_random": self.obstacle_random,
                    "buff_random": self.buff_random,
                    "episode_cnt": self.episode_cnt,
                    "buff_cooldown": int(self.buff_cooldown),
                    "talent_cooldown": int(self.talent_cooldown),
                }
                self.monitor.put_data({os.getpid(): monitor_data})
                self.last_time = now

                self.last_episode_cnt = self.episode_cnt
                self.logger.info(f"finish monitor_data is {monitor_data}")

        # 只有在评估模式下才会落平台数据
        if self.is_eval_or_exam_mode:
            log_folder = yaml_arena.platform_log_dir
            if not os.path.exists(log_folder):
                os.makedirs(log_folder)
            self.save_game_stat(f"{log_folder}/{self.env_id}.json")
            self.logger.info(f"save_game_stat success, file_path is {log_folder}/{self.env_id}.json")
        self.logger.info("data_handler finish success")

    def save_game_stat(self, file_path):
        """
        根据后端pb协议返回对局数据, 保存到json文件,不暴露给用户
        """
        end_info = EndInfo(
            frame=self.frame_no,
            step=self.step_no,
            total_score=self.total_score,
            treasure_collected_count=self.treasure_collected_count,
            treasure_score=self.treasure_score,
            buff_count=self.buff_count,
            talent_count=self.talent_count,
            obstacle_count=self.obstacle_count,
        )

        camp = CampInfo(
            camp_type="blue",
            camp_code="A",
            start_info=MessageToJson(
                self.start_info,
                including_default_value_fields=True,
                preserving_proto_field_name=True,
                use_integers_for_enums=True,
            ),
            end_info=MessageToJson(
                end_info,
                including_default_value_fields=True,
                preserving_proto_field_name=True,
                use_integers_for_enums=True,
            ),
        )
        json_messages = MessageToJson(
            self.frames,
            including_default_value_fields=True,
            preserving_proto_field_name=True,
            use_integers_for_enums=True,
        )

        # 获取当前的UTC时间
        now = datetime.datetime.utcnow()
        # 将当前时间转换为protobuf的Timestamp类型
        end_timestamp = Timestamp()
        end_timestamp.FromDatetime(now)

        output = GameData(
            name=self.env_id,
            project_code="back_to_the_realm_v2",
            status=self.game_status,
            camps=[camp],
            frames=json_messages,
            start_time=self.start_timestamp,
            end_time=end_timestamp,
        )

        # 将pb数据转换成json格式, 保存到文件
        with open(file_path, "w") as outfile:
            out_data = MessageToJson(
                output,
                including_default_value_fields=True,
                preserving_proto_field_name=True,
                use_integers_for_enums=True,
            )
            json.dump(json.loads(out_data), outfile, indent=4)

        # 在写完json文件后再写一个done文件，前面的文件名保持一致
        done_file = file_path.replace("json", "done")
        with open(done_file, "w") as done:
            done.writelines("done")

    def StepFrameReq2AISvrReq(self, pb_stepframe_req):
        """
        pb_stepframe_req 是已经反序列化后的StepFrameReq
        """

        result_code = pb_stepframe_req.result_code
        if result_code == 0:

            self.hero_pos = convert_grid_pos_to_pos(
                self.map_name,
                pb_stepframe_req.frame_state.heroes[0].pos.x,
                pb_stepframe_req.frame_state.heroes[0].pos.z,
            )
            # 第一帧刷新真实start_id和end_id和obstacle
            if pb_stepframe_req.step_no == 0:
                self.start_id = float_to_int(pb_stepframe_req.game_info.start_pos)
                self.end_id = float_to_int(pb_stepframe_req.game_info.end_pos)

                VALID_OBSTACLE_IDS = [1, 2, 3, 4, 5, 6]
                for obstacle in pb_stepframe_req.game_info.obstacle_id:
                    if obstacle not in VALID_OBSTACLE_IDS:
                        self.logger.error(f"阻挡物id是1到6, 设置的是 {obstacle}")
                        return
                    else:
                        self.obstacles.append(
                            RealmObstacle(
                                obstacle_id=obstacle,
                                outline_points=obstacle_outline[self.map_name][str(obstacle)],
                            )
                        )

                self.start_info = StartInfo(
                    start=convert_grid_pos_to_pos(
                        self.map_name,
                        self.start_id.x,
                        self.start_id.z,
                    ),
                    end=convert_grid_pos_to_pos(
                        self.map_name,
                        self.end_id.x,
                        self.end_id.z,
                    ),
                    map_info=Map(
                        map_name=self.map_name,
                        x_max=map_area[self.map_name]["x_max"],
                        z_max=map_area[self.map_name]["z_max"],
                        x_min=map_area[self.map_name]["x_min"],
                        z_min=map_area[self.map_name]["z_min"],
                    ),
                    obstacles=self.obstacles,
                    buff_cooldown=int(self.buff_cooldown),
                    talent_cooldown=int(self.talent_cooldown),
                )

            # 只在第一帧上报监控
            if pb_stepframe_req.step_no == 0 and self.monitor:
                # 去除掉buff的长度,organs - 1
                self.total_treasures = (
                    len(list(filter(lambda organ: organ.sub_type == 1, pb_stepframe_req.frame_state.organs)))
                    if self.total_treasures is None
                    else self.total_treasures
                )

            hero = pb_stepframe_req.frame_state.heroes[0]
            # 天赋技能状态
            talent_status = hero.talent.status

            frame_state = pb_stepframe_req.frame_state
            game_info = pb_stepframe_req.game_info

            # 处理局部视野
            view_size = game_conf.view

            local_frame_state = get_local_frame_state(self.map_name, frame_state, view_size)
            local_grid_info = game_info.map_info

            score_info = ScoreInfo(
                score=int(pb_stepframe_req.game_info.score),
                total_score=int(pb_stepframe_req.game_info.total_score),
                step_no=int(pb_stepframe_req.game_info.step_no),
                treasure_collected_count=int(pb_stepframe_req.game_info.treasure_collected_count),
                treasure_score=int(pb_stepframe_req.game_info.treasure_score),
                buff_count=int(pb_stepframe_req.game_info.buff_count),
                talent_count=int(pb_stepframe_req.game_info.talent_count),
            )

            observation = Observation(
                frame_state=local_frame_state,
                score_info=score_info,
                map_info=local_grid_info,
                legal_act=get_legal_act(talent_status),
            )

            return AIServerRequest(
                env_id=pb_stepframe_req.env_id,
                step_no=pb_stepframe_req.step_no,
                obs=observation,
                terminated=pb_stepframe_req.terminated,
                truncated=pb_stepframe_req.truncated,
                extra_info=ExtraInfo(
                    result_code=result_code,
                    result_message=pb_stepframe_req.result_message,
                    frame_state=frame_state,
                    game_info=game_info,
                ),
            ).SerializeToString()
        else:
            return AIServerRequest(
                env_id=pb_stepframe_req.env_id,
                step_no=pb_stepframe_req.step_no,
                obs=Observation(),
                terminated=pb_stepframe_req.terminated,
                truncated=pb_stepframe_req.truncated,
                extra_info=ExtraInfo(
                    result_code=result_code,
                    result_message=pb_stepframe_req.result_message,
                    frame_state=pb_stepframe_req.frame_state,
                    game_info=pb_stepframe_req.game_info,
                ),
            ).SerializeToString()

    def AISvrRsp2StepFrameRsp(self, pb_aisvr_rsp):
        position = polar_to_pos(
            self.hero_pos,
            pb_aisvr_rsp.action.move_dir,
            bool(pb_aisvr_rsp.action.use_talent),
            bool(self.speed_up),
            bool(self.telent_status),
        )

        command = Command(
            hero_id=1112,
            move_dir=pb_aisvr_rsp.action.move_dir,
            talent_type=pb_aisvr_rsp.action.use_talent,
            move_pos=position,
        )

        return StepFrameRsp(
            env_id=pb_aisvr_rsp.env_id,
            step_no=pb_aisvr_rsp.step_no,
            command=command,
            stop_game=1 if pb_aisvr_rsp.stop_env else 0,
        ).SerializeToString()
