import random
from time import sleep
import kaiwu_env
from tools.train_env_conf_validate import read_usr_conf

# from kaiwu_env.back_to_the_realm_v2.rules import *

kaiwu_env.setup(
    run_mode="entity",
    gamecore_type="SGWrapper",
    entity_type="cloak",
    aisvr_type="kaiwu_env",
)


STEP_COUNT = 8000
EPISODE_COUNT = 200

if __name__ == "__main__":
    # 使用kaiwu_envSDK需要两步:
    # 1. kaiwu_env.make("gorge_walk"), 实例化EnvProxy
    # 2. 以RL的标准方式使用env
    # set_logger()
    # init(EPISODE_COUNT)
    # init()
    # 初始化游戏环境
    env = kaiwu_env.make("back_to_the_realm_v2")

    # 配置文件读取和校验
    logger = None
    usr_conf = read_usr_conf("kaiwu_env/conf/back_to_the_realm_v2/train_env_conf.toml", logger)
    if usr_conf is None:
        raise ValueError("usr_conf is None, please check kaiwu_env/conf/back_to_the_realm_v2/train_env_conf.toml")

    for i in range(EPISODE_COUNT):

        print("-------------episode", i)
        # 进入对局
        sleep(0.5)
        obs = env.reset(usr_conf)  # obs = env.reset() aisvr可以传入指定的配置修改游戏配置
        for i in range(STEP_COUNT):
            action = 2

            step_no, _obs, terminated, truncated, env_info = env.step(action)

            if terminated or truncated:
                print("terminated :" + str(terminated))
                print("truncated : " + str(truncated))
                break
            obs = _obs
