from arena_proto.back_to_the_realm_v2.arena2aisvr_pb2 import (
    AIServerRequest,
    AIServerResponse,
)
from arena_proto.back_to_the_realm_v2.game2arena_pb2 import StepFrameReq, StepFrameRsp
from arena_proto.back_to_the_realm_v2.custom_pb2 import (
    Action,
    FrameState,
    GameInfo,
    MapInfo,
    RealmHero,
    Position,
    Talent,
    RealmOrgan,
    RelativePosition,
)
from kaiwu_env.back_to_the_realm_v2.utils import (
    convert_grid_pos_to_pos,
    convert_pos_to_grid_pos,
    get_nature_pos,
)
from kaiwu_env.env.protocol import BaseSkylarenaDataHandler
from google.protobuf.json_format import MessageToDict


class Parse_AIServerRequest:

    # AIServerRequest的encode应该由场景接入方在SkaylarenaDataHandler.StepFrameReq2AISvrReq中实现

    @staticmethod
    def decode(byte_aisvr_req):
        """
        将AIServerRequest反序列化后, 转换成用户调用env.reset或env.step期望获得的结构化数据
        转换逻辑由self.game_name决定(场景接入方实现), 该函数逻辑与env.step返回的数据强相关, 需要接入方仔细实现
        """
        aisvr_req = AIServerRequest()
        aisvr_req.ParseFromString(byte_aisvr_req)
        env_id = aisvr_req.env_id
        step_no = aisvr_req.step_no
        obs = MessageToDict(aisvr_req.obs, preserving_proto_field_name=True, including_default_value_fields=True)
        terminated = aisvr_req.terminated
        truncated = aisvr_req.truncated
        extra_info = MessageToDict(
            aisvr_req.extra_info, preserving_proto_field_name=True, including_default_value_fields=True
        )

        return env_id, step_no, obs, terminated, truncated, extra_info


class Parse_AIServerResponse:
    @staticmethod
    def encode(env_id, frame_no, action, stop_env):
        """
        用户env.step传入int或float类型的动作, 转换成AIServerResponse并序列化,
        转换逻辑由self.game_name决定(场景接入方实现), 该函数逻辑与env.step返回的参数强相关, 需要接入方仔细实现
        """
        # action 输入是 0 - 15 ，解析成为Action.move_dir, use_talent
        move_dir = action % 8
        use_talent = action // 8
        return AIServerResponse(
            env_id=env_id,
            step_no=frame_no,
            action=Action(move_dir=move_dir, use_talent=use_talent),
            stop_env=stop_env,
        ).SerializeToString()

    @staticmethod
    def decode(byte_aisvr_rsp):
        """
        Skylarena中调用, 用来解析aisvr传递过来的byte, 转换成pb, 一般业务方写成
        return AIServerResponse().ParseFromString(byte_aisvr_rsp)
        """
        pb_aisvr_rsp = AIServerResponse()
        pb_aisvr_rsp.ParseFromString(byte_aisvr_rsp)
        return pb_aisvr_rsp


class Parse_StepFrameReq:
    @staticmethod
    def encode(
        env_id,
        frame_no,
        frame_state,
        terminated,
        truncated,
        game_info,
        result_code,
        result_message,
    ):
        """
        将game.reset或game.step返回的每个游戏自定义的结构化数据转换成StepFrameReq并序列化
        转换逻辑由self.game_name决定(场景接入方实现), 该函数逻辑与game.step返回的数据强相关, 需要接入方仔细实现
        """
        if result_code == 0:
            current_game_info = game_info["game_info"]
            local_map_info = game_info["local_map_info"]

            heroes = []
            if hasattr(frame_state.heroes[0], "pos_x"):
                x, z = convert_pos_to_grid_pos(
                    current_game_info.map_name,
                    frame_state.heroes[0].pos_x,
                    frame_state.heroes[0].pos_z,
                )
                hero_pos = Position(x=x, z=z)
            else:
                x, z = convert_pos_to_grid_pos(
                    current_game_info.map_name,
                    frame_state.heroes[0].pos.x,
                    frame_state.heroes[0].pos.z,
                )
                hero_pos = Position(x=x, z=z)
            hero = RealmHero(
                hero_id=frame_state.heroes[0].hero_id,
                pos=hero_pos,
                speed_up=frame_state.heroes[0].speed_up,
                talent=Talent(
                    talent_type=frame_state.heroes[0].talent.talent_type,
                    status=frame_state.heroes[0].talent.status,
                    cooldown=frame_state.heroes[0].talent.cooldown,
                ),
                buff_remain_time=int(current_game_info.buff_remain_time),
            )

            heroes.append(hero)

            realmOrgans = []
            for organ in frame_state.organs:
                if hasattr(organ, "pos_x"):
                    x, z = convert_pos_to_grid_pos(current_game_info.map_name, organ.pos_x, organ.pos_z)
                    organ_pos = Position(x=x, z=z)
                else:
                    x, z = convert_pos_to_grid_pos(current_game_info.map_name, organ.pos.x, organ.pos.z)
                    organ_pos = Position(x=x, z=z)

                if hasattr(organ.r_pos, "relative_direction"):
                    relative_pos = RelativePosition(
                        direction=organ.r_pos.relative_direction,
                        l2_distance=organ.r_pos.relative_distance,
                    )

                else:
                    relative_pos = RelativePosition(
                        direction=organ.r_pos.direction,
                        l2_distance=organ.r_pos.l2_distance,
                    )

                temp = RealmOrgan(
                    sub_type=organ.sub_type,
                    config_id=organ.config_id,
                    status=organ.status,
                    pos=organ_pos,
                    cooldown=organ.cooldown,
                    relative_pos=relative_pos,
                )
                realmOrgans.append(temp)

            hero_grid_pos_x, hero_grid_pos_z = convert_pos_to_grid_pos(
                current_game_info.map_name,
                current_game_info.hero_pos_x,
                current_game_info.hero_pos_z,
            )
            _game_info = GameInfo(
                score=current_game_info.score,
                total_score=current_game_info.total_score,
                step_no=int(current_game_info.step_no),
                pos=Position(x=int(hero_grid_pos_x), z=int(hero_grid_pos_z)),
                start_pos=Position(x=current_game_info.start_id.x, z=current_game_info.start_id.z),
                end_pos=Position(x=current_game_info.end_id.x, z=current_game_info.end_id.z),
                treasure_collected_count=current_game_info.treasure_count,
                treasure_score=current_game_info.treasure_score,
                treasure_count=current_game_info.total_treasure_count - 1,
                buff_count=current_game_info.buff_count,
                talent_count=current_game_info.talent_count,
                buff_remain_time=int(current_game_info.buff_remain_time),
                buff_duration=int(current_game_info.buff_duration),
                obstacle_id=current_game_info.obstacle_id,
            )
            for row in local_map_info:
                map_info = MapInfo()
                map_info.values.extend(row)
                _game_info.map_info.append(map_info)
            return StepFrameReq(
                env_id=env_id,
                step_no=int(current_game_info.step_no),
                frame_state=FrameState(step_no=int(current_game_info.step_no), heroes=heroes, organs=realmOrgans),
                terminated=1 if terminated else 0,
                truncated=1 if truncated else 0,
                game_info=_game_info,
                result_code=result_code,
                result_message=result_message,
            ).SerializeToString()
        else:
            return StepFrameReq(
                env_id=env_id,
                step_no=0,
                frame_state=FrameState(),
                terminated=1 if terminated else 0,
                truncated=1 if truncated else 0,
                # 错误场景下不需要传递多余信息
                game_info=GameInfo(),
                result_code=result_code,
                result_message=result_message,
            ).SerializeToString()

    @staticmethod
    def decode(byte_stepframe_req):
        """
        Skaylarena中调用, 用来解析game传递过来的byte, 转换成pb, 一般业务方写成
        return StepFrameReq().ParseFromString(byte_stepframe_req)
        """
        pb_stepframe_req = StepFrameReq()
        pb_stepframe_req.ParseFromString(byte_stepframe_req)
        return pb_stepframe_req


class Parse_StepFrameRsp:

    # StepFrameRsp的encode应该由场景接入方在SkaylarenaDataHandler.AISvrRsp2StepFrameRsp中实现

    @staticmethod
    def decode(byte_game_rsp):
        """
        将StepFrameRsp反序列化得到pb数据, 将pb数据转换成能被game.step接受的输入(每个游戏自定义的结构化数据)
        转换逻辑由self.game_name决定(场景接入方实现), 该函数逻辑与game.step的参数强相关, 需要接入方仔细实现
        """
        game_rsp = StepFrameRsp()
        game_rsp.ParseFromString(byte_game_rsp)
        env_id = game_rsp.env_id
        step_no = game_rsp.step_no

        command = {
            "heroid": game_rsp.command.hero_id,
            "move_dir": game_rsp.command.move_dir,
            "talent_type": game_rsp.command.talent_type,
            "move_to_pos_x": game_rsp.command.move_pos.x,
            "move_to_pos_z": game_rsp.command.move_pos.z,
        }

        stop_game = bool(game_rsp.stop_game)
        return env_id, step_no, command, stop_game
