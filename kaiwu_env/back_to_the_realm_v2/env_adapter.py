import random
import numpy as np
from kaiwu_env.back_to_the_realm_v2.utils import Map, convert_pos_to_grid_pos
from kaiwu_env.conf import yaml_arena

if yaml_arena.gamecore_type == "SGWrapper" and yaml_arena.run_mode != "proxy":
    from sgwrapper import UsrConf, Command, GridPos
if yaml_arena.gamecore_type == "RPCWrapper":
    import msgpack
    import msgpack_numpy as m

    m.patch()
    from common_python.ipc.rpc_util import RpcUtil
    from arena_proto.back_to_the_realm_v2.server2game_pb2 import *
from kaiwu_env.conf import yaml_back_to_the_realm_v2_game as game_conf
from kaiwu_env.conf import yaml_back_to_the_realm_v2_game as GW2_CONFIG
from kaiwu_env.back_to_the_realm_v2.utils import *

import time
import sys


# UsrConf数据转换, 该函数由SGSceneWrapper, RPCSceneWrapper共用
def read_usr_conf(usr_conf):
    map_name = usr_conf["env_conf"]["map_name"]

    if map_name not in game_conf.valid_map_name:
        raise RuntimeError(f"{map_name} not in {game_conf.valid_map_name}")

    if map_name not in usr_conf["env_conf"]:
        raise RuntimeError(f"{map_name} not in usr_conf")

    map_config = usr_conf["env_conf"][map_name]
    start = map_config.get("start")
    if not check_coord_valid(start):
        raise RuntimeError(f"start {start} 坐标不合法")
    start = float_to_int(start)
    end = map_config.get("end")
    if not check_coord_valid(end):
        raise RuntimeError(f"end {end} 坐标不合法")
    end = float_to_int(end)
    treasure_id = map_config.get("treasure_pos")
    if len(treasure_id) > 0:
        for treasure in treasure_id:
            if not check_coord_valid(treasure):
                raise RuntimeError(f"treasure {treasure} 坐标不合法")
            treasure = float_to_int(treasure)

    talent_type = map_config.get("talent_type")
    treasure_count = map_config.get("treasure_count")
    treasure_random = map_config.get("treasure_random")
    obstacle_id = map_config.get("obstacle_id")
    obstacle_random = map_config.get("obstacle_random")
    buff = map_config.get("buff")
    if not check_coord_valid(buff):
        raise RuntimeError(f"buff {buff} 坐标不合法")
    buff = float_to_int(buff)
    max_step = map_config.get("max_step")
    start_random = map_config.get("start_random")
    end_random = map_config.get("end_random")
    buff_random = map_config.get("buff_random")
    buff_cooldown = map_config.get("buff_cooldown")
    talent_cooldown = map_config.get("talent_cooldown")

    return (
        start,
        end,
        treasure_id,
        talent_type,
        treasure_count,
        treasure_random,
        obstacle_id,
        buff,
        max_step,
        start_random,
        end_random,
        buff_random,
        obstacle_random,
        buff_cooldown,
        talent_cooldown,
        map_name,
    )


def back_to_the_realm_v2_check_user_conf_valid_partA(map_name, obstacle_id, obstacle_random):
    VALID_OBSTACLE_IDS = [1, 2, 3, 4, 5, 6]
    VALID_RANDOM_VALUES = [0, 1]

    # 检查随机字段
    for field_name, value in [
        ("obstacle_random", obstacle_random),
    ]:
        if value not in VALID_RANDOM_VALUES:
            return -1, f"{field_name}字段只能输入0或1, 设置的是{value}"

    if map_name not in game_conf.valid_map_name:
        return -2, f"{map_name}错误, 请勿修改map_name"

    if obstacle_random == 0:
        # 检查障碍物ID
        if not isinstance(obstacle_id, list):
            return -3, f"阻挡物应该是数组, 设置的是 {obstacle_id}"

        for id in obstacle_id:
            if id not in VALID_OBSTACLE_IDS:
                return -4, f"阻挡物id是1到6, 设置的是 {obstacle_id}"

        if len(obstacle_id) != len(set(obstacle_id)):
            return -5, f"阻挡物存在元素重复, 设置的是 {obstacle_id}"

    return 0, "ok"


def back_to_the_realm_v2_check_user_conf_valid_partB(
    start,
    end,
    treasure_id,
    talent_type,
    treasure_count,
    treasure_random,
    buff,
    max_step,
    start_random,
    end_random,
    buff_random,
    current_map,
    buff_cooldown,
    talent_cooldown,
):
    """
    重返秘境里公共的检测用户输入函数, 其规则如下:
    """
    grid = current_map.get_grid()

    # 常量定义
    MIN_COORD, MAX_COORD = 0, 127
    MIN_TREASURE_COUNT, MAX_TREASURE_COUNT = 0, 13
    MIN_STEP, MAX_STEP = 1, 2000
    MIN_BUFF_COOLDOWN, MAX_COOLDOWN = 1, 2000
    MIN_TALENT_COOLDOWN = 100
    VALID_RANDOM_VALUES = [0, 1]

    def is_valid_coordinate(coord):
        return MIN_COORD <= coord[0] <= MAX_COORD and MIN_COORD <= coord[1] <= MAX_COORD

    def is_on_obstacle(coord):
        return grid[coord[0]][coord[1]] == 0

    # 检查数值类型
    for field_name, value in [
        ("max_step", max_step),
        ("buff_cooldown", buff_cooldown),
        ("talent_cooldown", talent_cooldown),
        ("treasure_count", treasure_count),
        ("talent_type", talent_type),
    ]:
        if type(value) is not int:
            return -1, f"{field_name}字段必须是整数, 设置的是{value}"

    # 检查随机字段
    for field_name, value in [
        ("treasure_random", treasure_random),
        ("start_random", start_random),
        ("end_random", end_random),
        ("buff_random", buff_random),
    ]:
        if value not in VALID_RANDOM_VALUES:
            return -4, f"{field_name}字段只能输入0或1, 设置的是{value}"

    for field_name, value in [
        ("start", start),
        ("end", end),
        ("buff", buff),
        ("treasure_id", treasure_id),
    ]:
        if not isinstance(value, list):
            return -1, f"{field_name}字段必须是列表, 设置的是{value}"

    # 检查宝箱设置
    if treasure_random == 0:
        if not isinstance(treasure_id, list):
            return -5, f"treasure_id字段必须是数组, 设置的是{treasure_id}"

        if len(treasure_id) > 13:
            return -6, f"treasure_id数组大小需要小于等于13, 设置的是{len(treasure_id)}"

        sublist_tuples = [tuple(sublist) for sublist in treasure_id]
        if len(sublist_tuples) != len(set(sublist_tuples)):
            return -7, "宝箱位置设置重复"

        for item in treasure_id:
            if not is_valid_coordinate(item):
                return -8, f"treasure_id中元素范围应该为{MIN_COORD}-{MAX_COORD}, 设置的是 {item}"
            item = float_to_int(item)
            if is_on_obstacle(item):
                return -9, "宝箱不能设置到障碍物上"

        if start_random == 0 and start in treasure_id:
            return (
                -10,
                f"treasure_id不能包括起点, 设置的是 {treasure_id}, start {start}",
            )

        if end_random == 0 and end in treasure_id:
            return (
                -10,
                f"treasure_id不能包括终点, 设置的是 {treasure_id}, end {end}",
            )

        if buff_random == 0 and buff in treasure_id:
            return (
                -10,
                f"treasure_id不能包括buff, 设置的是 {treasure_id}, buff {buff}",
            )

    elif treasure_count not in range(MIN_TREASURE_COUNT, MAX_TREASURE_COUNT + 1):
        return -11, f"treasure_count应为{MIN_TREASURE_COUNT}-{MAX_TREASURE_COUNT}, 设置的是 {treasure_count}"

    # 检查起点和终点
    if start_random == 0 and end_random == 0:
        if start == end:
            return -12, "终点起点不应重复"

    # buff不应该与终点或者起点重合
    if start_random == 0 and buff_random == 0:
        if start == buff:
            return -13, "起点和buff不应重复"

    if end_random == 0 and buff_random == 0:
        if end == buff:
            return -14, "终点和buff不应重复"

    start = float_to_int(start)
    end = float_to_int(end)
    if start_random == 0:
        if len(start) != 2:
            return -15, "起点不应该为空或者超过2个元素"
        if not is_valid_coordinate(start):
            return -16, f"start数组元素超过限制, 设置的是{start}"
        if is_on_obstacle(start):
            return -17, "起点不能在障碍物上"
    if end_random == 0:
        if len(end) != 2:
            return -18, "终点不应该为空或者超过2个元素"
        if not is_valid_coordinate(end):
            return -19, f"end数组元素超过限制, 设置的是{end}"
        if is_on_obstacle(end):
            return -20, "终点不能在障碍物上"

    # 检查天赋类型
    if talent_type not in [1]:
        return -21, f"talent_type字段只能输入1, 设置的是 {talent_type}"

    # 检查buff位置
    if buff_random == 0:
        if len(buff) == 0:
            return -22, f"buff数组设置为空, 设置的是{buff}"

        if not is_valid_coordinate(buff):
            return -23, f"buff数组元素超过限制, 设置的是{buff}"

        if is_on_obstacle(buff):
            return -24, f"buff不应该在障碍物上, 设置的是{buff}"

    # 检查最大步数
    if not (MIN_STEP <= max_step <= MAX_STEP):
        return -25, f"max_step 需要设置大于等于{MIN_STEP}并且小于等于{MAX_STEP}, 设置的是 {max_step}"

    # 校验 buff_cooldown
    if not (MIN_BUFF_COOLDOWN <= buff_cooldown <= MAX_COOLDOWN):
        return (
            -26,
            f"buff_cooldown需要设置大于等于{MIN_BUFF_COOLDOWN}并且小于等于{MAX_COOLDOWN}, 设置的是 {buff_cooldown}",
        )

    # 校验 talent_cooldown
    if not (MIN_TALENT_COOLDOWN <= talent_cooldown <= MAX_COOLDOWN):
        return (
            -27,
            f"talent_cooldown需要设置大于等于{MIN_TALENT_COOLDOWN}并且小于等于{MAX_COOLDOWN}, 设置的是 {talent_cooldown}",
        )

    return 0, "ok"


class SGSceneWrapper:
    def __init__(self, env, logger) -> None:
        self.logger = logger
        self.env = env
        self.env.init()

    def reset(self, env_id, usr_conf):

        result_code = 0
        result_message = "ok"
        try:
            (
                start,
                end,
                treasure_id,
                talent_type,
                treasure_count,
                treasure_random,
                obstacle_id,
                buff,
                max_step,
                start_random,
                end_random,
                buff_random,
                obstacle_random,
                buff_cooldown,
                talent_cooldown,
                map_name,
            ) = read_usr_conf(usr_conf)
        except Exception as e:
            result_code = -100
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        # 异常情况下就不需要往下走逻辑了
        if result_code != 0:
            return env_id, -1, None, True, True, None, result_code, result_message

        try:
            result_code, result_message = back_to_the_realm_v2_check_user_conf_valid_partA(
                map_name, obstacle_id, obstacle_random
            )
        except Exception as e:
            result_code = -101
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        # 异常情况下就不需要往下走逻辑了
        if result_code != 0:
            return env_id, -1, None, True, True, None, result_code, result_message

        self.map = Map()
        self.map.reset_data(map_name)
        self.map.set_view_size(game_conf.view)
        self.env.set_map(map_name)

        if obstacle_random == 1:
            from kaiwu_env import np_random_generator

            obstacle_id = [np_random_generator.randint(1, 6 + 1)]
            if self.logger:
                self.logger.info(f"obstacle_id random is {obstacle_id}")

        # 先排除阻挡位置
        self.map.update_obstacle(obstacle_id)

        try:
            # FIXME:合法校验
            result_code, result_message = back_to_the_realm_v2_check_user_conf_valid_partB(
                start,
                end,
                treasure_id,
                talent_type,
                treasure_count,
                treasure_random,
                buff,
                max_step,
                start_random,
                end_random,
                buff_random,
                self.map,
                buff_cooldown,
                talent_cooldown,
            )
        except Exception as e:
            result_code = -102
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        # 异常情况下就不需要往下走逻辑了
        if result_code != 0:
            return env_id, -1, None, True, True, None, result_code, result_message

        try:

            if start_random == 1 and end_random == 1:
                start, _ = self.map.get_pos(2, 2)
                _, end = self.map.get_pos(2, 3)
                self.map.update_grid_start(start)
                self.map.update_grid_end(end)
                if self.logger:
                    self.logger.info(f"start random pos is {start}, end random pos is {end}")

            elif start_random == 1:
                self.map.update_grid_end(end)
                start, _ = self.map.get_pos(2, 3)
                self.map.update_grid_start(start)
                if self.logger:
                    self.logger.info(f"start random pos is {start}")
            elif end_random == 1:
                self.map.update_grid_start(start)
                _, end = self.map.get_pos(2, 3)
                self.map.update_grid_end(end)
                if self.logger:
                    self.logger.info(f"end random pos is {end}")
            else:
                self.map.update_grid_start(start)
                self.map.update_grid_end(end)

            if treasure_random == 1:
                treasure_id = self.map.get_pos(treasure_count, 4)
                for treasure in treasure_id:
                    self.map.update_grid_treasure(treasure)
                if self.logger:
                    self.logger.info(f"treasure random pos is {treasure_id}")
            else:
                for treasure in treasure_id:
                    self.map.update_grid_treasure(treasure)

            if buff_random == 1:
                buff, _ = self.map.get_pos(2, 6)
                self.map.update_grid_buff(buff)
                if self.logger:
                    self.logger.info(f"buff random pos is {buff}")
            else:
                self.map.update_grid_buff(buff)
            # self.map.show_map()

            # 1 step = 3 frame, reset自动会推一帧
            def grid_pos(pos):
                return GridPos(pos[0], pos[1])

            usr_conf = UsrConf(
                start_pos_id=grid_pos(start),
                end_pos_id=grid_pos(end),
                treasure_pos_id=[grid_pos(item) for item in treasure_id],
                buff_pos_id=grid_pos(buff),
                talent_id=talent_type,
                max_frame=int(max_step) * 3 + 1,
                buff_cd=buff_cooldown,
                talent_cd=talent_cooldown,
                obstacle_id=obstacle_id,
            )

            game_frame = self.env.reset(env_id, usr_conf)

            (
                env_id,
                frame_no,
                _frame_state,
                terminated,
                truncated,
                game_info,
            ) = self.__parse_game_frame(game_frame)

            # map已经是xz,这里不需要转置
            self.map.update_grid_hero(game_frame.frame_state.heroes[0].pos_x, game_frame.frame_state.heroes[0].pos_z)

            local_map_info = self.map.get_local_grid_info()
            extra_info = {"game_info": game_info, "local_map_info": local_map_info}

            # self.map.show_local_map()
            return (
                env_id,
                frame_no,
                _frame_state,
                terminated,
                truncated,
                extra_info,
                result_code,
                result_message,
            )
        except Exception as e:
            result_code = -103
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        return (env_id, -1, None, True, True, None, result_code, result_message)

    def step(self, env_id, frame_no, command, stop_game):

        result_code = 0
        result_message = "ok"

        rust_command = Command(
            heroid=command["heroid"],
            move_dir=command["move_dir"],
            talent_type=command["talent_type"],
            move_to_pos_x=command["move_to_pos_x"],
            move_to_pos_z=command["move_to_pos_z"],
        )

        try:
            game_frame = self.env.step(env_id, frame_no, rust_command, stop_game)
            (
                env_id,
                frame_no,
                _frame_state,
                terminated,
                truncated,
                game_info,
            ) = self.__parse_game_frame(game_frame)

            # map已经是xz,这里不需要转置
            self.map.update_grid_hero(game_frame.frame_state.heroes[0].pos_x, game_frame.frame_state.heroes[0].pos_z)

            local_map_info = self.map.get_local_grid_info()
            extra_info = {"game_info": game_info, "local_map_info": local_map_info}

            # self.map.show_local_map()
            # 保存abs文件
            if terminated or truncated or stop_game:
                if yaml_arena.train_or_eval == "eval":
                    self.env.save_abs()

            return (
                env_id,
                frame_no,
                _frame_state,
                terminated,
                truncated,
                extra_info,
                result_code,
                result_message,
            )
        except Exception as e:
            result_code = -1
            result_message = str(e)
            self.logger.exception(f"step error, message is {str(e)}")

        return (env_id, -1, None, True, True, None, result_code, result_message)

    def __parse_game_frame(self, game_frame):
        env_id, frame_no, _frame_state, terminated, truncated, game_info = (
            game_frame.game_id,
            game_frame.frame_no,
            game_frame.frame_state,
            game_frame.terminated,
            game_frame.truncated,
            game_frame.game_info,
        )
        return env_id, frame_no, _frame_state, terminated, truncated, game_info


class RPCSceneWrapper:
    def __init__(self, env, logger) -> None:
        self.logger = logger

        if not isinstance(env, dict):
            raise RuntimeError("wrong env_type, please check conf")

        # 采用TCP连接和gamecore通信
        self.host = env["rpc_host"]
        self.port = env["rpc_port"]

        self.rpc_util = RpcUtil(self.host, self.port, logger)
        self.rpc_util.connect()

    def reset(self, env_id, usr_conf):

        result_code = 0
        result_message = "ok"

        try:
            (
                start,
                end,
                treasure_id,
                talent_type,
                treasure_count,
                treasure_random,
                obstacle_id,
                buff,
                max_step,
                start_random,
                end_random,
                buff_random,
                obstacle_random,
                buff_cooldown,
                talent_cooldown,
                map_name,
            ) = read_usr_conf(usr_conf)
        except Exception as e:
            result_code = -100
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        # 异常情况下就不需要往下走逻辑了
        if result_code != 0:
            return env_id, -1, None, True, True, None, result_code, result_message

        try:
            result_code, result_message = back_to_the_realm_v2_check_user_conf_valid_partA(
                map_name, obstacle_id, obstacle_random
            )
        except Exception as e:
            result_code = -101
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        # 异常情况下就不需要往下走逻辑了
        if result_code != 0:
            return env_id, -1, None, True, True, None, result_code, result_message

        self.map = Map()
        self.map.reset_data(map_name)
        self.map.set_view_size(game_conf.view)

        if obstacle_random == 1:
            from kaiwu_env import np_random_generator

            obstacle_id = [np_random_generator.randint(1, 6 + 1)]
            self.logger.info(f"obstacle_id random is {obstacle_id}")

        # 先排除阻挡位置
        self.map.update_obstacle(obstacle_id)

        try:
            # 合法校验
            result_code, result_message = back_to_the_realm_v2_check_user_conf_valid_partB(
                start,
                end,
                treasure_id,
                talent_type,
                treasure_count,
                treasure_random,
                buff,
                max_step,
                start_random,
                end_random,
                buff_random,
                self.map,
                buff_cooldown,
                talent_cooldown,
            )
        except Exception as e:
            result_code = -101
            result_message = str(e)
            self.logger.exception(f"reset error, message is {str(e)}")

        # 异常情况下就不需要往下走逻辑了
        if result_code != 0:
            return env_id, -1, None, True, True, None, result_code, result_message

        if start_random == 1 and end_random == 1:
            start, _ = self.map.get_pos(2, 2)
            _, end = self.map.get_pos(2, 3)
            self.logger.info(f"start random pos is {start}, end random pos is {end}")

        elif start_random == 1:
            self.map.update_grid_end(end)
            start, _ = self.map.get_pos(2, 3)
            self.logger.info(f"start random pos is {start}")
        elif end_random == 1:
            self.map.update_grid_start(start)
            _, end = self.map.get_pos(2, 3)
            self.logger.info(f"end random pos is {end}")
        else:
            self.map.update_grid_start(start)
            self.map.update_grid_end(end)

        if treasure_random == 1:
            treasure_id = self.map.get_pos(treasure_count, 4)
            self.logger.info(f"treasure random pos is {treasure_id}")
        else:
            for treasure in treasure_id:
                self.map.update_grid_treasure(treasure)

        if buff_random == 1:
            buff, _ = self.map.get_pos(2, 6)
            self.logger.info(f"buff random pos is {buff}")
        else:
            self.map.update_grid_buff(buff)

        message = {
            "message_type": "reset",
            "game_id": env_id,
            "map_name": map_name,
            "user_conf_start": start,
            "user_conf_end": end,
            "user_conf_treasure_id": [item for item in treasure_id],
            "user_conf_buff_pos_id": buff,
            "user_conf_talent_type": talent_type,
            "user_conf_max_frame": max_step * 3 + 1,
            "user_conf_obstacle_id": obstacle_id,
            "user_conf_buff_cd": buff_cooldown,
            "user_conf_talent_cd": talent_cooldown,
        }
        message = msgpack.packb(message)

        # 发送信息
        if not self.rpc_util.send_all(message):
            # 此时由于网络通信出现的异常返回的result_code大于0, 便于调用者重试
            result_code = 100
            result_message = f"reset, self.rpc_util.send_all error"
            self.logger.exception(f"reset error, message is {result_message}")
            return (env_id, 0, None, True, False, None, result_code, result_message)

        # 接收信息
        response = self.rpc_util.recv_with_length()
        if response is None:
            # 此时由于网络通信出现的异常返回的result_code大于0, 便于调用者重试
            result_code = 101
            result_message = f"reset, self.rpc_util.recv_with_length error"
            self.logger.exception(f"reset error, message is {result_message}")
            return (env_id, 0, None, True, False, None, result_code, result_message)

        # 反序列化
        game_frame = GameFrame()

        game_frame.ParseFromString(response)

        (
            env_id,
            frame_no,
            _frame_state,
            terminated,
            truncated,
            game_info,
        ) = self.__parse_game_frame(game_frame)

        # map已经是xz,这里不需要转置
        self.map.update_grid_hero(game_frame.frame_state.heroes[0].pos.x, game_frame.frame_state.heroes[0].pos.z)

        local_map_info = self.map.get_local_grid_info()
        extra_info = {"game_info": game_info, "local_map_info": local_map_info}

        return (
            env_id,
            frame_no,
            _frame_state,
            terminated,
            truncated,
            extra_info,
            result_code,
            result_message,
        )

    def step(self, env_id, frame_no, command, stop_game):

        result_code = 0
        result_message = "ok"

        message = {
            "message_type": "step",
            "game_id": env_id,
            "frame_no": frame_no,
            "command": [
                command["heroid"],
                command["move_dir"],
                command["talent_type"],
                command["move_to_pos_x"],
                command["move_to_pos_z"],
            ],
            "stop_game": stop_game,
        }
        message = msgpack.packb(message)

        # 发送信息
        if not self.rpc_util.send_all(message):
            # 此时由于网络通信出现的异常返回的result_code大于0, 便于调用者重试
            result_code = 200
            result_message = f"step, self.rpc_util.send_all error"
            self.logger.exception(f"step error, message is {result_message}")
            return (env_id, 0, None, True, False, None, result_code, result_message)

        # 接收信息
        response = self.rpc_util.recv_with_length()
        if response is None:
            # 此时由于网络通信出现的异常返回的result_code大于0, 便于调用者重试
            result_code = 201
            result_message = f"step, self.rpc_util.recv_with_length error"
            self.logger.exception(f"step error, message is {result_message}")
            return (env_id, 0, None, True, False, None, result_code, result_message)

        # 反序列化
        game_frame = GameFrame()

        game_frame.ParseFromString(response)

        (
            env_id,
            frame_no,
            _frame_state,
            terminated,
            truncated,
            game_info,
        ) = self.__parse_game_frame(game_frame)
        # map已经是xz,这里不需要转置
        self.map.update_grid_hero(game_frame.frame_state.heroes[0].pos.x, game_frame.frame_state.heroes[0].pos.z)

        local_map_info = self.map.get_local_grid_info()
        extra_info = {"game_info": game_info, "local_map_info": local_map_info}

        # self.map.show_local_map()

        return (
            env_id,
            frame_no,
            _frame_state,
            terminated,
            truncated,
            extra_info,
            result_code,
            result_message,
        )

    def __parse_game_frame(self, game_frame):
        env_id, frame_no, _frame_state, terminated, truncated, game_info = (
            game_frame.game_id,
            game_frame.frame_no,
            game_frame.frame_state,
            game_frame.terminated,
            game_frame.truncated,
            game_frame.game_info,
        )
        return env_id, frame_no, _frame_state, terminated, truncated, game_info
