from kaiwu_env.env.protocol import Parse_StepFrameRsp, Parse_StepFrameReq
from random import randint
from pickle import dumps, loads
from kaiwu_env.utils.strategy import strategy_selector
from kaiwu_env.conf import yaml_arena
from common_python.utils.common_func import get_uuid
from common_python.ipc.zmq_util import ZmqServer


class EnvEntity:
    def __init__(self, env, game_name, scene_name="default_scene", logger=None, monitor=None) -> None:
        self.game_name = game_name
        self.env = strategy_selector(
            "scene_wrapper", yaml_arena.gamecore_type, env, game_name, yaml_arena.gamecore_type, logger
        )

        ip = yaml_arena.entity_zmq_url_ip
        port = yaml_arena.entity_zmq_url_port

        self.zmq_server = ZmqServer(ip, port)
        self.zmq_server.bind()

        self.logger = logger
        self.monitor = monitor

        # 计算当前进程的运行次数
        self.process_run_count = 0

        self.logger.info(f"entity, zmq_server bind at {ip}:{port} success")

    def reset(self):

        # client_id是专门为zmq通信的, 故不要修改
        client_id, message = self.zmq_server.recv(block=True, binary=False)
        if not message:
            return False

        message_type = message["message_type"]
        if message_type != "reset":
            self.logger.error(f"entity in reset recv message from env not reset message, so return")
            return False
        usr_conf = message["message_data"]

        try:
            usr_conf = loads(usr_conf)
        except Exception as e:
            self.logger.warning(f"entity recv message from env error, so return")
            return False

        # 每局游戏开始前设置个独立的env_id
        env_id = str(get_uuid())

        env_id = (
            yaml_arena.eval_game_id
            if yaml_arena.train_or_eval == "eval" or yaml_arena.train_or_eval == "exam"
            else env_id
        )

        # env.reset拿到游戏的信息
        (
            env_id,
            frame_no,
            _frame_state,
            terminated,
            truncated,
            game_info,
            result_code,
            result_message,
        ) = self.env.reset(env_id, usr_conf)

        # 将game.reset或game.step返回的每个游戏自定义的结构化数据转换成StepFrameReq并序列化
        # 转换逻辑由self.game_name决定（场景接入方实现）, 该函数逻辑与game.step返回的数据强相关, 需要接入方仔细实现
        byte_stepframe_req = Parse_StepFrameReq.encode(
            self.game_name,
            **{
                "env_id": env_id,
                "frame_no": frame_no,
                "frame_state": _frame_state,
                "terminated": terminated,
                "truncated": truncated,
                "game_info": game_info,
                "result_code": result_code,
                "result_message": result_message,
            },
        )

        self.logger.info(f"entity reset success, usr_conf is {usr_conf}")

        self.process_run_count += 1
        return self.zmq_server.send(str(client_id), byte_stepframe_req, binary=True)

    def step(self):
        # 收到StepFrameRsp
        try:
            client_id, message = self.zmq_server.recv(block=True, timeout=10, binary=False)
        except Exception as e:
            # 如果超过十秒没有出现step的交互请求, 开始新的reset链接新的请求
            self.logger.warning(f"entity recv message from env timeout 10s, so return")
            return True
        # 如果是空, 则开始下一轮的step而不是退出
        if not message:
            return False

        message_type = message["message_type"]
        if message_type != "step":
            self.logger.error(f"entity in step recv message from env not step message, so return")
            return True
        byte_stepframe_rsp = message["message_data"]

        # 将StepFrameRsp反序列化得到pb数据，将pb数据转换成能被game.step接受的输入(每个游戏自定义的结构化数据)
        # 转换逻辑由self.game_name决定（场景接入方实现), 该函数逻辑与game.step的参数强相关, 需要接入方仔细实现
        env_id, frame_no, command, stop_game = Parse_StepFrameRsp.decode(self.game_name, byte_stepframe_rsp)

        # 调用真实的游戏step, 推动一帧
        env_id, frame_no, _frame_state, terminated, truncated, game_info, result_code, result_message = self.env.step(
            env_id, frame_no, command, stop_game
        )

        # 将game.reset或game.step返回的每个游戏自定义的结构化数据转换成StepFrameReq并序列化
        # 转换逻辑由self.game_name决定（场景接入方实现）, 该函数逻辑与game.step返回的数据强相关, 需要接入方仔细实现
        byte_stepframe_req = Parse_StepFrameReq.encode(
            self.game_name,
            **{
                "env_id": env_id,
                "frame_no": frame_no,
                "frame_state": _frame_state,
                "terminated": terminated,
                "truncated": truncated,
                "game_info": game_info,
                "result_code": result_code,
                "result_message": result_message,
            },
        )

        # 对局退出的条件
        stop = terminated or truncated or stop_game

        # 注意此时byte_stepframe_req已经是序列化后的不再需要binary设置为True
        self.zmq_server.send(str(client_id), byte_stepframe_req, binary=True)

        self.process_run_count += 1
        return stop

    def run_once(self):
        flag = self.reset()
        if flag == False:
            self.logger.warning("reset failed, so entity run_once return")
            return

        while True:
            flag = self.step()
            if flag:
                self.logger.info(f"EnvEntity run one episode done, process_run_count is {self.process_run_count}")
                return
