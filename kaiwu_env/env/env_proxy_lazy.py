import os
from kaiwu_env.env.base_env import BaseEnv
from kaiwu_env.env.protocol import Parse_AIServerRequest, Parse_AIServerResponse
from pickle import dumps, loads
from kaiwu_env.conf import yaml_arena
from common_python.utils.common_func import get_uuid
from common_python.ipc.zmq_util import ZmqClient
from common_python.config.config_control import CONFIG
from kaiwu_env.utils.common_func import extract_ip_port

# 返回错误码和错误字符串, 支持按照字典或者属性读取, 规避各个项目的访问方法不一致问题
class ExtraInfo:
    def __init__(self, result_code, result_message):
        self.result_code = result_code
        self.result_message = result_message

    def __getitem__(self, key):
        # 通过字典键访问时触发
        return self.__dict__[key]  # 直接返回属性值

    def __setitem__(self, key, value):
        # 支持字典式赋值（可选）
        self.__dict__[key] = value

    def get(self, key, default=None):
        # 实现类似字典的 get 方法（可选）
        return self.__dict__.get(key, default)


class EnvProxy(BaseEnv):
    def __init__(self, game_name, scene_name="default_scene", logger=None, monitor=None) -> None:
        self.game_name = game_name
        self.scene_name = scene_name

        # 采用zmq_client时需要设置client_id
        self.client_id = get_uuid()

        # 配置解析, 因为zmq里需要配置项
        file_path = f"{os.getcwd()}/kaiwu_env/conf/configure_system.toml"
        CONFIG.parse_configure(["main_system"], file_path, ["main_system"], file_path)

        # 兼容现有代码, 最终需要解决
        skylarena_url = yaml_arena.skylarena_url
        self.ip, self.port = extract_ip_port(skylarena_url)

        self.zmq_client = ZmqClient(str(self.client_id), self.ip, self.port)
        self.zmq_client.connect()

        self.logger = logger

        self.need_connect = False

        self.logger.info(f"env proxy, zmq_client connect to {self.ip}:{self.port} success, client_id {self.client_id}")

    def reset(self, usr_conf={}):
        b_usr_conf = dumps(usr_conf)

        if self.need_connect:
            self.client_id = get_uuid()
            self.zmq_client = ZmqClient(str(self.client_id), self.ip, self.port)
            self.zmq_client.connect()
            self.need_connect = False
            self.logger.info(
                f"env proxy, zmq_client reconnect to {self.ip}:{self.port} success, client_id {self.client_id}"
            )

        # 增加消息类型, 目的是解决reset和step可能乱序的问题
        message = {"message_type": "reset", "message_data": b_usr_conf}

        try:
            # aisrv --> env方向通信
            self.zmq_client.send(message, binary=False)

            # env --> aisrv方向通信
            while True:
                byte_aisvr_req = self.zmq_client.recv(block=True, timeout=60, binary=True)
                if byte_aisvr_req:
                    break
        except Exception as e:
            self.logger.warning(f"aisrv recv message from env timeout 60s, so return")
            self.need_connect = True

            # 这里需要构造下extra_info, 否则业务调用extra_info.result_code会因为extra_info为None而报错
            # 属于系统错误, 故返回错误码大于0
            result_code = 1
            result_message = "env proxy reset timeout, so return"
            extra_info = ExtraInfo(result_code, result_message)
            return None, extra_info

        # 将AIServerRequest反序列化后, 转换成用户调用env.reset或env.step期望获得的结构化数据
        # 转换逻辑由self.game_name决定（场景接入方实现）, 该函数逻辑与env.step返回的数据强相关, 需要接入方仔细实现
        (
            self.env_id,
            self.frame_no,
            self.obs,
            self.terminated,
            self.truncated,
            self.env_info,
        ) = Parse_AIServerRequest.decode(self.game_name, byte_aisvr_req)
        self.logger.info("EnvProxy reset one game success")
        return self.obs, self.env_info

    def step(self, act, stop_game=False):
        # 用户env.step传入int或float类型的动作，转换成AIServerResponse并序列化,
        # 转换逻辑由self.game_name决定（场景接入方实现）, 该函数逻辑与env.step返回的参数强相关, 需要接入方仔细实现
        byte_aisvr_rsp = Parse_AIServerResponse.encode(
            self.game_name, self.env_id, self.frame_no, act, self.terminated or self.truncated or stop_game
        )

        # 增加消息类型, 目的是解决reset和step可能乱序的问题
        message = {"message_type": "step", "message_data": byte_aisvr_rsp}

        # 将AIServerResponse序列化后的数据发送给skylarena, aisrv --> env方向通信
        self.zmq_client.send(message, binary=False)

        # env --> aisrv方向通信
        try:
            while True:
                byte_aisvr_req = self.zmq_client.recv(block=True, timeout=10, binary=True)
                if byte_aisvr_req:
                    break
        except Exception as e:
            self.logger.warning(f"EnvProxy step timeout, so return")
            self.need_connect = True

            # 这里需要构造下extra_info, 否则业务调用extra_info.result_code会因为extra_info为None而报错
            # 属于系统错误, 故返回错误码大于0
            result_code = 2
            result_message = "env proxy step timeout, so return"
            extra_info = ExtraInfo(result_code, result_message)
            return -1, None, True, True, extra_info

        # 将AIServerRequest反序列化后, 转换成用户调用env.reset或env.step期望获得的结构化数据
        # 转换逻辑由self.game_name决定（场景接入方实现）, 该函数逻辑与env.step返回的数据强相关, 需要接入方仔细实现
        (
            self.env_id,
            self.frame_no,
            self.obs,
            self.terminated,
            self.truncated,
            self.env_info,
        ) = Parse_AIServerRequest.decode(self.game_name, byte_aisvr_req)

        return self.frame_no, self.obs, self.terminated, self.truncated, self.env_info
