from kaiwu_env.env.base_env import BaseEnv
from kaiwu_env.env.protocol import (
    Parse_StepFrameReq,
    SkylarenaDataHandler,
    Parse_AIServerResponse,
)
from common_python.utils.common_func import get_uuid
from common_python.ipc.zmq_util import ZmqClient, ZmqServer
from kaiwu_env.conf import yaml_arena
from pickle import dumps, loads
from kaiwu_env.utils.common_func import extract_ip_port


class EnvSkylarena(BaseEnv):
    def __init__(self, game_name, scene_name="default_scene", logger=None, monitor=None) -> None:
        self.game_name = game_name
        self.scene_name = scene_name
        self.data_handler = SkylarenaDataHandler(self.game_name, logger, monitor)

        self.logger = logger

        # 兼容现有代码, 最终需要解决
        skylarena_url = yaml_arena.skylarena_url
        ip, port = extract_ip_port(skylarena_url)

        # 因为是zmq_server, 此时就绑定在0.0.0.0上面
        ip = "0.0.0.0"

        # zmq_server, 负责aisrv与env进程之间的通信
        self.zmq_server = ZmqServer(ip, port)
        self.zmq_server.bind()
        self.logger.info(f"env, zmq_server aisrv <--> env, start at {ip}:{port} success")

        # zmq_client, 负责env与entity进程之间的通信
        self.entity_ip = yaml_arena.entity_zmq_url_ip
        self.entity_port = yaml_arena.entity_zmq_url_port

        self.client_id = get_uuid()
        self.zmq_client = ZmqClient(str(self.client_id), self.entity_ip, self.entity_port)
        self.zmq_client.connect()
        # 注意这个是标志env的zmq_client和entity的zmq_server之间重试的
        self.need_connect = False
        self.logger.info(f"env, zmq_client entity <--> env, start at {self.entity_ip}:{self.entity_port} success")

        self.monitor = monitor
        self.usr_conf = None

        # 计算当前进程的运行次数
        self.process_run_count = 0

    def reset(self):

        # 如果发生收不到stepframereq的情况, 会一直循环直到收到
        client_id, message = self.zmq_server.recv(block=True, binary=False)
        if not message:
            return False
        self.logger.info(f"env recv message from aisrv, client_id: {client_id}")

        message_type = message["message_type"]
        if message_type != "reset":
            self.logger.error(f"env in reset recv message from aisrv not reset message, so return")
            return False
        byte_stepframe_req = message["message_data"]

        try:
            self.usr_conf = loads(byte_stepframe_req)
        except Exception as e:
            self.logger.warning(f"env recv message from aisrv error, so return")
            return False

        """
        StepFrameRsp请求返回给entity, 从entity收到StepFrameReq请求
        主要是env进程发送给entity进程, 再从entity进程获取结果返回env进程
        如果需要重连, 则增加重连操作
        """
        if self.need_connect:
            self.client_id = get_uuid()
            self.zmq_client = ZmqClient(str(self.client_id), self.entity_ip, self.entity_port)
            self.zmq_client.connect()
            self.need_connect = False
            self.logger.info(
                f"zmq_client entity <--> env, reconnect to {self.entity_ip}:{self.entity_port} success, client_id {self.client_id}"
            )

        # 小概率出现entity进程异常后, env进程卡在reset上, 容灾方式为返回False进程退出, 等容灾进程重新拉起来
        try:
            message = {"message_type": "reset", "message_data": byte_stepframe_req}
            self.zmq_client.send(message, binary=False)
            while True:
                byte_stepframe_req = self.zmq_client.recv(block=True, timeout=60, binary=True)

                if byte_stepframe_req:
                    break
        except Exception as e:
            # 如果超过60秒没有出现reset的交互请求, 直接返回False导致进程被重启
            self.logger.warning(f"env recv message from entity timeout 60s, so return")
            self.need_connect = True
            return False

        # 将StepFrameReq请求反序列化得到结构化数据, 逻辑由self.game_name决定（场景接入方实现)
        # env_id, frame_no, frame_state, terminated, truncated, game_info
        pb_stepframe_req = Parse_StepFrameReq.decode(self.game_name, byte_stepframe_req)

        # result_code为0是进入到self.data_handler.reset逻辑
        # result_code大于0则退出该次reset，再次进入reset, 此时不需要重连, 说明env和entity之间的zmq链接是正常的, 因为错误码都能返回
        # result_code小于0则返回给调用者
        if pb_stepframe_req.result_code == 0:
            self.data_handler.reset(self.usr_conf)
        elif pb_stepframe_req.result_code > 0:
            self.logger.warning(
                f"env recv message from entity reset result_code is {pb_stepframe_req.result_code}, so return"
            )
            return False

        # 将StepFrameReq的结构化数据转换成AIServerRequest并序列化，转换逻辑由self.game_name决定（场景接入方实现)
        byte_aisvr_req = self.data_handler.StepFrameReq2AISvrReq(pb_stepframe_req)

        self.process_run_count += 1

        # 发送AIServerRequest完成一次交互流程
        return self.zmq_server.send(str(client_id), byte_aisvr_req, binary=True)

    def step(self):
        # 收到AIServerResponse
        try:
            client_id, message = self.zmq_server.recv(block=True, timeout=10, binary=False)
        except Exception as e:
            # 如果超过十秒没有出现step的交互请求, 开始新的reset链接新的请求
            self.logger.warning(f"env recv message from aisrv timeout 10s, so return")
            return True
        # 如果是空, 则开始下一轮的step而不是退出
        if not message:
            return True

        message_type = message["message_type"]
        if message_type != "step":
            self.logger.error(f"env in step recv message from aisrv not step message, so return")
            return True

        byte_aisvr_rsp = message["message_data"]

        # 将AIServerResponse反序列化得到结构化数据, 逻辑由self.game_name决定（场景接入方实现)
        # 返回的pb可以解析成: env_id, frame_no, action, stop_game
        pb_aisvr_rsp = Parse_AIServerResponse.decode(self.game_name, byte_aisvr_rsp)

        # 将AIServerResponse的结构化数据转换成StepFrameRsp并序列化，转换逻辑由self.game_name决定（场景接入方实现）
        byte_stepframe_rsp = self.data_handler.AISvrRsp2StepFrameRsp(pb_aisvr_rsp)

        """
        StepFrameRsp请求返回给entity, 从entity收到StepFrameReq请求
        主要是env进程发送给entity进程, 再从entity进程获取结果返回env进程
        """
        try:
            message = {"message_type": "step", "message_data": byte_stepframe_rsp}
            self.zmq_client.send(message, binary=False)
            while True:
                byte_stepframe_req = self.zmq_client.recv(block=True, timeout=10, binary=True)
                if byte_stepframe_req:
                    break
        except Exception as e:
            self.logger.warning(f"env <--> entity step timeout, so return")
            self.need_connect = True

            # 这里简单的返回即可, 因为entity和env, env和aisrv之间的超时是一样的, 如果env和entity之间的超时达到了重试
            # 那么env和aisrv之间的超时也会达到重试
            return True

        # 将StepFrameReq请求反序列化得到结构化数据, 逻辑由self.game_name决定（场景接入方实现)
        # env_id, frame_no, frame_state, terminated, truncated, game_info
        pb_stepframe_req = Parse_StepFrameReq.decode(self.game_name, byte_stepframe_req)

        # 对局退出的条件
        terminated = pb_stepframe_req.terminated
        truncated = pb_stepframe_req.truncated
        stop = terminated or truncated

        # 将StepFrameReq的结构化数据转换成AIServerRequest并序列化，转换逻辑由self.game_name决定（场景接入方实现)
        byte_aisvr_req = self.data_handler.StepFrameReq2AISvrReq(pb_stepframe_req)

        # result_code为0是进入到self.data_handler.step逻辑
        # result_code大于0则退出该次step，此时不需要重连, 因为错误码都能返回, 说明env和entity之间的zmq链接是正常的, 需要再次进入reset
        # result_code小于0则再次进入reset, 因为调用者直接就重新开始调用reset
        if pb_stepframe_req.result_code == 0:
            self.data_handler.step(pb_stepframe_req, pb_aisvr_rsp)
        elif pb_stepframe_req.result_code > 0:
            self.logger.warning(
                f"env recv message from entity step result_code is {pb_stepframe_req.result_code}, so return"
            )
            return True

        # 发送AIServerRequest
        self.zmq_server.send(str(client_id), byte_aisvr_req, binary=True)

        self.process_run_count += 1

        return stop

    def run_once(self):
        flag = self.reset()
        if flag == False:
            self.logger.warning("reset failed, so this env run_once return")
            return

        while True:
            # 游戏是否结束由game状态terminated,truncated和aisrv状态game_over决定
            flag = self.step()
            if flag:
                # 如果data_handler要在游戏结束时做特殊处理, 需要在finish中实现
                self.data_handler.finish()
                self.logger.info(f"EnvSkylarena run once episode done, process_run_count is {self.process_run_count}")
                break
