class BaseEnv:
    def reset(self):
        raise NotImplementedError

    def step(self, act):
        raise NotImplementedError


def chose_env_adapter_by_game_name(game_name, gamecore_type, env, logger):
    """
    根据游戏名称选择对应的环境适配器
    """

    if game_name == "gorge_walk":
        if gamecore_type == "PYWrapper":
            from kaiwu_env.gorge_walk.env_adapter import PYSceneWrapper

            return PYSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "gorge_walk_v2":
        if gamecore_type == "PYWrapper":
            from kaiwu_env.gorge_walk_v2.env_adapter import PYSceneWrapper

            return PYSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "back_to_the_realm":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.back_to_the_realm.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "back_to_the_realm_rpc":
        if gamecore_type == "RPCWrapper":
            from kaiwu_env.back_to_the_realm.env_adapter import RPCSceneWrapper

            return RPCSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "back_to_the_realm_v2":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.back_to_the_realm_v2.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "back_to_the_realm_v2_rpc":
        if gamecore_type == "RPCWrapper":
            from kaiwu_env.back_to_the_realm_v2.env_adapter import RPCSceneWrapper

            return RPCSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "infinity_valley":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.infinity_valley.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "hok1v1":
        if gamecore_type == "HokWrapper":
            from kaiwu_env.hok1v1.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "hok3v3_collaboration":
        if gamecore_type == "HokWrapper":
            from kaiwu_env.hok3v3_collaboration.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "economic_dispatch_in_generation_grid_load_storage":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.economic_dispatch_in_generation_grid_load_storage.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "intelligent_traffic_lights":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.intelligent_traffic_lights.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "intelligent_traffic_lights_rpc":
        if gamecore_type == "RPCWrapper":
            from kaiwu_env.intelligent_traffic_lights.env_adapter import RPCSceneWrapper

            return RPCSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "intelligent_traffic_lights_v2":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.intelligent_traffic_lights_v2.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "intelligent_traffic_lights_v2_rpc":
        if gamecore_type == "RPCWrapper":
            from kaiwu_env.intelligent_traffic_lights_v2.env_adapter import RPCSceneWrapper

            return RPCSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "border_breakout":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.border_breakout.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    elif game_name == "legged_robot_locomotion_control":
        if gamecore_type == "SGWrapper":
            from kaiwu_env.legged_robot_locomotion_control.env_adapter import SGSceneWrapper

            return SGSceneWrapper(env, logger)
        else:
            raise NotImplementedError
    else:
        pass


class PYSceneWrapper:
    def __init__(self, env, game_name, gamecore_type, logger) -> None:
        self.wrapper = chose_env_adapter_by_game_name(game_name, gamecore_type, env, logger)

    def reset(self, game_id, usr_conf):
        return self.wrapper.reset(game_id, usr_conf)

    def step(self, game_id, frame_no, command, stop_game):
        return self.wrapper.step(game_id, frame_no, command, stop_game)

    def __getattr__(self, attr):
        return getattr(self.env, attr)


class SGSceneWrapper:
    def __init__(self, env, game_name, gamecore_type, logger) -> None:
        self.wrapper = chose_env_adapter_by_game_name(game_name, gamecore_type, env, logger)

    def reset(self, game_id, usr_conf):
        return self.wrapper.reset(game_id, usr_conf)

    def step(self, game_id, frame_no, command, stop_game):
        return self.wrapper.step(game_id, frame_no, command, stop_game)


class RPCSceneWrapper:
    def __init__(self, env, game_name, gamecore_type, logger) -> None:
        self.wrapper = chose_env_adapter_by_game_name(game_name, gamecore_type, env, logger)

    def reset(self, game_id, usr_conf):
        return self.wrapper.reset(game_id, usr_conf)

    def step(self, game_id, frame_no, command, stop_game):
        return self.wrapper.step(game_id, frame_no, command, stop_game)
