from kaiwu_env.utils.conf_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TreeParser
import glob
import importlib.util
import sys
from functools import partial

__doc__ = "自动实例化conf下所有类型的配置文件，实例后的对象名称为[type]_[folder]_[subfolder]_[filename]"

__all__ = []

_excluded_file_list = ["__init__.py", "metayaml.yaml"]
_excluded_folder_list = ["__pycache__", "template"]


class _LazyLoader:
    """支持字典操作的惰性加载器"""

    __slots__ = ["_loader", "_data"]

    def __init__(self, loader, path):
        self._loader = partial(loader, path)
        self._data = None

    def __getattr__(self, name):
        if self._data is None:
            self._data = self._loader()
        return getattr(self._data, name)

    # 新增字典协议支持
    def __getitem__(self, key):
        if self._data is None:
            self._data = self._loader()
        return self._data[key]

    def items(self):
        if self._data is None:
            self._data = self._loader()
        return self._data.items()

    def __iter__(self):
        if self._data is None:
            self._data = self._loader()
        return iter(self._data)

    def __contains__(self, key):
        if self._data is None:
            self._data = self._loader()
        return key in self._data

    def __repr__(self):
        return f"<LazyConfig {self._loader}>"


def instance_conf_obj_from_spec_type(type_file, caller, base_path=__path__[0], name_prefix=None):
    for file_path in glob.glob(f"{base_path}/*.{type_file}"):
        filename = file_path.split("/")[-1]
        if filename in _excluded_file_list:
            continue

        name_obj = f"{name_prefix or type_file}_{filename.split('.')[0]}"
        globals()[name_obj] = _LazyLoader(caller, file_path)
        __all__.append(name_obj)

    for folder_path in glob.glob(f"{base_path}/**/"):
        folder_path = folder_path.rstrip("/")
        foldername = folder_path.split("/")[-1]
        if foldername in _excluded_folder_list:
            continue

        name_obj = f"{name_prefix or type_file}_{foldername}"
        instance_conf_obj_from_spec_type(type_file, caller, folder_path, name_obj)


def _instance_conf_obj_from_py(config_file_path):
    module_name = f"dynamic_pyconf_{hash(config_file_path)}"
    spec = importlib.util.spec_from_file_location(module_name, config_file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)

    class PyProxy:
        def __getattr__(self, name):
            return getattr(module, name, None)

    proxy = PyProxy()
    del sys.modules[module_name]
    return proxy


instance_conf_obj_from_spec_type("yaml", YamlParser)
instance_conf_obj_from_spec_type("ini", IniParser)
instance_conf_obj_from_spec_type("json", JsonParser)
instance_conf_obj_from_spec_type("tree", TreeParser)
instance_conf_obj_from_spec_type("py", _instance_conf_obj_from_py)
