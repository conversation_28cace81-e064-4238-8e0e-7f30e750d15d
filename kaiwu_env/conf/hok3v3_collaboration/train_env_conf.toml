[monitor]
# Monitor side, statistics are from the perspective of this side.
# Type: integer, range [-1,0,1], -1 represents auto switch monitor side, 0 represents blue side, 1 represents red side, default -1.
# 监控上报的阵营，统计指标以该阵营为第一视角。类型整型，取值范围[-1,0,1]，-1表示自动换边，0表示蓝方阵营，1表示红方阵营，默认值-1。
monitor_side = 0

[episode]
# Opponent agent.
# Type: string, range [selfplay, common_ai, custom model id], default "selfplay".
# 1. selfplay: Indicates self-play.
# 2. common_ai: Indicates playing against a rule-based common AI.
# 3. custom_model_id: Indicates playing against a custom model specified by its model ID.
# 对手智能体。类型字符串，取值范围[selfplay, common_ai, 自定义模型id]，默认值"selfplay"。
# 1. selfplay 表示自对弈
# 2. common_ai 表示对战基于规则的common_ai
# 2. 自定义的模型id 表示对战模型管理中的对手模型
opponent_agent = "selfplay"
# Evaluation interval (unit: games). Type: integer, value range: integer >= 0, default: 10
# 评估间隔(单位局)，类型整型，取值范围为大于等于0的整数,默认值10。
eval_interval = 10
# Evaluation opponent type. Type: string, value range: [selfplay, common_ai, custom model ID], default: "common_ai"
# 评估对手类型。类型字符串，取值范围为[selfplay, common_ai, 自定义模型id]，默认值"common_ai"。
eval_opponent_type = "common_ai"

[collaboration_command]
# Command types: target position, target object, interrupt. Interrupt commands are not restricted by the following configurations.
# Command duration (unit: frame). Type: integer, value range: [0,20000], default: 450
# 指令一共分为三种类型，指定目标位置、指定目标对象、中断。其中，中断指令不受以下指令配置限制。
# 指令的持续时间(单位帧)。类型整型，取值范围为[0,20000]。默认450。
command_duration = 450
# Command cooldown (unit: frame). Type: integer, value range: [0,20000], default: 150
# 指令的冷却时间(单位帧)。类型整型，取值范围为[0,20000]。默认150。
command_cooldown = 150

# Blue camp lineup configuration
# 蓝方阵容配置。
[lineups.blue_camp]
# official_model: hero ID controlled by the official model in the blue camp.
# Type: integer, allowed values [128, 169, 176], default: 128. 128: Cao Cao, 169: Hou Yi, 176: Yang Yuhuan.
# official_model表示蓝方阵营中官方模型控制的英雄id。
# 类型整数，取值范围[128,169,176]，默认值为128。128表示曹操，169表示后羿，176表示杨玉环。
official_model = 128
# is_official_model_enabled: Whether to enable official model control. Type: boolean, true=enabled, false=disabled.
# is_official_model_enabled表示是否启用官方模型控制，类型布尔值，true表示启用官方模型控制，false表示不启用官方模型控制。
is_official_model_enabled = true

# Red camp lineup configuration
# 红方阵容配置。
[lineups.red_camp]
# official_model: hero ID controlled by the official model in the blue camp.
# Type: integer, allowed values [128, 169, 176], default: 128. 128: Cao Cao, 169: Hou Yi, 176: Yang Yuhuan.
# official_model表示红方阵营中官方模型控制的英雄id。
# 类型整数，取值范围[128,169,176]，默认值为128。128表示曹操，169表示后羿，176表示杨玉环。
official_model = 128
# is_official_model_enabled: Whether to enable official model control. Type: boolean, true=enabled, false=disabled.
# is_official_model_enabled表示是否启用官方模型控制，类型布尔值，true表示启用官方模型控制，false表示不启用官方模型控制。
is_official_model_enabled = true

[reward_policys]
# Whether to enable zero-sum reward mechanism
# 是否启用零和奖励机制
whether_use_zero_sum_reward = true
# Team spirit coefficient
# 团队协作精神系数
team_spirit = 0.2
# Time scaling discount factor
# 时间衰减折扣因子
time_scaling_discount = 0.75
# Base duration for time scaling
# 时间缩放基准时长
time_scaling_time = 4500

  [reward_policys.128]
# HP ratio to the fourth root
# 血量比值开四次方
hp_rate_sqrt_sqrt = 1
# Economy growth value (Gold)
# 经济增长值
money = 0.005
# Experience growth value
# 经验增长值
exp = 0.002
# Tower health points
# 塔血量
tower = 1.5
# Hero kills counter
# 击杀
killCnt = 1.1
# Deaths counter (penalty)
# 死亡
deadCnt = -1
# Assists counter
# 助攻
assistCnt = 1
# Total damage dealt to heroes
# 对英雄伤害值
total_hurt_to_hero = 0.2
# Damage to neutral monsters
# 攻击野怪
atk_monster = 0.3
# Destroy enemy nexus (victory condition)
# 摧毁敌方水晶
win_crystal = 6
# Attacking enemy nexus
# 攻击敌方水晶
atk_crystal = 3

  [reward_policys.169]
# HP ratio to the fourth root
# 血量比值开四次方
hp_rate_sqrt_sqrt = 1
# Economy growth value (Gold)
# 经济增长值
money = 0.005
# Experience growth value
# 经验增长值
exp = 0.002
# Tower health points
# 塔血量
tower = 1.5
# Hero kills counter
# 击杀
killCnt = 1.1
# Deaths counter (penalty)
# 死亡
deadCnt = -1
# Assists counter
# 助攻
assistCnt = 1.2
# Total damage dealt to heroes
# 对英雄伤害值
total_hurt_to_hero = 0.3
# Damage to neutral monsters
# 攻击野怪
atk_monster = 0.1
# Destroy enemy nexus (victory condition)
# 摧毁敌方水晶
win_crystal = 6
# Attacking enemy nexus
# 攻击敌方水晶
atk_crystal = 3

  [reward_policys.176]
# HP ratio to the fourth root
# 血量比值开四次方
hp_rate_sqrt_sqrt = 1
# Economy growth value (Gold)
# 经济增长值
money = 0.005
# Experience growth value
# 经验增长值
exp = 0.002
# Tower health points
# 塔血量
tower = 1.5
# Hero kills counter
# 击杀
killCnt = 1.1
# Deaths counter (penalty)
# 死亡
deadCnt = -1
# Assists counter
# 助攻
assistCnt = 1
# Total damage dealt to heroes
# 对英雄伤害值
total_hurt_to_hero = 0.2
# Damage to neutral monsters
# 攻击野怪
atk_monster = 0.05
# Destroy enemy nexus (victory condition)
# 摧毁敌方水晶
win_crystal = 6
# Attacking enemy nexus
# 攻击敌方水晶
atk_crystal = 3
