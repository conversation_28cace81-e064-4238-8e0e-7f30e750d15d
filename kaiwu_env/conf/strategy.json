{"kaiwu_env.__init__.run_mode_selector": {"default_strategy": "kaiwu_env.__init__.StrategyRunModeSelector.default_mode", "entity": "kaiwu_env.__init__.StrategyRunModeSelector.entity", "skylarena": "kaiwu_env.__init__.StrategyRunModeSelector.skylarena", "proxy": "kaiwu_env.__init__.StrategyRunModeSelector.proxy"}, "kaiwu_env.__init__.scene_selector": {"default_strategy": "kaiwu_env.__init__.StrategySceneSelector.default_scene", "gorge_walk": "kaiwu_env.__init__.StrategySceneSelector.gorge_walk", "gorge_walk_v2": "kaiwu_env.__init__.StrategySceneSelector.gorge_walk_v2", "back_to_the_realm": "kaiwu_env.__init__.StrategySceneSelector.back_to_the_realm", "back_to_the_realm_rpc": "kaiwu_env.__init__.StrategySceneSelector.back_to_the_realm_rpc", "back_to_the_realm_v2": "kaiwu_env.__init__.StrategySceneSelector.back_to_the_realm_v2", "back_to_the_realm_v2_rpc": "kaiwu_env.__init__.StrategySceneSelector.back_to_the_realm_v2_rpc", "intelligent_traffic_lights": "kaiwu_env.__init__.StrategySceneSelector.intelligent_traffic_lights", "intelligent_traffic_lights_rpc": "kaiwu_env.__init__.StrategySceneSelector.intelligent_traffic_lights_rpc", "intelligent_traffic_lights_v2": "kaiwu_env.__init__.StrategySceneSelector.intelligent_traffic_lights_v2", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.__init__.StrategySceneSelector.intelligent_traffic_lights_v2_rpc", "border_breakout": "kaiwu_env.__init__.StrategySceneSelector.border_breakout", "hok1v1": "kaiwu_env.__init__.StrategySceneSelector.hok1v1", "infinity_valley": "kaiwu_env.__init__.StrategySceneSelector.infinity_valley", "hok3v3_collaboration": "kaiwu_env.__init__.StrategySceneSelector.hok3v3_collaboration", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.__init__.StrategySceneSelector.economic_dispatch_in_generation_grid_load_storage", "legged_robot_locomotion_control": "kaiwu_env.__init__.StrategySceneSelector.legged_robot_locomotion_control"}, "kaiwu_env.env.protocol.Parse_AIServerRequest.decode": {"default_strategy": "kaiwu_env.env.protocol.Parse_AIServerRequest_Default.decode", "gorge_walk": "kaiwu_env.gorge_walk.protocol.Parse_AIServerRequest.decode", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.protocol.Parse_AIServerRequest.decode", "back_to_the_realm": "kaiwu_env.back_to_the_realm.protocol.Parse_AIServerRequest.decode", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.protocol.Parse_AIServerRequest.decode", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_AIServerRequest.decode", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_AIServerRequest.decode", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_AIServerRequest.decode", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights_rpc.protocol.Parse_AIServerRequest.decode", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_AIServerRequest.decode", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_AIServerRequest.decode", "border_breakout": "kaiwu_env.border_breakout.protocol.Parse_AIServerRequest.decode", "hok1v1": "kaiwu_env.hok1v1.protocol.Parse_AIServerRequest.decode", "infinity_valley": "kaiwu_env.infinity_valley.protocol.Parse_AIServerRequest.decode", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.protocol.Parse_AIServerRequest.decode", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.protocol.Parse_AIServerRequest.decode", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.protocol.Parse_AIServerRequest.decode"}, "kaiwu_env.env.protocol.Parse_AIServerResponse.encode": {"default_strategy": "kaiwu_env.env.protocol.Parse_AIServerResponse_Default.encode", "gorge_walk": "kaiwu_env.gorge_walk.protocol.Parse_AIServerResponse.encode", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.protocol.Parse_AIServerResponse.encode", "back_to_the_realm": "kaiwu_env.back_to_the_realm.protocol.Parse_AIServerResponse.encode", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.protocol.Parse_AIServerResponse.encode", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_AIServerResponse.encode", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_AIServerResponse.encode", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_AIServerResponse.encode", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights_rpc.protocol.Parse_AIServerResponse.encode", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_AIServerResponse.encode", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_AIServerResponse.encode", "border_breakout": "kaiwu_env.border_breakout.protocol.Parse_AIServerResponse.encode", "hok1v1": "kaiwu_env.hok1v1.protocol.Parse_AIServerResponse.encode", "infinity_valley": "kaiwu_env.infinity_valley.protocol.Parse_AIServerResponse.encode", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.protocol.Parse_AIServerResponse.encode", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.protocol.Parse_AIServerResponse.encode", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.protocol.Parse_AIServerResponse.encode"}, "kaiwu_env.env.protocol.Parse_AIServerResponse.decode": {"default_strategy": "kaiwu_env.env.protocol.Parse_AIServerResponse_Default.decode", "gorge_walk": "kaiwu_env.gorge_walk.protocol.Parse_AIServerResponse.decode", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.protocol.Parse_AIServerResponse.decode", "back_to_the_realm": "kaiwu_env.back_to_the_realm.protocol.Parse_AIServerResponse.decode", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.protocol.Parse_AIServerResponse.decode", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_AIServerResponse.decode", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_AIServerResponse.decode", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_AIServerResponse.decode", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights_rpc.protocol.Parse_AIServerResponse.decode", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_AIServerResponse.decode", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_AIServerResponse.decode", "border_breakout": "kaiwu_env.border_breakout.protocol.Parse_AIServerResponse.decode", "hok1v1": "kaiwu_env.hok1v1.protocol.Parse_AIServerResponse.decode", "infinity_valley": "kaiwu_env.infinity_valley.protocol.Parse_AIServerResponse.decode", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.protocol.Parse_AIServerResponse.decode", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.protocol.Parse_AIServerResponse.decode", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.protocol.Parse_AIServerResponse.decode"}, "kaiwu_env.env.protocol.Parse_StepFrameReq.encode": {"default_strategy": "kaiwu_env.env.protocol.Parse_StepFrameReq_Default.encode", "gorge_walk": "kaiwu_env.gorge_walk.protocol.Parse_StepFrameReq.encode", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.protocol.Parse_StepFrameReq.encode", "back_to_the_realm": "kaiwu_env.back_to_the_realm.protocol.Parse_StepFrameReq.encode", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.protocol.Parse_StepFrameReq.encode", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameReq.encode", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameReq.encode", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_StepFrameReq.encode", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_StepFrameReq.encode", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_StepFrameReq.encode", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_StepFrameReq.encode", "border_breakout": "kaiwu_env.border_breakout.protocol.Parse_StepFrameReq.encode", "hok1v1": "kaiwu_env.hok1v1.protocol.Parse_StepFrameReq.encode", "infinity_valley": "kaiwu_env.infinity_valley.protocol.Parse_StepFrameReq.encode", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.protocol.Parse_StepFrameReq.encode", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.protocol.Parse_StepFrameReq.encode", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.protocol.Parse_StepFrameReq.encode"}, "kaiwu_env.env.protocol.Parse_StepFrameReq.decode": {"default_strategy": "kaiwu_env.env.protocol.Parse_StepFrameReq_Default.decode", "gorge_walk": "kaiwu_env.gorge_walk.protocol.Parse_StepFrameReq.decode", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.protocol.Parse_StepFrameReq.decode", "back_to_the_realm": "kaiwu_env.back_to_the_realm.protocol.Parse_StepFrameReq.decode", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.protocol.Parse_StepFrameReq.decode", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameReq.decode", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameReq.decode", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_StepFrameReq.decode", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_StepFrameReq.decode", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_StepFrameReq.decode", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_StepFrameReq.decode", "border_breakout": "kaiwu_env.border_breakout.protocol.Parse_StepFrameReq.decode", "hok1v1": "kaiwu_env.hok1v1.protocol.Parse_StepFrameReq.decode", "infinity_valley": "kaiwu_env.infinity_valley.protocol.Parse_StepFrameReq.decode", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.protocol.Parse_StepFrameReq.decode", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.protocol.Parse_StepFrameReq.decode", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.protocol.Parse_StepFrameReq.decode"}, "kaiwu_env.env.protocol.Parse_StepFrameRsp.decode": {"default_strategy": "kaiwu_env.env.protocol.Parse_StepFrameRsp_Default.decode", "gorge_walk": "kaiwu_env.gorge_walk.protocol.Parse_StepFrameRsp.decode", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.protocol.Parse_StepFrameRsp.decode", "back_to_the_realm": "kaiwu_env.back_to_the_realm.protocol.Parse_StepFrameRsp.decode", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.protocol.Parse_StepFrameRsp.decode", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameRsp.decode", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameRsp.decode", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.protocol.Parse_StepFrameRsp.decode", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights_rpc.protocol.Parse_StepFrameRsp.decode", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_StepFrameRsp.decode", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.protocol.Parse_StepFrameRsp.decode", "border_breakout": "kaiwu_env.border_breakout.protocol.Parse_StepFrameRsp.decode", "hok1v1": "kaiwu_env.hok1v1.protocol.Parse_StepFrameRsp.decode", "infinity_valley": "kaiwu_env.back_to_the_realm_v2.protocol.Parse_StepFrameRsp.decode", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.protocol.Parse_StepFrameRsp.decode", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.protocol.Parse_StepFrameRsp.decode", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.protocol.Parse_StepFrameRsp.decode"}, "kaiwu_env.env.protocol.SkylarenaDataHandler": {"default_strategy": "kaiwu_env.env.protocol.SkylarenaDataHandler_Default", "gorge_walk": "kaiwu_env.gorge_walk.data_handler.SkylarenaDataHandler", "gorge_walk_v2": "kaiwu_env.gorge_walk_v2.data_handler.SkylarenaDataHandler", "back_to_the_realm": "kaiwu_env.back_to_the_realm.data_handler.SkylarenaDataHandler", "back_to_the_realm_rpc": "kaiwu_env.back_to_the_realm.data_handler.SkylarenaDataHandler", "back_to_the_realm_v2": "kaiwu_env.back_to_the_realm_v2.data_handler.SkylarenaDataHandler", "back_to_the_realm_v2_rpc": "kaiwu_env.back_to_the_realm_v2.data_handler.SkylarenaDataHandler", "intelligent_traffic_lights": "kaiwu_env.intelligent_traffic_lights.data_handler.SkylarenaDataHandler", "intelligent_traffic_lights_rpc": "kaiwu_env.intelligent_traffic_lights_rpc.data_handler.SkylarenaDataHandler", "intelligent_traffic_lights_v2": "kaiwu_env.intelligent_traffic_lights_v2.data_handler.SkylarenaDataHandler", "intelligent_traffic_lights_v2_rpc": "kaiwu_env.intelligent_traffic_lights_v2.data_handler.SkylarenaDataHandler", "border_breakout": "kaiwu_env.border_breakout.data_handler.SkylarenaDataHandler", "hok1v1": "kaiwu_env.hok1v1.data_handler.SkylarenaDataHandler", "infinity_valley": "kaiwu_env.infinity_valley.data_handler.SkylarenaDataHandler", "hok3v3_collaboration": "kaiwu_env.hok3v3_collaboration.data_handler.SkylarenaDataHandler", "economic_dispatch_in_generation_grid_load_storage": "kaiwu_env.economic_dispatch_in_generation_grid_load_storage.data_handler.SkylarenaDataHandler", "legged_robot_locomotion_control": "kaiwu_env.legged_robot_locomotion_control.data_handler.SkylarenaDataHandler"}, "kaiwu_env.env.env_entity_base.run_env_entity": {"kaiwu_env": "kaiwu_env.env.env_entity_base.StrategySenderSelector.kaiwu_env", "drl": "kaiwu_env.env.env_entity_base.StrategySenderSelector.drl"}, "scene_wrapper": {"PYWrapper": "kaiwu_env.env.base_env.PYSceneWrapper", "SGWrapper": "kaiwu_env.env.base_env.SGSceneWrapper", "RPCWrapper": "kaiwu_env.env.base_env.RPCSceneWrapper"}}