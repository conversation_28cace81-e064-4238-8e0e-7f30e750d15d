[env_conf]
# Is the start point number.
# Integer. The value range is 1 to 15, and the default value is 2.
# The start point number and the end point number cannot be repeated.
# 整型，取值范围为1~15，默认值为2。
# 起点编号和终点编号不能重复。
# 起点编号
start = 2

# Is the end point number.
# Integer. The value range is 1 to 15, and the default value is 1.
# The start point number and the end point number cannot be repeated.
# 整型，取值范围为1~15，默认值为1。
# 起点编号和终点编号不能重复。
# 终点编号
end = 1

# boolean, false - fixed chest, true - random chest
# If fixed is enabled, use treasure_id to generate fixed chests.
# If random is enabled, use treasure_count to randomly generate chests.
# If the treasure chest random or not
# The default value is true.
# 布尔值，false - 固定宝箱，true - 随机宝箱
# 若开启固定，则使用treasure_id生成固定宝箱。
# 若开启随机，则使用treasure_count随机生成宝箱。
# 宝箱是否随机
# 默认值为true。
treasure_random = true

# Integer, 0~13, and the default value is 8.
# Description: Number of chests. Only works when treasure_random = true
# 整型，生成随机宝箱时的宝箱数量，仅在treasure_random = true时生效，取值范围为0~13，默认值为8。
treasure_count = 8

# The treasure chest number when generating a fixed treasure chest.
# It is only valid when treasure_random = false. It is an array with a value range of 1 to 15. The default value is [].
# Note that the start and end point numbers need to be excluded. If you need to generate 0 treasure chests, pass in [].
# 生成固定宝箱时的宝箱编号，仅在treasure_random = false时生效，数组，取值范围为1~15，默认值为[]。
# 注意需要排除起点和终点编号，如果需要固定生成0个宝箱则传入[]。
treasure_id = []

# Is hero talent.
# Integer. The default value is 1, 1 means flash.
# Other values ​​are illegal.
# 整型，默认值为1，1表示超级闪现技能，其他值非法。
# 英雄技能
talent_type = 1

# Integer, Value range: 1~2000, default: 2000
# Description: Max step
# 整型，取值范围1~2000, 默认2000
# 最大步数
max_step = 2000
