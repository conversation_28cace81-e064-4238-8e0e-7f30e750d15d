#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from kaiwu_agent.agent.protocol.protocol import *
from importlib import import_module

# 根据不同的算法名去加载不同的配置文件
from kaiwudrl.common.config.config_control import CONFIG

# 移除了workflow中的preprocessor
# # 定义算法与模块路径的映射关系
# ALGO_MODULE_MAP = {
#     "target_dqn": "agent_target_dqn.feature.preprocessor",
#     "dqn": "agent_dqn.feature.preprocessor",
#     "ppo": "agent_ppo.feature.preprocessor",
#     "diy": "agent_diy.feature.preprocessor",
# }
# # 动态导入
# algo = CONFIG.algo
# if algo not in ALGO_MODULE_MAP:
#     raise ValueError(f"Unsupported algorithm: {algo}")

# module = import_module(ALGO_MODULE_MAP[algo])
# Preprocessor = module.Preprocessor

from tools.train_env_conf_validate import read_usr_conf


def workflow(envs, agents, logger=None, monitor=None):
    try:
        env, agent = envs[0], agents[0]

        # 评估开始
        logger.info(".......... Evaluation Start ..........")

        # 配置文件读取和校验
        usr_conf = read_usr_conf("tools/eval/conf/eval_env_conf.toml", logger)
        if usr_conf is None:
            logger.error(f"usr_conf is None, please check tools/eval/conf/eval_env_conf.toml")
            raise ValueError("usr_conf is None, please check tools/eval/conf/eval_env_conf.toml")

        # 打印下usr_conf
        logger.info(f"usr_conf is {usr_conf}")

        # 在容灾情况下能运行正常
        EPISODE_CNT = 10000
        total_score, win_cnt, treasure_cnt = 0, 0, 0
        episode = 0
        while episode < EPISODE_CNT:

            # 重置任务, 并获取初始状态
            obs, extra_info = env.reset(usr_conf=usr_conf)
            if extra_info["result_code"] < 0:
                logger.error(
                    f"env.reset result_code is {extra_info['result_code']}, result_message is {extra_info['result_message']}"
                )
                raise RuntimeError(extra_info["result_message"])
            elif extra_info["result_code"] > 0:
                continue

            # 任务循环
            done = False
            current_score = 0
            while not done:
                # Agent 预测
                observation = {
                    "obs": obs,
                    "extra_info": None,
                }
                act, model_version = agent.exploit(observation)

                # 与环境交互, 执行动作, 获取下一步的状态
                step_no, _obs, terminated, truncated, _extra_info = env.step(act)

                if _extra_info["result_code"] != 0:
                    logger.warning(
                        f"env.step result_code is {_extra_info['result_code']}, result_message is {_extra_info['result_message']}"
                    )
                    episode -= 1
                    current_score = 0
                    break

                if terminated:
                    win_cnt += 1

                # 更新总奖励和状态
                current_score = _extra_info["game_info"]["total_score"]
                current_treasure_collected_count = _extra_info["game_info"]["treasure_collected_count"]
                obs, extra_info = _obs, _extra_info

                # 判断任务结束, 并更新胜利次数
                done = terminated or truncated
                if done:
                    logger.info(f"env.step return done, terminated: {terminated}, truncated: {truncated}, so break")

            # 更新总奖励和状态
            total_score += current_score

            # 更新宝箱收集数量
            treasure_cnt += current_treasure_collected_count

            episode += 1

            # 确保只有一局正常评估结束就跳出循环
            if done:
                break

        # 打印评估结果
        logger.info(f"Average Total Score: {total_score / episode}")
        logger.info(f"Average Treasure Collected: {treasure_cnt / episode}")
        logger.info(f"Success Rate : {win_cnt / episode}")

        # 评估结束
        logger.info(".......... Evaluation End ..........")
    except Exception as e:
        raise RuntimeError(f"workflow error")
