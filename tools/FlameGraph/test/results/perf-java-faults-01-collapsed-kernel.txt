java;_int_malloc 2
java;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub;java/lang/Thread:::run;Interpreter;java/util/concurrent/ThreadPoolExecutor$Worker:::run;java/util/concurrent/ThreadPoolExecutor:::runWorker;org/apache/tomcat/util/net/AprEndpoint$SocketProcessor:::run;org/apache/tomcat/util/net/AprEndpoint$SocketProcessor:::doRun;org/apache/coyote/AbstractProtocol$AbstractConnectionHandler:::process;org/apache/coyote/http11/AbstractHttp11Processor:::process;org/apache/catalina/connector/CoyoteAdapter:::service;org/apache/coyote/http11/AbstractHttp11Processor:::action;org/apache/tomcat/jni/Socket:::sendbb 1
java;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub;net/spy/memcached/EVCacheConnection:::run;net/spy/memcached/MemcachedConnection:::handleIO;net/spy/memcached/MemcachedConnection:::handleIO;net/spy/memcached/MemcachedConnection:::handleReads;net/spy/memcached/protocol/binary/OperationImpl:::readFromBuffer;net/spy/memcached/protocol/binary/OperationImpl:::finishedPayload;net/spy/memcached/protocol/binary/GetOperationImpl:::decodePayload;net/spy/memcached/transcoders/TranscodeService$1:::call;XXX::XXX;java/util/zip/Inflater:::inflateBytes;Java_java_util_zip_Inflater_inflateBytes;inflate;__memmove_ssse3_back 18
java;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub;net/spy/memcached/EVCacheConnection:::run;net/spy/memcached/MemcachedConnection:::handleIO;net/spy/memcached/MemcachedConnection:::handleIO;net/spy/memcached/MemcachedConnection:::handleReads;net/spy/memcached/protocol/binary/OperationImpl:::readFromBuffer;net/spy/memcached/protocol/binary/OperationImpl:::finishedPayload;net/spy/memcached/protocol/binary/GetOperationImpl:::decodePayload;net/spy/memcached/transcoders/TranscodeService$1:::call;com/XXX::XXX;java/util/zip/Inflater:::inflateBytes;Java_java_util_zip_Inflater_inflateBytes;inflate;__memmove_ssse3_back 1
perf;__libc_start_main;main;run_builtin;cmd_record 40
perf;do_lookup_x 27
sleep;[unknown];memcmp 24
sleep;__execve;return_from_execve_[k];sys_execve_[k];do_execveat_common.isra.31_[k];search_binary_handler_[k];copy_user_enhanced_fast_string_[k] 1
sleep;__execve;return_from_execve_[k];sys_execve_[k];do_execveat_common.isra.31_[k];search_binary_handler_[k];load_elf_binary_[k];padzero_[k];clear_user_[k];__clear_user_[k] 2
sleep;_dl_start_user;_dl_start 9
sleep;_start 3
sleep;handle_intel 72
