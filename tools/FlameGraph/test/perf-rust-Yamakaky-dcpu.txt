emulator  4152 75695.008865:          1 cycles:u: 
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008879:          1 cycles:u: 
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008886:          3 cycles:u: 
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008892:         16 cycles:u: 
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008898:         98 cycles:u: 
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008904:        597 cycles:u: 
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008912:       3646 cycles:u: 
	          7d9750 page_fault+0xfe200000 ([kernel.kallsyms])
	             d70 _start+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.008945:      17736 cycles:u: 
	            4f4f _dl_start+0xffff018fd5dce3df (/usr/lib/ld-2.24.so)
	             d78 _dl_start_user+0xffff018fd5dce000 (/usr/lib/ld-2.24.so)

emulator  4152 75695.009016:      32156 cycles:u: 
	            7f31 _dl_init_paths+0xffff018fd5dce001 (/usr/lib/ld-2.24.so)
	           174ef _dl_sysdep_start+0xffff018fd5dce3df (/usr/lib/ld-2.24.so)
	              40 [unknown] ([unknown])

emulator  4152 75695.009206:      42339 cycles:u: 
	           1654f _dl_load_cache_lookup+0xffff018fd5dce29f (/usr/lib/ld-2.24.so)
	            88c0 _dl_map_object+0xffff018fd5dce550 (/usr/lib/ld-2.24.so)

emulator  4152 75695.009539:      44000 cycles:u: 
	           16387 _dl_load_cache_lookup+0xffff018fd5dce0d7 (/usr/lib/ld-2.24.so)
	            88c0 _dl_map_object+0xffff018fd5dce550 (/usr/lib/ld-2.24.so)

emulator  4152 75695.009667:      42632 cycles:u: 
	           105ae _dl_name_match_p+0xffff018fd5dce01e (/usr/lib/ld-2.24.so)
	2e747262696c0036 [unknown] ([unknown])

emulator  4152 75695.009762:      47729 cycles:u: 
	            b96d _dl_relocate_object+0xffff018fd5dce40d (/usr/lib/ld-2.24.so)
	            39b1 dl_main+0xffff018fd5dd0081 (/usr/lib/ld-2.24.so)
	           174ef _dl_sysdep_start+0xffff018fd5dce3df (/usr/lib/ld-2.24.so)
	              40 [unknown] ([unknown])

emulator  4152 75695.009874:      52068 cycles:u: 
	           844d7 __strcasecmp+0xffff018fd717a007 (/usr/lib/libc-2.24.so)
	            39b1 dl_main+0xffff018fd5dd0081 (/usr/lib/ld-2.24.so)
	           174ef _dl_sysdep_start+0xffff018fd5dce3df (/usr/lib/ld-2.24.so)
	              40 [unknown] ([unknown])

emulator  4152 75695.010021:      60144 cycles:u: 
	           196b7 strcmp+0xffff018fd5dce077 (/usr/lib/ld-2.24.so)
	63636762696c0036 [unknown] ([unknown])

emulator  4152 75695.010239:      65345 cycles:u: 
	            b8f2 _dl_relocate_object+0xffff018fd5dce392 (/usr/lib/ld-2.24.so)
	            39b1 dl_main+0xffff018fd5dd0081 (/usr/lib/ld-2.24.so)
	           174ef _dl_sysdep_start+0xffff018fd5dce3df (/usr/lib/ld-2.24.so)
	              40 [unknown] ([unknown])

emulator  4152 75695.010508:      66557 cycles:u: 
	           dcc45 __GI___readlink+0xffff018fd717a005 (/usr/lib/libc-2.24.so)

emulator  4152 75695.010666:      65964 cycles:u: 
	          6681e7 je_tcache_boot+0xffff53cc2fce4227 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.010880:      70745 cycles:u: 
	          7d9750 page_fault+0xfe200000 ([kernel.kallsyms])
	          64c156 je_arena_tcache_fill_small+0xffff53cc2fce40b6 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.011165:      72243 cycles:u: 
	           37315 __GI_____strtoull_l_internal+0xffff018fd717a0f5 (/usr/lib/libc-2.24.so)

emulator  4152 75695.011360:      71141 cycles:u: 
	          7d9750 page_fault+0xfe200000 ([kernel.kallsyms])
	          375990 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4000 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.011556:      73671 cycles:u: 
	          62e25d std::path::PathBuf::_push::h766d676eb9b04254+0xffff53cc2fce401d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b2eb0 term::terminfo::searcher::get_dbpath_for_term::hffa8fd0e9637bc76+0xffff53cc2fce4820 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ab2 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4042 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.011685:      76227 cycles:u: 
	          38e1c0 _$LT$u8$u20$as$u20$core..clone..Clone$GT$::clone::h7bfab8630dda96cf+0xffff53cc2fce4010 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3849e4 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce41a4 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.011788:      85066 cycles:u: 
	          38e1c9 _$LT$u8$u20$as$u20$core..clone..Clone$GT$::clone::h7bfab8630dda96cf+0xffff53cc2fce4019 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3849e4 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce41a4 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.011900:     100227 cycles:u: 
	          390da8 core::cmp::impls::_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$usize$GT$::lt::hf4d08bdc2d45569c+0xffff53cc2fce4038 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          394a27 core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce4037 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.012032:     115539 cycles:u: 
	          393fec core::ptr::write::haabbb39ab969e5ac+0xffff53cc2fce401c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384a01 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce41c1 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.012182:     128577 cycles:u: 
	          390870 _$LT$usize$u20$as$u20$core..iter..range..Step$GT$::add_one::h0701a52b56dc0bbb+0xffff53cc2fce4000 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.012352:     139193 cycles:u: 
	          393fe9 core::ptr::write::haabbb39ab969e5ac+0xffff53cc2fce4019 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384a01 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce41c1 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.012533:     147440 cycles:u: 
	          390d93 core::cmp::impls::_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$usize$GT$::lt::hf4d08bdc2d45569c+0xffff53cc2fce4023 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          394a27 core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce4037 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.012723:     154471 cycles:u: 
	          38e1cc _$LT$u8$u20$as$u20$core..clone..Clone$GT$::clone::h7bfab8630dda96cf+0xffff53cc2fce401c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3849e4 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce41a4 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.012923:     160490 cycles:u: 
	          3858c0 _$LT$collections..vec..Vec$LT$T$GT$$GT$::set_len::hfd64239413386906+0xffff53cc2fce4000 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.013177:     161094 cycles:u: 
	          39448e core::str::next_code_point::hc208947b28200a26+0xffff53cc2fce402e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3abf9d _$LT$core..str..Chars$LT$$u27$a$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h3192360e451206ea+0xffff53cc2fce401d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ae5f3 _$LT$core..str..CharIndices$LT$$u27$a$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h6d3c2b6db3f8302b+0xffff53cc2fce4043 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37aaff _$LT$core..str..pattern..CharEqSearcher$LT$$u27$a$C$$u20$C$GT$$u20$as$u20$core..str..pattern..Searcher$LT$$u27$a$GT$$GT$::next::hbefb8ac4a49c1d1e+0xffff53cc2fce404f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39471c core::str::pattern::Searcher::next_match::h3b4c786aa5e821fc+0xffff53cc2fce402c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37997c _$LT$core..str..pattern..CharSearcher$LT$$u27$a$GT$$u20$as$u20$core..str..pattern..Searcher$LT$$u27$a$GT$$GT$::next_match::ha7c6689628ca2406+0xffff53cc2fce402c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39aeb4 _$LT$core..str..SplitInternal$LT$$u27$a$C$$u20$P$GT$$GT$::next::h6ff90db1c517060d+0xffff53cc2fce4074 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3af16c _$LT$core..str..Split$LT$$u27$a$C$$u20$P$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h640ea8ab0b8e51ce+0xffff53cc2fce402c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ad6ac _$LT$core..iter..Map$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h87371dcef7b54f45+0xffff53cc2fce402c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384155 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::hf30fd19863432c7b+0xffff53cc2fce4065 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b00e7 _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::he13ccf618f424fa8+0xffff53cc2fce43a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395d3d core::iter::iterator::Iterator::collect::h185e3c12af3fc754+0xffff53cc2fce40ad (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b4d84 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5254 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.013402:     160789 cycles:u: 
	          390d9a core::cmp::impls::_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$usize$GT$::lt::hf4d08bdc2d45569c+0xffff53cc2fce402a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          394a27 core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce4037 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a762f _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hb223ec6a8effeb5f+0xffff53cc2fce402f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3aeeb8 _$LT$core..iter..FilterMap$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::he1189ac8e3187a50+0xffff53cc2fce4078 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37f201 _$LT$$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$..from_iter..Adapter$LT$Iter$C$$u20$E$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h2d4f8260955f00a9+0xffff53cc2fce4051 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a74bf _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h1191ad8aa35e2822+0xffff53cc2fce402f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37b9e4 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::h42174e2928c223fa+0xffff53cc2fce4074 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d93e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h9375446625dcf9e8+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c35f _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h72ebd1bc97b2075e+0xffff53cc2fce414f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395c62 core::iter::iterator::Iterator::collect::h08724396296cdea6+0xffff53cc2fce4062 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b534b term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce581b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.013613:     163021 cycles:u: 
	          39f09a _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..ops..DerefMut$GT$::deref_mut::h1489b09ee24485ec+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          383da1 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::h5bbb8e6b5a245d7f+0xffff53cc2fce4181 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3afc1e _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::h461e3a924bca1725+0xffff53cc2fce429e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c744 _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h7ad818accf02e73a+0xffff53cc2fce4144 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395eca core::iter::iterator::Iterator::collect::h53b7863e73fadbfc+0xffff53cc2fce405a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b55c6 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5a96 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.013829:     166807 cycles:u: 
	          396798 core::slice::from_raw_parts::hf2d070766f9033b0+0xffff53cc2fce4038 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          396c58 core::slice::_$LT$impl$u20$core..ops..Index$LT$core..ops..Range$LT$usize$GT$$GT$$u20$for$u20$$u5b$T$u5d$$GT$::index::h4d1325c39b2e2225+0xffff53cc2fce40e8 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          396ce8 core::slice::_$LT$impl$u20$core..ops..Index$LT$core..ops..RangeTo$LT$usize$GT$$GT$$u20$for$u20$$u5b$T$u5d$$GT$::index::h9c7a1847e7ff452a+0xffff53cc2fce4068 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39a093 _$LT$$u5b$T$u5d$$u20$as$u20$core..slice..SliceExt$GT$::split_at::h80c52dadb70c5280+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37a36c collections::slice::_$LT$impl$u20$$u5b$T$u5d$$GT$::split_at::h9377899ac1bb5027+0xffff53cc2fce404c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38bb4a std::io::impls::_$LT$impl$u20$std..io..Read$u20$for$u20$$RF$$u27$a$u20$$u5b$u8$u5d$$GT$::read::he880dac0905a777b+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39fec8 _$LT$std..io..buffered..BufReader$LT$R$GT$$u20$as$u20$std..io..Read$GT$::read::h0092352920edf903+0xffff53cc2fce4238 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b3682 term::terminfo::parser::compiled::read_le_u16::hd7bae50659ca04c4+0xffff53cc2fce4112 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c28e2 term::terminfo::parser::compiled::parse::_$u7b$$u7b$closure$u7d$$u7d$::h503fb3ffeae6899e+0xffff53cc2fce4032 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3935ff core::ops::impls::_$LT$impl$u20$core..ops..FnOnce$LT$A$GT$$u20$for$u20$$RF$$u27$a$u20$mut$u20$F$GT$::call_once::h493b1835d917190a+0xffff53cc2fce403f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          380880 _$LT$core..option..Option$LT$T$GT$$GT$::map::h0de7409612e0b28e+0xffff53cc2fce4100 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ad654 _$LT$core..iter..Map$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h5837a103f73c01d4+0xffff53cc2fce4044 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37fd40 _$LT$$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$..from_iter..Adapter$LT$Iter$C$$u20$E$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hc915775adb42698d+0xffff53cc2fce4040 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a7560 _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h507e8ba941b3616a+0xffff53cc2fce4020 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          383c54 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::h5bbb8e6b5a245d7f+0xffff53cc2fce4034 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3afc1e _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::h461e3a924bca1725+0xffff53cc2fce429e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c744 _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h7ad818accf02e73a+0xffff53cc2fce4144 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395eca core::iter::iterator::Iterator::collect::h53b7863e73fadbfc+0xffff53cc2fce405a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b55c6 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5a96 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.014045:     170071 cycles:u: 
	          3996a8 _$LT$$u5b$T$u5d$$u20$as$u20$core..slice..SliceExt$GT$::get_unchecked_mut::h49fa6b2e956b2ceb+0xffff53cc2fce4018 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379f7d collections::slice::_$LT$impl$u20$$u5b$T$u5d$$GT$::get_unchecked_mut::hecb03b761e9b7d9e+0xffff53cc2fce403d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          383dc8 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::h5bbb8e6b5a245d7f+0xffff53cc2fce41a8 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3afc1e _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::h461e3a924bca1725+0xffff53cc2fce429e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c744 _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h7ad818accf02e73a+0xffff53cc2fce4144 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395eca core::iter::iterator::Iterator::collect::h53b7863e73fadbfc+0xffff53cc2fce405a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b55c6 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5a96 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.014265:     173377 cycles:u: 
	          64db3d je_arena_ralloc_no_move+0xffff53cc2fce40ed (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.014492:     176362 cycles:u: 
	          393920 core::ptr::_$LT$impl$u20$$BP$mut$u20$T$GT$::is_null::h0535f15cf1003af9+0xffff53cc2fce4010 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39f13a _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..ops..DerefMut$GT$::deref_mut::hb94ba71a1dd47d71+0xffff53cc2fce402a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          385a6d _$LT$collections..vec..Vec$LT$T$GT$$GT$::truncate::h2f857c8801e6ea48+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38a406 std::io::read_to_end::heeed5f53db31e2d5+0xffff53cc2fce4336 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38abdc std::io::Read::read_to_end::hf3e43392e443d646+0xffff53cc2fce403c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b57bd term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5c8d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.014727:     178602 cycles:u: 
	          37d420 _$LT$std..collections..hash..table..FullBucket$LT$K$C$$u20$V$C$$u20$M$GT$$u20$as$u20$std..collections..hash..table..Put$LT$K$C$$u20$V$GT$$GT$::borrow_table_mut::h7b43ee0d3891fd5f+0xffff53cc2fce4000 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3860b5 std::collections::hash::map::robin_hood::h47885a7d58732a87+0xffff53cc2fce4625 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ac4da _$LT$std..collections..hash..map..VacantEntry$LT$$u27$a$C$$u20$K$C$$u20$V$GT$$GT$::insert::h9082baf2d93a92c8+0xffff53cc2fce41ca (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a0ce2 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert_hashed_nocheck::h980e74df27f75c7d+0xffff53cc2fce43d2 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a293c _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::h111f2759872ecfc7+0xffff53cc2fce417c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bd1d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce417d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.014972:     180014 cycles:u: 
	           83d96 __memmove_sse2_unaligned_erms+0xffff018fd717a096 (/usr/lib/libc-2.24.so)
	          3a3cc0 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::resize::h995383f95c6d03bf+0xffff53cc2fce4700 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a4a70 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::reserve::h5970cef3fb225dd7+0xffff53cc2fce40f0 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a28c3 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::h111f2759872ecfc7+0xffff53cc2fce4103 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bd1d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce417d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.015205:     180495 cycles:u: 
	          38837d std::collections::hash::map::search_hashed::hae33740b510f48a0+0xffff53cc2fce416d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a0a4b _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert_hashed_nocheck::h980e74df27f75c7d+0xffff53cc2fce413b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a293c _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::h111f2759872ecfc7+0xffff53cc2fce417c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bd1d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce417d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.015439:     182088 cycles:u: 
	          3960c1 core::iter::iterator::Iterator::position::h71611bea69605be3+0xffff53cc2fce4131 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2c35 term::terminfo::parser::compiled::parse::_$u7b$$u7b$closure$u7d$$u7d$::hf95a5d0239e6304e+0xffff53cc2fce42c5 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39375f core::ops::impls::_$LT$impl$u20$core..ops..FnOnce$LT$A$GT$$u20$for$u20$$RF$$u27$a$u20$mut$u20$F$GT$::call_once::ha8cdcc06344fe1b5+0xffff53cc2fce404f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          381abb _$LT$core..option..Option$LT$T$GT$$GT$::map::hbf38c6908f07f125+0xffff53cc2fce414b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ad7a4 _$LT$core..iter..Map$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hc8817fc039bbb0b3+0xffff53cc2fce4044 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37f5a4 _$LT$$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$..from_iter..Adapter$LT$Iter$C$$u20$E$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h5c98bd2640d176d7+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a75af _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h7e7c4b3a79f55d8f+0xffff53cc2fce402f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bc17 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce4077 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.015682:     183668 cycles:u: 
	          3a19ba _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert_hashed_ordered::h7b9d42bf7a5f0d35+0xffff53cc2fce407a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a3c2e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::resize::h995383f95c6d03bf+0xffff53cc2fce466e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a4a70 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::reserve::h5970cef3fb225dd7+0xffff53cc2fce40f0 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a28c3 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::h111f2759872ecfc7+0xffff53cc2fce4103 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bd1d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce417d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.015920:     184354 cycles:u: 
	          393c78 core::ptr::_$LT$impl$u20$$BP$const$u20$T$GT$::offset::hffec494e9fc99daf+0xffff53cc2fce4028 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3af497 _$LT$core..slice..Iter$LT$$u27$a$C$$u20$T$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::ha5ae786e76ac1132+0xffff53cc2fce40c7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a7480 _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h01cfe524df2e1837+0xffff53cc2fce4020 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3abf38 _$LT$core..iter..Enumerate$LT$I$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h6382d5064d2f104a+0xffff53cc2fce4028 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          396032 core::iter::iterator::Iterator::position::h71611bea69605be3+0xffff53cc2fce40a2 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2c35 term::terminfo::parser::compiled::parse::_$u7b$$u7b$closure$u7d$$u7d$::hf95a5d0239e6304e+0xffff53cc2fce42c5 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39375f core::ops::impls::_$LT$impl$u20$core..ops..FnOnce$LT$A$GT$$u20$for$u20$$RF$$u27$a$u20$mut$u20$F$GT$::call_once::ha8cdcc06344fe1b5+0xffff53cc2fce404f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          381abb _$LT$core..option..Option$LT$T$GT$$GT$::map::hbf38c6908f07f125+0xffff53cc2fce414b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ad7a4 _$LT$core..iter..Map$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hc8817fc039bbb0b3+0xffff53cc2fce4044 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37f5a4 _$LT$$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$..from_iter..Adapter$LT$Iter$C$$u20$E$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h5c98bd2640d176d7+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a75af _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h7e7c4b3a79f55d8f+0xffff53cc2fce402f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bc17 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce4077 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.016158:     185542 cycles:u: 
	          39b78f _$LT$$RF$$u27$a$u20$mut$u20$T$u20$as$u20$core..ops..Deref$GT$::deref::h9629b61700da8d48+0xffff53cc2fce401f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379734 _$LT$std..collections..hash..table..FullBucket$LT$K$C$$u20$V$C$$u20$M$GT$$u20$as$u20$core..ops..Deref$GT$::deref::hf5640e419faf6aa3+0xffff53cc2fce4024 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a8424 _$LT$std..collections..hash..table..FullBucket$LT$K$C$$u20$V$C$$u20$M$GT$$GT$::displacement::h23649dceee14681b+0xffff53cc2fce4074 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          385cb7 std::collections::hash::map::robin_hood::h47885a7d58732a87+0xffff53cc2fce4227 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ac4da _$LT$std..collections..hash..map..VacantEntry$LT$$u27$a$C$$u20$K$C$$u20$V$GT$$GT$::insert::h9082baf2d93a92c8+0xffff53cc2fce41ca (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a0ce2 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert_hashed_nocheck::h980e74df27f75c7d+0xffff53cc2fce43d2 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a293c _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::h111f2759872ecfc7+0xffff53cc2fce417c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bd1d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce417d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.016398:     186650 cycles:u: 
	          3814d0 _$LT$core..option..Option$LT$T$GT$$GT$::map::h4d3efd64a17eb7e4+0xffff53cc2fce4140 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3abee0 _$LT$core..iter..Enumerate$LT$I$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h503f07e91a8c70cc+0xffff53cc2fce4060 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a771f _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hf6d2c4c5538b1320+0xffff53cc2fce402f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ae389 _$LT$core..iter..Filter$LT$I$C$$u20$P$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hc46470b5838977c4+0xffff53cc2fce4069 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ad78c _$LT$core..iter..Map$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hc8817fc039bbb0b3+0xffff53cc2fce402c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37f5a4 _$LT$$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$..from_iter..Adapter$LT$Iter$C$$u20$E$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h5c98bd2640d176d7+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a75af _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h7e7c4b3a79f55d8f+0xffff53cc2fce402f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bc17 _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce4077 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c003a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::hcd1c44cd143417f6+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0f93 term::stderr::h99e770fdfcb59b6c+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375a34 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4064 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.016703:     187543 cycles:u: 
	          394a2d core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce403d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.016947:     183381 cycles:u: 
	          38e1cc _$LT$u8$u20$as$u20$core..clone..Clone$GT$::clone::h7bfab8630dda96cf+0xffff53cc2fce401c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3849e4 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce41a4 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.017182:     183868 cycles:u: 
	          3922ce core::mem::swap::hcb834a1162e5ad6c+0xffff53cc2fce404e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          394a77 core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce4087 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.017418:     185267 cycles:u: 
	          394a2a core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce403a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.017651:     186625 cycles:u: 
	          394a2d core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce403d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.017847:     188406 cycles:u: 
	          390da3 core::cmp::impls::_$LT$impl$u20$core..cmp..PartialOrd$u20$for$u20$usize$GT$::lt::hf4d08bdc2d45569c+0xffff53cc2fce4033 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          394a27 core::iter::range::_$LT$impl$u20$core..iter..iterator..Iterator$u20$for$u20$core..ops..Range$LT$A$GT$$GT$::next::hd0b7b2668add6c40+0xffff53cc2fce4037 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384985 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce4145 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          379bd0 collections::vec::from_elem::h0cb09490c5e14fb9+0xffff53cc2fce4080 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e54d _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::with_capacity::h149b1cb009d20694+0xffff53cc2fce405d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38e637 _$LT$std..io..buffered..BufReader$LT$R$GT$$GT$::new::h893d205748cacdd5+0xffff53cc2fce4057 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1dd3 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4263 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.018094:     194816 cycles:u: 
	          39330f core::num::_$LT$impl$u20$usize$GT$::overflowing_add::h4bf465fac5e6d437+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          389868 std::collections::hash::table::calculate_offsets::hfe569e8904133a1b+0xffff53cc2fce4068 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39c714 _$LT$std..collections..hash..table..RawTable$LT$K$C$$u20$V$GT$$GT$::first_bucket_raw::hb9d07d7e2fb8d059+0xffff53cc2fce40f4 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a6bd5 _$LT$std..collections..hash..table..Bucket$LT$K$C$$u20$V$C$$u20$M$GT$$GT$::at_index::h34ab787a9a6009ea+0xffff53cc2fce40c5 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a50cf _$LT$std..collections..hash..table..Bucket$LT$K$C$$u20$V$C$$u20$M$GT$$GT$::new::h610d20a99611ae46+0xffff53cc2fce408f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          388a7b std::collections::hash::map::search_hashed::hb56562c80a350a98+0xffff53cc2fce415b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a10ba _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert_hashed_nocheck::h98844db7b829b7c9+0xffff53cc2fce410a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a2c3c _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::hcb451fe86918197e+0xffff53cc2fce411c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37b86e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::h0c6331f8890ff8d7+0xffff53cc2fce413e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d76e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h84d1c44a62ed8a23+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bf6f _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h16fe3c65a4c16e46+0xffff53cc2fce414f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395f62 core::iter::iterator::Iterator::collect::h6ce9ad9125cfc58e+0xffff53cc2fce4062 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b4fa7 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5477 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.018353:     195165 cycles:u: 
	          3966b8 core::slice::_$LT$impl$u20$core..ops..IndexMut$LT$core..ops..RangeFrom$LT$usize$GT$$GT$$u20$for$u20$$u5b$T$u5d$$GT$::index_mut::hc2a47196a9b3dc9f+0xffff53cc2fce4058 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b364a term::terminfo::parser::compiled::read_le_u16::hd7bae50659ca04c4+0xffff53cc2fce40da (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c28e2 term::terminfo::parser::compiled::parse::_$u7b$$u7b$closure$u7d$$u7d$::h503fb3ffeae6899e+0xffff53cc2fce4032 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3935ff core::ops::impls::_$LT$impl$u20$core..ops..FnOnce$LT$A$GT$$u20$for$u20$$RF$$u27$a$u20$mut$u20$F$GT$::call_once::h493b1835d917190a+0xffff53cc2fce403f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          380880 _$LT$core..option..Option$LT$T$GT$$GT$::map::h0de7409612e0b28e+0xffff53cc2fce4100 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ad654 _$LT$core..iter..Map$LT$I$C$$u20$F$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h5837a103f73c01d4+0xffff53cc2fce4044 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37fd40 _$LT$$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$..from_iter..Adapter$LT$Iter$C$$u20$E$GT$$u20$as$u20$core..iter..iterator..Iterator$GT$::next::hc915775adb42698d+0xffff53cc2fce4040 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a7560 _$LT$$RF$$u27$a$u20$mut$u20$I$u20$as$u20$core..iter..iterator..Iterator$GT$::next::h507e8ba941b3616a+0xffff53cc2fce4020 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          383c54 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::h5bbb8e6b5a245d7f+0xffff53cc2fce4034 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3afc1e _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::h461e3a924bca1725+0xffff53cc2fce429e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c744 _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h7ad818accf02e73a+0xffff53cc2fce4144 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395eca core::iter::iterator::Iterator::collect::h53b7863e73fadbfc+0xffff53cc2fce405a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b55c6 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5a96 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.018605:     194314 cycles:u: 
	          385888 _$LT$collections..vec..Vec$LT$T$GT$$GT$::set_len::h32f778ca25724bf1+0xffff53cc2fce4028 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          383dfa _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::h5bbb8e6b5a245d7f+0xffff53cc2fce41da (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3afc1e _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::h461e3a924bca1725+0xffff53cc2fce429e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c744 _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h7ad818accf02e73a+0xffff53cc2fce4144 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395eca core::iter::iterator::Iterator::collect::h53b7863e73fadbfc+0xffff53cc2fce405a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b55c6 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5a96 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.018855:     194076 cycles:u: 
	          39f075 _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..ops..DerefMut$GT$::deref_mut::h1489b09ee24485ec+0xffff53cc2fce4025 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          383da1 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_desugared::h5bbb8e6b5a245d7f+0xffff53cc2fce4181 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3afc1e _$LT$collections..vec..Vec$LT$T$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$T$GT$$GT$::from_iter::h461e3a924bca1725+0xffff53cc2fce429e (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37c744 _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::h7ad818accf02e73a+0xffff53cc2fce4144 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395eca core::iter::iterator::Iterator::collect::h53b7863e73fadbfc+0xffff53cc2fce405a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b55c6 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5a96 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.019105:     194077 cycles:u: 
	          3938bc core::ptr::_$LT$impl$u20$$BP$mut$u20$T$GT$::offset::h83331dc01d57b6ff+0xffff53cc2fce402c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          384914 _$LT$collections..vec..Vec$LT$T$GT$$GT$::extend_with_element::hadc3afe1b04eb21a+0xffff53cc2fce40d4 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38575a _$LT$collections..vec..Vec$LT$T$GT$$GT$::resize::h33edb9dae7314c90+0xffff53cc2fce408a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38a1ae std::io::read_to_end::heeed5f53db31e2d5+0xffff53cc2fce40de (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          38abdc std::io::Read::read_to_end::hf3e43392e443d646+0xffff53cc2fce403c (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b57bd term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5c8d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

emulator  4152 75695.019356:     194103 cycles:u: 
	          392f34 core::num::_$LT$impl$u20$u64$GT$::wrapping_add::h021932e4ee83e791+0xffff53cc2fce4034 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39e93a _$LT$core..hash..sip..Sip13Rounds$u20$as$u20$core..hash..sip..Sip$GT$::d_rounds::hae93b8d08d9c9a13+0xffff53cc2fce434a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39fc6e _$LT$core..hash..sip..Hasher$LT$S$GT$$u20$as$u20$core..hash..Hasher$GT$::finish::ha781266cba0e4a7c+0xffff53cc2fce40ae (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          39dd3d _$LT$core..hash..sip..SipHasher13$u20$as$u20$core..hash..Hasher$GT$::finish::h73025af53645a0f3+0xffff53cc2fce401d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3ac04d _$LT$std..collections..hash..map..DefaultHasher$u20$as$u20$core..hash..Hasher$GT$::finish::h2c804330acc776d7+0xffff53cc2fce401d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          389a32 std::collections::hash::table::make_hash::h9f03ce4a8d5d1b78+0xffff53cc2fce4072 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a4d4d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::make_hash::h992d9241b64fceb7+0xffff53cc2fce402d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3a285f _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$GT$::insert::h111f2759872ecfc7+0xffff53cc2fce409f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37bd1d _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..Extend$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::extend::hdf73438726a85d11+0xffff53cc2fce417d (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37d59e _$LT$std..collections..hash..map..HashMap$LT$K$C$$u20$V$C$$u20$S$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$$LP$K$C$$u20$V$RP$$GT$$GT$::from_iter::h3e3cdf90b15b4d33+0xffff53cc2fce40fe (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37cb8b _$LT$core..result..Result$LT$V$C$$u20$E$GT$$u20$as$u20$core..iter..traits..FromIterator$LT$core..result..Result$LT$A$C$$u20$E$GT$$GT$$GT$::from_iter::hfcc04b97f5e4cef8+0xffff53cc2fce41bb (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          395e27 core::iter::iterator::Iterator::collect::h3b339c4ccaa2a490+0xffff53cc2fce40a7 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b59e9 term::terminfo::parser::compiled::parse::h0bfa24a8d6483291+0xffff53cc2fce5eb9 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1e07 term::terminfo::TermInfo::_from_path::h51064971a80093cd+0xffff53cc2fce4297 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1b54 term::terminfo::TermInfo::from_path::hc007f27f9c5301db+0xffff53cc2fce4054 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c2837 term::terminfo::TermInfo::from_name::_$u7b$$u7b$closure$u7d$$u7d$::hbdb8aa57609652e0+0xffff53cc2fce4047 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3906d1 _$LT$core..result..Result$LT$T$C$$u20$E$GT$$GT$::and_then::h47fa4b8545196b9b+0xffff53cc2fce4161 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b1ae3 term::terminfo::TermInfo::from_name::h721edfed0d4e6840+0xffff53cc2fce4073 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3b17e6 term::terminfo::TermInfo::from_env::h7aa5bbfa652bcb0d+0xffff53cc2fce4186 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3bff3a _$LT$term..terminfo..TerminfoTerminal$LT$T$GT$$GT$::new::h52a3a52cf0fd4041+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3c0ee3 term::stdout::hc71a921b9549a869+0xffff53cc2fce4053 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          375b58 simplelog::termlog::TermLogger::new::h94d15a7bc0cbc21f+0xffff53cc2fce4188 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3788ea simplelog::termlog::TermLogger::init::_$u7b$$u7b$closure$u7d$$u7d$::h347f6695ed91405f+0xffff53cc2fce404a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37883b log::set_logger::_$u7b$$u7b$closure$u7d$$u7d$::hcb7821323b596727+0xffff53cc2fce402b (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          37209a log::set_logger_raw::h2040ab7e0793ea3f+0xffff53cc2fce409a (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          371fc0 log::set_logger::hfce3bfc5d262a203+0xffff53cc2fce4030 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          3759b9 simplelog::termlog::TermLogger::init::ha7463b1622ff979e+0xffff53cc2fce4029 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           61223 emulator::main_ret::hc4b7fa9090639ebe+0xffff53cc2fce40c3 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	           6351f emulator::main::hc2aaa9b4591a10c7+0xffff53cc2fce400f (/home/<USER>/dev/rust/dcpu/target/debug/emulator)
	          63c477 __rust_maybe_catch_panic+0xffff53cc2fce4017 (/home/<USER>/dev/rust/dcpu/target/debug/emulator)

