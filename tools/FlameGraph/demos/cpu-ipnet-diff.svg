<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="546" onload="init(evt)" viewBox="0 0 1200 546" xmlns="http://www.w3.org/2000/svg" >
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	rect[rx]:hover { stroke:black; stroke-width:1; }
	text:hover { stroke:black; stroke-width:1; stroke-opacity:0.35; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="546.0" fill="url(#background)"  />
<text text-anchor="middle" x="600" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="left" x="10" y="529" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Function:</text>
<text text-anchor="" x="70" y="529" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<rect x="927.8" y="305" width="7.7" height="15.0" fill="rgb(0, 162, 226)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="893.1" y="449" width="42.4" height="15.0" fill="rgb(77, 195, 159)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_drain (4 sample weight, 11 samples, 3.59%)')" onmouseout="c()" />
<rect x="908.5" y="273" width="3.9" height="15.0" fill="rgb(0, 155, 218)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="530.6" y="305" width="108.0" height="15.0" fill="rgb(63, 174, 182)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_proto_fanout (8 sample weight, 28 samples, 9.15%)')" onmouseout="c()" />
<text text-anchor="" x="533.588235294118" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_proto_fanout (8 sample weight, 28 samples, 9.15%)')" onmouseout="c()" >mac`mac_rx_s..</text>
<rect x="492.0" y="113" width="38.6" height="15.0" fill="rgb(246, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="912.4" y="129" width="7.7" height="15.0" fill="rgb(0, 184, 236)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1043.5" y="177" width="7.7" height="15.0" fill="rgb(0, 180, 215)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="156.5" y="289" width="7.7" height="15.0" fill="rgb(0, 161, 223)" rx="2" ry="2" onmouseover="s('ip`tcp_send (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1066.6" y="209" width="7.7" height="15.0" fill="rgb(0, 194, 233)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1004.9" y="257" width="38.6" height="15.0" fill="rgb(0, 183, 223)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="10.0" y="97" width="138.8" height="15.0" fill="rgb(0, 190, 248)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ipnet`ipobs_bounce_func (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >ipnet`ipobs_boun..</text>
<rect x="1074.3" y="65" width="92.6" height="15.0" fill="rgb(0, 172, 216)" rx="2" ry="2" onmouseover="s('hook`hook_run (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('hook`hook_run (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >hook`hook_r..</text>
<rect x="935.5" y="353" width="69.4" height="15.0" fill="rgb(0, 192, 247)" rx="2" ry="2" onmouseover="s('hook`hook_run (-18 sample weight, 18 samples, 5.88%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('hook`hook_run (-18 sample weight, 18 samples, 5.88%)')" onmouseout="c()" >hook`hoo..</text>
<rect x="156.5" y="273" width="7.7" height="15.0" fill="rgb(0, 185, 254)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="156.5" y="305" width="7.7" height="15.0" fill="rgb(0, 199, 211)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="314.6" y="193" width="23.2" height="15.0" fill="rgb(0, 184, 227)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="10.0" y="113" width="138.8" height="15.0" fill="rgb(0, 165, 215)" rx="2" ry="2" onmouseover="s('hook`hook_run (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('hook`hook_run (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >hook`hook_run</text>
<rect x="908.5" y="145" width="3.9" height="15.0" fill="rgb(0, 157, 230)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="337.8" y="273" width="77.1" height="15.0" fill="rgb(0, 176, 232)" rx="2" ry="2" onmouseover="s('hook`hook_run (-20 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('hook`hook_run (-20 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >hook`hook..</text>
<rect x="337.8" y="465" width="300.8" height="15.0" fill="rgb(182, 199, 58)" rx="2" ry="2" onmouseover="s('unix`dispatch_hardint (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`dispatch_hardint (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >unix`dispatch_hardint</text>
<rect x="1004.9" y="177" width="38.6" height="15.0" fill="rgb(0, 154, 208)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="638.6" y="289" width="246.8" height="15.0" fill="rgb(0, 177, 228)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_send (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >mac`mac_tx_send</text>
<rect x="453.5" y="321" width="185.1" height="15.0" fill="rgb(53, 171, 168)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_drain (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_drain (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >mac`mac_rx_srs_drain</text>
<rect x="152.7" y="145" width="3.8" height="15.0" fill="rgb(0, 164, 231)" rx="2" ry="2" onmouseover="s('ip`ip_input (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1043.5" y="113" width="7.7" height="15.0" fill="rgb(0, 187, 249)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="453.5" y="337" width="185.1" height="15.0" fill="rgb(44, 158, 194)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_process (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_process (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >mac`mac_rx_srs_process</text>
<rect x="164.2" y="257" width="150.4" height="15.0" fill="rgb(0, 195, 219)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >mac`mac_tx</text>
<rect x="156.5" y="129" width="7.7" height="15.0" fill="rgb(0, 151, 233)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="927.8" y="177" width="7.7" height="15.0" fill="rgb(0, 171, 215)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="164.2" y="209" width="150.4" height="15.0" fill="rgb(0, 155, 216)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >mac`mac_promisc_d..</text>
<rect x="1043.5" y="81" width="7.7" height="15.0" fill="rgb(0, 158, 234)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="935.5" y="337" width="69.4" height="15.0" fill="rgb(0, 198, 216)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-18 sample weight, 18 samples, 5.88%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ipnet`ipobs_bounce_func (-18 sample weight, 18 samples, 5.88%)')" onmouseout="c()" >ipnet`ip..</text>
<rect x="638.6" y="193" width="246.8" height="15.0" fill="rgb(0, 190, 223)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ipobs_hook (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >ip`ipobs_hook</text>
<rect x="912.4" y="177" width="7.7" height="15.0" fill="rgb(0, 183, 250)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="638.6" y="305" width="246.8" height="15.0" fill="rgb(0, 168, 247)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_single_ring_mode (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >mac`mac_tx_single_ring_mode</text>
<rect x="314.6" y="177" width="23.2" height="15.0" fill="rgb(0, 188, 254)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="453.5" y="305" width="77.1" height="15.0" fill="rgb(112, 164, 126)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_client_dispatch (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_client_dispatch (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >mac`mac_p..</text>
<rect x="337.8" y="369" width="115.7" height="15.0" fill="rgb(72, 186, 148)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >mac`mac_promi..</text>
<rect x="10.0" y="449" width="142.7" height="15.0" fill="rgb(7, 197, 219)" rx="2" ry="2" onmouseover="s('genunix`write (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`write (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >genunix`write</text>
<rect x="927.8" y="273" width="7.7" height="15.0" fill="rgb(0, 165, 244)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="927.8" y="97" width="7.7" height="15.0" fill="rgb(0, 179, 224)" rx="2" ry="2" onmouseover="s('hook`hook_run (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1051.2" y="113" width="15.4" height="15.0" fill="rgb(0, 192, 224)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="927.8" y="337" width="7.7" height="15.0" fill="rgb(0, 153, 222)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1004.9" y="289" width="38.6" height="15.0" fill="rgb(0, 159, 253)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1004.9" y="49" width="38.6" height="15.0" fill="rgb(0, 183, 245)" rx="2" ry="2" onmouseover="s('hook`hook_run (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1043.5" y="193" width="7.7" height="15.0" fill="rgb(0, 196, 214)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="414.9" y="273" width="38.6" height="15.0" fill="rgb(0, 177, 245)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="152.7" y="353" width="185.1" height="15.0" fill="rgb(233, 151, 5)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_enter (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >ip`squeue_enter</text>
<rect x="561.4" y="273" width="77.2" height="15.0" fill="rgb(120, 154, 107)" rx="2" ry="2" onmouseover="s('mac`mac_rx_deliver (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="564.437908496732" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_deliver (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >mac`mac_r..</text>
<rect x="414.9" y="241" width="38.6" height="15.0" fill="rgb(0, 178, 221)" rx="2" ry="2" onmouseover="s('ip`icmp_send_reply_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="912.4" y="225" width="7.7" height="15.0" fill="rgb(0, 187, 223)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="908.5" y="97" width="3.9" height="15.0" fill="rgb(0, 160, 224)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1004.9" y="337" width="185.1" height="15.0" fill="rgb(163, 170, 44)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (-10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="1007.90196078431" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_enter (-10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >ip`squeue_enter</text>
<rect x="1043.5" y="241" width="7.7" height="15.0" fill="rgb(0, 195, 244)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1051.2" y="321" width="138.8" height="15.0" fill="rgb(206, 151, 28)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (-4 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="1054.17647058824" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_input_data (-4 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >ip`tcp_input_dat..</text>
<rect x="152.7" y="193" width="3.8" height="15.0" fill="rgb(0, 159, 220)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="912.4" y="161" width="7.7" height="15.0" fill="rgb(0, 188, 210)" rx="2" ry="2" onmouseover="s('ip`ip_input (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="912.4" y="209" width="7.7" height="15.0" fill="rgb(0, 186, 245)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="337.8" y="289" width="77.1" height="15.0" fill="rgb(0, 154, 246)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-20 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ipobs_hook (-20 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >ip`ipobs_..</text>
<rect x="314.6" y="97" width="23.2" height="15.0" fill="rgb(0, 168, 251)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="927.8" y="161" width="7.7" height="15.0" fill="rgb(0, 152, 215)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="893.1" y="369" width="15.4" height="15.0" fill="rgb(205, 0, 0)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="492.0" y="161" width="38.6" height="15.0" fill="rgb(234, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="10.0" y="289" width="142.7" height="15.0" fill="rgb(7, 195, 223)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_xmit (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >ip`ip_xmit</text>
<rect x="337.8" y="337" width="115.7" height="15.0" fill="rgb(74, 166, 137)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`dls_rx_promisc (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >dls`dls_rx_pr..</text>
<rect x="314.6" y="225" width="23.2" height="15.0" fill="rgb(0, 154, 238)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="908.5" y="177" width="3.9" height="15.0" fill="rgb(0, 195, 206)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="152.7" y="97" width="3.8" height="15.0" fill="rgb(0, 191, 221)" rx="2" ry="2" onmouseover="s('hook`hook_run (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="414.9" y="289" width="38.6" height="15.0" fill="rgb(0, 161, 239)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="414.9" y="193" width="38.6" height="15.0" fill="rgb(0, 187, 216)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="314.6" y="129" width="23.2" height="15.0" fill="rgb(0, 153, 243)" rx="2" ry="2" onmouseover="s('ip`ip_input (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="912.4" y="353" width="23.1" height="15.0" fill="rgb(146, 179, 76)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (-2 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="10.0" y="129" width="138.8" height="15.0" fill="rgb(0, 153, 228)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ipobs_hook (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >ip`ipobs_hook</text>
<rect x="1074.3" y="145" width="92.6" height="15.0" fill="rgb(0, 193, 240)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch_one (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >mac`mac_pro..</text>
<rect x="908.5" y="241" width="3.9" height="15.0" fill="rgb(0, 169, 209)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="638.6" y="209" width="246.8" height="15.0" fill="rgb(0, 185, 227)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >ip`ill_input_short_v4</text>
<rect x="414.9" y="257" width="38.6" height="15.0" fill="rgb(0, 199, 218)" rx="2" ry="2" onmouseover="s('ip`icmp_inbound_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1004.9" y="65" width="38.6" height="15.0" fill="rgb(0, 193, 235)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="152.7" y="113" width="3.8" height="15.0" fill="rgb(0, 178, 248)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1043.5" y="257" width="7.7" height="15.0" fill="rgb(0, 157, 230)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="600.0" y="161" width="38.6" height="15.0" fill="rgb(0, 168, 212)" rx="2" ry="2" onmouseover="s('ip`icmp_send_reply_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="893.1" y="417" width="42.4" height="15.0" fill="rgb(75, 169, 156)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (4 sample weight, 11 samples, 3.59%)')" onmouseout="c()" />
<rect x="152.7" y="161" width="3.8" height="15.0" fill="rgb(0, 175, 238)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="152.7" y="257" width="3.8" height="15.0" fill="rgb(0, 176, 238)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="908.5" y="209" width="3.9" height="15.0" fill="rgb(0, 154, 237)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="638.6" y="161" width="246.8" height="15.0" fill="rgb(0, 170, 234)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ipnet`ipobs_bounce_func (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >ipnet`ipobs_bounce_func</text>
<rect x="148.8" y="273" width="3.9" height="15.0" fill="rgb(250, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="164.2" y="97" width="150.4" height="15.0" fill="rgb(0, 180, 212)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ipnet`ipobs_bounce_func (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ipnet`ipobs_bounc..</text>
<rect x="908.5" y="225" width="3.9" height="15.0" fill="rgb(0, 184, 252)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1074.3" y="97" width="92.6" height="15.0" fill="rgb(0, 168, 243)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >ip`ill_inpu..</text>
<rect x="414.9" y="145" width="38.6" height="15.0" fill="rgb(0, 167, 228)" rx="2" ry="2" onmouseover="s('hook`hook_run (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="492.0" y="209" width="38.6" height="15.0" fill="rgb(216, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="10.0" y="145" width="138.8" height="15.0" fill="rgb(0, 173, 225)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >ip`ill_input_sho..</text>
<rect x="1004.9" y="241" width="38.6" height="15.0" fill="rgb(0, 178, 210)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="492.0" y="145" width="38.6" height="15.0" fill="rgb(254, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple_v4 (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="600.0" y="145" width="38.6" height="15.0" fill="rgb(0, 169, 243)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="927.8" y="241" width="7.7" height="15.0" fill="rgb(0, 161, 206)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1051.2" y="81" width="15.4" height="15.0" fill="rgb(0, 179, 230)" rx="2" ry="2" onmouseover="s('hook`hook_run (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="1166.9" y="225" width="23.1" height="15.0" fill="rgb(0, 187, 237)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="1043.5" y="161" width="7.7" height="15.0" fill="rgb(0, 180, 213)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="241" width="115.7" height="15.0" fill="rgb(49, 165, 183)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_xmit (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`ip_xmit</text>
<rect x="1004.9" y="273" width="38.6" height="15.0" fill="rgb(0, 154, 208)" rx="2" ry="2" onmouseover="s('ip`tcp_send (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="561.4" y="241" width="77.2" height="15.0" fill="rgb(116, 181, 120)" rx="2" ry="2" onmouseover="s('ip`ip_input (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="564.437908496732" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >ip`ip_inp..</text>
<rect x="1004.9" y="113" width="38.6" height="15.0" fill="rgb(0, 187, 253)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="638.6" y="481" width="551.4" height="15.0" fill="rgb(135, 177, 110)" rx="2" ry="2" onmouseover="s('unix`thread_start (-64 sample weight, 143 samples, 46.73%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`thread_start (-64 sample weight, 143 samples, 46.73%)')" onmouseout="c()" >unix`thread_start</text>
<rect x="1051.2" y="289" width="23.1" height="15.0" fill="rgb(71, 152, 158)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-4 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="908.5" y="401" width="27.0" height="15.0" fill="rgb(199, 156, 31)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (-1 sample weight, 7 samples, 2.29%)')" onmouseout="c()" />
<rect x="561.4" y="193" width="38.6" height="15.0" fill="rgb(0, 152, 247)" rx="2" ry="2" onmouseover="s('hook`hook_run (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="337.8" y="385" width="300.8" height="15.0" fill="rgb(176, 178, 64)" rx="2" ry="2" onmouseover="s('mac`mac_rx_common (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_common (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >mac`mac_rx_common</text>
<rect x="1051.2" y="145" width="15.4" height="15.0" fill="rgb(0, 154, 238)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="10.0" y="417" width="142.7" height="15.0" fill="rgb(7, 153, 213)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_write (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`socket_vop_write (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >sockfs`socket_vo..</text>
<rect x="638.6" y="417" width="254.5" height="15.0" fill="rgb(8, 199, 206)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_ack_timer (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`tcp_ack_timer</text>
<rect x="10.0" y="385" width="142.7" height="15.0" fill="rgb(7, 169, 203)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`so_sendmsg (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >sockfs`so_sendms..</text>
<rect x="885.4" y="337" width="7.7" height="15.0" fill="rgb(0, 151, 224)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="453.5" y="225" width="38.5" height="15.0" fill="rgb(223, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="600.0" y="129" width="38.6" height="15.0" fill="rgb(0, 154, 236)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="10.0" y="305" width="142.7" height="15.0" fill="rgb(6, 176, 211)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_send_wire_v4 (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >ip`ire_send_wire..</text>
<rect x="600.0" y="81" width="38.6" height="15.0" fill="rgb(0, 179, 236)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="492.0" y="177" width="38.6" height="15.0" fill="rgb(229, 0, 0)" rx="2" ry="2" onmouseover="s('ip`icmp_send_reply_v4 (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1043.5" y="145" width="7.7" height="15.0" fill="rgb(0, 182, 232)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="530.6" y="225" width="30.8" height="15.0" fill="rgb(231, 0, 0)" rx="2" ry="2" onmouseover="s('hook`hook_run (8 sample weight, 8 samples, 2.61%)')" onmouseout="c()" />
<rect x="1004.9" y="193" width="38.6" height="15.0" fill="rgb(0, 157, 207)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="530.6" y="289" width="108.0" height="15.0" fill="rgb(65, 172, 158)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_process (8 sample weight, 28 samples, 9.15%)')" onmouseout="c()" />
<text text-anchor="" x="533.588235294118" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_soft_ring_process (8 sample weight, 28 samples, 9.15%)')" onmouseout="c()" >mac`mac_rx_s..</text>
<rect x="927.8" y="81" width="7.7" height="15.0" fill="rgb(0, 188, 247)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1043.5" y="33" width="7.7" height="15.0" fill="rgb(0, 196, 237)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="164.2" y="289" width="150.4" height="15.0" fill="rgb(0, 161, 220)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_xmit (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ip`ip_xmit</text>
<rect x="912.4" y="257" width="7.7" height="15.0" fill="rgb(0, 192, 252)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1043.5" y="225" width="7.7" height="15.0" fill="rgb(0, 197, 232)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="257" width="115.7" height="15.0" fill="rgb(48, 190, 178)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_send_wire_v4 (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`ire_send_w..</text>
<rect x="453.5" y="241" width="77.1" height="15.0" fill="rgb(116, 178, 114)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >ip`ill_in..</text>
<rect x="156.5" y="81" width="7.7" height="15.0" fill="rgb(0, 151, 217)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1066.6" y="241" width="7.7" height="15.0" fill="rgb(0, 186, 245)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="935.5" y="417" width="254.5" height="15.0" fill="rgb(171, 152, 62)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_process (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_soft_ring_process (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >mac`mac_rx_soft_ring_process</text>
<rect x="148.8" y="257" width="3.9" height="15.0" fill="rgb(239, 0, 0)" rx="2" ry="2" onmouseover="s('hook`hook_run (1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1074.3" y="273" width="115.7" height="15.0" fill="rgb(47, 182, 172)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_ip_output (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`conn_ip_ou..</text>
<rect x="912.4" y="273" width="7.7" height="15.0" fill="rgb(0, 152, 227)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="927.8" y="145" width="7.7" height="15.0" fill="rgb(0, 157, 247)" rx="2" ry="2" onmouseover="s('ip`ip_input (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="113" width="92.6" height="15.0" fill="rgb(0, 193, 209)" rx="2" ry="2" onmouseover="s('ip`ip_input (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="600.0" y="97" width="38.6" height="15.0" fill="rgb(0, 151, 208)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="314.6" y="273" width="23.2" height="15.0" fill="rgb(0, 177, 223)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="164.2" y="305" width="150.4" height="15.0" fill="rgb(0, 199, 248)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_send_wire_v4 (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ip`ire_send_wire_..</text>
<rect x="1004.9" y="209" width="38.6" height="15.0" fill="rgb(0, 194, 208)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="638.6" y="401" width="254.5" height="15.0" fill="rgb(7, 176, 223)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_send_data (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`tcp_send_data</text>
<rect x="1004.9" y="369" width="185.1" height="15.0" fill="rgb(174, 180, 44)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (-10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="1007.90196078431" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_recv_local_v4 (-10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >ip`ire_recv_local_v4</text>
<rect x="912.4" y="289" width="15.4" height="15.0" fill="rgb(127, 198, 111)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-2 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="10.0" y="177" width="138.8" height="15.0" fill="rgb(0, 185, 221)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`dls_rx_promisc (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >dls`dls_rx_promi..</text>
<rect x="152.7" y="369" width="185.1" height="15.0" fill="rgb(201, 173, 5)" rx="2" ry="2" onmouseover="s('ip`tcp_sendmsg (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_sendmsg (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >ip`tcp_sendmsg</text>
<rect x="530.6" y="273" width="30.8" height="15.0" fill="rgb(217, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ip_input (8 sample weight, 8 samples, 2.61%)')" onmouseout="c()" />
<rect x="314.6" y="241" width="23.2" height="15.0" fill="rgb(0, 172, 250)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="164.2" y="337" width="173.6" height="15.0" fill="rgb(31, 156, 196)" rx="2" ry="2" onmouseover="s('ip`tcp_output (-39 sample weight, 45 samples, 14.71%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_output (-39 sample weight, 45 samples, 14.71%)')" onmouseout="c()" >ip`tcp_output</text>
<rect x="885.4" y="305" width="7.7" height="15.0" fill="rgb(0, 178, 206)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="164.2" y="177" width="150.4" height="15.0" fill="rgb(0, 198, 209)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`dls_rx_promisc (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >dls`dls_rx_promis..</text>
<rect x="10.0" y="161" width="138.8" height="15.0" fill="rgb(0, 185, 231)" rx="2" ry="2" onmouseover="s('ip`ip_input (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="152.7" y="209" width="3.8" height="15.0" fill="rgb(0, 185, 253)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1051.2" y="97" width="15.4" height="15.0" fill="rgb(0, 193, 211)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="1074.3" y="289" width="115.7" height="15.0" fill="rgb(50, 174, 197)" rx="2" ry="2" onmouseover="s('ip`tcp_send (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_send (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`tcp_send</text>
<rect x="156.5" y="177" width="7.7" height="15.0" fill="rgb(0, 174, 208)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="935.5" y="449" width="254.5" height="15.0" fill="rgb(180, 160, 62)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_drain (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_drain (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >mac`mac_rx_srs_drain</text>
<rect x="314.6" y="321" width="23.2" height="15.0" fill="rgb(0, 189, 247)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="908.5" y="289" width="3.9" height="15.0" fill="rgb(0, 175, 231)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="638.6" y="241" width="246.8" height="15.0" fill="rgb(0, 172, 238)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`dls_rx_promisc (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >dls`dls_rx_promisc</text>
<rect x="927.8" y="289" width="7.7" height="15.0" fill="rgb(0, 164, 248)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="156.5" y="257" width="7.7" height="15.0" fill="rgb(0, 191, 238)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="177" width="92.6" height="15.0" fill="rgb(0, 190, 220)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_send (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >mac`mac_tx_..</text>
<rect x="638.6" y="225" width="246.8" height="15.0" fill="rgb(0, 152, 252)" rx="2" ry="2" onmouseover="s('ip`ip_input (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="1066.6" y="225" width="7.7" height="15.0" fill="rgb(0, 162, 206)" rx="2" ry="2" onmouseover="s('hook`hook_run (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1051.2" y="177" width="15.4" height="15.0" fill="rgb(0, 198, 216)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="935.5" y="465" width="254.5" height="15.0" fill="rgb(173, 174, 60)" rx="2" ry="2" onmouseover="s('mac`mac_srs_worker (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_srs_worker (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >mac`mac_srs_worker</text>
<rect x="314.6" y="305" width="23.2" height="15.0" fill="rgb(0, 151, 232)" rx="2" ry="2" onmouseover="s('ip`tcp_send (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="935.5" y="369" width="69.4" height="15.0" fill="rgb(0, 155, 214)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-18 sample weight, 18 samples, 5.88%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ipobs_hook (-18 sample weight, 18 samples, 5.88%)')" onmouseout="c()" >ip`ipobs..</text>
<rect x="156.5" y="225" width="7.7" height="15.0" fill="rgb(0, 168, 245)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1004.9" y="97" width="38.6" height="15.0" fill="rgb(0, 153, 251)" rx="2" ry="2" onmouseover="s('ip`ip_input (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="152.7" y="337" width="11.5" height="15.0" fill="rgb(156, 199, 69)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (-1 sample weight, 3 samples, 0.98%)')" onmouseout="c()" />
<rect x="908.5" y="113" width="3.9" height="15.0" fill="rgb(0, 160, 215)" rx="2" ry="2" onmouseover="s('hook`hook_run (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="908.5" y="305" width="3.9" height="15.0" fill="rgb(0, 196, 242)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="912.4" y="193" width="7.7" height="15.0" fill="rgb(0, 156, 240)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1051.2" y="129" width="15.4" height="15.0" fill="rgb(0, 178, 243)" rx="2" ry="2" onmouseover="s('ip`ip_input (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="912.4" y="145" width="7.7" height="15.0" fill="rgb(0, 187, 237)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="152.7" y="305" width="3.8" height="15.0" fill="rgb(0, 152, 220)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="10.0" y="497" width="1180.0" height="15.0" fill="rgb(195, 174, 28)" rx="2" ry="2" onmouseover="s('all samples (306 samples, 100%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('all samples (306 samples, 100%)')" onmouseout="c()" ></text>
<rect x="638.6" y="449" width="254.5" height="15.0" fill="rgb(7, 177, 218)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_drain (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`squeue_drain</text>
<rect x="10.0" y="337" width="142.7" height="15.0" fill="rgb(7, 167, 233)" rx="2" ry="2" onmouseover="s('ip`tcp_output (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_output (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >ip`tcp_output</text>
<rect x="908.5" y="353" width="3.9" height="15.0" fill="rgb(0, 175, 250)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="337.8" y="257" width="77.1" height="15.0" fill="rgb(0, 153, 228)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-20 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ipnet`ipobs_bounce_func (-20 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >ipnet`ipo..</text>
<rect x="638.6" y="337" width="246.8" height="15.0" fill="rgb(0, 179, 212)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dld`str_mdata_fastpath_put (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >dld`str_mdata_fastpath_put</text>
<rect x="156.5" y="241" width="7.7" height="15.0" fill="rgb(0, 182, 220)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1043.5" y="209" width="7.7" height="15.0" fill="rgb(0, 151, 208)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="156.5" y="209" width="7.7" height="15.0" fill="rgb(0, 172, 240)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="561.4" y="257" width="77.2" height="15.0" fill="rgb(112, 174, 121)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_rx (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="564.437908496732" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`i_dls_link_rx (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >dls`i_dls..</text>
<rect x="10.0" y="193" width="138.8" height="15.0" fill="rgb(0, 163, 208)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch_one (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >mac`mac_promisc_..</text>
<rect x="453.5" y="193" width="38.5" height="15.0" fill="rgb(247, 0, 0)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="908.5" y="321" width="3.9" height="15.0" fill="rgb(0, 186, 253)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="492.0" y="193" width="38.6" height="15.0" fill="rgb(230, 0, 0)" rx="2" ry="2" onmouseover="s('ip`icmp_inbound_v4 (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="453.5" y="257" width="77.1" height="15.0" fill="rgb(124, 199, 119)" rx="2" ry="2" onmouseover="s('ip`ip_input (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >ip`ip_inp..</text>
<rect x="1051.2" y="257" width="23.1" height="15.0" fill="rgb(83, 181, 167)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-4 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="893.1" y="433" width="42.4" height="15.0" fill="rgb(88, 158, 159)" rx="2" ry="2" onmouseover="s('ip`ip_input (4 sample weight, 11 samples, 3.59%)')" onmouseout="c()" />
<rect x="10.0" y="369" width="142.7" height="15.0" fill="rgb(7, 155, 244)" rx="2" ry="2" onmouseover="s('ip`tcp_sendmsg (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_sendmsg (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >ip`tcp_sendmsg</text>
<rect x="156.5" y="113" width="7.7" height="15.0" fill="rgb(0, 196, 222)" rx="2" ry="2" onmouseover="s('ip`ip_input (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="927.8" y="321" width="7.7" height="15.0" fill="rgb(0, 161, 221)" rx="2" ry="2" onmouseover="s('ip`tcp_send (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="10.0" y="481" width="327.8" height="15.0" fill="rgb(131, 178, 92)" rx="2" ry="2" onmouseover="s('unix`_sys_sysenter_post_swapgs (-36 sample weight, 85 samples, 27.78%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`_sys_sysenter_post_swapgs (-36 sample weight, 85 samples, 27.78%)')" onmouseout="c()" >unix`_sys_sysenter_post_swapgs</text>
<rect x="1043.5" y="273" width="7.7" height="15.0" fill="rgb(0, 178, 244)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="314.6" y="161" width="23.2" height="15.0" fill="rgb(0, 172, 240)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="1051.2" y="273" width="23.1" height="15.0" fill="rgb(69, 178, 156)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-4 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="638.6" y="257" width="246.8" height="15.0" fill="rgb(0, 198, 234)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch_one (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >mac`mac_promisc_dispatch_one</text>
<rect x="1051.2" y="161" width="15.4" height="15.0" fill="rgb(0, 173, 210)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="561.4" y="177" width="38.6" height="15.0" fill="rgb(0, 171, 227)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="893.1" y="401" width="15.4" height="15.0" fill="rgb(234, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="337.8" y="433" width="300.8" height="15.0" fill="rgb(155, 182, 65)" rx="2" ry="2" onmouseover="s('igb`igb_intr_rx (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('igb`igb_intr_rx (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >igb`igb_intr_rx</text>
<rect x="337.8" y="449" width="300.8" height="15.0" fill="rgb(154, 194, 55)" rx="2" ry="2" onmouseover="s('unix`av_dispatch_autovect (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`av_dispatch_autovect (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >unix`av_dispatch_autovect</text>
<rect x="1074.3" y="49" width="92.6" height="15.0" fill="rgb(0, 154, 253)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ipnet`ipobs_bounce_func (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >ipnet`ipobs..</text>
<rect x="1043.5" y="305" width="7.7" height="15.0" fill="rgb(0, 162, 224)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="912.4" y="337" width="15.4" height="15.0" fill="rgb(109, 174, 110)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (-2 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="10.0" y="225" width="138.8" height="15.0" fill="rgb(0, 186, 229)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_send (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >mac`mac_tx_send</text>
<rect x="1004.9" y="353" width="185.1" height="15.0" fill="rgb(186, 175, 49)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (-10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="1007.90196078431" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_fanout_v4 (-10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >ip`ip_fanout_v4</text>
<rect x="1004.9" y="161" width="38.6" height="15.0" fill="rgb(0, 165, 210)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="10.0" y="401" width="142.7" height="15.0" fill="rgb(7, 157, 226)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`socket_sendmsg (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >sockfs`socket_se..</text>
<rect x="314.6" y="113" width="23.2" height="15.0" fill="rgb(0, 176, 219)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="152.7" y="417" width="185.1" height="15.0" fill="rgb(210, 160, 6)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_write (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`socket_vop_write (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >sockfs`socket_vop_writ..</text>
<rect x="453.5" y="273" width="77.1" height="15.0" fill="rgb(120, 169, 115)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`dls_rx_promisc (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >dls`dls_r..</text>
<rect x="1004.9" y="225" width="38.6" height="15.0" fill="rgb(0, 154, 211)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1051.2" y="209" width="15.4" height="15.0" fill="rgb(0, 161, 228)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="337.8" y="481" width="300.8" height="15.0" fill="rgb(157, 186, 62)" rx="2" ry="2" onmouseover="s('unix`switch_sp_and_call (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`switch_sp_and_call (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >unix`switch_sp_and_call</text>
<rect x="935.5" y="433" width="254.5" height="15.0" fill="rgb(179, 164, 64)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_proto_fanout (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_proto_fanout (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >mac`mac_rx_srs_proto_fanout</text>
<rect x="492.0" y="81" width="38.6" height="15.0" fill="rgb(239, 0, 0)" rx="2" ry="2" onmouseover="s('hook`hook_run (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1074.3" y="209" width="92.6" height="15.0" fill="rgb(0, 166, 221)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >mac`mac_tx</text>
<rect x="337.8" y="417" width="300.8" height="15.0" fill="rgb(177, 178, 58)" rx="2" ry="2" onmouseover="s('mac`mac_rx_ring (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_ring (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >mac`mac_rx_ring</text>
<rect x="156.5" y="193" width="7.7" height="15.0" fill="rgb(0, 157, 252)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="638.6" y="465" width="254.5" height="15.0" fill="rgb(8, 191, 213)" rx="2" ry="2" onmouseover="s('ip`squeue_worker (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_worker (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`squeue_worker</text>
<rect x="927.8" y="225" width="7.7" height="15.0" fill="rgb(0, 171, 215)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="156.5" y="145" width="7.7" height="15.0" fill="rgb(0, 167, 240)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="10.0" y="433" width="142.7" height="15.0" fill="rgb(7, 153, 217)" rx="2" ry="2" onmouseover="s('genunix`fop_write (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`fop_write (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >genunix`fop_writ..</text>
<rect x="453.5" y="209" width="38.5" height="15.0" fill="rgb(237, 0, 0)" rx="2" ry="2" onmouseover="s('hook`hook_run (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1051.2" y="225" width="15.4" height="15.0" fill="rgb(0, 183, 233)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="912.4" y="241" width="7.7" height="15.0" fill="rgb(0, 181, 233)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="152.7" y="129" width="3.8" height="15.0" fill="rgb(0, 184, 224)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="156.5" y="161" width="7.7" height="15.0" fill="rgb(0, 166, 245)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1004.9" y="145" width="38.6" height="15.0" fill="rgb(0, 191, 238)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="927.8" y="209" width="7.7" height="15.0" fill="rgb(0, 153, 230)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="314.6" y="257" width="23.2" height="15.0" fill="rgb(0, 193, 234)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="1074.3" y="193" width="92.6" height="15.0" fill="rgb(0, 190, 212)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_single_ring_mode (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >mac`mac_tx_..</text>
<rect x="152.7" y="289" width="3.8" height="15.0" fill="rgb(0, 192, 245)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1043.5" y="97" width="7.7" height="15.0" fill="rgb(0, 194, 248)" rx="2" ry="2" onmouseover="s('ip`ip_input (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="638.6" y="353" width="254.5" height="15.0" fill="rgb(7, 199, 224)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_xmit (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`ip_xmit</text>
<rect x="337.8" y="353" width="115.7" height="15.0" fill="rgb(83, 178, 156)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch_one (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >mac`mac_promi..</text>
<rect x="920.1" y="241" width="7.7" height="15.0" fill="rgb(209, 0, 0)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="305" width="115.7" height="15.0" fill="rgb(42, 165, 165)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_wput_data (-24 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`tcp_wput_d..</text>
<rect x="912.4" y="113" width="7.7" height="15.0" fill="rgb(0, 198, 223)" rx="2" ry="2" onmouseover="s('hook`hook_run (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="935.5" y="401" width="254.5" height="15.0" fill="rgb(151, 180, 65)" rx="2" ry="2" onmouseover="s('ip`ip_input (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="935.5" y="385" width="254.5" height="15.0" fill="rgb(168, 167, 66)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="938.490196078431" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-18 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`ill_input_short_v4</text>
<rect x="156.5" y="49" width="7.7" height="15.0" fill="rgb(0, 156, 246)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="908.5" y="129" width="3.9" height="15.0" fill="rgb(0, 184, 218)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="920.1" y="257" width="7.7" height="15.0" fill="rgb(213, 0, 0)" rx="2" ry="2" onmouseover="s('hook`hook_run (2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="912.4" y="305" width="15.4" height="15.0" fill="rgb(104, 175, 116)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-2 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="152.7" y="241" width="3.8" height="15.0" fill="rgb(0, 155, 245)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="912.4" y="321" width="15.4" height="15.0" fill="rgb(104, 172, 103)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-2 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="530.6" y="209" width="30.8" height="15.0" fill="rgb(228, 0, 0)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (8 sample weight, 8 samples, 2.61%)')" onmouseout="c()" />
<rect x="1051.2" y="65" width="15.4" height="15.0" fill="rgb(0, 150, 210)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="912.4" y="97" width="7.7" height="15.0" fill="rgb(0, 157, 238)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="561.4" y="225" width="77.2" height="15.0" fill="rgb(115, 164, 119)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="564.437908496732" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >ip`ill_in..</text>
<rect x="1051.2" y="305" width="23.1" height="15.0" fill="rgb(80, 198, 148)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (-4 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="164.2" y="225" width="150.4" height="15.0" fill="rgb(0, 181, 231)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_send (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >mac`mac_tx_send</text>
<rect x="927.8" y="129" width="7.7" height="15.0" fill="rgb(0, 199, 237)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="225" width="92.6" height="15.0" fill="rgb(0, 169, 254)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dld`str_mdata_fastpath_put (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >dld`str_mda..</text>
<rect x="638.6" y="433" width="254.5" height="15.0" fill="rgb(7, 155, 210)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_timer_handler (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`tcp_timer_handler</text>
<rect x="1004.9" y="305" width="38.6" height="15.0" fill="rgb(0, 184, 214)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1051.2" y="193" width="15.4" height="15.0" fill="rgb(0, 194, 236)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="337.8" y="401" width="300.8" height="15.0" fill="rgb(166, 195, 55)" rx="2" ry="2" onmouseover="s('mac`mac_rx (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx (-20 sample weight, 78 samples, 25.49%)')" onmouseout="c()" >mac`mac_rx</text>
<rect x="414.9" y="177" width="38.6" height="15.0" fill="rgb(0, 192, 245)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1004.9" y="33" width="38.6" height="15.0" fill="rgb(0, 152, 211)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="414.9" y="225" width="38.6" height="15.0" fill="rgb(0, 150, 253)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="164.2" y="273" width="150.4" height="15.0" fill="rgb(0, 182, 241)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dld`str_mdata_fastpath_put (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >dld`str_mdata_fas..</text>
<rect x="164.2" y="145" width="150.4" height="15.0" fill="rgb(0, 172, 241)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ip`ill_input_shor..</text>
<rect x="927.8" y="113" width="7.7" height="15.0" fill="rgb(0, 190, 224)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1051.2" y="241" width="15.4" height="15.0" fill="rgb(0, 176, 249)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="600.0" y="193" width="38.6" height="15.0" fill="rgb(0, 179, 243)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1074.3" y="81" width="92.6" height="15.0" fill="rgb(0, 178, 233)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ipobs_hook (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >ip`ipobs_ho..</text>
<rect x="638.6" y="177" width="246.8" height="15.0" fill="rgb(0, 192, 232)" rx="2" ry="2" onmouseover="s('hook`hook_run (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('hook`hook_run (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >hook`hook_run</text>
<rect x="530.6" y="257" width="30.8" height="15.0" fill="rgb(218, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (8 sample weight, 8 samples, 2.61%)')" onmouseout="c()" />
<rect x="893.1" y="465" width="42.4" height="15.0" fill="rgb(82, 187, 147)" rx="2" ry="2" onmouseover="s('mac`mac_soft_ring_worker (4 sample weight, 11 samples, 3.59%)')" onmouseout="c()" />
<rect x="152.7" y="225" width="3.8" height="15.0" fill="rgb(0, 160, 208)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="156.5" y="97" width="7.7" height="15.0" fill="rgb(0, 187, 223)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="492.0" y="65" width="38.6" height="15.0" fill="rgb(235, 0, 0)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="600.0" y="65" width="38.6" height="15.0" fill="rgb(0, 166, 231)" rx="2" ry="2" onmouseover="s('hook`hook_run (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1004.9" y="129" width="38.6" height="15.0" fill="rgb(0, 175, 231)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="600.0" y="209" width="38.6" height="15.0" fill="rgb(0, 157, 237)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="152.7" y="465" width="185.1" height="15.0" fill="rgb(218, 153, 5)" rx="2" ry="2" onmouseover="s('genunix`writev32 (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`writev32 (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >genunix`writev32</text>
<rect x="10.0" y="209" width="138.8" height="15.0" fill="rgb(0, 168, 248)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >mac`mac_promisc_..</text>
<rect x="164.2" y="321" width="150.4" height="15.0" fill="rgb(0, 158, 218)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_ip_output (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ip`conn_ip_output</text>
<rect x="148.8" y="241" width="3.9" height="15.0" fill="rgb(240, 0, 0)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="414.9" y="209" width="38.6" height="15.0" fill="rgb(0, 190, 245)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="314.6" y="209" width="23.2" height="15.0" fill="rgb(0, 196, 206)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="156.5" y="65" width="7.7" height="15.0" fill="rgb(0, 169, 232)" rx="2" ry="2" onmouseover="s('hook`hook_run (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="908.5" y="385" width="27.0" height="15.0" fill="rgb(214, 194, 34)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (-1 sample weight, 7 samples, 2.29%)')" onmouseout="c()" />
<rect x="530.6" y="241" width="30.8" height="15.0" fill="rgb(242, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (8 sample weight, 8 samples, 2.61%)')" onmouseout="c()" />
<rect x="453.5" y="369" width="185.1" height="15.0" fill="rgb(51, 153, 175)" rx="2" ry="2" onmouseover="s('mac`mac_rx_flow (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_flow (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >mac`mac_rx_flow</text>
<rect x="314.6" y="81" width="23.2" height="15.0" fill="rgb(0, 153, 229)" rx="2" ry="2" onmouseover="s('hook`hook_run (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="10.0" y="273" width="138.8" height="15.0" fill="rgb(0, 172, 221)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dld`str_mdata_fastpath_put (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >dld`str_mdata_fa..</text>
<rect x="561.4" y="209" width="38.6" height="15.0" fill="rgb(0, 152, 219)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="453.5" y="289" width="77.1" height="15.0" fill="rgb(114, 166, 107)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch_one (10 sample weight, 20 samples, 6.54%)')" onmouseout="c()" >mac`mac_p..</text>
<rect x="638.6" y="273" width="246.8" height="15.0" fill="rgb(0, 187, 229)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >mac`mac_promisc_dispatch</text>
<rect x="1043.5" y="49" width="7.7" height="15.0" fill="rgb(0, 183, 247)" rx="2" ry="2" onmouseover="s('hook`hook_run (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="908.5" y="193" width="3.9" height="15.0" fill="rgb(0, 192, 231)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="492.0" y="129" width="38.6" height="15.0" fill="rgb(234, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="600.0" y="113" width="38.6" height="15.0" fill="rgb(0, 159, 244)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1004.9" y="81" width="38.6" height="15.0" fill="rgb(0, 156, 248)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="10.0" y="257" width="138.8" height="15.0" fill="rgb(0, 192, 231)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >mac`mac_tx</text>
<rect x="152.7" y="433" width="185.1" height="15.0" fill="rgb(234, 190, 6)" rx="2" ry="2" onmouseover="s('genunix`fop_write (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`fop_write (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >genunix`fop_write</text>
<rect x="1004.9" y="321" width="46.3" height="15.0" fill="rgb(38, 153, 171)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (-10 sample weight, 12 samples, 3.92%)')" onmouseout="c()" />
<rect x="152.7" y="401" width="185.1" height="15.0" fill="rgb(204, 180, 5)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`socket_sendmsg (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >sockfs`socket_sendmsg</text>
<rect x="314.6" y="289" width="23.2" height="15.0" fill="rgb(0, 162, 243)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="152.7" y="321" width="11.5" height="15.0" fill="rgb(142, 166, 75)" rx="2" ry="2" onmouseover="s('ip`tcp_output (-1 sample weight, 3 samples, 0.98%)')" onmouseout="c()" />
<rect x="1043.5" y="65" width="7.7" height="15.0" fill="rgb(0, 178, 220)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1043.5" y="289" width="7.7" height="15.0" fill="rgb(0, 179, 239)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="152.7" y="449" width="185.1" height="15.0" fill="rgb(208, 181, 6)" rx="2" ry="2" onmouseover="s('genunix`writev (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`writev (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >genunix`writev</text>
<rect x="893.1" y="385" width="15.4" height="15.0" fill="rgb(242, 0, 0)" rx="2" ry="2" onmouseover="s('hook`hook_run (4 sample weight, 4 samples, 1.31%)')" onmouseout="c()" />
<rect x="314.6" y="145" width="23.2" height="15.0" fill="rgb(0, 164, 253)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="10.0" y="241" width="138.8" height="15.0" fill="rgb(0, 194, 212)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_single_ring_mode (-36 sample weight, 36 samples, 11.76%)')" onmouseout="c()" >mac`mac_tx_singl..</text>
<rect x="927.8" y="193" width="7.7" height="15.0" fill="rgb(0, 180, 239)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="414.9" y="129" width="38.6" height="15.0" fill="rgb(0, 194, 251)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="927.8" y="257" width="7.7" height="15.0" fill="rgb(0, 185, 220)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="600.0" y="177" width="38.6" height="15.0" fill="rgb(0, 158, 232)" rx="2" ry="2" onmouseover="s('ip`icmp_inbound_v4 (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="453.5" y="353" width="185.1" height="15.0" fill="rgb(52, 154, 168)" rx="2" ry="2" onmouseover="s('mac`mac_rx_classify (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="456.464052287582" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_classify (10 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >mac`mac_rx_classify</text>
<rect x="492.0" y="97" width="38.6" height="15.0" fill="rgb(225, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="638.6" y="369" width="254.5" height="15.0" fill="rgb(8, 194, 224)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_send_wire_v4 (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`ire_send_wire_v4</text>
<rect x="920.1" y="273" width="7.7" height="15.0" fill="rgb(233, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="908.5" y="369" width="27.0" height="15.0" fill="rgb(212, 150, 37)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (-1 sample weight, 7 samples, 2.29%)')" onmouseout="c()" />
<rect x="908.5" y="257" width="3.9" height="15.0" fill="rgb(0, 176, 227)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="638.6" y="321" width="246.8" height="15.0" fill="rgb(0, 189, 253)" rx="2" ry="2" onmouseover="s('mac`mac_tx (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx (-64 sample weight, 64 samples, 20.92%)')" onmouseout="c()" >mac`mac_tx</text>
<rect x="908.5" y="161" width="3.9" height="15.0" fill="rgb(0, 197, 239)" rx="2" ry="2" onmouseover="s('ip`ip_input (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="1166.9" y="193" width="23.1" height="15.0" fill="rgb(0, 166, 229)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="337.8" y="321" width="115.7" height="15.0" fill="rgb(79, 173, 150)" rx="2" ry="2" onmouseover="s('ip`ip_input (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="314.6" y="65" width="23.2" height="15.0" fill="rgb(0, 184, 242)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="414.9" y="161" width="38.6" height="15.0" fill="rgb(0, 198, 220)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="1043.5" y="129" width="7.7" height="15.0" fill="rgb(0, 190, 234)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="10.0" y="353" width="142.7" height="15.0" fill="rgb(7, 195, 238)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_enter (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >ip`squeue_enter</text>
<rect x="164.2" y="241" width="150.4" height="15.0" fill="rgb(0, 193, 219)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_tx_single_ring_mode (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >mac`mac_tx_single..</text>
<rect x="908.5" y="337" width="3.9" height="15.0" fill="rgb(0, 168, 242)" rx="2" ry="2" onmouseover="s('ip`tcp_output (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="152.7" y="385" width="185.1" height="15.0" fill="rgb(244, 158, 5)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" />
<text text-anchor="" x="155.679738562092" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`so_sendmsg (-1 sample weight, 48 samples, 15.69%)')" onmouseout="c()" >sockfs`so_sendmsg</text>
<rect x="164.2" y="193" width="150.4" height="15.0" fill="rgb(0, 194, 210)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch_one (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >mac`mac_promisc_d..</text>
<rect x="164.2" y="161" width="150.4" height="15.0" fill="rgb(0, 164, 249)" rx="2" ry="2" onmouseover="s('ip`ip_input (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="1166.9" y="209" width="23.1" height="15.0" fill="rgb(0, 192, 206)" rx="2" ry="2" onmouseover="s('hook`hook_run (-6 sample weight, 6 samples, 1.96%)')" onmouseout="c()" />
<rect x="1074.3" y="129" width="92.6" height="15.0" fill="rgb(0, 164, 240)" rx="2" ry="2" onmouseover="s('dls`dls_rx_promisc (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`dls_rx_promisc (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >dls`dls_rx_..</text>
<rect x="152.7" y="81" width="3.8" height="15.0" fill="rgb(0, 156, 231)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="638.6" y="385" width="254.5" height="15.0" fill="rgb(8, 151, 241)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" />
<text text-anchor="" x="641.562091503268" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_ip_output (-64 sample weight, 66 samples, 21.57%)')" onmouseout="c()" >ip`conn_ip_output</text>
<rect x="492.0" y="225" width="38.6" height="15.0" fill="rgb(249, 0, 0)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="164.2" y="113" width="150.4" height="15.0" fill="rgb(0, 181, 209)" rx="2" ry="2" onmouseover="s('hook`hook_run (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('hook`hook_run (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >hook`hook_run</text>
<rect x="152.7" y="273" width="3.8" height="15.0" fill="rgb(0, 177, 229)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="152.7" y="177" width="3.8" height="15.0" fill="rgb(0, 198, 234)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch_one (-1 sample weight, 1 samples, 0.33%)')" onmouseout="c()" />
<rect x="600.0" y="49" width="38.6" height="15.0" fill="rgb(0, 195, 243)" rx="2" ry="2" onmouseover="s('ipnet`ipobs_bounce_func (-10 sample weight, 10 samples, 3.27%)')" onmouseout="c()" />
<rect x="885.4" y="321" width="7.7" height="15.0" fill="rgb(0, 161, 247)" rx="2" ry="2" onmouseover="s('hook`hook_run (-2 sample weight, 2 samples, 0.65%)')" onmouseout="c()" />
<rect x="1074.3" y="161" width="92.6" height="15.0" fill="rgb(0, 199, 236)" rx="2" ry="2" onmouseover="s('mac`mac_promisc_dispatch (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" />
<text text-anchor="" x="1077.3137254902" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_promisc_dispatch (-24 sample weight, 24 samples, 7.84%)')" onmouseout="c()" >mac`mac_pro..</text>
<rect x="337.8" y="305" width="115.7" height="15.0" fill="rgb(85, 168, 157)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" />
<text text-anchor="" x="340.777777777778" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (-20 sample weight, 30 samples, 9.80%)')" onmouseout="c()" >ip`ill_input_..</text>
<rect x="10.0" y="321" width="142.7" height="15.0" fill="rgb(7, 186, 223)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_ip_output (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >ip`conn_ip_outpu..</text>
<rect x="164.2" y="129" width="150.4" height="15.0" fill="rgb(0, 154, 235)" rx="2" ry="2" onmouseover="s('ip`ipobs_hook (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" />
<text text-anchor="" x="167.248366013072" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ipobs_hook (-39 sample weight, 39 samples, 12.75%)')" onmouseout="c()" >ip`ipobs_hook</text>
<rect x="10.0" y="465" width="142.7" height="15.0" fill="rgb(7, 175, 204)" rx="2" ry="2" onmouseover="s('genunix`write32 (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`write32 (-36 sample weight, 37 samples, 12.09%)')" onmouseout="c()" >genunix`write32</text>
</svg>
