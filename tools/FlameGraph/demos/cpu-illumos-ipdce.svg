<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="770" onload="init(evt)" viewBox="0 0 1200 770" xmlns="http://www.w3.org/2000/svg" >
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	rect[rx]:hover { stroke:black; stroke-width:1; }
	text:hover { stroke:black; stroke-width:1; stroke-opacity:0.35; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="770.0" fill="url(#background)"  />
<text text-anchor="middle" x="600" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="left" x="10" y="753" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Function:</text>
<text text-anchor="" x="70" y="753" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<rect x="1044.0" y="497" width="0.2" height="15.0" fill="rgb(229,125,42)" rx="2" ry="2" onmouseover="s('genunix`turnstile_wakeup (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="612.5" y="609" width="0.5" height="15.0" fill="rgb(217,58,49)" rx="2" ry="2" onmouseover="s('genunix`dblk_decref (151 samples, 0.04%)')" onmouseout="c()" />
<rect x="1070.4" y="529" width="0.2" height="15.0" fill="rgb(239,161,16)" rx="2" ry="2" onmouseover="s('genunix`freeb (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="41.5" y="561" width="0.2" height="15.0" fill="rgb(251,117,17)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1158.2" y="481" width="0.2" height="15.0" fill="rgb(233,11,35)" rx="2" ry="2" onmouseover="s('ipf`fr_scanlist (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="577.5" y="641" width="1.4" height="15.0" fill="rgb(224,158,5)" rx="2" ry="2" onmouseover="s('ip`tcp_setsockopt (435 samples, 0.11%)')" onmouseout="c()" />
<rect x="953.6" y="609" width="0.2" height="15.0" fill="rgb(245,205,17)" rx="2" ry="2" onmouseover="s('unix`hat_updateattr (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="198.3" y="577" width="0.2" height="15.0" fill="rgb(236,222,7)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="49.3" y="673" width="1.0" height="15.0" fill="rgb(234,166,15)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_exit (301 samples, 0.08%)')" onmouseout="c()" />
<rect x="654.0" y="321" width="0.2" height="15.0" fill="rgb(222,122,5)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1050.6" y="465" width="0.3" height="15.0" fill="rgb(244,149,49)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (109 samples, 0.03%)')" onmouseout="c()" />
<rect x="980.2" y="593" width="1.4" height="15.0" fill="rgb(217,187,5)" rx="2" ry="2" onmouseover="s('FSS`fss_sleep (439 samples, 0.12%)')" onmouseout="c()" />
<rect x="314.8" y="449" width="0.2" height="15.0" fill="rgb(246,72,39)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_v4 (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="193.9" y="513" width="0.1" height="15.0" fill="rgb(225,203,28)" rx="2" ry="2" onmouseover="s('bmc`kcs_transfer_pending (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="639.4" y="321" width="0.2" height="15.0" fill="rgb(231,155,35)" rx="2" ry="2" onmouseover="s('ipf`frpr_tcpcommon (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="38.4" y="545" width="0.4" height="15.0" fill="rgb(253,151,15)" rx="2" ry="2" onmouseover="s('unix`hment_assign (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="1039.4" y="545" width="6.1" height="15.0" fill="rgb(246,206,38)" rx="2" ry="2" onmouseover="s('ipf`fr_check (1943 samples, 0.51%)')" onmouseout="c()" />
<rect x="663.6" y="337" width="0.3" height="15.0" fill="rgb(214,177,10)" rx="2" ry="2" onmouseover="s('ip`ip_verify_ire (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="136.4" y="513" width="0.8" height="15.0" fill="rgb(213,32,13)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_rx_recycle (239 samples, 0.06%)')" onmouseout="c()" />
<rect x="660.4" y="305" width="0.2" height="15.0" fill="rgb(228,82,10)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="674.2" y="209" width="0.9" height="15.0" fill="rgb(236,195,29)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (270 samples, 0.07%)')" onmouseout="c()" />
<rect x="195.7" y="577" width="1.4" height="15.0" fill="rgb(234,118,30)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (465 samples, 0.12%)')" onmouseout="c()" />
<rect x="22.3" y="593" width="0.4" height="15.0" fill="rgb(211,124,26)" rx="2" ry="2" onmouseover="s('unix`page_lookup (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="182.4" y="497" width="0.1" height="15.0" fill="rgb(237,219,23)" rx="2" ry="2" onmouseover="s('genunix`issig_justlooking (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="1008.4" y="625" width="0.7" height="15.0" fill="rgb(238,159,1)" rx="2" ry="2" onmouseover="s('ip`tcp_time_wait_collector (220 samples, 0.06%)')" onmouseout="c()" />
<rect x="613.6" y="609" width="0.2" height="15.0" fill="rgb(214,199,53)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_unbind_handle (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="556.5" y="481" width="0.6" height="15.0" fill="rgb(221,20,31)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (187 samples, 0.05%)')" onmouseout="c()" />
<rect x="945.5" y="577" width="0.2" height="15.0" fill="rgb(235,164,28)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="327.2" y="529" width="0.4" height="15.0" fill="rgb(245,103,54)" rx="2" ry="2" onmouseover="s('genunix`freeb (118 samples, 0.03%)')" onmouseout="c()" />
<rect x="600.5" y="593" width="0.1" height="15.0" fill="rgb(215,58,21)" rx="2" ry="2" onmouseover="s('mac`mac_rx (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="198.3" y="609" width="0.8" height="15.0" fill="rgb(248,68,23)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (266 samples, 0.07%)')" onmouseout="c()" />
<rect x="621.6" y="529" width="0.7" height="15.0" fill="rgb(224,7,23)" rx="2" ry="2" onmouseover="s('mac`mac_protect_intercept_dhcp (242 samples, 0.06%)')" onmouseout="c()" />
<rect x="542.3" y="625" width="8.2" height="15.0" fill="rgb(223,134,3)" rx="2" ry="2" onmouseover="s('ip`udp_do_connect (2646 samples, 0.69%)')" onmouseout="c()" />
<rect x="755.9" y="513" width="0.2" height="15.0" fill="rgb(207,11,11)" rx="2" ry="2" onmouseover="s('unix`rw_enter (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="786.3" y="561" width="0.4" height="15.0" fill="rgb(210,204,22)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (126 samples, 0.03%)')" onmouseout="c()" />
<rect x="648.3" y="337" width="0.2" height="15.0" fill="rgb(232,169,49)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="968.8" y="545" width="0.2" height="15.0" fill="rgb(213,150,51)" rx="2" ry="2" onmouseover="s('lofs`lo_lookup (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="1003.4" y="689" width="10.6" height="15.0" fill="rgb(246,55,24)" rx="2" ry="2" onmouseover="s('genunix`taskq_thread (3422 samples, 0.90%)')" onmouseout="c()" />
<rect x="756.3" y="529" width="1.3" height="15.0" fill="rgb(222,29,9)" rx="2" ry="2" onmouseover="s('unix`bzero (408 samples, 0.11%)')" onmouseout="c()" />
<rect x="556.0" y="513" width="0.3" height="15.0" fill="rgb(236,178,23)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (109 samples, 0.03%)')" onmouseout="c()" />
<rect x="1034.0" y="609" width="0.1" height="15.0" fill="rgb(234,37,18)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="133.1" y="609" width="0.1" height="15.0" fill="rgb(253,60,26)" rx="2" ry="2" onmouseover="s('genunix`fop_read (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="742.7" y="369" width="0.1" height="15.0" fill="rgb(222,17,23)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="554.1" y="481" width="0.2" height="15.0" fill="rgb(220,161,49)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple_v4 (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="53.3" y="673" width="0.1" height="15.0" fill="rgb(218,143,1)" rx="2" ry="2" onmouseover="s('unix`prunstop (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="647.7" y="353" width="0.3" height="15.0" fill="rgb(231,202,47)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="1018.2" y="577" width="0.2" height="15.0" fill="rgb(237,154,20)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="773.1" y="545" width="0.3" height="15.0" fill="rgb(212,58,15)" rx="2" ry="2" onmouseover="s('unix`do_splx (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="989.5" y="561" width="0.2" height="15.0" fill="rgb(210,81,42)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="659.3" y="369" width="0.2" height="15.0" fill="rgb(251,16,31)" rx="2" ry="2" onmouseover="s('genunix`freemsg (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="1068.0" y="497" width="0.1" height="15.0" fill="rgb(252,196,31)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1146.1" y="497" width="4.2" height="15.0" fill="rgb(234,29,50)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_v4 (1377 samples, 0.36%)')" onmouseout="c()" />
<rect x="38.3" y="593" width="1.1" height="15.0" fill="rgb(215,87,35)" rx="2" ry="2" onmouseover="s('unix`hat_memload (351 samples, 0.09%)')" onmouseout="c()" />
<rect x="673.1" y="321" width="0.1" height="15.0" fill="rgb(254,134,39)" rx="2" ry="2" onmouseover="s('ip`ip_verify_ire (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="311.9" y="449" width="0.3" height="15.0" fill="rgb(228,25,41)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="579.5" y="673" width="2.6" height="15.0" fill="rgb(207,58,26)" rx="2" ry="2" onmouseover="s('genunix`fop_open (868 samples, 0.23%)')" onmouseout="c()" />
<rect x="1013.2" y="593" width="0.2" height="15.0" fill="rgb(222,51,17)" rx="2" ry="2" onmouseover="s('zfs`zio_compress_data (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.6" y="689" width="1.1" height="15.0" fill="rgb(243,12,54)" rx="2" ry="2" onmouseover="s('genunix`open (354 samples, 0.09%)')" onmouseout="c()" />
<rect x="1014.8" y="625" width="0.4" height="15.0" fill="rgb(213,80,50)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (125 samples, 0.03%)')" onmouseout="c()" />
<rect x="964.5" y="577" width="0.9" height="15.0" fill="rgb(233,88,1)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (284 samples, 0.07%)')" onmouseout="c()" />
<rect x="160.1" y="529" width="4.9" height="15.0" fill="rgb(236,209,25)" rx="2" ry="2" onmouseover="s('unix`segkmem_free_vn (1598 samples, 0.42%)')" onmouseout="c()" />
<rect x="93.3" y="465" width="0.2" height="15.0" fill="rgb(237,186,41)" rx="2" ry="2" onmouseover="s('genunix`allocb (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="681.3" y="353" width="61.2" height="15.0" fill="rgb(216,120,49)" rx="2" ry="2" onmouseover="s('ip`conn_connect (19772 samples, 5.19%)')" onmouseout="c()" />
<text text-anchor="" x="684.26173365297" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_connect (19772 samples, 5.19%)')" onmouseout="c()" >ip`conn..</text>
<rect x="1144.2" y="449" width="0.1" height="15.0" fill="rgb(216,206,10)" rx="2" ry="2" onmouseover="s('unix`rw_enter_sleep (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.9" y="625" width="0.8" height="15.0" fill="rgb(216,193,29)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (259 samples, 0.07%)')" onmouseout="c()" />
<rect x="774.2" y="577" width="0.2" height="15.0" fill="rgb(246,63,28)" rx="2" ry="2" onmouseover="s('genunix`cyclic_reprogram_cyclic (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="1081.1" y="465" width="0.8" height="15.0" fill="rgb(214,219,27)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (237 samples, 0.06%)')" onmouseout="c()" />
<rect x="318.9" y="433" width="2.1" height="15.0" fill="rgb(216,133,41)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (690 samples, 0.18%)')" onmouseout="c()" />
<rect x="584.1" y="593" width="0.1" height="15.0" fill="rgb(249,21,17)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="923.0" y="657" width="12.9" height="15.0" fill="rgb(234,99,11)" rx="2" ry="2" onmouseover="s('specfs`spec_ioctl (4165 samples, 1.09%)')" onmouseout="c()" />
<rect x="1051.2" y="433" width="0.2" height="15.0" fill="rgb(235,217,25)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="643.4" y="401" width="0.6" height="15.0" fill="rgb(232,44,14)" rx="2" ry="2" onmouseover="s('ip`ip_squeue_get (201 samples, 0.05%)')" onmouseout="c()" />
<rect x="989.6" y="545" width="0.1" height="15.0" fill="rgb(237,98,31)" rx="2" ry="2" onmouseover="s('unix`tsc_read (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1011.2" y="625" width="0.9" height="15.0" fill="rgb(237,73,36)" rx="2" ry="2" onmouseover="s('unix`disp (312 samples, 0.08%)')" onmouseout="c()" />
<rect x="658.4" y="369" width="0.4" height="15.0" fill="rgb(213,42,19)" rx="2" ry="2" onmouseover="s('genunix`dblk_decref (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="770.3" y="609" width="2.1" height="15.0" fill="rgb(213,112,17)" rx="2" ry="2" onmouseover="s('genunix`callout_expire (683 samples, 0.18%)')" onmouseout="c()" />
<rect x="1050.6" y="449" width="0.2" height="15.0" fill="rgb(230,142,18)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="1012.4" y="657" width="0.2" height="15.0" fill="rgb(210,45,13)" rx="2" ry="2" onmouseover="s('zfs`zio_done (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="1042.1" y="513" width="0.2" height="15.0" fill="rgb(212,88,25)" rx="2" ry="2" onmouseover="s('unix`bcmp (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="668.6" y="273" width="0.8" height="15.0" fill="rgb(219,104,0)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (262 samples, 0.07%)')" onmouseout="c()" />
<rect x="742.2" y="273" width="0.2" height="15.0" fill="rgb(225,143,6)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_v4 (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="170.2" y="593" width="0.2" height="15.0" fill="rgb(240,62,39)" rx="2" ry="2" onmouseover="s('genunix`anon_decref (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="167.1" y="593" width="0.3" height="15.0" fill="rgb(240,68,51)" rx="2" ry="2" onmouseover="s('unix`bzero (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="604.7" y="609" width="0.1" height="15.0" fill="rgb(212,123,33)" rx="2" ry="2" onmouseover="s('genunix`ddi_fm_acc_err_clear (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="340.1" y="657" width="0.5" height="15.0" fill="rgb(254,80,46)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (155 samples, 0.04%)')" onmouseout="c()" />
<rect x="1081.1" y="513" width="0.8" height="15.0" fill="rgb(251,42,52)" rx="2" ry="2" onmouseover="s('ip`tcp_send_synack (268 samples, 0.07%)')" onmouseout="c()" />
<rect x="154.6" y="545" width="0.2" height="15.0" fill="rgb(242,98,47)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="159.3" y="673" width="10.8" height="15.0" fill="rgb(211,51,52)" rx="2" ry="2" onmouseover="s('genunix`fop_ioctl (3471 samples, 0.91%)')" onmouseout="c()" />
<rect x="1042.1" y="497" width="0.2" height="15.0" fill="rgb(242,12,40)" rx="2" ry="2" onmouseover="s('genunix`memcmp (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="1013.4" y="657" width="0.4" height="15.0" fill="rgb(248,133,45)" rx="2" ry="2" onmouseover="s('zfs`zio_vdev_io_done (121 samples, 0.03%)')" onmouseout="c()" />
<rect x="1027.3" y="609" width="0.2" height="15.0" fill="rgb(218,220,39)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (89 samples, 0.02%)')" onmouseout="c()" />
<rect x="540.5" y="545" width="0.6" height="15.0" fill="rgb(243,38,33)" rx="2" ry="2" onmouseover="s('hook`hook_run (210 samples, 0.06%)')" onmouseout="c()" />
<rect x="171.1" y="593" width="0.1" height="15.0" fill="rgb(235,74,50)" rx="2" ry="2" onmouseover="s('unix`caps_charge_adjust (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1159.5" y="481" width="0.3" height="15.0" fill="rgb(226,82,36)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="953.4" y="641" width="0.4" height="15.0" fill="rgb(217,208,9)" rx="2" ry="2" onmouseover="s('genunix`segvn_dup (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="673.1" y="305" width="0.1" height="15.0" fill="rgb(244,100,42)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_pkt (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="949.8" y="577" width="0.4" height="15.0" fill="rgb(218,179,26)" rx="2" ry="2" onmouseover="s('ip`tcp_output (118 samples, 0.03%)')" onmouseout="c()" />
<rect x="212.9" y="673" width="0.3" height="15.0" fill="rgb(238,223,6)" rx="2" ry="2" onmouseover="s('unix`tsc_gethrtimeunscaled (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="1187.1" y="657" width="0.6" height="15.0" fill="rgb(222,110,53)" rx="2" ry="2" onmouseover="s('unix`pg_ev_thread_swtch (172 samples, 0.05%)')" onmouseout="c()" />
<rect x="983.7" y="593" width="0.2" height="15.0" fill="rgb(209,16,3)" rx="2" ry="2" onmouseover="s('genunix`callout_heap_insert (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="1017.4" y="625" width="0.6" height="15.0" fill="rgb(249,150,36)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (185 samples, 0.05%)')" onmouseout="c()" />
<rect x="66.6" y="657" width="0.2" height="15.0" fill="rgb(228,207,13)" rx="2" ry="2" onmouseover="s('unix`tsc_read (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="775.6" y="497" width="6.7" height="15.0" fill="rgb(229,201,29)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (2185 samples, 0.57%)')" onmouseout="c()" />
<rect x="247.1" y="673" width="0.1" height="15.0" fill="rgb(251,9,51)" rx="2" ry="2" onmouseover="s('genunix`getf (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1013.8" y="641" width="0.1" height="15.0" fill="rgb(247,26,52)" rx="2" ry="2" onmouseover="s('zfs`vdev_mirror_io_start (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="938.9" y="577" width="0.5" height="15.0" fill="rgb(206,116,50)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (151 samples, 0.04%)')" onmouseout="c()" />
<rect x="80.8" y="497" width="0.3" height="15.0" fill="rgb(213,53,11)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (93 samples, 0.02%)')" onmouseout="c()" />
<rect x="785.8" y="689" width="1.5" height="15.0" fill="rgb(216,2,40)" rx="2" ry="2" onmouseover="s('genunix`exece (495 samples, 0.13%)')" onmouseout="c()" />
<rect x="213.4" y="641" width="1.8" height="15.0" fill="rgb(212,3,45)" rx="2" ry="2" onmouseover="s('genunix`cv_wait_sig_swap (598 samples, 0.16%)')" onmouseout="c()" />
<rect x="668.0" y="241" width="0.3" height="15.0" fill="rgb(237,144,37)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="137.4" y="465" width="0.2" height="15.0" fill="rgb(217,5,10)" rx="2" ry="2" onmouseover="s('unix`segkmem_free (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1154.3" y="625" width="0.2" height="15.0" fill="rgb(231,121,28)" rx="2" ry="2" onmouseover="s('unix`rw_enter (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="196.1" y="561" width="0.6" height="15.0" fill="rgb(244,114,8)" rx="2" ry="2" onmouseover="s('unix`hment_remove (199 samples, 0.05%)')" onmouseout="c()" />
<rect x="62.7" y="673" width="0.1" height="15.0" fill="rgb(206,165,53)" rx="2" ry="2" onmouseover="s('unix`prunstop (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="1078.9" y="529" width="1.1" height="15.0" fill="rgb(228,74,43)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (336 samples, 0.09%)')" onmouseout="c()" />
<rect x="1157.9" y="625" width="5.4" height="15.0" fill="rgb(222,7,47)" rx="2" ry="2" onmouseover="s('mac`mac_rx_deliver (1745 samples, 0.46%)')" onmouseout="c()" />
<rect x="949.9" y="561" width="0.3" height="15.0" fill="rgb(235,23,17)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="46.9" y="689" width="0.1" height="15.0" fill="rgb(252,165,41)" rx="2" ry="2" onmouseover="s('genunix`audit_getstate (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="215.9" y="641" width="1.2" height="15.0" fill="rgb(248,148,30)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (413 samples, 0.11%)')" onmouseout="c()" />
<rect x="173.4" y="641" width="0.2" height="15.0" fill="rgb(205,19,43)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="654.4" y="337" width="0.6" height="15.0" fill="rgb(249,161,7)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (204 samples, 0.05%)')" onmouseout="c()" />
<rect x="958.5" y="561" width="0.6" height="15.0" fill="rgb(233,217,47)" rx="2" ry="2" onmouseover="s('genunix`as_fault (187 samples, 0.05%)')" onmouseout="c()" />
<rect x="1027.3" y="625" width="0.2" height="15.0" fill="rgb(254,198,34)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="659.9" y="353" width="0.2" height="15.0" fill="rgb(234,23,8)" rx="2" ry="2" onmouseover="s('genunix`allocb (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="616.2" y="625" width="143.5" height="15.0" fill="rgb(210,134,54)" rx="2" ry="2" onmouseover="s('mac`mac_rx (46309 samples, 12.16%)')" onmouseout="c()" />
<text text-anchor="" x="619.243051708157" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx (46309 samples, 12.16%)')" onmouseout="c()" >mac`mac_rx</text>
<rect x="194.6" y="497" width="0.5" height="15.0" fill="rgb(232,53,7)" rx="2" ry="2" onmouseover="s('genunix`fs_dispose (153 samples, 0.04%)')" onmouseout="c()" />
<rect x="58.9" y="657" width="0.7" height="15.0" fill="rgb(223,110,7)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_exit (213 samples, 0.06%)')" onmouseout="c()" />
<rect x="787.0" y="593" width="0.2" height="15.0" fill="rgb(245,76,24)" rx="2" ry="2" onmouseover="s('genunix`exec_args (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="677.9" y="241" width="0.2" height="15.0" fill="rgb(228,94,20)" rx="2" ry="2" onmouseover="s('unix`setbackdq (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="37.8" y="561" width="0.2" height="15.0" fill="rgb(220,79,28)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1051.7" y="529" width="16.2" height="15.0" fill="rgb(216,189,4)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (5235 samples, 1.37%)')" onmouseout="c()" />
<rect x="673.5" y="257" width="0.6" height="15.0" fill="rgb(223,29,8)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (178 samples, 0.05%)')" onmouseout="c()" />
<rect x="1183.5" y="657" width="0.8" height="15.0" fill="rgb(230,124,10)" rx="2" ry="2" onmouseover="s('genunix`restore_mstate (256 samples, 0.07%)')" onmouseout="c()" />
<rect x="1158.7" y="529" width="4.1" height="15.0" fill="rgb(241,35,5)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (1326 samples, 0.35%)')" onmouseout="c()" />
<rect x="1081.4" y="417" width="0.2" height="15.0" fill="rgb(235,154,14)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (85 samples, 0.02%)')" onmouseout="c()" />
<rect x="1188.1" y="641" width="0.3" height="15.0" fill="rgb(241,188,19)" rx="2" ry="2" onmouseover="s('unix`hat_switch (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="749.7" y="433" width="0.5" height="15.0" fill="rgb(245,145,7)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (158 samples, 0.04%)')" onmouseout="c()" />
<rect x="785.9" y="593" width="0.1" height="15.0" fill="rgb(220,174,42)" rx="2" ry="2" onmouseover="s('genunix`as_fault (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1013.2" y="577" width="0.2" height="15.0" fill="rgb(231,224,8)" rx="2" ry="2" onmouseover="s('zfs`lzjb_compress (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="40.9" y="561" width="0.6" height="15.0" fill="rgb(213,144,46)" rx="2" ry="2" onmouseover="s('unix`hment_prepare (175 samples, 0.05%)')" onmouseout="c()" />
<rect x="181.7" y="497" width="0.5" height="15.0" fill="rgb(226,8,23)" rx="2" ry="2" onmouseover="s('unix`inb (145 samples, 0.04%)')" onmouseout="c()" />
<rect x="744.8" y="289" width="0.2" height="15.0" fill="rgb(243,72,24)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="99.5" y="657" width="10.3" height="15.0" fill="rgb(247,156,30)" rx="2" ry="2" onmouseover="s('genunix`fop_read (3315 samples, 0.87%)')" onmouseout="c()" />
<rect x="641.9" y="433" width="104.3" height="15.0" fill="rgb(224,126,4)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (33680 samples, 8.84%)')" onmouseout="c()" />
<text text-anchor="" x="644.888018611051" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_recv_local_v4 (33680 samples, 8.84%)')" onmouseout="c()" >ip`ire_recv_..</text>
<rect x="970.2" y="641" width="0.1" height="15.0" fill="rgb(222,180,23)" rx="2" ry="2" onmouseover="s('unix`bcopy (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="95.2" y="577" width="0.2" height="15.0" fill="rgb(222,49,50)" rx="2" ry="2" onmouseover="s('sockfs`socket_destroy_internal (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="1027.2" y="657" width="0.6" height="15.0" fill="rgb(210,31,43)" rx="2" ry="2" onmouseover="s('ip`tcp_output (196 samples, 0.05%)')" onmouseout="c()" />
<rect x="325.1" y="513" width="0.3" height="15.0" fill="rgb(226,4,35)" rx="2" ry="2" onmouseover="s('genunix`callout_list_get (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="554.1" y="593" width="0.3" height="15.0" fill="rgb(239,183,43)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="319.8" y="401" width="0.3" height="15.0" fill="rgb(216,77,48)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_copy (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="61.2" y="657" width="0.8" height="15.0" fill="rgb(222,18,18)" rx="2" ry="2" onmouseover="s('unix`lock_try (269 samples, 0.07%)')" onmouseout="c()" />
<rect x="638.3" y="305" width="0.2" height="15.0" fill="rgb(250,20,8)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl_spin (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="962.0" y="545" width="0.7" height="15.0" fill="rgb(235,100,16)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage_ra (227 samples, 0.06%)')" onmouseout="c()" />
<rect x="786.1" y="593" width="0.6" height="15.0" fill="rgb(243,43,33)" rx="2" ry="2" onmouseover="s('genunix`as_free (182 samples, 0.05%)')" onmouseout="c()" />
<rect x="551.4" y="673" width="0.1" height="15.0" fill="rgb(224,179,3)" rx="2" ry="2" onmouseover="s('sockfs`getsonode (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="215.5" y="673" width="2.1" height="15.0" fill="rgb(211,190,33)" rx="2" ry="2" onmouseover="s('genunix`lwp_unpark (680 samples, 0.18%)')" onmouseout="c()" />
<rect x="1010.1" y="577" width="0.3" height="15.0" fill="rgb(247,47,10)" rx="2" ry="2" onmouseover="s('genunix`timeout (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="962.8" y="609" width="0.4" height="15.0" fill="rgb(205,191,3)" rx="2" ry="2" onmouseover="s('genunix`fop_map (121 samples, 0.03%)')" onmouseout="c()" />
<rect x="638.6" y="369" width="1.2" height="15.0" fill="rgb(250,191,1)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (368 samples, 0.10%)')" onmouseout="c()" />
<rect x="1077.1" y="449" width="1.0" height="15.0" fill="rgb(207,161,8)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (335 samples, 0.09%)')" onmouseout="c()" />
<rect x="674.5" y="161" width="0.2" height="15.0" fill="rgb(234,161,51)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_dma_bindhdl (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="1028.5" y="609" width="3.2" height="15.0" fill="rgb(206,148,33)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (1039 samples, 0.27%)')" onmouseout="c()" />
<rect x="537.0" y="465" width="0.2" height="15.0" fill="rgb(249,191,45)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1010.7" y="673" width="1.5" height="15.0" fill="rgb(222,194,48)" rx="2" ry="2" onmouseover="s('genunix`taskq_thread_wait (468 samples, 0.12%)')" onmouseout="c()" />
<rect x="1051.2" y="417" width="0.1" height="15.0" fill="rgb(219,193,16)" rx="2" ry="2" onmouseover="s('mac`mac_tx (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1189.2" y="657" width="0.1" height="15.0" fill="rgb(218,48,42)" rx="2" ry="2" onmouseover="s('unix`pg_ev_thread_swtch (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="1150.6" y="545" width="0.4" height="15.0" fill="rgb(222,5,5)" rx="2" ry="2" onmouseover="s('ip`udp_ulp_recv (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="1075.1" y="465" width="0.4" height="15.0" fill="rgb(245,68,23)" rx="2" ry="2" onmouseover="s('hook`hook_run (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="634.5" y="337" width="3.0" height="15.0" fill="rgb(242,35,49)" rx="2" ry="2" onmouseover="s('ipf`fr_ipfcheck (964 samples, 0.25%)')" onmouseout="c()" />
<rect x="673.5" y="273" width="1.7" height="15.0" fill="rgb(227,170,1)" rx="2" ry="2" onmouseover="s('mac`mac_tx (539 samples, 0.14%)')" onmouseout="c()" />
<rect x="1186.7" y="641" width="0.1" height="15.0" fill="rgb(241,140,21)" rx="2" ry="2" onmouseover="s('unix`disp_ratify (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="198.0" y="657" width="1.2" height="15.0" fill="rgb(229,65,23)" rx="2" ry="2" onmouseover="s('genunix`cstatat32 (387 samples, 0.10%)')" onmouseout="c()" />
<rect x="1156.0" y="673" width="0.4" height="15.0" fill="rgb(229,195,37)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (106 samples, 0.03%)')" onmouseout="c()" />
<rect x="97.7" y="593" width="1.5" height="15.0" fill="rgb(229,2,26)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (474 samples, 0.12%)')" onmouseout="c()" />
<rect x="324.6" y="529" width="0.8" height="15.0" fill="rgb(243,198,32)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (259 samples, 0.07%)')" onmouseout="c()" />
<rect x="170.2" y="657" width="0.4" height="15.0" fill="rgb(221,169,12)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (116 samples, 0.03%)')" onmouseout="c()" />
<rect x="535.7" y="561" width="0.3" height="15.0" fill="rgb(206,196,24)" rx="2" ry="2" onmouseover="s('ip`conn_build_hdr_template (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="159.5" y="593" width="0.1" height="15.0" fill="rgb(219,81,52)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_helper_slurp (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="40.2" y="529" width="0.5" height="15.0" fill="rgb(208,184,1)" rx="2" ry="2" onmouseover="s('genunix`avl_add (160 samples, 0.04%)')" onmouseout="c()" />
<rect x="78.5" y="513" width="1.8" height="15.0" fill="rgb(247,204,18)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (576 samples, 0.15%)')" onmouseout="c()" />
<rect x="673.0" y="337" width="2.6" height="15.0" fill="rgb(226,206,45)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (836 samples, 0.22%)')" onmouseout="c()" />
<rect x="139.4" y="625" width="0.2" height="15.0" fill="rgb(209,65,24)" rx="2" ry="2" onmouseover="s('ufs`ufs_read (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="1144.6" y="417" width="0.3" height="15.0" fill="rgb(210,68,36)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_impl_v4 (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="153.1" y="625" width="1.1" height="15.0" fill="rgb(220,75,36)" rx="2" ry="2" onmouseover="s('genunix`exec_args (352 samples, 0.09%)')" onmouseout="c()" />
<rect x="181.6" y="625" width="12.5" height="15.0" fill="rgb(239,127,36)" rx="2" ry="2" onmouseover="s('genunix`stream_runservice (4044 samples, 1.06%)')" onmouseout="c()" />
<rect x="742.0" y="241" width="0.1" height="15.0" fill="rgb(225,208,28)" rx="2" ry="2" onmouseover="s('ip`ipif_lookup_addr_common (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="194.3" y="625" width="3.0" height="15.0" fill="rgb(219,142,42)" rx="2" ry="2" onmouseover="s('genunix`as_free (966 samples, 0.25%)')" onmouseout="c()" />
<rect x="589.9" y="657" width="5.0" height="15.0" fill="rgb(214,197,13)" rx="2" ry="2" onmouseover="s('unix`dtrace_interrupt_enable (1594 samples, 0.42%)')" onmouseout="c()" />
<rect x="324.5" y="545" width="1.0" height="15.0" fill="rgb(247,93,25)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (343 samples, 0.09%)')" onmouseout="c()" />
<rect x="1031.8" y="641" width="0.2" height="15.0" fill="rgb(252,115,1)" rx="2" ry="2" onmouseover="s('ip`tcp_timer (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="1046.6" y="577" width="104.8" height="15.0" fill="rgb(225,150,34)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (33847 samples, 8.89%)')" onmouseout="c()" />
<text text-anchor="" x="1049.55301707501" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_fanout_v4 (33847 samples, 8.89%)')" onmouseout="c()" >ip`ip_fanout..</text>
<rect x="753.1" y="465" width="0.2" height="15.0" fill="rgb(238,18,17)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="320.3" y="369" width="0.2" height="15.0" fill="rgb(247,151,43)" rx="2" ry="2" onmouseover="s('genunix`ddi_fm_acc_err_get (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="970.7" y="689" width="1.1" height="15.0" fill="rgb(233,176,4)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (365 samples, 0.10%)')" onmouseout="c()" />
<rect x="1013.8" y="609" width="0.1" height="15.0" fill="rgb(217,112,23)" rx="2" ry="2" onmouseover="s('zfs`zio_execute (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1158.9" y="465" width="0.2" height="15.0" fill="rgb(208,39,52)" rx="2" ry="2" onmouseover="s('ip`conn_connect (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="923.2" y="641" width="12.7" height="15.0" fill="rgb(210,205,14)" rx="2" ry="2" onmouseover="s('genunix`cdev_ioctl (4076 samples, 1.07%)')" onmouseout="c()" />
<rect x="975.2" y="625" width="0.1" height="15.0" fill="rgb(248,116,20)" rx="2" ry="2" onmouseover="s('fifofs`fifo_poll (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="155.1" y="641" width="0.4" height="15.0" fill="rgb(248,93,15)" rx="2" ry="2" onmouseover="s('intpexec`intpexec (142 samples, 0.04%)')" onmouseout="c()" />
<rect x="152.8" y="513" width="0.1" height="15.0" fill="rgb(214,102,21)" rx="2" ry="2" onmouseover="s('unix`hati_load_common (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="210.1" y="689" width="3.1" height="15.0" fill="rgb(233,144,3)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (994 samples, 0.26%)')" onmouseout="c()" />
<rect x="1150.7" y="465" width="0.2" height="15.0" fill="rgb(238,21,35)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="610.5" y="641" width="5.5" height="15.0" fill="rgb(245,6,46)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_recycle_legacy (1777 samples, 0.47%)')" onmouseout="c()" />
<rect x="770.2" y="625" width="3.3" height="15.0" fill="rgb(212,187,33)" rx="2" ry="2" onmouseover="s('genunix`callout_realtime (1065 samples, 0.28%)')" onmouseout="c()" />
<rect x="1079.2" y="481" width="0.6" height="15.0" fill="rgb(205,174,0)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (172 samples, 0.05%)')" onmouseout="c()" />
<rect x="152.4" y="673" width="3.3" height="15.0" fill="rgb(248,108,54)" rx="2" ry="2" onmouseover="s('genunix`exec_common (1090 samples, 0.29%)')" onmouseout="c()" />
<rect x="967.6" y="641" width="1.7" height="15.0" fill="rgb(248,102,16)" rx="2" ry="2" onmouseover="s('genunix`cstatat_getvp (538 samples, 0.14%)')" onmouseout="c()" />
<rect x="1009.6" y="561" width="0.1" height="15.0" fill="rgb(217,1,18)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.1" y="545" width="4.9" height="15.0" fill="rgb(222,157,8)" rx="2" ry="2" onmouseover="s('unix`segkmem_free (1599 samples, 0.42%)')" onmouseout="c()" />
<rect x="946.0" y="593" width="0.7" height="15.0" fill="rgb(235,78,54)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (227 samples, 0.06%)')" onmouseout="c()" />
<rect x="678.1" y="241" width="0.3" height="15.0" fill="rgb(216,93,36)" rx="2" ry="2" onmouseover="s('unix`setfrontdq (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="199.5" y="625" width="6.0" height="15.0" fill="rgb(205,54,36)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (1942 samples, 0.51%)')" onmouseout="c()" />
<rect x="14.0" y="657" width="0.2" height="15.0" fill="rgb(230,179,39)" rx="2" ry="2" onmouseover="s('genunix`cpu_update_pct (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="937.6" y="641" width="0.2" height="15.0" fill="rgb(223,54,34)" rx="2" ry="2" onmouseover="s('unix`kpreempt (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="1016.7" y="657" width="2.0" height="15.0" fill="rgb(240,20,0)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (630 samples, 0.17%)')" onmouseout="c()" />
<rect x="213.2" y="689" width="4.6" height="15.0" fill="rgb(222,148,25)" rx="2" ry="2" onmouseover="s('genunix`syslwp_park (1473 samples, 0.39%)')" onmouseout="c()" />
<rect x="602.6" y="641" width="7.8" height="15.0" fill="rgb(235,124,21)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_rx (2539 samples, 0.67%)')" onmouseout="c()" />
<rect x="160.2" y="465" width="4.5" height="15.0" fill="rgb(231,151,5)" rx="2" ry="2" onmouseover="s('unix`x86pte_inval (1455 samples, 0.38%)')" onmouseout="c()" />
<rect x="136.9" y="465" width="0.2" height="15.0" fill="rgb(224,211,43)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_alloc (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1068.5" y="401" width="0.1" height="15.0" fill="rgb(254,194,2)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="44.6" y="657" width="0.5" height="15.0" fill="rgb(228,184,9)" rx="2" ry="2" onmouseover="s('genunix`save_syscall_args (154 samples, 0.04%)')" onmouseout="c()" />
<rect x="759.4" y="577" width="0.1" height="15.0" fill="rgb(209,63,13)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="33.0" y="513" width="0.1" height="15.0" fill="rgb(224,124,6)" rx="2" ry="2" onmouseover="s('unix`page_do_hashin (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1186.9" y="641" width="0.2" height="15.0" fill="rgb(213,90,36)" rx="2" ry="2" onmouseover="s('unix`membar_enter (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="154.6" y="561" width="0.2" height="15.0" fill="rgb(224,162,30)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="319.9" y="385" width="0.2" height="15.0" fill="rgb(227,51,38)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="40.3" y="497" width="0.4" height="15.0" fill="rgb(249,1,54)" rx="2" ry="2" onmouseover="s('unix`hment_compare (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="766.5" y="657" width="0.1" height="15.0" fill="rgb(225,110,37)" rx="2" ry="2" onmouseover="s('unix`av_check_softint_pending (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="578.1" y="561" width="0.1" height="15.0" fill="rgb(233,129,6)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="49.2" y="673" width="0.1" height="15.0" fill="rgb(254,121,21)" rx="2" ry="2" onmouseover="s('genunix`clear_stale_fd (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="604.2" y="625" width="0.3" height="15.0" fill="rgb(246,126,31)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (96 samples, 0.03%)')" onmouseout="c()" />
<rect x="975.8" y="593" width="0.4" height="15.0" fill="rgb(212,37,6)" rx="2" ry="2" onmouseover="s('sockfs`so_poll (131 samples, 0.03%)')" onmouseout="c()" />
<rect x="661.3" y="337" width="0.7" height="15.0" fill="rgb(215,94,32)" rx="2" ry="2" onmouseover="s('genunix`fop_open (230 samples, 0.06%)')" onmouseout="c()" />
<rect x="1008.2" y="577" width="0.2" height="15.0" fill="rgb(233,205,31)" rx="2" ry="2" onmouseover="s('unix`tsc_read (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="749.6" y="433" width="0.1" height="15.0" fill="rgb(253,196,8)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_in (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1154.8" y="641" width="1.1" height="15.0" fill="rgb(206,18,47)" rx="2" ry="2" onmouseover="s('mac`mac_strip_vlan_tag_chain (365 samples, 0.10%)')" onmouseout="c()" />
<rect x="1082.3" y="481" width="62.7" height="15.0" fill="rgb(230,162,15)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (20232 samples, 5.31%)')" onmouseout="c()" />
<text text-anchor="" x="1085.3233617523" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_set_destination_v4 (20232 samples, 5.31%)')" onmouseout="c()" >ip`ip_s..</text>
<rect x="347.2" y="545" width="188.2" height="15.0" fill="rgb(230,98,4)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (60754 samples, 15.95%)')" onmouseout="c()" />
<text text-anchor="" x="350.207922299914" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_set_destination_v4 (60754 samples, 15.95%)')" onmouseout="c()" >ip`ip_set_destination_..</text>
<rect x="81.1" y="497" width="13.1" height="15.0" fill="rgb(236,153,18)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_end (4231 samples, 1.11%)')" onmouseout="c()" />
<rect x="578.0" y="577" width="0.2" height="15.0" fill="rgb(229,50,21)" rx="2" ry="2" onmouseover="s('unix`disp (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="337.2" y="609" width="0.3" height="15.0" fill="rgb(217,197,23)" rx="2" ry="2" onmouseover="s('zfs`dmu_write_uio_dnode (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="165.0" y="577" width="0.9" height="15.0" fill="rgb(211,215,34)" rx="2" ry="2" onmouseover="s('genunix`kmem_alloc (274 samples, 0.07%)')" onmouseout="c()" />
<rect x="1074.3" y="417" width="0.8" height="15.0" fill="rgb(244,104,23)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (266 samples, 0.07%)')" onmouseout="c()" />
<rect x="943.8" y="641" width="0.9" height="15.0" fill="rgb(254,154,43)" rx="2" ry="2" onmouseover="s('genunix`vn_openat (319 samples, 0.08%)')" onmouseout="c()" />
<rect x="300.4" y="657" width="37.4" height="15.0" fill="rgb(233,18,10)" rx="2" ry="2" onmouseover="s('genunix`fop_write (12074 samples, 3.17%)')" onmouseout="c()" />
<rect x="943.0" y="641" width="0.6" height="15.0" fill="rgb(208,20,38)" rx="2" ry="2" onmouseover="s('unix`mmapobj_map_elf (220 samples, 0.06%)')" onmouseout="c()" />
<rect x="647.8" y="273" width="0.1" height="15.0" fill="rgb(217,187,5)" rx="2" ry="2" onmouseover="s('mac`mac_tx (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="938.8" y="609" width="0.1" height="15.0" fill="rgb(217,22,43)" rx="2" ry="2" onmouseover="s('zfs`zfs_getattr (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="311.9" y="465" width="0.4" height="15.0" fill="rgb(226,123,23)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="321.7" y="481" width="1.9" height="15.0" fill="rgb(206,74,52)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (603 samples, 0.16%)')" onmouseout="c()" />
<rect x="967.3" y="657" width="2.1" height="15.0" fill="rgb(221,209,1)" rx="2" ry="2" onmouseover="s('genunix`cstatat64_32 (669 samples, 0.18%)')" onmouseout="c()" />
<rect x="159.4" y="625" width="0.3" height="15.0" fill="rgb(238,84,47)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_ioctl (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="215.5" y="657" width="1.7" height="15.0" fill="rgb(210,21,53)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (533 samples, 0.14%)')" onmouseout="c()" />
<rect x="1029.3" y="529" width="0.5" height="15.0" fill="rgb(247,0,6)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (156 samples, 0.04%)')" onmouseout="c()" />
<rect x="773.1" y="593" width="0.3" height="15.0" fill="rgb(234,126,12)" rx="2" ry="2" onmouseover="s('genunix`cyclic_reprogram (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="175.2" y="609" width="0.1" height="15.0" fill="rgb(240,85,4)" rx="2" ry="2" onmouseover="s('lofs`lo_getattr (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="671.8" y="337" width="0.1" height="15.0" fill="rgb(230,159,26)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="195.1" y="513" width="0.1" height="15.0" fill="rgb(209,203,43)" rx="2" ry="2" onmouseover="s('unix`page_lookup_create (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="663.0" y="369" width="0.1" height="15.0" fill="rgb(214,197,39)" rx="2" ry="2" onmouseover="s('ip`tcp_rwnd_reopen (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="98.1" y="561" width="1.1" height="15.0" fill="rgb(208,131,47)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (357 samples, 0.09%)')" onmouseout="c()" />
<rect x="662.9" y="353" width="0.1" height="15.0" fill="rgb(229,215,7)" rx="2" ry="2" onmouseover="s('ip`tcp_mss_set (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="347.4" y="529" width="186.2" height="15.0" fill="rgb(239,156,43)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (60088 samples, 15.78%)')" onmouseout="c()" />
<text text-anchor="" x="350.396921089461" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`dce_lookup_and_add_v4 (60088 samples, 15.78%)')" onmouseout="c()" >ip`dce_lookup_and_add_..</text>
<rect x="340.9" y="673" width="0.2" height="15.0" fill="rgb(205,9,54)" rx="2" ry="2" onmouseover="s('genunix`falloc (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.3" y="385" width="0.6" height="15.0" fill="rgb(253,87,14)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (212 samples, 0.06%)')" onmouseout="c()" />
<rect x="967.6" y="625" width="1.7" height="15.0" fill="rgb(247,226,25)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (535 samples, 0.14%)')" onmouseout="c()" />
<rect x="1037.8" y="625" width="116.1" height="15.0" fill="rgb(243,174,16)" rx="2" ry="2" onmouseover="s('ip`ip_input (37454 samples, 9.83%)')" onmouseout="c()" />
<text text-anchor="" x="1040.83428602937" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (37454 samples, 9.83%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="33.9" y="577" width="1.2" height="15.0" fill="rgb(209,197,48)" rx="2" ry="2" onmouseover="s('unix`pfnzero (401 samples, 0.11%)')" onmouseout="c()" />
<rect x="774.8" y="529" width="7.6" height="15.0" fill="rgb(228,66,23)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter_high (2443 samples, 0.64%)')" onmouseout="c()" />
<rect x="982.6" y="609" width="0.2" height="15.0" fill="rgb(209,129,39)" rx="2" ry="2" onmouseover="s('genunix`schedctl_cancel_pending (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="625.9" y="481" width="126.0" height="15.0" fill="rgb(236,162,29)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_rx (40669 samples, 10.68%)')" onmouseout="c()" />
<text text-anchor="" x="628.925366746401" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`i_dls_link_rx (40669 samples, 10.68%)')" onmouseout="c()" >dls`i_dls_link_..</text>
<rect x="675.2" y="289" width="0.3" height="15.0" fill="rgb(231,146,21)" rx="2" ry="2" onmouseover="s('hook`hook_run (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="67.8" y="705" width="4.3" height="15.0" fill="rgb(232,148,5)" rx="2" ry="2" onmouseover="s('unix`_sys_rtt_ints_disabled (1386 samples, 0.36%)')" onmouseout="c()" />
<rect x="786.1" y="545" width="0.2" height="15.0" fill="rgb(239,14,51)" rx="2" ry="2" onmouseover="s('genunix`segvn_free (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="14.7" y="657" width="27.9" height="15.0" fill="rgb(230,202,19)" rx="2" ry="2" onmouseover="s('genunix`as_fault (9013 samples, 2.37%)')" onmouseout="c()" />
<rect x="182.6" y="513" width="11.0" height="15.0" fill="rgb(214,214,24)" rx="2" ry="2" onmouseover="s('unix`inb (3548 samples, 0.93%)')" onmouseout="c()" />
<rect x="311.3" y="481" width="0.2" height="15.0" fill="rgb(210,190,16)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="988.9" y="577" width="0.8" height="15.0" fill="rgb(246,21,33)" rx="2" ry="2" onmouseover="s('genunix`savectx (252 samples, 0.07%)')" onmouseout="c()" />
<rect x="62.8" y="705" width="0.9" height="15.0" fill="rgb(248,183,39)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800ed8 (289 samples, 0.08%)')" onmouseout="c()" />
<rect x="1014.7" y="641" width="0.5" height="15.0" fill="rgb(237,146,6)" rx="2" ry="2" onmouseover="s('unix`disp (165 samples, 0.04%)')" onmouseout="c()" />
<rect x="680.0" y="369" width="0.9" height="15.0" fill="rgb(237,120,26)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (289 samples, 0.08%)')" onmouseout="c()" />
<rect x="600.5" y="625" width="0.1" height="15.0" fill="rgb(216,153,49)" rx="2" ry="2" onmouseover="s('bnx`bnx_rxpkts_intr (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="618.0" y="561" width="1.2" height="15.0" fill="rgb(216,193,16)" rx="2" ry="2" onmouseover="s('mac`mac_flow_lookup (404 samples, 0.11%)')" onmouseout="c()" />
<rect x="945.9" y="657" width="0.8" height="15.0" fill="rgb(234,139,5)" rx="2" ry="2" onmouseover="s('genunix`cstatat (280 samples, 0.07%)')" onmouseout="c()" />
<rect x="970.0" y="641" width="0.2" height="15.0" fill="rgb(224,176,27)" rx="2" ry="2" onmouseover="s('genunix`watch_disable_addr (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="555.6" y="593" width="2.3" height="15.0" fill="rgb(240,67,45)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (748 samples, 0.20%)')" onmouseout="c()" />
<rect x="613.6" y="593" width="0.2" height="15.0" fill="rgb(237,65,43)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_dma_unbindhdl (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="968.0" y="577" width="1.2" height="15.0" fill="rgb(227,229,45)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (411 samples, 0.11%)')" onmouseout="c()" />
<rect x="22.7" y="593" width="1.0" height="15.0" fill="rgb(248,216,37)" rx="2" ry="2" onmouseover="s('unix`page_lookup_nowait (320 samples, 0.08%)')" onmouseout="c()" />
<rect x="758.7" y="561" width="0.4" height="15.0" fill="rgb(228,49,37)" rx="2" ry="2" onmouseover="s('unix`rw_enter (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="1176.9" y="625" width="4.0" height="15.0" fill="rgb(217,1,31)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl_spin (1294 samples, 0.34%)')" onmouseout="c()" />
<rect x="68.2" y="673" width="3.9" height="15.0" fill="rgb(244,61,12)" rx="2" ry="2" onmouseover="s('unix`trap (1248 samples, 0.33%)')" onmouseout="c()" />
<rect x="742.3" y="225" width="0.1" height="15.0" fill="rgb(244,118,7)" rx="2" ry="2" onmouseover="s('ip`rn_match_args (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1160.1" y="513" width="2.7" height="15.0" fill="rgb(235,214,39)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (875 samples, 0.23%)')" onmouseout="c()" />
<rect x="1072.6" y="497" width="0.4" height="15.0" fill="rgb(211,209,34)" rx="2" ry="2" onmouseover="s('sockfs`socket_newconn (138 samples, 0.04%)')" onmouseout="c()" />
<rect x="208.3" y="641" width="0.3" height="15.0" fill="rgb(224,128,11)" rx="2" ry="2" onmouseover="s('unix`bcopy (118 samples, 0.03%)')" onmouseout="c()" />
<rect x="555.0" y="625" width="21.7" height="15.0" fill="rgb(237,217,25)" rx="2" ry="2" onmouseover="s('ip`udp_send (7022 samples, 1.84%)')" onmouseout="c()" />
<rect x="160.2" y="449" width="4.5" height="15.0" fill="rgb(229,64,24)" rx="2" ry="2" onmouseover="s('unix`hat_tlb_inval (1437 samples, 0.38%)')" onmouseout="c()" />
<rect x="10.0" y="705" width="0.1" height="15.0" fill="rgb(251,30,35)" rx="2" ry="2" onmouseover="s('genunix`dtrace_systrace_syscall32 (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1158.9" y="449" width="0.2" height="15.0" fill="rgb(246,29,42)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="181.6" y="529" width="12.1" height="15.0" fill="rgb(243,44,38)" rx="2" ry="2" onmouseover="s('bmc`kcs_read (3900 samples, 1.02%)')" onmouseout="c()" />
<rect x="1188.4" y="673" width="1.1" height="15.0" fill="rgb(248,151,5)" rx="2" ry="2" onmouseover="s('unix`swtch_to (350 samples, 0.09%)')" onmouseout="c()" />
<rect x="39.9" y="593" width="2.2" height="15.0" fill="rgb(253,213,36)" rx="2" ry="2" onmouseover="s('unix`hati_load_common (696 samples, 0.18%)')" onmouseout="c()" />
<rect x="175.7" y="609" width="5.4" height="15.0" fill="rgb(205,187,4)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (1721 samples, 0.45%)')" onmouseout="c()" />
<rect x="1073.4" y="497" width="2.3" height="15.0" fill="rgb(224,30,19)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (731 samples, 0.19%)')" onmouseout="c()" />
<rect x="583.6" y="593" width="0.4" height="15.0" fill="rgb(234,119,15)" rx="2" ry="2" onmouseover="s('ip`tcp_get_conn (125 samples, 0.03%)')" onmouseout="c()" />
<rect x="155.3" y="529" width="0.1" height="15.0" fill="rgb(246,99,17)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1032.7" y="641" width="0.1" height="15.0" fill="rgb(220,131,53)" rx="2" ry="2" onmouseover="s('unix`lock_set (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1073.7" y="449" width="1.4" height="15.0" fill="rgb(221,225,11)" rx="2" ry="2" onmouseover="s('mac`mac_tx (436 samples, 0.11%)')" onmouseout="c()" />
<rect x="785.4" y="657" width="0.2" height="15.0" fill="rgb(231,217,15)" rx="2" ry="2" onmouseover="s('genunix`fop_read (88 samples, 0.02%)')" onmouseout="c()" />
<rect x="193.7" y="497" width="0.2" height="15.0" fill="rgb(249,146,23)" rx="2" ry="2" onmouseover="s('unix`inb (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="170.2" y="641" width="0.2" height="15.0" fill="rgb(227,30,1)" rx="2" ry="2" onmouseover="s('genunix`seg_free (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="77.9" y="593" width="16.9" height="15.0" fill="rgb(209,157,49)" rx="2" ry="2" onmouseover="s('sockfs`socket_close_internal (5433 samples, 1.43%)')" onmouseout="c()" />
<rect x="198.9" y="561" width="0.2" height="15.0" fill="rgb(240,172,21)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.4" y="625" width="0.2" height="15.0" fill="rgb(212,212,14)" rx="2" ry="2" onmouseover="s('zfs`zio_execute (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="170.2" y="609" width="0.2" height="15.0" fill="rgb(233,106,2)" rx="2" ry="2" onmouseover="s('genunix`anon_free (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="68.7" y="641" width="0.2" height="15.0" fill="rgb(223,109,33)" rx="2" ry="2" onmouseover="s('genunix`cpu_update_pct (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="322.5" y="433" width="0.1" height="15.0" fill="rgb(205,1,3)" rx="2" ry="2" onmouseover="s('ipf`fr_firewall (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="965.1" y="561" width="0.2" height="15.0" fill="rgb(248,198,22)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="1017.5" y="593" width="0.5" height="15.0" fill="rgb(214,7,53)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (158 samples, 0.04%)')" onmouseout="c()" />
<rect x="1012.4" y="641" width="0.2" height="15.0" fill="rgb(235,8,37)" rx="2" ry="2" onmouseover="s('zfs`zio_notify_parent (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="99.2" y="609" width="0.2" height="15.0" fill="rgb(219,137,42)" rx="2" ry="2" onmouseover="s('genunix`vn_createat (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="533.8" y="497" width="0.9" height="15.0" fill="rgb(233,25,36)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_v4 (282 samples, 0.07%)')" onmouseout="c()" />
<rect x="557.9" y="577" width="18.5" height="15.0" fill="rgb(222,142,31)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_v4 (5954 samples, 1.56%)')" onmouseout="c()" />
<rect x="78.7" y="449" width="0.2" height="15.0" fill="rgb(231,211,12)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="75.7" y="625" width="0.3" height="15.0" fill="rgb(232,66,39)" rx="2" ry="2" onmouseover="s('genunix`segvn_create (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="334.7" y="561" width="0.1" height="15.0" fill="rgb(254,124,41)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb85a6ea (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="38.5" y="529" width="0.3" height="15.0" fill="rgb(225,54,45)" rx="2" ry="2" onmouseover="s('unix`hment_insert (103 samples, 0.03%)')" onmouseout="c()" />
<rect x="949.5" y="689" width="1.2" height="15.0" fill="rgb(217,207,24)" rx="2" ry="2" onmouseover="s('genunix`write (403 samples, 0.11%)')" onmouseout="c()" />
<rect x="1052.0" y="433" width="0.1" height="15.0" fill="rgb(228,21,43)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="640.6" y="385" width="0.2" height="15.0" fill="rgb(225,65,8)" rx="2" ry="2" onmouseover="s('ipf`fr_checkstate (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="171.0" y="609" width="0.2" height="15.0" fill="rgb(232,134,35)" rx="2" ry="2" onmouseover="s('unix`cpucaps_charge (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="938.9" y="625" width="1.9" height="15.0" fill="rgb(234,101,45)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (610 samples, 0.16%)')" onmouseout="c()" />
<rect x="785.9" y="609" width="0.2" height="15.0" fill="rgb(246,37,25)" rx="2" ry="2" onmouseover="s('genunix`execmap (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="924.4" y="609" width="0.3" height="15.0" fill="rgb(219,82,13)" rx="2" ry="2" onmouseover="s('genunix`ddi_get_soft_state (106 samples, 0.03%)')" onmouseout="c()" />
<rect x="536.4" y="593" width="0.2" height="15.0" fill="rgb(206,6,42)" rx="2" ry="2" onmouseover="s('ip`tcp_update_next_port (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="537.3" y="465" width="1.2" height="15.0" fill="rgb(234,110,52)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (399 samples, 0.10%)')" onmouseout="c()" />
<rect x="38.6" y="481" width="0.1" height="15.0" fill="rgb(220,214,23)" rx="2" ry="2" onmouseover="s('unix`hment_compare (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1018.1" y="625" width="0.3" height="15.0" fill="rgb(214,93,7)" rx="2" ry="2" onmouseover="s('ip`tcp_send (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="1010.8" y="657" width="1.4" height="15.0" fill="rgb(226,179,27)" rx="2" ry="2" onmouseover="s('genunix`cv_wait (452 samples, 0.12%)')" onmouseout="c()" />
<rect x="943.0" y="625" width="0.1" height="15.0" fill="rgb(227,186,52)" rx="2" ry="2" onmouseover="s('unix`bzero (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="327.4" y="497" width="0.1" height="15.0" fill="rgb(224,192,51)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_free (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="310.1" y="497" width="1.1" height="15.0" fill="rgb(222,115,50)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="662.7" y="369" width="0.3" height="15.0" fill="rgb(223,176,46)" rx="2" ry="2" onmouseover="s('ip`tcp_process_options (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="576.7" y="625" width="0.3" height="15.0" fill="rgb(231,110,44)" rx="2" ry="2" onmouseover="s('sockfs`socopyinuio (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="342.7" y="673" width="0.5" height="15.0" fill="rgb(226,182,32)" rx="2" ry="2" onmouseover="s('sockfs`socket_accept (141 samples, 0.04%)')" onmouseout="c()" />
<rect x="310.1" y="529" width="1.1" height="15.0" fill="rgb(232,38,39)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (356 samples, 0.09%)')" onmouseout="c()" />
<rect x="1011.3" y="561" width="0.6" height="15.0" fill="rgb(219,28,1)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (182 samples, 0.05%)')" onmouseout="c()" />
<rect x="1035.8" y="641" width="0.1" height="15.0" fill="rgb(222,86,30)" rx="2" ry="2" onmouseover="s('unix`pg_ev_thread_swtch (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="1068.3" y="497" width="0.4" height="15.0" fill="rgb(247,51,35)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (117 samples, 0.03%)')" onmouseout="c()" />
<rect x="78.2" y="545" width="16.3" height="15.0" fill="rgb(208,176,23)" rx="2" ry="2" onmouseover="s('ip`tcp_close_common (5275 samples, 1.39%)')" onmouseout="c()" />
<rect x="938.8" y="625" width="0.1" height="15.0" fill="rgb(209,101,23)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="654.7" y="209" width="0.2" height="15.0" fill="rgb(207,95,37)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="199.2" y="689" width="6.4" height="15.0" fill="rgb(207,28,7)" rx="2" ry="2" onmouseover="s('genunix`stat64_32 (2061 samples, 0.54%)')" onmouseout="c()" />
<rect x="339.3" y="657" width="0.3" height="15.0" fill="rgb(216,21,32)" rx="2" ry="2" onmouseover="s('genunix`releasef (106 samples, 0.03%)')" onmouseout="c()" />
<rect x="787.0" y="577" width="0.2" height="15.0" fill="rgb(242,71,14)" rx="2" ry="2" onmouseover="s('genunix`relvm (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="1163.5" y="577" width="0.3" height="15.0" fill="rgb(251,22,47)" rx="2" ry="2" onmouseover="s('unix`setbackdq (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="327.9" y="497" width="1.5" height="15.0" fill="rgb(238,117,13)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (490 samples, 0.13%)')" onmouseout="c()" />
<rect x="751.9" y="481" width="0.1" height="15.0" fill="rgb(236,196,40)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_subchain (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="155.9" y="673" width="0.2" height="15.0" fill="rgb(236,146,20)" rx="2" ry="2" onmouseover="s('genunix`f_getfd_error (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="1052.3" y="481" width="15.5" height="15.0" fill="rgb(209,124,54)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (4995 samples, 1.31%)')" onmouseout="c()" />
<rect x="13.5" y="625" width="0.2" height="15.0" fill="rgb(217,24,43)" rx="2" ry="2" onmouseover="s('genunix`segvn_faultpage (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="1188.9" y="657" width="0.2" height="15.0" fill="rgb(225,97,30)" rx="2" ry="2" onmouseover="s('unix`_resume_from_idle (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="1018.9" y="609" width="8.2" height="15.0" fill="rgb(245,74,6)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (2637 samples, 0.69%)')" onmouseout="c()" />
<rect x="1044.3" y="513" width="0.5" height="15.0" fill="rgb(224,183,21)" rx="2" ry="2" onmouseover="s('ipf`frpr_ipv4hdr (174 samples, 0.05%)')" onmouseout="c()" />
<rect x="1006.8" y="593" width="1.6" height="15.0" fill="rgb(220,160,44)" rx="2" ry="2" onmouseover="s('unix`mutex_vector_enter (501 samples, 0.13%)')" onmouseout="c()" />
<rect x="681.2" y="369" width="61.5" height="15.0" fill="rgb(234,76,34)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (19822 samples, 5.20%)')" onmouseout="c()" />
<text text-anchor="" x="684.246241948909" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_set_destination (19822 samples, 5.20%)')" onmouseout="c()" >ip`tcp_..</text>
<rect x="1189.3" y="657" width="0.2" height="15.0" fill="rgb(222,107,42)" rx="2" ry="2" onmouseover="s('unix`resume (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1036.9" y="609" width="0.1" height="15.0" fill="rgb(246,122,39)" rx="2" ry="2" onmouseover="s('dls`dls_accept_common (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="327.8" y="513" width="1.6" height="15.0" fill="rgb(232,87,13)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (513 samples, 0.13%)')" onmouseout="c()" />
<rect x="15.0" y="641" width="0.3" height="15.0" fill="rgb(210,66,9)" rx="2" ry="2" onmouseover="s('genunix`as_segat (114 samples, 0.03%)')" onmouseout="c()" />
<rect x="787.0" y="545" width="0.2" height="15.0" fill="rgb(240,187,33)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="942.6" y="545" width="0.1" height="15.0" fill="rgb(217,192,31)" rx="2" ry="2" onmouseover="s('unix`page_get_mnode_freelist (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="773.6" y="641" width="9.8" height="15.0" fill="rgb(244,229,22)" rx="2" ry="2" onmouseover="s('genunix`cyclic_softint (3148 samples, 0.83%)')" onmouseout="c()" />
<rect x="141.1" y="641" width="0.3" height="15.0" fill="rgb(227,154,29)" rx="2" ry="2" onmouseover="s('genunix`getf (81 samples, 0.02%)')" onmouseout="c()" />
<rect x="1146.1" y="529" width="4.3" height="15.0" fill="rgb(251,178,7)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple (1413 samples, 0.37%)')" onmouseout="c()" />
<rect x="137.4" y="481" width="0.2" height="15.0" fill="rgb(243,177,37)" rx="2" ry="2" onmouseover="s('genunix`vmem_xfree (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="167.4" y="593" width="0.5" height="15.0" fill="rgb(207,207,0)" rx="2" ry="2" onmouseover="s('unix`header_kstat_snapshot (151 samples, 0.04%)')" onmouseout="c()" />
<rect x="617.5" y="561" width="0.2" height="15.0" fill="rgb(241,25,54)" rx="2" ry="2" onmouseover="s('mac`flow_ether_accept (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.6" y="673" width="1.1" height="15.0" fill="rgb(229,162,0)" rx="2" ry="2" onmouseover="s('genunix`openat (354 samples, 0.09%)')" onmouseout="c()" />
<rect x="744.8" y="257" width="0.2" height="15.0" fill="rgb(208,151,4)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="303.7" y="625" width="0.3" height="15.0" fill="rgb(246,34,17)" rx="2" ry="2" onmouseover="s('genunix`allocb (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="137.4" y="433" width="0.1" height="15.0" fill="rgb(246,209,28)" rx="2" ry="2" onmouseover="s('unix`hat_unload (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1027.3" y="577" width="0.2" height="15.0" fill="rgb(216,55,22)" rx="2" ry="2" onmouseover="s('mac`mac_tx (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="1044.6" y="497" width="0.2" height="15.0" fill="rgb(217,204,36)" rx="2" ry="2" onmouseover="s('ipf`frpr_tcp (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="542.3" y="609" width="8.2" height="15.0" fill="rgb(210,224,34)" rx="2" ry="2" onmouseover="s('ip`conn_connect (2639 samples, 0.69%)')" onmouseout="c()" />
<rect x="1051.2" y="481" width="0.2" height="15.0" fill="rgb(238,47,15)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="556.9" y="465" width="0.1" height="15.0" fill="rgb(228,131,52)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="672.6" y="337" width="0.4" height="15.0" fill="rgb(236,139,31)" rx="2" ry="2" onmouseover="s('genunix`dupb (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="1158.6" y="561" width="4.4" height="15.0" fill="rgb(231,40,53)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (1422 samples, 0.37%)')" onmouseout="c()" />
<rect x="1016.2" y="641" width="0.5" height="15.0" fill="rgb(217,216,2)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_end (170 samples, 0.04%)')" onmouseout="c()" />
<rect x="542.3" y="577" width="8.1" height="15.0" fill="rgb(227,204,15)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (2630 samples, 0.69%)')" onmouseout="c()" />
<rect x="554.1" y="465" width="0.2" height="15.0" fill="rgb(241,190,3)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_v4 (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="584.4" y="657" width="0.4" height="15.0" fill="rgb(209,227,16)" rx="2" ry="2" onmouseover="s('sockfs`socket_sonode_create (142 samples, 0.04%)')" onmouseout="c()" />
<rect x="605.8" y="609" width="0.3" height="15.0" fill="rgb(229,119,6)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="661.3" y="353" width="1.3" height="15.0" fill="rgb(243,58,45)" rx="2" ry="2" onmouseover="s('sockfs`so_newconn (427 samples, 0.11%)')" onmouseout="c()" />
<rect x="675.9" y="337" width="0.3" height="15.0" fill="rgb(249,129,12)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="1042.5" y="481" width="0.3" height="15.0" fill="rgb(251,177,29)" rx="2" ry="2" onmouseover="s('unix`swtch (85 samples, 0.02%)')" onmouseout="c()" />
<rect x="923.6" y="625" width="10.2" height="15.0" fill="rgb(252,190,44)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_ioctl (3309 samples, 0.87%)')" onmouseout="c()" />
<rect x="314.7" y="497" width="0.3" height="15.0" fill="rgb(225,45,34)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (116 samples, 0.03%)')" onmouseout="c()" />
<rect x="992.2" y="625" width="0.6" height="15.0" fill="rgb(228,102,44)" rx="2" ry="2" onmouseover="s('portfs`port_fd_callback (198 samples, 0.05%)')" onmouseout="c()" />
<rect x="69.5" y="593" width="0.4" height="15.0" fill="rgb(250,156,30)" rx="2" ry="2" onmouseover="s('unix`setfrontdq (142 samples, 0.04%)')" onmouseout="c()" />
<rect x="1181.9" y="673" width="0.6" height="15.0" fill="rgb(241,207,51)" rx="2" ry="2" onmouseover="s('unix`idle_enter (199 samples, 0.05%)')" onmouseout="c()" />
<rect x="943.1" y="609" width="0.4" height="15.0" fill="rgb(216,137,19)" rx="2" ry="2" onmouseover="s('genunix`as_faulta (132 samples, 0.03%)')" onmouseout="c()" />
<rect x="197.4" y="673" width="0.2" height="15.0" fill="rgb(224,221,27)" rx="2" ry="2" onmouseover="s('genunix`schedctl_shared_alloc (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="158.5" y="689" width="11.7" height="15.0" fill="rgb(218,179,39)" rx="2" ry="2" onmouseover="s('genunix`ioctl (3768 samples, 0.99%)')" onmouseout="c()" />
<rect x="536.6" y="593" width="0.2" height="15.0" fill="rgb(214,127,26)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="54.9" y="689" width="0.2" height="15.0" fill="rgb(212,207,16)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="165.0" y="561" width="0.9" height="15.0" fill="rgb(213,58,2)" rx="2" ry="2" onmouseover="s('genunix`vmem_alloc (274 samples, 0.07%)')" onmouseout="c()" />
<rect x="213.3" y="657" width="1.9" height="15.0" fill="rgb(220,224,46)" rx="2" ry="2" onmouseover="s('genunix`cv_waituntil_sig (610 samples, 0.16%)')" onmouseout="c()" />
<rect x="944.9" y="689" width="0.1" height="15.0" fill="rgb(209,156,50)" rx="2" ry="2" onmouseover="s('genunix`readlink (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="41.3" y="545" width="0.2" height="15.0" fill="rgb(218,41,30)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="97.0" y="625" width="0.1" height="15.0" fill="rgb(219,169,53)" rx="2" ry="2" onmouseover="s('genunix`fop_access (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="640.0" y="369" width="0.3" height="15.0" fill="rgb(218,141,1)" rx="2" ry="2" onmouseover="s('unix`rw_enter (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="943.9" y="577" width="0.2" height="15.0" fill="rgb(246,116,34)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="153.0" y="593" width="0.1" height="15.0" fill="rgb(243,106,3)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb8001d6 (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="774.2" y="561" width="0.2" height="15.0" fill="rgb(236,124,21)" rx="2" ry="2" onmouseover="s('unix`cbe_restore_level (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="85.3" y="369" width="0.2" height="15.0" fill="rgb(249,97,41)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="1159.5" y="417" width="0.2" height="15.0" fill="rgb(236,64,35)" rx="2" ry="2" onmouseover="s('mac`mac_tx (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="13.0" y="705" width="29.8" height="15.0" fill="rgb(225,157,14)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb8001d6 (9632 samples, 2.53%)')" onmouseout="c()" />
<rect x="1033.1" y="657" width="0.1" height="15.0" fill="rgb(252,160,4)" rx="2" ry="2" onmouseover="s('unix`_resume_from_idle (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="632.1" y="369" width="0.2" height="15.0" fill="rgb(211,46,44)" rx="2" ry="2" onmouseover="s('ipf`fr_checkstate (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="85.3" y="353" width="0.2" height="15.0" fill="rgb(233,55,26)" rx="2" ry="2" onmouseover="s('ipf`frpr_ipv4hdr (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="752.4" y="465" width="0.1" height="15.0" fill="rgb(221,94,20)" rx="2" ry="2" onmouseover="s('mac`mac_strip_vlan_tag (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1036.9" y="625" width="0.2" height="15.0" fill="rgb(244,144,28)" rx="2" ry="2" onmouseover="s('dls`dls_accept (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="533.7" y="513" width="1.0" height="15.0" fill="rgb(209,28,2)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (321 samples, 0.08%)')" onmouseout="c()" />
<rect x="751.2" y="449" width="0.2" height="15.0" fill="rgb(243,74,47)" rx="2" ry="2" onmouseover="s('mac`mac_header_info (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="825.9" y="673" width="0.2" height="15.0" fill="rgb(252,127,41)" rx="2" ry="2" onmouseover="s('genunix`cstat (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="945.0" y="689" width="0.1" height="15.0" fill="rgb(222,185,23)" rx="2" ry="2" onmouseover="s('genunix`releasef (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1078.7" y="497" width="0.2" height="15.0" fill="rgb(219,105,45)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="950.0" y="417" width="0.1" height="15.0" fill="rgb(218,142,40)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="772.4" y="609" width="1.1" height="15.0" fill="rgb(247,154,27)" rx="2" ry="2" onmouseover="s('genunix`callout_heap_delete (365 samples, 0.10%)')" onmouseout="c()" />
<rect x="643.7" y="385" width="0.3" height="15.0" fill="rgb(221,162,26)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="967.1" y="673" width="0.2" height="15.0" fill="rgb(240,171,15)" rx="2" ry="2" onmouseover="s('genunix`smmap_common (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="676.6" y="369" width="2.4" height="15.0" fill="rgb(211,26,54)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (779 samples, 0.20%)')" onmouseout="c()" />
<rect x="640.5" y="385" width="0.1" height="15.0" fill="rgb(235,169,24)" rx="2" ry="2" onmouseover="s('ipf`fr_checknatin (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="55.6" y="657" width="0.2" height="15.0" fill="rgb(215,160,43)" rx="2" ry="2" onmouseover="s('unix`tsc_read (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="1152.0" y="561" width="0.8" height="15.0" fill="rgb(228,97,0)" rx="2" ry="2" onmouseover="s('ip`rn_match_args (254 samples, 0.07%)')" onmouseout="c()" />
<rect x="957.9" y="673" width="5.3" height="15.0" fill="rgb(225,219,29)" rx="2" ry="2" onmouseover="s('unix`mmapobj (1728 samples, 0.45%)')" onmouseout="c()" />
<rect x="541.4" y="609" width="0.4" height="15.0" fill="rgb(216,117,14)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (140 samples, 0.04%)')" onmouseout="c()" />
<rect x="328.6" y="273" width="0.1" height="15.0" fill="rgb(206,214,1)" rx="2" ry="2" onmouseover="s('unix`htable_getpage (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="48.9" y="673" width="0.3" height="15.0" fill="rgb(208,183,49)" rx="2" ry="2" onmouseover="s('genunix`audit_getstate (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="325.5" y="545" width="0.7" height="15.0" fill="rgb(225,139,49)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (209 samples, 0.05%)')" onmouseout="c()" />
<rect x="316.3" y="497" width="5.2" height="15.0" fill="rgb(251,70,19)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (1703 samples, 0.45%)')" onmouseout="c()" />
<rect x="743.4" y="401" width="0.1" height="15.0" fill="rgb(233,150,19)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1031.0" y="513" width="0.3" height="15.0" fill="rgb(231,99,16)" rx="2" ry="2" onmouseover="s('ipf`fr_check (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="975.6" y="609" width="0.6" height="15.0" fill="rgb(237,195,27)" rx="2" ry="2" onmouseover="s('sockfs`socket_poll (207 samples, 0.05%)')" onmouseout="c()" />
<rect x="198.0" y="673" width="1.2" height="15.0" fill="rgb(221,178,2)" rx="2" ry="2" onmouseover="s('genunix`fstatat32 (389 samples, 0.10%)')" onmouseout="c()" />
<rect x="967.3" y="689" width="2.1" height="15.0" fill="rgb(241,85,28)" rx="2" ry="2" onmouseover="s('genunix`stat64_32 (672 samples, 0.18%)')" onmouseout="c()" />
<rect x="321.5" y="497" width="2.1" height="15.0" fill="rgb(236,162,35)" rx="2" ry="2" onmouseover="s('hook`hook_run (667 samples, 0.18%)')" onmouseout="c()" />
<rect x="660.9" y="321" width="0.2" height="15.0" fill="rgb(206,182,12)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="54.4" y="657" width="0.4" height="15.0" fill="rgb(207,42,12)" rx="2" ry="2" onmouseover="s('unix`tsc_read (126 samples, 0.03%)')" onmouseout="c()" />
<rect x="1012.9" y="529" width="0.2" height="15.0" fill="rgb(227,38,31)" rx="2" ry="2" onmouseover="s('zfs`zio_execute (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="626.5" y="449" width="0.2" height="15.0" fill="rgb(233,111,4)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="1158.9" y="481" width="0.2" height="15.0" fill="rgb(235,81,41)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="1016.3" y="609" width="0.3" height="15.0" fill="rgb(208,187,26)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="25.4" y="545" width="0.3" height="15.0" fill="rgb(221,69,7)" rx="2" ry="2" onmouseover="s('unix`page_create_va (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="1076.9" y="497" width="1.5" height="15.0" fill="rgb(211,93,48)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (487 samples, 0.13%)')" onmouseout="c()" />
<rect x="980.6" y="577" width="0.2" height="15.0" fill="rgb(230,3,42)" rx="2" ry="2" onmouseover="s('genunix`ddi_get_lbolt (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1159.5" y="449" width="0.2" height="15.0" fill="rgb(216,106,48)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="324.0" y="497" width="0.1" height="15.0" fill="rgb(252,195,18)" rx="2" ry="2" onmouseover="s('unix`bcopy (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="1036.4" y="657" width="119.5" height="15.0" fill="rgb(250,210,40)" rx="2" ry="2" onmouseover="s('mac`mac_rx_deliver (38588 samples, 10.13%)')" onmouseout="c()" />
<text text-anchor="" x="1039.39045921087" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_deliver (38588 samples, 10.13%)')" onmouseout="c()" >mac`mac_rx_del..</text>
<rect x="24.3" y="609" width="2.1" height="15.0" fill="rgb(225,131,36)" rx="2" ry="2" onmouseover="s('genunix`anon_private (693 samples, 0.18%)')" onmouseout="c()" />
<rect x="181.6" y="673" width="12.5" height="15.0" fill="rgb(242,173,47)" rx="2" ry="2" onmouseover="s('genunix`msgio32 (4047 samples, 1.06%)')" onmouseout="c()" />
<rect x="601.9" y="641" width="0.3" height="15.0" fill="rgb(231,73,7)" rx="2" ry="2" onmouseover="s('genunix`bt_getlowbit (121 samples, 0.03%)')" onmouseout="c()" />
<rect x="1068.3" y="529" width="0.5" height="15.0" fill="rgb(231,90,52)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (157 samples, 0.04%)')" onmouseout="c()" />
<rect x="984.8" y="593" width="0.1" height="15.0" fill="rgb(216,23,46)" rx="2" ry="2" onmouseover="s('unix`do_splx (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="627.1" y="449" width="0.5" height="15.0" fill="rgb(220,60,38)" rx="2" ry="2" onmouseover="s('genunix`i_mod_hash_find_nosync (150 samples, 0.04%)')" onmouseout="c()" />
<rect x="583.2" y="609" width="1.0" height="15.0" fill="rgb(223,113,4)" rx="2" ry="2" onmouseover="s('ip`tcp_create_common (332 samples, 0.09%)')" onmouseout="c()" />
<rect x="674.6" y="129" width="0.1" height="15.0" fill="rgb(224,222,2)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_get_sgl (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="213.4" y="625" width="1.8" height="15.0" fill="rgb(234,67,40)" rx="2" ry="2" onmouseover="s('genunix`cv_wait_sig_swap_core (580 samples, 0.15%)')" onmouseout="c()" />
<rect x="175.7" y="577" width="0.9" height="15.0" fill="rgb(214,140,42)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (290 samples, 0.08%)')" onmouseout="c()" />
<rect x="305.9" y="625" width="29.9" height="15.0" fill="rgb(227,156,9)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (9657 samples, 2.54%)')" onmouseout="c()" />
<rect x="54.4" y="673" width="0.4" height="15.0" fill="rgb(251,219,45)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (145 samples, 0.04%)')" onmouseout="c()" />
<rect x="542.3" y="561" width="8.1" height="15.0" fill="rgb(254,56,15)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_v4 (2614 samples, 0.69%)')" onmouseout="c()" />
<rect x="137.4" y="449" width="0.2" height="15.0" fill="rgb(240,139,0)" rx="2" ry="2" onmouseover="s('unix`segkmem_free_vn (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1165.6" y="673" width="0.1" height="15.0" fill="rgb(245,118,12)" rx="2" ry="2" onmouseover="s('unix`bitset_atomic_del (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="198.0" y="689" width="1.2" height="15.0" fill="rgb(231,87,42)" rx="2" ry="2" onmouseover="s('genunix`stat32 (390 samples, 0.10%)')" onmouseout="c()" />
<rect x="63.2" y="673" width="0.2" height="15.0" fill="rgb(235,227,25)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="1070.8" y="529" width="0.2" height="15.0" fill="rgb(209,64,48)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_mp (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="663.2" y="353" width="6.7" height="15.0" fill="rgb(225,217,6)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (2139 samples, 0.56%)')" onmouseout="c()" />
<rect x="194.6" y="513" width="0.5" height="15.0" fill="rgb(232,33,4)" rx="2" ry="2" onmouseover="s('genunix`swap_dispose (164 samples, 0.04%)')" onmouseout="c()" />
<rect x="993.4" y="609" width="0.2" height="15.0" fill="rgb(220,48,41)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_alloc (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="97.0" y="673" width="2.4" height="15.0" fill="rgb(248,109,38)" rx="2" ry="2" onmouseover="s('genunix`open64 (775 samples, 0.20%)')" onmouseout="c()" />
<rect x="676.8" y="353" width="2.1" height="15.0" fill="rgb(239,15,34)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (692 samples, 0.18%)')" onmouseout="c()" />
<rect x="785.0" y="673" width="0.1" height="15.0" fill="rgb(241,103,9)" rx="2" ry="2" onmouseover="s('genunix`fop_read (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="33.2" y="545" width="0.5" height="15.0" fill="rgb(212,133,19)" rx="2" ry="2" onmouseover="s('unix`page_lookup (155 samples, 0.04%)')" onmouseout="c()" />
<rect x="647.8" y="305" width="0.2" height="15.0" fill="rgb(234,89,31)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="198.3" y="625" width="0.8" height="15.0" fill="rgb(224,18,20)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (268 samples, 0.07%)')" onmouseout="c()" />
<rect x="1016.0" y="657" width="0.1" height="15.0" fill="rgb(213,148,3)" rx="2" ry="2" onmouseover="s('ip`squeue_wakeup_conn (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.0" y="353" width="0.2" height="15.0" fill="rgb(217,13,43)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="1037.1" y="625" width="0.2" height="15.0" fill="rgb(250,198,54)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_subchain (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="99.0" y="545" width="0.1" height="15.0" fill="rgb(211,118,47)" rx="2" ry="2" onmouseover="s('genunix`traverse (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="588.4" y="705" width="6.7" height="15.0" fill="rgb(207,31,12)" rx="2" ry="2" onmouseover="s('unix`dtrace_trap (2157 samples, 0.57%)')" onmouseout="c()" />
<rect x="536.8" y="593" width="4.5" height="15.0" fill="rgb(218,125,27)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (1453 samples, 0.38%)')" onmouseout="c()" />
<rect x="170.7" y="673" width="2.4" height="15.0" fill="rgb(221,167,20)" rx="2" ry="2" onmouseover="s('genunix`cv_waituntil_sig (761 samples, 0.20%)')" onmouseout="c()" />
<rect x="773.6" y="625" width="9.1" height="15.0" fill="rgb(238,168,28)" rx="2" ry="2" onmouseover="s('genunix`callout_normal (2938 samples, 0.77%)')" onmouseout="c()" />
<rect x="645.8" y="385" width="0.3" height="15.0" fill="rgb(253,31,17)" rx="2" ry="2" onmouseover="s('ip`ip_recv_attr_to_mblk (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="153.4" y="561" width="0.6" height="15.0" fill="rgb(243,152,25)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (174 samples, 0.05%)')" onmouseout="c()" />
<rect x="56.4" y="673" width="0.2" height="15.0" fill="rgb(221,118,26)" rx="2" ry="2" onmouseover="s('genunix`clear_stale_fd (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="199.5" y="609" width="6.0" height="15.0" fill="rgb(221,172,34)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (1938 samples, 0.51%)')" onmouseout="c()" />
<rect x="996.6" y="673" width="6.0" height="15.0" fill="rgb(219,191,37)" rx="2" ry="2" onmouseover="s('genunix`fsflush_do_pages (1947 samples, 0.51%)')" onmouseout="c()" />
<rect x="1033.3" y="657" width="0.1" height="15.0" fill="rgb(217,75,1)" rx="2" ry="2" onmouseover="s('unix`lock_try (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="181.2" y="689" width="0.3" height="15.0" fill="rgb(239,50,15)" rx="2" ry="2" onmouseover="s('genunix`pollsys (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="951.3" y="705" width="45.0" height="15.0" fill="rgb(242,83,23)" rx="2" ry="2" onmouseover="s('unix`sys_syscall32 (14500 samples, 3.81%)')" onmouseout="c()" />
<rect x="956.9" y="641" width="0.9" height="15.0" fill="rgb(207,20,19)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (301 samples, 0.08%)')" onmouseout="c()" />
<rect x="539.6" y="497" width="0.8" height="15.0" fill="rgb(220,71,42)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (277 samples, 0.07%)')" onmouseout="c()" />
<rect x="336.6" y="625" width="0.2" height="15.0" fill="rgb(227,221,35)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_hold_sa (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="97.1" y="625" width="0.2" height="15.0" fill="rgb(229,99,25)" rx="2" ry="2" onmouseover="s('genunix`fop_open (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="538.9" y="529" width="1.5" height="15.0" fill="rgb(237,38,42)" rx="2" ry="2" onmouseover="s('mac`mac_tx (481 samples, 0.13%)')" onmouseout="c()" />
<rect x="619.8" y="545" width="0.2" height="15.0" fill="rgb(229,164,36)" rx="2" ry="2" onmouseover="s('genunix`msgdsize (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="1146.0" y="545" width="4.5" height="15.0" fill="rgb(251,26,17)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_early_reset (1433 samples, 0.38%)')" onmouseout="c()" />
<rect x="165.4" y="465" width="0.5" height="15.0" fill="rgb(223,101,0)" rx="2" ry="2" onmouseover="s('unix`page_create_va (167 samples, 0.04%)')" onmouseout="c()" />
<rect x="595.1" y="705" width="1.3" height="15.0" fill="rgb(213,226,30)" rx="2" ry="2" onmouseover="s('unix`resume (414 samples, 0.11%)')" onmouseout="c()" />
<rect x="981.6" y="593" width="0.5" height="15.0" fill="rgb(231,30,46)" rx="2" ry="2" onmouseover="s('genunix`new_mstate (146 samples, 0.04%)')" onmouseout="c()" />
<rect x="783.4" y="705" width="167.9" height="15.0" fill="rgb(210,94,33)" rx="2" ry="2" onmouseover="s('unix`sys_syscall (54211 samples, 14.23%)')" onmouseout="c()" />
<text text-anchor="" x="786.376850142708" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`sys_syscall (54211 samples, 14.23%)')" onmouseout="c()" >unix`sys_syscall</text>
<rect x="194.8" y="449" width="0.2" height="15.0" fill="rgb(209,200,2)" rx="2" ry="2" onmouseover="s('unix`page_list_add (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="1067.6" y="449" width="0.1" height="15.0" fill="rgb(220,10,36)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_v4 (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="137.4" y="513" width="0.2" height="15.0" fill="rgb(254,160,30)" rx="2" ry="2" onmouseover="s('genunix`kmem_free (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="643.3" y="401" width="0.1" height="15.0" fill="rgb(237,168,46)" rx="2" ry="2" onmouseover="s('ip`ip_input_cksum_v4 (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="985.5" y="593" width="0.1" height="15.0" fill="rgb(251,192,3)" rx="2" ry="2" onmouseover="s('unix`cmt_ev_thread_swtch (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="662.4" y="321" width="0.1" height="15.0" fill="rgb(218,17,5)" rx="2" ry="2" onmouseover="s('sockfs`socket_sonode_create (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="671.2" y="337" width="0.6" height="15.0" fill="rgb(224,166,46)" rx="2" ry="2" onmouseover="s('genunix`untimeout_generic (195 samples, 0.05%)')" onmouseout="c()" />
<rect x="67.0" y="673" width="0.4" height="15.0" fill="rgb(211,45,45)" rx="2" ry="2" onmouseover="s('unix`lwp_segregs_restore32 (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="600.5" y="561" width="0.1" height="15.0" fill="rgb(247,151,43)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_process (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1068.0" y="449" width="0.1" height="15.0" fill="rgb(245,119,49)" rx="2" ry="2" onmouseover="s('mac`mac_tx (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="744.8" y="273" width="0.2" height="15.0" fill="rgb(224,88,28)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="670.7" y="337" width="0.2" height="15.0" fill="rgb(235,192,39)" rx="2" ry="2" onmouseover="s('genunix`callout_list_get (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="315.4" y="513" width="0.2" height="15.0" fill="rgb(252,217,29)" rx="2" ry="2" onmouseover="s('ip`ip_output_cksum_v4 (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="663.6" y="305" width="0.2" height="15.0" fill="rgb(215,104,30)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="1073.2" y="529" width="2.5" height="15.0" fill="rgb(210,19,40)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (802 samples, 0.21%)')" onmouseout="c()" />
<rect x="1037.7" y="609" width="0.1" height="15.0" fill="rgb(243,174,26)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1160.2" y="449" width="2.6" height="15.0" fill="rgb(236,217,21)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (838 samples, 0.22%)')" onmouseout="c()" />
<rect x="1012.6" y="577" width="0.1" height="15.0" fill="rgb(245,129,36)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="944.9" y="673" width="0.1" height="15.0" fill="rgb(241,200,44)" rx="2" ry="2" onmouseover="s('genunix`readlinkat (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="949.8" y="657" width="0.5" height="15.0" fill="rgb(238,145,16)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_write (167 samples, 0.04%)')" onmouseout="c()" />
<rect x="949.9" y="481" width="0.3" height="15.0" fill="rgb(224,13,7)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="173.6" y="641" width="0.3" height="15.0" fill="rgb(229,6,43)" rx="2" ry="2" onmouseover="s('genunix`fop_open (105 samples, 0.03%)')" onmouseout="c()" />
<rect x="84.8" y="433" width="1.0" height="15.0" fill="rgb(245,14,5)" rx="2" ry="2" onmouseover="s('hook`hook_run (338 samples, 0.09%)')" onmouseout="c()" />
<rect x="664.8" y="289" width="3.5" height="15.0" fill="rgb(216,53,31)" rx="2" ry="2" onmouseover="s('mac`mac_tx (1109 samples, 0.29%)')" onmouseout="c()" />
<rect x="979.5" y="641" width="11.1" height="15.0" fill="rgb(236,121,7)" rx="2" ry="2" onmouseover="s('genunix`cv_waituntil_sig (3614 samples, 0.95%)')" onmouseout="c()" />
<rect x="321.8" y="465" width="1.8" height="15.0" fill="rgb(249,216,52)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (584 samples, 0.15%)')" onmouseout="c()" />
<rect x="168.8" y="577" width="0.2" height="15.0" fill="rgb(224,108,29)" rx="2" ry="2" onmouseover="s('unix`kstat_zone_find (89 samples, 0.02%)')" onmouseout="c()" />
<rect x="945.5" y="673" width="0.2" height="15.0" fill="rgb(224,68,29)" rx="2" ry="2" onmouseover="s('genunix`exit (88 samples, 0.02%)')" onmouseout="c()" />
<rect x="557.3" y="529" width="0.4" height="15.0" fill="rgb(220,162,18)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="38.4" y="561" width="0.8" height="15.0" fill="rgb(251,107,22)" rx="2" ry="2" onmouseover="s('unix`hati_pte_map (251 samples, 0.07%)')" onmouseout="c()" />
<rect x="618.9" y="545" width="0.3" height="15.0" fill="rgb(206,175,42)" rx="2" ry="2" onmouseover="s('mac`flow_l2_match (100 samples, 0.03%)')" onmouseout="c()" />
<rect x="79.3" y="481" width="0.7" height="15.0" fill="rgb(251,62,11)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (239 samples, 0.06%)')" onmouseout="c()" />
<rect x="1042.8" y="497" width="1.0" height="15.0" fill="rgb(248,106,12)" rx="2" ry="2" onmouseover="s('genunix`turnstile_lookup (350 samples, 0.09%)')" onmouseout="c()" />
<rect x="536.8" y="609" width="4.5" height="15.0" fill="rgb(245,142,42)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (1458 samples, 0.38%)')" onmouseout="c()" />
<rect x="346.9" y="593" width="189.5" height="15.0" fill="rgb(209,111,43)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (61151 samples, 16.06%)')" onmouseout="c()" />
<text text-anchor="" x="349.919776604376" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_set_destination (61151 samples, 16.06%)')" onmouseout="c()" >ip`tcp_set_destination</text>
<rect x="648.1" y="337" width="0.2" height="15.0" fill="rgb(218,8,35)" rx="2" ry="2" onmouseover="s('ip`tcp_send (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="541.9" y="593" width="0.2" height="15.0" fill="rgb(249,144,9)" rx="2" ry="2" onmouseover="s('genunix`allocb (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="632.3" y="369" width="0.1" height="15.0" fill="rgb(216,107,49)" rx="2" ry="2" onmouseover="s('ipf`fr_dolog (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="1015.3" y="673" width="16.9" height="15.0" fill="rgb(226,182,42)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (5440 samples, 1.43%)')" onmouseout="c()" />
<rect x="744.7" y="321" width="0.3" height="15.0" fill="rgb(224,44,18)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="787.0" y="561" width="0.2" height="15.0" fill="rgb(246,104,7)" rx="2" ry="2" onmouseover="s('genunix`as_free (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="577.3" y="673" width="1.6" height="15.0" fill="rgb(252,163,43)" rx="2" ry="2" onmouseover="s('sockfs`socket_setsockopt (524 samples, 0.14%)')" onmouseout="c()" />
<rect x="584.5" y="641" width="0.2" height="15.0" fill="rgb(225,127,42)" rx="2" ry="2" onmouseover="s('sockfs`sonode_init (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="624.7" y="481" width="0.1" height="15.0" fill="rgb(221,184,49)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="666.3" y="225" width="1.6" height="15.0" fill="rgb(240,76,5)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (509 samples, 0.13%)')" onmouseout="c()" />
<rect x="1156.4" y="689" width="7.6" height="15.0" fill="rgb(243,2,20)" rx="2" ry="2" onmouseover="s('mac`mac_srs_worker (2460 samples, 0.65%)')" onmouseout="c()" />
<rect x="771.1" y="497" width="0.7" height="15.0" fill="rgb(252,10,27)" rx="2" ry="2" onmouseover="s('unix`setbackdq (215 samples, 0.06%)')" onmouseout="c()" />
<rect x="670.2" y="321" width="0.1" height="15.0" fill="rgb(234,222,15)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1029.5" y="497" width="0.2" height="15.0" fill="rgb(213,99,4)" rx="2" ry="2" onmouseover="s('mac`mac_client_check_flow_vid (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="786.4" y="529" width="0.1" height="15.0" fill="rgb(241,50,27)" rx="2" ry="2" onmouseover="s('unix`hment_remove (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="785.4" y="625" width="0.2" height="15.0" fill="rgb(239,224,35)" rx="2" ry="2" onmouseover="s('zfs`dmu_read_uio (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="207.7" y="641" width="0.6" height="15.0" fill="rgb(216,13,18)" rx="2" ry="2" onmouseover="s('genunix`watch_disable_addr (185 samples, 0.05%)')" onmouseout="c()" />
<rect x="75.5" y="641" width="0.5" height="15.0" fill="rgb(230,197,35)" rx="2" ry="2" onmouseover="s('genunix`as_map_locked (154 samples, 0.04%)')" onmouseout="c()" />
<rect x="1082.2" y="529" width="63.2" height="15.0" fill="rgb(240,56,27)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (20371 samples, 5.35%)')" onmouseout="c()" />
<text text-anchor="" x="1085.24280489118" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_set_destination (20371 samples, 5.35%)')" onmouseout="c()" >ip`tcp_..</text>
<rect x="1176.8" y="609" width="0.1" height="15.0" fill="rgb(253,92,38)" rx="2" ry="2" onmouseover="s('unix`splr (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1181.4" y="673" width="0.1" height="15.0" fill="rgb(205,101,50)" rx="2" ry="2" onmouseover="s('unix`i86_monitor (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="80.7" y="481" width="0.1" height="15.0" fill="rgb(226,86,15)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="314.6" y="529" width="0.4" height="15.0" fill="rgb(223,197,3)" rx="2" ry="2" onmouseover="s('ip`ip_verify_ire (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="1051.2" y="449" width="0.2" height="15.0" fill="rgb(211,130,42)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="1012.8" y="625" width="0.6" height="15.0" fill="rgb(234,69,26)" rx="2" ry="2" onmouseover="s('zfs`zio_execute (199 samples, 0.05%)')" onmouseout="c()" />
<rect x="1050.6" y="481" width="0.3" height="15.0" fill="rgb(211,165,0)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="319.7" y="385" width="0.1" height="15.0" fill="rgb(250,136,44)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_addr_bind_handle (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="785.4" y="641" width="0.2" height="15.0" fill="rgb(244,31,7)" rx="2" ry="2" onmouseover="s('zfs`zfs_read (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="958.7" y="513" width="0.3" height="15.0" fill="rgb(246,173,45)" rx="2" ry="2" onmouseover="s('genunix`anon_private (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="1032.0" y="641" width="0.2" height="15.0" fill="rgb(207,134,24)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_free (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="990.1" y="625" width="0.1" height="15.0" fill="rgb(254,44,25)" rx="2" ry="2" onmouseover="s('genunix`gethrestime (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="977.6" y="641" width="0.2" height="15.0" fill="rgb(230,105,3)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_poll (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="615.1" y="577" width="0.2" height="15.0" fill="rgb(253,103,1)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_free (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="786.9" y="625" width="0.3" height="15.0" fill="rgb(238,141,20)" rx="2" ry="2" onmouseover="s('genunix`gexec (100 samples, 0.03%)')" onmouseout="c()" />
<rect x="1079.8" y="481" width="0.1" height="15.0" fill="rgb(247,69,23)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="133.1" y="593" width="0.1" height="15.0" fill="rgb(230,213,53)" rx="2" ry="2" onmouseover="s('ufs`ufs_read (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="958.3" y="625" width="0.2" height="15.0" fill="rgb(220,210,17)" rx="2" ry="2" onmouseover="s('genunix`fop_map (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="322.8" y="401" width="0.2" height="15.0" fill="rgb(248,225,0)" rx="2" ry="2" onmouseover="s('ipf`frpr_tcp (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="1040.6" y="513" width="1.5" height="15.0" fill="rgb(212,53,35)" rx="2" ry="2" onmouseover="s('ipf`fr_scanlist (466 samples, 0.12%)')" onmouseout="c()" />
<rect x="1068.9" y="545" width="11.1" height="15.0" fill="rgb(205,202,3)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (3607 samples, 0.95%)')" onmouseout="c()" />
<rect x="975.4" y="609" width="0.2" height="15.0" fill="rgb(247,197,28)" rx="2" ry="2" onmouseover="s('sockfs`so_poll (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="197.7" y="657" width="0.1" height="15.0" fill="rgb(244,156,28)" rx="2" ry="2" onmouseover="s('genunix`fop_map (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="948.3" y="673" width="0.9" height="15.0" fill="rgb(205,86,36)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (299 samples, 0.08%)')" onmouseout="c()" />
<rect x="344.8" y="609" width="192.0" height="15.0" fill="rgb(244,22,38)" rx="2" ry="2" onmouseover="s('ip`tcp_connect_ipv4 (61976 samples, 16.27%)')" onmouseout="c()" />
<text text-anchor="" x="347.800511488805" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_connect_ipv4 (61976 samples, 16.27%)')" onmouseout="c()" >ip`tcp_connect_ipv4</text>
<rect x="337.2" y="625" width="0.3" height="15.0" fill="rgb(221,69,23)" rx="2" ry="2" onmouseover="s('zfs`dmu_write_uio_dbuf (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="935.7" y="625" width="0.2" height="15.0" fill="rgb(210,81,6)" rx="2" ry="2" onmouseover="s('unix`mutex_exit (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="965.8" y="641" width="1.2" height="15.0" fill="rgb(244,219,43)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (390 samples, 0.10%)')" onmouseout="c()" />
<rect x="1031.0" y="529" width="0.4" height="15.0" fill="rgb(215,32,38)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (138 samples, 0.04%)')" onmouseout="c()" />
<rect x="1081.1" y="497" width="0.8" height="15.0" fill="rgb(217,33,22)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (265 samples, 0.07%)')" onmouseout="c()" />
<rect x="754.2" y="433" width="0.2" height="15.0" fill="rgb(245,170,30)" rx="2" ry="2" onmouseover="s('genunix`cpu_update_pct (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="1047.3" y="545" width="0.1" height="15.0" fill="rgb(238,38,39)" rx="2" ry="2" onmouseover="s('ip`ip_squeue_random (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="321.8" y="449" width="1.3" height="15.0" fill="rgb(236,91,44)" rx="2" ry="2" onmouseover="s('ipf`fr_check (413 samples, 0.11%)')" onmouseout="c()" />
<rect x="318.9" y="417" width="2.0" height="15.0" fill="rgb(213,13,13)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (618 samples, 0.16%)')" onmouseout="c()" />
<rect x="170.7" y="657" width="2.2" height="15.0" fill="rgb(242,130,49)" rx="2" ry="2" onmouseover="s('genunix`cv_timedwait_sig_hires (714 samples, 0.19%)')" onmouseout="c()" />
<rect x="1030.4" y="465" width="0.1" height="15.0" fill="rgb(206,30,45)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="992.8" y="625" width="0.3" height="15.0" fill="rgb(209,70,22)" rx="2" ry="2" onmouseover="s('genunix`kmem_free (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="131.6" y="641" width="8.9" height="15.0" fill="rgb(251,210,43)" rx="2" ry="2" onmouseover="s('genunix`fop_read (2891 samples, 0.76%)')" onmouseout="c()" />
<rect x="339.4" y="641" width="0.1" height="15.0" fill="rgb(252,18,27)" rx="2" ry="2" onmouseover="s('genunix`clear_active_fd (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="213.5" y="593" width="0.3" height="15.0" fill="rgb(207,34,0)" rx="2" ry="2" onmouseover="s('FSS`fss_sleep (109 samples, 0.03%)')" onmouseout="c()" />
<rect x="540.3" y="481" width="0.1" height="15.0" fill="rgb(216,162,35)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="675.3" y="241" width="0.1" height="15.0" fill="rgb(231,76,54)" rx="2" ry="2" onmouseover="s('ipf`fr_check (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.2" y="657" width="0.2" height="15.0" fill="rgb(237,27,49)" rx="2" ry="2" onmouseover="s('zfs`zio_checksum_generate (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="140.9" y="641" width="0.1" height="15.0" fill="rgb(209,159,45)" rx="2" ry="2" onmouseover="s('genunix`fs_rwlock (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1047.6" y="561" width="0.6" height="15.0" fill="rgb(209,90,44)" rx="2" ry="2" onmouseover="s('ip`ipcl_classify_v4 (195 samples, 0.05%)')" onmouseout="c()" />
<rect x="577.1" y="689" width="1.8" height="15.0" fill="rgb(238,26,14)" rx="2" ry="2" onmouseover="s('sockfs`setsockopt (580 samples, 0.15%)')" onmouseout="c()" />
<rect x="677.1" y="321" width="1.5" height="15.0" fill="rgb(248,181,33)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (471 samples, 0.12%)')" onmouseout="c()" />
<rect x="10.6" y="705" width="0.1" height="15.0" fill="rgb(241,134,32)" rx="2" ry="2" onmouseover="s('genunix`syscall_entry (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="1015.8" y="657" width="0.1" height="15.0" fill="rgb(234,50,19)" rx="2" ry="2" onmouseover="s('ip`ip_recv_attr_is_mblk (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.3" y="289" width="0.4" height="15.0" fill="rgb(240,141,7)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (138 samples, 0.04%)')" onmouseout="c()" />
<rect x="198.3" y="641" width="0.8" height="15.0" fill="rgb(249,121,0)" rx="2" ry="2" onmouseover="s('genunix`cstatat_getvp (270 samples, 0.07%)')" onmouseout="c()" />
<rect x="1049.5" y="513" width="0.3" height="15.0" fill="rgb(250,89,23)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_end (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="552.6" y="657" width="1.5" height="15.0" fill="rgb(242,202,32)" rx="2" ry="2" onmouseover="s('sockfs`socket_recvmsg (483 samples, 0.13%)')" onmouseout="c()" />
<rect x="1189.9" y="673" width="0.1" height="15.0" fill="rgb(228,180,19)" rx="2" ry="2" onmouseover="s('zfs`spa_sync (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1052.3" y="465" width="15.5" height="15.0" fill="rgb(225,212,53)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (4990 samples, 1.31%)')" onmouseout="c()" />
<rect x="576.8" y="609" width="0.2" height="15.0" fill="rgb(246,163,13)" rx="2" ry="2" onmouseover="s('genunix`allocb (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="1012.9" y="577" width="0.3" height="15.0" fill="rgb(227,39,47)" rx="2" ry="2" onmouseover="s('zfs`zio_execute (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="957.0" y="609" width="0.7" height="15.0" fill="rgb(212,183,7)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage_ra (231 samples, 0.06%)')" onmouseout="c()" />
<rect x="991.1" y="625" width="0.2" height="15.0" fill="rgb(247,51,47)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="554.8" y="641" width="22.2" height="15.0" fill="rgb(228,48,24)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (7176 samples, 1.88%)')" onmouseout="c()" />
<rect x="999.3" y="657" width="3.3" height="15.0" fill="rgb(217,223,16)" rx="2" ry="2" onmouseover="s('unix`page_nextn (1042 samples, 0.27%)')" onmouseout="c()" />
<rect x="1079.4" y="433" width="0.3" height="15.0" fill="rgb(222,196,5)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="38.8" y="529" width="0.2" height="15.0" fill="rgb(222,10,39)" rx="2" ry="2" onmouseover="s('unix`hment_alloc (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="675.2" y="273" width="0.3" height="15.0" fill="rgb(211,192,19)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="78.6" y="481" width="0.3" height="15.0" fill="rgb(234,167,20)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_end (101 samples, 0.03%)')" onmouseout="c()" />
<rect x="1072.4" y="497" width="0.2" height="15.0" fill="rgb(207,192,6)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_newconn (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="534.9" y="513" width="0.1" height="15.0" fill="rgb(215,162,30)" rx="2" ry="2" onmouseover="s('ip`ipif_select_source_v4 (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="329.1" y="465" width="0.2" height="15.0" fill="rgb(246,43,42)" rx="2" ry="2" onmouseover="s('hook`hook_run (85 samples, 0.02%)')" onmouseout="c()" />
<rect x="981.2" y="561" width="0.4" height="15.0" fill="rgb(232,99,29)" rx="2" ry="2" onmouseover="s('unix`caps_charge_adjust (132 samples, 0.03%)')" onmouseout="c()" />
<rect x="988.7" y="577" width="0.1" height="15.0" fill="rgb(244,194,1)" rx="2" ry="2" onmouseover="s('unix`atomic_add_32 (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="247.2" y="673" width="93.5" height="15.0" fill="rgb(212,200,21)" rx="2" ry="2" onmouseover="s('genunix`write (30177 samples, 7.92%)')" onmouseout="c()" />
<text text-anchor="" x="250.221365948184" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`write (30177 samples, 7.92%)')" onmouseout="c()" >genunix`wri..</text>
<rect x="681.3" y="321" width="61.2" height="15.0" fill="rgb(217,97,19)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (19750 samples, 5.19%)')" onmouseout="c()" />
<text text-anchor="" x="684.295815401905" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_set_destination_v4 (19750 samples, 5.19%)')" onmouseout="c()" >ip`ip_s..</text>
<rect x="1011.9" y="609" width="0.2" height="15.0" fill="rgb(253,151,53)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="923.1" y="641" width="0.1" height="15.0" fill="rgb(249,136,48)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_ioctl (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="196.0" y="561" width="0.1" height="15.0" fill="rgb(245,191,20)" rx="2" ry="2" onmouseover="s('unix`hment_free (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="334.4" y="561" width="0.1" height="15.0" fill="rgb(238,124,4)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb85a673 (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="975.4" y="625" width="0.8" height="15.0" fill="rgb(212,6,36)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_poll (267 samples, 0.07%)')" onmouseout="c()" />
<rect x="965.5" y="673" width="1.5" height="15.0" fill="rgb(233,73,6)" rx="2" ry="2" onmouseover="s('genunix`lookuppn (500 samples, 0.13%)')" onmouseout="c()" />
<rect x="554.1" y="529" width="0.2" height="15.0" fill="rgb(218,8,38)" rx="2" ry="2" onmouseover="s('ip`icmp_unreachable (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="616.8" y="593" width="142.8" height="15.0" fill="rgb(244,170,42)" rx="2" ry="2" onmouseover="s('mac`mac_rx_flow (46072 samples, 12.10%)')" onmouseout="c()" />
<text text-anchor="" x="619.834834803295" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_flow (46072 samples, 12.10%)')" onmouseout="c()" >mac`mac_rx_flow</text>
<rect x="946.4" y="561" width="0.2" height="15.0" fill="rgb(207,95,36)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1050.5" y="497" width="0.4" height="15.0" fill="rgb(235,210,49)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (133 samples, 0.03%)')" onmouseout="c()" />
<rect x="55.6" y="673" width="0.2" height="15.0" fill="rgb(222,50,22)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="644.1" y="401" width="1.3" height="15.0" fill="rgb(252,226,17)" rx="2" ry="2" onmouseover="s('ip`ipcl_classify_v4 (424 samples, 0.11%)')" onmouseout="c()" />
<rect x="1076.2" y="497" width="0.1" height="15.0" fill="rgb(225,59,1)" rx="2" ry="2" onmouseover="s('genunix`untimeout_generic (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="1077.1" y="417" width="0.4" height="15.0" fill="rgb(229,105,50)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="645.9" y="369" width="0.2" height="15.0" fill="rgb(253,21,29)" rx="2" ry="2" onmouseover="s('genunix`allocb (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="1013.8" y="625" width="0.1" height="15.0" fill="rgb(216,208,2)" rx="2" ry="2" onmouseover="s('zfs`zio_nowait (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.6" y="657" width="0.2" height="15.0" fill="rgb(222,149,48)" rx="2" ry="2" onmouseover="s('zfs`zio_dva_allocate (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="13.5" y="609" width="0.1" height="15.0" fill="rgb(236,164,37)" rx="2" ry="2" onmouseover="s('genunix`anon_zero (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="136.2" y="529" width="1.2" height="15.0" fill="rgb(236,147,27)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree_desb (394 samples, 0.10%)')" onmouseout="c()" />
<rect x="675.2" y="257" width="0.3" height="15.0" fill="rgb(236,27,20)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="32.0" y="529" width="0.1" height="15.0" fill="rgb(250,195,27)" rx="2" ry="2" onmouseover="s('unix`lgrp_mem_choose (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="774.2" y="593" width="0.2" height="15.0" fill="rgb(233,62,8)" rx="2" ry="2" onmouseover="s('genunix`cyclic_reprogram (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="217.2" y="657" width="0.3" height="15.0" fill="rgb(220,1,49)" rx="2" ry="2" onmouseover="s('genunix`idtot_and_lock (114 samples, 0.03%)')" onmouseout="c()" />
<rect x="139.7" y="577" width="0.5" height="15.0" fill="rgb(223,92,4)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb85a6ea (144 samples, 0.04%)')" onmouseout="c()" />
<rect x="152.4" y="657" width="3.1" height="15.0" fill="rgb(225,131,30)" rx="2" ry="2" onmouseover="s('genunix`gexec (1013 samples, 0.27%)')" onmouseout="c()" />
<rect x="335.2" y="593" width="0.2" height="15.0" fill="rgb(219,156,45)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="632.9" y="353" width="4.8" height="15.0" fill="rgb(205,106,9)" rx="2" ry="2" onmouseover="s('ipf`fr_scanlist (1528 samples, 0.40%)')" onmouseout="c()" />
<rect x="139.7" y="593" width="0.5" height="15.0" fill="rgb(254,69,16)" rx="2" ry="2" onmouseover="s('genunix`uiomove (146 samples, 0.04%)')" onmouseout="c()" />
<rect x="953.3" y="673" width="0.1" height="15.0" fill="rgb(218,112,5)" rx="2" ry="2" onmouseover="s('unix`dtrace_interrupt_enable (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="165.6" y="433" width="0.2" height="15.0" fill="rgb(209,129,25)" rx="2" ry="2" onmouseover="s('unix`page_get_mnode_freelist (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="938.5" y="689" width="2.4" height="15.0" fill="rgb(217,125,12)" rx="2" ry="2" onmouseover="s('genunix`lstat (786 samples, 0.21%)')" onmouseout="c()" />
<rect x="1182.8" y="657" width="0.1" height="15.0" fill="rgb(209,39,33)" rx="2" ry="2" onmouseover="s('pcplusmp`apic_unset_idlecpu (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="200.9" y="577" width="4.6" height="15.0" fill="rgb(246,138,30)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (1471 samples, 0.39%)')" onmouseout="c()" />
<rect x="943.1" y="625" width="0.5" height="15.0" fill="rgb(233,7,10)" rx="2" ry="2" onmouseover="s('unix`mmapobj_map_ptload (178 samples, 0.05%)')" onmouseout="c()" />
<rect x="637.3" y="321" width="0.2" height="15.0" fill="rgb(220,218,42)" rx="2" ry="2" onmouseover="s('ipf`fr_tcpudpchk (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="553.5" y="609" width="0.3" height="15.0" fill="rgb(221,41,47)" rx="2" ry="2" onmouseover="s('sockfs`socopyoutuio (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="949.8" y="641" width="0.5" height="15.0" fill="rgb(238,126,46)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (167 samples, 0.04%)')" onmouseout="c()" />
<rect x="12.4" y="705" width="0.6" height="15.0" fill="rgb(230,59,47)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb8001d1 (202 samples, 0.05%)')" onmouseout="c()" />
<rect x="341.3" y="673" width="0.4" height="15.0" fill="rgb(208,9,35)" rx="2" ry="2" onmouseover="s('genunix`ufalloc (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="1009.6" y="577" width="0.4" height="15.0" fill="rgb(220,23,23)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="311.9" y="433" width="0.3" height="15.0" fill="rgb(251,76,15)" rx="2" ry="2" onmouseover="s('mac`mac_tx (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="536.1" y="577" width="0.1" height="15.0" fill="rgb(245,184,29)" rx="2" ry="2" onmouseover="s('ip`tcp_iss_init (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1052.0" y="465" width="0.2" height="15.0" fill="rgb(211,86,47)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1042.8" y="481" width="1.0" height="15.0" fill="rgb(215,66,42)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (342 samples, 0.09%)')" onmouseout="c()" />
<rect x="1184.3" y="657" width="0.4" height="15.0" fill="rgb(217,217,20)" rx="2" ry="2" onmouseover="s('unix`_resume_from_idle (125 samples, 0.03%)')" onmouseout="c()" />
<rect x="680.7" y="289" width="0.1" height="15.0" fill="rgb(253,91,30)" rx="2" ry="2" onmouseover="s('hook`hook_run (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="958.5" y="593" width="0.6" height="15.0" fill="rgb(211,221,20)" rx="2" ry="2" onmouseover="s('unix`trap (195 samples, 0.05%)')" onmouseout="c()" />
<rect x="1159.6" y="401" width="0.1" height="15.0" fill="rgb(249,162,16)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="540.9" y="481" width="0.1" height="15.0" fill="rgb(244,108,4)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="157.1" y="689" width="0.5" height="15.0" fill="rgb(253,24,18)" rx="2" ry="2" onmouseover="s('genunix`fstat32 (163 samples, 0.04%)')" onmouseout="c()" />
<rect x="949.8" y="673" width="0.9" height="15.0" fill="rgb(218,151,14)" rx="2" ry="2" onmouseover="s('genunix`fop_write (296 samples, 0.08%)')" onmouseout="c()" />
<rect x="680.4" y="257" width="0.3" height="15.0" fill="rgb(208,123,12)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="1033.9" y="641" width="1.9" height="15.0" fill="rgb(208,17,25)" rx="2" ry="2" onmouseover="s('unix`disp (610 samples, 0.16%)')" onmouseout="c()" />
<rect x="58.7" y="657" width="0.2" height="15.0" fill="rgb(222,167,31)" rx="2" ry="2" onmouseover="s('genunix`clear_stale_fd (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="62.4" y="657" width="0.1" height="15.0" fill="rgb(252,202,5)" rx="2" ry="2" onmouseover="s('unix`splr (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1067.9" y="513" width="0.2" height="15.0" fill="rgb(212,229,22)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="551.9" y="689" width="2.2" height="15.0" fill="rgb(232,92,47)" rx="2" ry="2" onmouseover="s('sockfs`recvmsg (727 samples, 0.19%)')" onmouseout="c()" />
<rect x="75.5" y="657" width="0.5" height="15.0" fill="rgb(230,31,43)" rx="2" ry="2" onmouseover="s('genunix`as_map (166 samples, 0.04%)')" onmouseout="c()" />
<rect x="1189.6" y="689" width="0.1" height="15.0" fill="rgb(232,39,16)" rx="2" ry="2" onmouseover="s('unix`idle_enter (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="945.5" y="657" width="0.2" height="15.0" fill="rgb(244,67,37)" rx="2" ry="2" onmouseover="s('genunix`proc_exit (88 samples, 0.02%)')" onmouseout="c()" />
<rect x="766.7" y="641" width="6.9" height="15.0" fill="rgb(247,114,14)" rx="2" ry="2" onmouseover="s('genunix`cyclic_softint (2233 samples, 0.59%)')" onmouseout="c()" />
<rect x="76.8" y="673" width="0.1" height="15.0" fill="rgb(240,211,9)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_probe (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1153.9" y="625" width="0.3" height="15.0" fill="rgb(239,54,16)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (109 samples, 0.03%)')" onmouseout="c()" />
<rect x="604.6" y="625" width="0.7" height="15.0" fill="rgb(229,61,51)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_check_acc_handle (206 samples, 0.05%)')" onmouseout="c()" />
<rect x="316.7" y="465" width="1.5" height="15.0" fill="rgb(246,229,29)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (501 samples, 0.13%)')" onmouseout="c()" />
<rect x="329.1" y="433" width="0.2" height="15.0" fill="rgb(236,168,16)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="981.4" y="545" width="0.1" height="15.0" fill="rgb(242,38,24)" rx="2" ry="2" onmouseover="s('genunix`mstate_thread_onproc_time (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1029.9" y="497" width="0.8" height="15.0" fill="rgb(241,5,6)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (253 samples, 0.07%)')" onmouseout="c()" />
<rect x="157.6" y="673" width="0.5" height="15.0" fill="rgb(239,68,21)" rx="2" ry="2" onmouseover="s('genunix`cstat64_32 (151 samples, 0.04%)')" onmouseout="c()" />
<rect x="938.5" y="641" width="0.4" height="15.0" fill="rgb(222,221,10)" rx="2" ry="2" onmouseover="s('genunix`cstat (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="159.5" y="577" width="0.1" height="15.0" fill="rgb(235,117,8)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_helper_provider_register (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="940.9" y="689" width="2.0" height="15.0" fill="rgb(236,113,19)" rx="2" ry="2" onmouseover="s('genunix`memcntl (623 samples, 0.16%)')" onmouseout="c()" />
<rect x="164.8" y="513" width="0.2" height="15.0" fill="rgb(229,210,26)" rx="2" ry="2" onmouseover="s('unix`page_destroy (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="536.5" y="577" width="0.1" height="15.0" fill="rgb(220,211,30)" rx="2" ry="2" onmouseover="s('kcf`random_get_pseudo_bytes (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="155.6" y="625" width="0.1" height="15.0" fill="rgb(250,38,41)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.0" y="609" width="0.1" height="15.0" fill="rgb(218,190,13)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb8001d6 (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1027.4" y="561" width="0.1" height="15.0" fill="rgb(240,73,17)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="621.7" y="513" width="0.6" height="15.0" fill="rgb(254,181,12)" rx="2" ry="2" onmouseover="s('mac`mac_protect_intercept_dhcp_one (202 samples, 0.05%)')" onmouseout="c()" />
<rect x="1014.0" y="689" width="18.3" height="15.0" fill="rgb(249,212,14)" rx="2" ry="2" onmouseover="s('ip`squeue_worker (5891 samples, 1.55%)')" onmouseout="c()" />
<rect x="208.6" y="641" width="0.2" height="15.0" fill="rgb(245,77,21)" rx="2" ry="2" onmouseover="s('unix`copyin (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="668.6" y="289" width="0.8" height="15.0" fill="rgb(246,190,26)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (264 samples, 0.07%)')" onmouseout="c()" />
<rect x="766.0" y="673" width="17.4" height="15.0" fill="rgb(254,13,46)" rx="2" ry="2" onmouseover="s('unix`av_dispatch_softvect (5596 samples, 1.47%)')" onmouseout="c()" />
<rect x="336.8" y="625" width="0.4" height="15.0" fill="rgb(222,64,19)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_hold_write (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="965.5" y="657" width="1.5" height="15.0" fill="rgb(210,52,22)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (495 samples, 0.13%)')" onmouseout="c()" />
<rect x="990.1" y="609" width="0.1" height="15.0" fill="rgb(245,5,52)" rx="2" ry="2" onmouseover="s('unix`pc_gethrestime (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1002.7" y="673" width="0.5" height="15.0" fill="rgb(215,198,21)" rx="2" ry="2" onmouseover="s('unix`page_nextn (182 samples, 0.05%)')" onmouseout="c()" />
<rect x="615.3" y="577" width="0.1" height="15.0" fill="rgb(228,36,39)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="77.1" y="625" width="0.2" height="15.0" fill="rgb(224,97,2)" rx="2" ry="2" onmouseover="s('genunix`audit_unfalloc (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="665.6" y="241" width="0.1" height="15.0" fill="rgb(235,48,0)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="13.1" y="689" width="29.7" height="15.0" fill="rgb(227,209,19)" rx="2" ry="2" onmouseover="s('unix`trap (9593 samples, 2.52%)')" onmouseout="c()" />
<rect x="976.7" y="641" width="0.1" height="15.0" fill="rgb(240,87,37)" rx="2" ry="2" onmouseover="s('genunix`releasef (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1181.5" y="673" width="0.4" height="15.0" fill="rgb(225,170,18)" rx="2" ry="2" onmouseover="s('unix`i86_mwait (140 samples, 0.04%)')" onmouseout="c()" />
<rect x="654.0" y="369" width="0.3" height="15.0" fill="rgb(211,117,51)" rx="2" ry="2" onmouseover="s('ip`tcp_output (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="97.3" y="625" width="2.1" height="15.0" fill="rgb(249,1,34)" rx="2" ry="2" onmouseover="s('genunix`vn_openat (673 samples, 0.18%)')" onmouseout="c()" />
<rect x="45.7" y="705" width="1.1" height="15.0" fill="rgb(230,10,31)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800c86 (353 samples, 0.09%)')" onmouseout="c()" />
<rect x="182.2" y="513" width="0.2" height="15.0" fill="rgb(241,144,13)" rx="2" ry="2" onmouseover="s('bmc`kcs_status (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="32.3" y="529" width="0.6" height="15.0" fill="rgb(226,215,34)" rx="2" ry="2" onmouseover="s('unix`page_get_freelist (207 samples, 0.05%)')" onmouseout="c()" />
<rect x="336.4" y="593" width="0.1" height="15.0" fill="rgb(244,127,43)" rx="2" ry="2" onmouseover="s('zfs`dsl_dir_tempreserve_space (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="945.1" y="657" width="0.3" height="15.0" fill="rgb(221,123,2)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (105 samples, 0.03%)')" onmouseout="c()" />
<rect x="967.6" y="593" width="1.6" height="15.0" fill="rgb(238,62,20)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (532 samples, 0.14%)')" onmouseout="c()" />
<rect x="82.3" y="433" width="2.5" height="15.0" fill="rgb(211,49,47)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (801 samples, 0.21%)')" onmouseout="c()" />
<rect x="647.1" y="337" width="0.2" height="15.0" fill="rgb(206,186,43)" rx="2" ry="2" onmouseover="s('ip`dce_update_uinfo_v4 (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="676.3" y="289" width="0.2" height="15.0" fill="rgb(206,55,46)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="199.5" y="593" width="6.0" height="15.0" fill="rgb(228,98,12)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (1924 samples, 0.51%)')" onmouseout="c()" />
<rect x="989.7" y="577" width="0.3" height="15.0" fill="rgb(225,115,49)" rx="2" ry="2" onmouseover="s('unix`fpxsave_ctxt (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="1039.0" y="593" width="7.2" height="15.0" fill="rgb(219,168,31)" rx="2" ry="2" onmouseover="s('hook`hook_run (2314 samples, 0.61%)')" onmouseout="c()" />
<rect x="538.6" y="577" width="2.7" height="15.0" fill="rgb(206,2,13)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (894 samples, 0.23%)')" onmouseout="c()" />
<rect x="668.0" y="225" width="0.2" height="15.0" fill="rgb(231,94,52)" rx="2" ry="2" onmouseover="s('mac`mac_flow_lookup (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="625.7" y="497" width="0.1" height="15.0" fill="rgb(205,226,11)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_rx (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="216.0" y="609" width="0.2" height="15.0" fill="rgb(235,131,51)" rx="2" ry="2" onmouseover="s('FSS`fss_active (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="743.6" y="353" width="0.8" height="15.0" fill="rgb(212,120,28)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple_v4 (277 samples, 0.07%)')" onmouseout="c()" />
<rect x="774.7" y="545" width="7.9" height="15.0" fill="rgb(247,6,27)" rx="2" ry="2" onmouseover="s('unix`setkpdq (2555 samples, 0.67%)')" onmouseout="c()" />
<rect x="1165.3" y="673" width="0.2" height="15.0" fill="rgb(229,197,29)" rx="2" ry="2" onmouseover="s('unix`_resume_from_idle (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="551.0" y="657" width="0.2" height="15.0" fill="rgb(228,31,41)" rx="2" ry="2" onmouseover="s('sockfs`so_getpeername (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="308.7" y="577" width="21.5" height="15.0" fill="rgb(240,143,10)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (6943 samples, 1.82%)')" onmouseout="c()" />
<rect x="1082.0" y="529" width="0.1" height="15.0" fill="rgb(250,174,15)" rx="2" ry="2" onmouseover="s('ip`tcp_get_conn (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="153.1" y="577" width="0.9" height="15.0" fill="rgb(207,174,45)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (263 samples, 0.07%)')" onmouseout="c()" />
<rect x="156.0" y="657" width="0.1" height="15.0" fill="rgb(246,3,46)" rx="2" ry="2" onmouseover="s('procfs`pr_isself (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="627.9" y="465" width="122.9" height="15.0" fill="rgb(250,82,7)" rx="2" ry="2" onmouseover="s('ip`ip_input (39653 samples, 10.41%)')" onmouseout="c()" />
<text text-anchor="" x="630.942386615168" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_input (39653 samples, 10.41%)')" onmouseout="c()" >ip`ip_input</text>
<rect x="305.6" y="625" width="0.3" height="15.0" fill="rgb(240,44,15)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="1008.6" y="593" width="0.2" height="15.0" fill="rgb(209,156,24)" rx="2" ry="2" onmouseover="s('ip`ixa_cleanup (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="1034.2" y="625" width="1.5" height="15.0" fill="rgb(228,226,5)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (478 samples, 0.13%)')" onmouseout="c()" />
<rect x="93.5" y="465" width="0.7" height="15.0" fill="rgb(248,229,17)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_mp_aux_fss (220 samples, 0.06%)')" onmouseout="c()" />
<rect x="959.4" y="593" width="0.8" height="15.0" fill="rgb(222,59,50)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (262 samples, 0.07%)')" onmouseout="c()" />
<rect x="301.7" y="641" width="2.6" height="15.0" fill="rgb(224,128,43)" rx="2" ry="2" onmouseover="s('fifofs`fifo_write (835 samples, 0.22%)')" onmouseout="c()" />
<rect x="782.9" y="577" width="0.3" height="15.0" fill="rgb(227,178,38)" rx="2" ry="2" onmouseover="s('unix`clock_tick_process (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="26.1" y="593" width="0.3" height="15.0" fill="rgb(210,173,32)" rx="2" ry="2" onmouseover="s('unix`ppcopy (114 samples, 0.03%)')" onmouseout="c()" />
<rect x="1151.7" y="577" width="1.2" height="15.0" fill="rgb(241,188,16)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_simple_v4 (373 samples, 0.10%)')" onmouseout="c()" />
<rect x="80.1" y="497" width="0.2" height="15.0" fill="rgb(247,157,4)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="77.0" y="641" width="18.7" height="15.0" fill="rgb(243,50,24)" rx="2" ry="2" onmouseover="s('genunix`closef (6046 samples, 1.59%)')" onmouseout="c()" />
<rect x="1157.8" y="641" width="6.0" height="15.0" fill="rgb(216,173,25)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_process (1937 samples, 0.51%)')" onmouseout="c()" />
<rect x="311.2" y="545" width="0.5" height="15.0" fill="rgb(232,45,26)" rx="2" ry="2" onmouseover="s('ip`tcp_output (151 samples, 0.04%)')" onmouseout="c()" />
<rect x="1073.8" y="417" width="0.4" height="15.0" fill="rgb(244,120,9)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="673.8" y="209" width="0.1" height="15.0" fill="rgb(221,47,49)" rx="2" ry="2" onmouseover="s('mac`get_l3_info (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="658.8" y="353" width="0.4" height="15.0" fill="rgb(238,115,37)" rx="2" ry="2" onmouseover="s('genunix`dblk_decref (136 samples, 0.04%)')" onmouseout="c()" />
<rect x="537.0" y="449" width="0.2" height="15.0" fill="rgb(247,88,33)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="303.2" y="513" width="0.2" height="15.0" fill="rgb(226,175,34)" rx="2" ry="2" onmouseover="s('unix`setfrontdq (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="43.0" y="689" width="2.6" height="15.0" fill="rgb(251,212,28)" rx="2" ry="2" onmouseover="s('genunix`pre_syscall (832 samples, 0.22%)')" onmouseout="c()" />
<rect x="554.1" y="609" width="0.3" height="15.0" fill="rgb(243,61,45)" rx="2" ry="2" onmouseover="s('ip`udp_send (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="646.8" y="369" width="0.2" height="15.0" fill="rgb(241,229,11)" rx="2" ry="2" onmouseover="s('ip`squeue_wakeup_conn (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="328.6" y="241" width="0.1" height="15.0" fill="rgb(218,162,34)" rx="2" ry="2" onmouseover="s('unix`htable_lookup (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="17.4" y="641" width="25.1" height="15.0" fill="rgb(237,222,53)" rx="2" ry="2" onmouseover="s('genunix`segvn_fault (8089 samples, 2.12%)')" onmouseout="c()" />
<rect x="534.0" y="465" width="0.6" height="15.0" fill="rgb(253,62,7)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_v4 (183 samples, 0.05%)')" onmouseout="c()" />
<rect x="944.5" y="561" width="0.1" height="15.0" fill="rgb(215,26,54)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="1078.1" y="449" width="0.2" height="15.0" fill="rgb(222,211,51)" rx="2" ry="2" onmouseover="s('hook`hook_run (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="962.1" y="513" width="0.5" height="15.0" fill="rgb(220,118,2)" rx="2" ry="2" onmouseover="s('unix`page_create_va (153 samples, 0.04%)')" onmouseout="c()" />
<rect x="209.9" y="657" width="0.2" height="15.0" fill="rgb(224,45,13)" rx="2" ry="2" onmouseover="s('genunix`save_syscall_args (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="1071.0" y="513" width="0.3" height="15.0" fill="rgb(254,111,33)" rx="2" ry="2" onmouseover="s('ip`tcp_close_detached (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="980.8" y="577" width="0.8" height="15.0" fill="rgb(218,149,14)" rx="2" ry="2" onmouseover="s('unix`cpucaps_charge (259 samples, 0.07%)')" onmouseout="c()" />
<rect x="745.0" y="401" width="0.9" height="15.0" fill="rgb(243,119,14)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (271 samples, 0.07%)')" onmouseout="c()" />
<rect x="213.6" y="577" width="0.2" height="15.0" fill="rgb(218,205,16)" rx="2" ry="2" onmouseover="s('unix`cpucaps_charge (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="46.3" y="657" width="0.3" height="15.0" fill="rgb(244,103,38)" rx="2" ry="2" onmouseover="s('unix`tsc_read (122 samples, 0.03%)')" onmouseout="c()" />
<rect x="165.1" y="481" width="0.2" height="15.0" fill="rgb(207,169,22)" rx="2" ry="2" onmouseover="s('unix`hat_memload (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="539.3" y="481" width="0.1" height="15.0" fill="rgb(243,169,36)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1052.3" y="449" width="15.3" height="15.0" fill="rgb(238,90,43)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (4938 samples, 1.30%)')" onmouseout="c()" />
<rect x="677.6" y="241" width="0.2" height="15.0" fill="rgb(250,65,45)" rx="2" ry="2" onmouseover="s('FSS`fss_active (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="1176.5" y="657" width="4.8" height="15.0" fill="rgb(242,92,29)" rx="2" ry="2" onmouseover="s('unix`disp_getbest (1545 samples, 0.41%)')" onmouseout="c()" />
<rect x="97.0" y="641" width="2.4" height="15.0" fill="rgb(247,207,2)" rx="2" ry="2" onmouseover="s('genunix`copen (775 samples, 0.20%)')" onmouseout="c()" />
<rect x="55.1" y="705" width="0.2" height="15.0" fill="rgb(235,120,33)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800eb1 (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="537.0" y="513" width="0.2" height="15.0" fill="rgb(233,205,4)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="588.8" y="689" width="6.2" height="15.0" fill="rgb(236,124,36)" rx="2" ry="2" onmouseover="s('unix`dtrace_user_probe (1999 samples, 0.52%)')" onmouseout="c()" />
<rect x="42.2" y="593" width="0.1" height="15.0" fill="rgb(210,193,25)" rx="2" ry="2" onmouseover="s('unix`htable_getpte (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="665.2" y="241" width="0.1" height="15.0" fill="rgb(233,77,11)" rx="2" ry="2" onmouseover="s('mac`dhcpnospoof_check (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1079.2" y="497" width="0.7" height="15.0" fill="rgb(244,62,44)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_data (233 samples, 0.06%)')" onmouseout="c()" />
<rect x="137.6" y="545" width="0.8" height="15.0" fill="rgb(247,37,36)" rx="2" ry="2" onmouseover="s('genunix`uiomove (261 samples, 0.07%)')" onmouseout="c()" />
<rect x="1047.4" y="545" width="0.2" height="15.0" fill="rgb(230,202,28)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="170.2" y="625" width="0.2" height="15.0" fill="rgb(217,64,54)" rx="2" ry="2" onmouseover="s('genunix`segvn_free (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="665.4" y="241" width="0.1" height="15.0" fill="rgb(233,159,7)" rx="2" ry="2" onmouseover="s('mac`mac_client_check_flow_vid (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1030.7" y="497" width="0.1" height="15.0" fill="rgb(232,177,34)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="218.5" y="673" width="28.6" height="15.0" fill="rgb(247,158,35)" rx="2" ry="2" onmouseover="s('genunix`fop_write (9217 samples, 2.42%)')" onmouseout="c()" />
<rect x="141.8" y="641" width="0.3" height="15.0" fill="rgb(216,158,34)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (114 samples, 0.03%)')" onmouseout="c()" />
<rect x="554.1" y="689" width="0.3" height="15.0" fill="rgb(215,185,46)" rx="2" ry="2" onmouseover="s('sockfs`send32 (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="313.9" y="529" width="0.1" height="15.0" fill="rgb(229,213,30)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="671.2" y="353" width="0.7" height="15.0" fill="rgb(241,11,34)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (247 samples, 0.06%)')" onmouseout="c()" />
<rect x="141.6" y="641" width="0.1" height="15.0" fill="rgb(229,24,14)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_read (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="930.9" y="609" width="2.9" height="15.0" fill="rgb(211,0,39)" rx="2" ry="2" onmouseover="s('unix`mutex_vector_enter (960 samples, 0.25%)')" onmouseout="c()" />
<rect x="1050.3" y="497" width="0.2" height="15.0" fill="rgb(229,212,49)" rx="2" ry="2" onmouseover="s('sockfs`so_newconn (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="155.1" y="609" width="0.4" height="15.0" fill="rgb(208,65,36)" rx="2" ry="2" onmouseover="s('elfexec`elf32exec (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="1070.6" y="529" width="0.1" height="15.0" fill="rgb(239,201,22)" rx="2" ry="2" onmouseover="s('genunix`freemsg (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="303.0" y="497" width="0.1" height="15.0" fill="rgb(221,59,4)" rx="2" ry="2" onmouseover="s('unix`cmt_balance (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="75.5" y="673" width="0.6" height="15.0" fill="rgb(213,113,17)" rx="2" ry="2" onmouseover="s('genunix`brk_internal (185 samples, 0.05%)')" onmouseout="c()" />
<rect x="209.7" y="657" width="0.2" height="15.0" fill="rgb(230,87,18)" rx="2" ry="2" onmouseover="s('c2audit`audit_start (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="958.5" y="625" width="0.6" height="15.0" fill="rgb(220,121,39)" rx="2" ry="2" onmouseover="s('unix`bzero (200 samples, 0.05%)')" onmouseout="c()" />
<rect x="79.3" y="433" width="0.7" height="15.0" fill="rgb(242,44,39)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (238 samples, 0.06%)')" onmouseout="c()" />
<rect x="785.5" y="609" width="0.1" height="15.0" fill="rgb(224,17,46)" rx="2" ry="2" onmouseover="s('zfs`dmu_buf_hold_array (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="336.6" y="593" width="0.2" height="15.0" fill="rgb(240,166,2)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_hold_object_impl (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="752.5" y="465" width="0.5" height="15.0" fill="rgb(224,14,3)" rx="2" ry="2" onmouseover="s('unix`ovbcopy (166 samples, 0.04%)')" onmouseout="c()" />
<rect x="1012.9" y="593" width="0.3" height="15.0" fill="rgb(211,207,21)" rx="2" ry="2" onmouseover="s('zfs`zio_notify_parent (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="1074.2" y="433" width="0.9" height="15.0" fill="rgb(211,163,34)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (284 samples, 0.07%)')" onmouseout="c()" />
<rect x="1051.5" y="481" width="0.1" height="15.0" fill="rgb(223,96,54)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_data (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="82.6" y="385" width="0.6" height="15.0" fill="rgb(224,13,31)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (204 samples, 0.05%)')" onmouseout="c()" />
<rect x="1156.8" y="625" width="0.3" height="15.0" fill="rgb(210,224,50)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="759.8" y="641" width="0.1" height="15.0" fill="rgb(253,114,41)" rx="2" ry="2" onmouseover="s('unix`ddi_io_put32 (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="317.3" y="433" width="0.1" height="15.0" fill="rgb(216,96,10)" rx="2" ry="2" onmouseover="s('mac`ipnospoof_check (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1078.5" y="513" width="0.2" height="15.0" fill="rgb(254,46,42)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="94.8" y="593" width="0.1" height="15.0" fill="rgb(233,178,52)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="301.9" y="625" width="1.8" height="15.0" fill="rgb(228,99,18)" rx="2" ry="2" onmouseover="s('fifofs`fifo_wakereader (587 samples, 0.15%)')" onmouseout="c()" />
<rect x="600.4" y="657" width="0.2" height="15.0" fill="rgb(239,68,27)" rx="2" ry="2" onmouseover="s('bnx`bnx_intr_1lvl (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="1074.3" y="401" width="0.7" height="15.0" fill="rgb(241,189,47)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (200 samples, 0.05%)')" onmouseout="c()" />
<rect x="1157.2" y="673" width="6.8" height="15.0" fill="rgb(226,121,28)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_drain (2171 samples, 0.57%)')" onmouseout="c()" />
<rect x="40.2" y="513" width="0.5" height="15.0" fill="rgb(237,192,30)" rx="2" ry="2" onmouseover="s('genunix`avl_find (141 samples, 0.04%)')" onmouseout="c()" />
<rect x="750.4" y="449" width="0.2" height="15.0" fill="rgb(217,66,14)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_dstonly_v4 (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.0" y="593" width="0.1" height="15.0" fill="rgb(228,27,13)" rx="2" ry="2" onmouseover="s('unix`trap (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="38.5" y="513" width="0.3" height="15.0" fill="rgb(229,54,12)" rx="2" ry="2" onmouseover="s('genunix`avl_add (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="1017.7" y="513" width="0.1" height="15.0" fill="rgb(207,97,6)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="132.2" y="609" width="0.2" height="15.0" fill="rgb(225,187,45)" rx="2" ry="2" onmouseover="s('genunix`freeb (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="1181.0" y="641" width="0.2" height="15.0" fill="rgb(233,16,5)" rx="2" ry="2" onmouseover="s('unix`dispdeq (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="627.6" y="449" width="0.3" height="15.0" fill="rgb(216,157,40)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (100 samples, 0.03%)')" onmouseout="c()" />
<rect x="37.7" y="593" width="0.5" height="15.0" fill="rgb(247,46,29)" rx="2" ry="2" onmouseover="s('unix`hati_load_common (170 samples, 0.04%)')" onmouseout="c()" />
<rect x="172.2" y="609" width="0.5" height="15.0" fill="rgb(216,114,25)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (154 samples, 0.04%)')" onmouseout="c()" />
<rect x="1046.4" y="593" width="105.3" height="15.0" fill="rgb(248,214,46)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (33985 samples, 8.92%)')" onmouseout="c()" />
<text text-anchor="" x="1049.3733133079" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ire_recv_local_v4 (33985 samples, 8.92%)')" onmouseout="c()" >ip`ire_recv_..</text>
<rect x="763.1" y="689" width="20.3" height="15.0" fill="rgb(244,56,15)" rx="2" ry="2" onmouseover="s('unix`dispatch_softint (6538 samples, 1.72%)')" onmouseout="c()" />
<rect x="311.4" y="433" width="0.1" height="15.0" fill="rgb(232,133,3)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="953.2" y="689" width="0.2" height="15.0" fill="rgb(217,191,2)" rx="2" ry="2" onmouseover="s('genunix`dtrace_systrace_syscall32 (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="550.7" y="689" width="0.5" height="15.0" fill="rgb(245,208,21)" rx="2" ry="2" onmouseover="s('sockfs`getpeername (160 samples, 0.04%)')" onmouseout="c()" />
<rect x="648.2" y="305" width="0.1" height="15.0" fill="rgb(211,87,38)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="93.6" y="449" width="0.6" height="15.0" fill="rgb(206,20,43)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (202 samples, 0.05%)')" onmouseout="c()" />
<rect x="1073.8" y="433" width="0.4" height="15.0" fill="rgb(252,100,16)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="341.8" y="609" width="0.4" height="15.0" fill="rgb(220,66,30)" rx="2" ry="2" onmouseover="s('genunix`as_segcompar (122 samples, 0.03%)')" onmouseout="c()" />
<rect x="1188.6" y="657" width="0.3" height="15.0" fill="rgb(250,50,32)" rx="2" ry="2" onmouseover="s('genunix`restore_mstate (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="825.8" y="673" width="0.1" height="15.0" fill="rgb(235,12,9)" rx="2" ry="2" onmouseover="s('genunix`cfork (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="679.0" y="369" width="0.2" height="15.0" fill="rgb(250,134,45)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="67.8" y="689" width="4.3" height="15.0" fill="rgb(225,154,20)" rx="2" ry="2" onmouseover="s('unix`sys_rtt_common (1384 samples, 0.36%)')" onmouseout="c()" />
<rect x="782.9" y="609" width="0.3" height="15.0" fill="rgb(208,4,46)" rx="2" ry="2" onmouseover="s('unix`clock_tick_schedule (122 samples, 0.03%)')" onmouseout="c()" />
<rect x="648.1" y="321" width="0.2" height="15.0" fill="rgb(241,47,14)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="925.1" y="577" width="5.7" height="15.0" fill="rgb(205,175,53)" rx="2" ry="2" onmouseover="s('unix`xc_common (1822 samples, 0.48%)')" onmouseout="c()" />
<rect x="945.9" y="689" width="0.8" height="15.0" fill="rgb(244,31,30)" rx="2" ry="2" onmouseover="s('genunix`stat (280 samples, 0.07%)')" onmouseout="c()" />
<rect x="180.6" y="561" width="0.1" height="15.0" fill="rgb(238,59,41)" rx="2" ry="2" onmouseover="s('genunix`pn_getsymlink (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="1071.5" y="497" width="0.9" height="15.0" fill="rgb(251,91,25)" rx="2" ry="2" onmouseover="s('genunix`fop_open (278 samples, 0.07%)')" onmouseout="c()" />
<rect x="67.1" y="641" width="0.2" height="15.0" fill="rgb(251,165,20)" rx="2" ry="2" onmouseover="s('unix`gdt_update_usegd (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="166.1" y="577" width="0.2" height="15.0" fill="rgb(219,7,1)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="78.9" y="497" width="0.3" height="15.0" fill="rgb(251,105,22)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="617.7" y="561" width="0.3" height="15.0" fill="rgb(239,93,53)" rx="2" ry="2" onmouseover="s('mac`flow_l2_match (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="181.7" y="513" width="0.5" height="15.0" fill="rgb(249,105,8)" rx="2" ry="2" onmouseover="s('bmc`kcs_data_pending (153 samples, 0.04%)')" onmouseout="c()" />
<rect x="576.4" y="561" width="0.1" height="15.0" fill="rgb(223,4,19)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="786.1" y="529" width="0.2" height="15.0" fill="rgb(241,13,18)" rx="2" ry="2" onmouseover="s('genunix`anon_free (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="96.0" y="577" width="0.2" height="15.0" fill="rgb(208,125,0)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_free (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="937.3" y="673" width="0.3" height="15.0" fill="rgb(234,55,6)" rx="2" ry="2" onmouseover="s('genunix`set_errno (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="1052.3" y="497" width="15.5" height="15.0" fill="rgb(251,168,52)" rx="2" ry="2" onmouseover="s('ip`conn_connect (5000 samples, 1.31%)')" onmouseout="c()" />
<rect x="584.9" y="641" width="0.1" height="15.0" fill="rgb(216,45,17)" rx="2" ry="2" onmouseover="s('sockfs`sockparams_find (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1150.5" y="561" width="0.5" height="15.0" fill="rgb(249,97,40)" rx="2" ry="2" onmouseover="s('ip`udp_input (179 samples, 0.05%)')" onmouseout="c()" />
<rect x="748.9" y="417" width="0.6" height="15.0" fill="rgb(253,78,47)" rx="2" ry="2" onmouseover="s('unix`rw_enter (197 samples, 0.05%)')" onmouseout="c()" />
<rect x="983.0" y="609" width="1.1" height="15.0" fill="rgb(251,190,10)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="170.0" y="641" width="0.1" height="15.0" fill="rgb(208,104,30)" rx="2" ry="2" onmouseover="s('genunix`strioctl (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1151.7" y="593" width="1.5" height="15.0" fill="rgb(210,70,6)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_dstonly_v4 (501 samples, 0.13%)')" onmouseout="c()" />
<rect x="1048.2" y="561" width="97.7" height="15.0" fill="rgb(209,55,7)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (31529 samples, 8.28%)')" onmouseout="c()" />
<text text-anchor="" x="1051.22921945443" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_enter (31529 samples, 8.28%)')" onmouseout="c()" >ip`squeue_e..</text>
<rect x="766.8" y="625" width="3.4" height="15.0" fill="rgb(229,158,12)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_state_clean (1095 samples, 0.29%)')" onmouseout="c()" />
<rect x="982.8" y="609" width="0.1" height="15.0" fill="rgb(209,79,43)" rx="2" ry="2" onmouseover="s('genunix`sigcheck (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1049.6" y="497" width="0.1" height="15.0" fill="rgb(240,165,33)" rx="2" ry="2" onmouseover="s('ip`dce_update_uinfo_v4 (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="344.3" y="577" width="0.2" height="15.0" fill="rgb(241,156,49)" rx="2" ry="2" onmouseover="s('unix`setbackdq (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="180.1" y="513" width="0.1" height="15.0" fill="rgb(226,76,40)" rx="2" ry="2" onmouseover="s('ufs`ufs_lookup (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="647.8" y="257" width="0.1" height="15.0" fill="rgb(232,174,22)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1052.3" y="513" width="15.5" height="15.0" fill="rgb(219,67,24)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (5022 samples, 1.32%)')" onmouseout="c()" />
<rect x="638.2" y="353" width="0.3" height="15.0" fill="rgb(248,122,20)" rx="2" ry="2" onmouseover="s('unix`rw_enter_sleep (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="1028.5" y="625" width="3.2" height="15.0" fill="rgb(233,205,54)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (1045 samples, 0.27%)')" onmouseout="c()" />
<rect x="336.3" y="625" width="0.2" height="15.0" fill="rgb(210,81,45)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_assign (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="1013.8" y="577" width="0.1" height="15.0" fill="rgb(210,169,50)" rx="2" ry="2" onmouseover="s('zfs`vdev_queue_io (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="770.7" y="497" width="0.3" height="15.0" fill="rgb(219,76,41)" rx="2" ry="2" onmouseover="s('FSS`fss_active (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="933.9" y="625" width="1.8" height="15.0" fill="rgb(243,154,43)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (580 samples, 0.15%)')" onmouseout="c()" />
<rect x="40.0" y="577" width="1.8" height="15.0" fill="rgb(230,100,0)" rx="2" ry="2" onmouseover="s('unix`hati_pte_map (595 samples, 0.16%)')" onmouseout="c()" />
<rect x="964.3" y="577" width="0.2" height="15.0" fill="rgb(244,124,12)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="1187.9" y="641" width="0.2" height="15.0" fill="rgb(214,83,4)" rx="2" ry="2" onmouseover="s('unix`atomic_or_64 (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="213.5" y="609" width="0.5" height="15.0" fill="rgb(235,43,0)" rx="2" ry="2" onmouseover="s('genunix`cv_block (172 samples, 0.05%)')" onmouseout="c()" />
<rect x="344.6" y="625" width="197.6" height="15.0" fill="rgb(239,33,10)" rx="2" ry="2" onmouseover="s('ip`tcp_do_connect (63761 samples, 16.74%)')" onmouseout="c()" />
<text text-anchor="" x="347.605316017634" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_do_connect (63761 samples, 16.74%)')" onmouseout="c()" >ip`tcp_do_connect</text>
<rect x="326.2" y="545" width="3.3" height="15.0" fill="rgb(223,170,2)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (1068 samples, 0.28%)')" onmouseout="c()" />
<rect x="132.9" y="625" width="0.3" height="15.0" fill="rgb(232,23,45)" rx="2" ry="2" onmouseover="s('lofs`lo_read (105 samples, 0.03%)')" onmouseout="c()" />
<rect x="930.9" y="593" width="0.3" height="15.0" fill="rgb(240,46,39)" rx="2" ry="2" onmouseover="s('unix`default_lock_delay (103 samples, 0.03%)')" onmouseout="c()" />
<rect x="938.5" y="657" width="2.4" height="15.0" fill="rgb(223,89,24)" rx="2" ry="2" onmouseover="s('genunix`cstatat (786 samples, 0.21%)')" onmouseout="c()" />
<rect x="1012.9" y="609" width="0.3" height="15.0" fill="rgb(220,137,22)" rx="2" ry="2" onmouseover="s('zfs`zio_ready (96 samples, 0.03%)')" onmouseout="c()" />
<rect x="199.3" y="641" width="0.2" height="15.0" fill="rgb(245,198,40)" rx="2" ry="2" onmouseover="s('genunix`cstat64_32 (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="311.3" y="465" width="0.2" height="15.0" fill="rgb(215,176,40)" rx="2" ry="2" onmouseover="s('mac`mac_tx (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="542.3" y="593" width="8.2" height="15.0" fill="rgb(219,127,11)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (2634 samples, 0.69%)')" onmouseout="c()" />
<rect x="342.8" y="657" width="0.3" height="15.0" fill="rgb(245,15,5)" rx="2" ry="2" onmouseover="s('sockfs`so_accept (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="770.3" y="593" width="2.0" height="15.0" fill="rgb(233,11,1)" rx="2" ry="2" onmouseover="s('genunix`callout_list_expire (659 samples, 0.17%)')" onmouseout="c()" />
<rect x="133.4" y="609" width="0.1" height="15.0" fill="rgb(254,8,35)" rx="2" ry="2" onmouseover="s('sockfs`so_recvmsg (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="336.8" y="593" width="0.1" height="15.0" fill="rgb(251,84,23)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_check_ioerr (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="311.4" y="449" width="0.1" height="15.0" fill="rgb(226,161,38)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="676.3" y="369" width="0.3" height="15.0" fill="rgb(240,215,5)" rx="2" ry="2" onmouseover="s('sockfs`so_opctl (105 samples, 0.03%)')" onmouseout="c()" />
<rect x="980.3" y="577" width="0.3" height="15.0" fill="rgb(217,169,17)" rx="2" ry="2" onmouseover="s('FSS`fss_inactive (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="194.2" y="673" width="3.2" height="15.0" fill="rgb(210,27,9)" rx="2" ry="2" onmouseover="s('genunix`exit (1043 samples, 0.27%)')" onmouseout="c()" />
<rect x="533.1" y="513" width="0.4" height="15.0" fill="rgb(238,171,33)" rx="2" ry="2" onmouseover="s('unix`_sys_rtt_ints_disabled (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="615.9" y="625" width="0.1" height="15.0" fill="rgb(248,2,26)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="318.3" y="465" width="3.1" height="15.0" fill="rgb(218,146,48)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (1001 samples, 0.26%)')" onmouseout="c()" />
<rect x="304.3" y="641" width="0.3" height="15.0" fill="rgb(229,93,39)" rx="2" ry="2" onmouseover="s('genunix`crgetmapped (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="1009.6" y="593" width="0.8" height="15.0" fill="rgb(224,184,19)" rx="2" ry="2" onmouseover="s('ip`squeue_worker_wakeup (268 samples, 0.07%)')" onmouseout="c()" />
<rect x="14.6" y="673" width="28.2" height="15.0" fill="rgb(246,30,17)" rx="2" ry="2" onmouseover="s('unix`pagefault (9112 samples, 2.39%)')" onmouseout="c()" />
<rect x="551.5" y="673" width="0.2" height="15.0" fill="rgb(237,218,48)" rx="2" ry="2" onmouseover="s('sockfs`socket_getsockopt (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="962.1" y="529" width="0.5" height="15.0" fill="rgb(225,87,10)" rx="2" ry="2" onmouseover="s('genunix`pvn_read_kluster (176 samples, 0.05%)')" onmouseout="c()" />
<rect x="310.1" y="465" width="1.1" height="15.0" fill="rgb(238,142,8)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (351 samples, 0.09%)')" onmouseout="c()" />
<rect x="621.0" y="513" width="0.4" height="15.0" fill="rgb(220,131,31)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="1158.9" y="417" width="0.2" height="15.0" fill="rgb(225,186,49)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="328.4" y="321" width="0.4" height="15.0" fill="rgb(224,0,20)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_coredma_bindhdl (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="205.6" y="689" width="4.5" height="15.0" fill="rgb(244,27,42)" rx="2" ry="2" onmouseover="s('genunix`syscall_entry (1450 samples, 0.38%)')" onmouseout="c()" />
<rect x="744.4" y="401" width="0.6" height="15.0" fill="rgb(249,57,17)" rx="2" ry="2" onmouseover="s('ip`udp_input (186 samples, 0.05%)')" onmouseout="c()" />
<rect x="990.7" y="625" width="0.1" height="15.0" fill="rgb(225,116,4)" rx="2" ry="2" onmouseover="s('unix`pc_gethrestime (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="954.1" y="657" width="0.8" height="15.0" fill="rgb(224,141,35)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (270 samples, 0.07%)')" onmouseout="c()" />
<rect x="670.5" y="353" width="0.4" height="15.0" fill="rgb(223,160,29)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (149 samples, 0.04%)')" onmouseout="c()" />
<rect x="79.3" y="417" width="0.7" height="15.0" fill="rgb(227,188,23)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (234 samples, 0.06%)')" onmouseout="c()" />
<rect x="194.6" y="481" width="0.5" height="15.0" fill="rgb(207,84,3)" rx="2" ry="2" onmouseover="s('unix`page_destroy (153 samples, 0.04%)')" onmouseout="c()" />
<rect x="1030.0" y="481" width="0.7" height="15.0" fill="rgb(234,220,50)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (233 samples, 0.06%)')" onmouseout="c()" />
<rect x="600.8" y="657" width="159.9" height="15.0" fill="rgb(249,186,30)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_intr_msix (51614 samples, 13.55%)')" onmouseout="c()" />
<text text-anchor="" x="603.776134373466" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ixgbe`ixgbe_intr_msix (51614 samples, 13.55%)')" onmouseout="c()" >ixgbe`ixgbe_intr_ms..</text>
<rect x="214.2" y="609" width="1.0" height="15.0" fill="rgb(205,81,48)" rx="2" ry="2" onmouseover="s('unix`swtch (304 samples, 0.08%)')" onmouseout="c()" />
<rect x="132.0" y="625" width="0.7" height="15.0" fill="rgb(222,61,36)" rx="2" ry="2" onmouseover="s('fifofs`fifo_read (232 samples, 0.06%)')" onmouseout="c()" />
<rect x="154.5" y="577" width="0.3" height="15.0" fill="rgb(237,150,0)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="207.4" y="641" width="0.3" height="15.0" fill="rgb(248,219,22)" rx="2" ry="2" onmouseover="s('genunix`avl_numnodes (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="537.0" y="497" width="0.2" height="15.0" fill="rgb(217,0,47)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="753.2" y="433" width="0.1" height="15.0" fill="rgb(241,135,1)" rx="2" ry="2" onmouseover="s('unix`splr (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="980.6" y="561" width="0.2" height="15.0" fill="rgb(205,66,49)" rx="2" ry="2" onmouseover="s('genunix`lbolt_cyclic_driven (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="624.3" y="513" width="0.7" height="15.0" fill="rgb(206,124,6)" rx="2" ry="2" onmouseover="s('mac`mac_client_check_flow_vid (208 samples, 0.05%)')" onmouseout="c()" />
<rect x="1077.6" y="369" width="0.4" height="15.0" fill="rgb(237,88,39)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="979.9" y="609" width="0.2" height="15.0" fill="rgb(216,207,42)" rx="2" ry="2" onmouseover="s('FSS`fss_sleep (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="949.9" y="513" width="0.3" height="15.0" fill="rgb(246,8,19)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (88 samples, 0.02%)')" onmouseout="c()" />
<rect x="137.1" y="465" width="0.1" height="15.0" fill="rgb(250,226,41)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="165.3" y="481" width="0.6" height="15.0" fill="rgb(252,160,0)" rx="2" ry="2" onmouseover="s('unix`segkmem_page_create (178 samples, 0.05%)')" onmouseout="c()" />
<rect x="209.5" y="673" width="0.2" height="15.0" fill="rgb(208,126,30)" rx="2" ry="2" onmouseover="s('genunix`copyin_nowatch (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="181.6" y="657" width="12.5" height="15.0" fill="rgb(226,83,39)" rx="2" ry="2" onmouseover="s('genunix`strputmsg (4047 samples, 1.06%)')" onmouseout="c()" />
<rect x="786.9" y="609" width="0.3" height="15.0" fill="rgb(213,19,41)" rx="2" ry="2" onmouseover="s('elfexec`elf32exec (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="154.5" y="593" width="0.3" height="15.0" fill="rgb(214,151,31)" rx="2" ry="2" onmouseover="s('genunix`as_free (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="948.3" y="657" width="0.2" height="15.0" fill="rgb(239,174,11)" rx="2" ry="2" onmouseover="s('unix`tsc_gethrtimeunscaled (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="35.1" y="609" width="2.6" height="15.0" fill="rgb(217,14,43)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (827 samples, 0.22%)')" onmouseout="c()" />
<rect x="648.2" y="289" width="0.1" height="15.0" fill="rgb(225,170,28)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="93.7" y="433" width="0.4" height="15.0" fill="rgb(248,32,48)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (144 samples, 0.04%)')" onmouseout="c()" />
<rect x="534.8" y="513" width="0.1" height="15.0" fill="rgb(211,48,51)" rx="2" ry="2" onmouseover="s('ip`ipif_refrele (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="663.6" y="289" width="0.2" height="15.0" fill="rgb(248,191,51)" rx="2" ry="2" onmouseover="s('ip`ip_select_src_ill (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="1072.4" y="433" width="0.1" height="15.0" fill="rgb(236,182,15)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="96.7" y="641" width="0.2" height="15.0" fill="rgb(233,24,52)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="139.7" y="609" width="0.7" height="15.0" fill="rgb(225,3,13)" rx="2" ry="2" onmouseover="s('zfs`dmu_read_uio (219 samples, 0.06%)')" onmouseout="c()" />
<rect x="75.5" y="689" width="0.6" height="15.0" fill="rgb(223,141,30)" rx="2" ry="2" onmouseover="s('genunix`brk (192 samples, 0.05%)')" onmouseout="c()" />
<rect x="1153.3" y="593" width="0.2" height="15.0" fill="rgb(249,52,20)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (81 samples, 0.02%)')" onmouseout="c()" />
<rect x="172.8" y="625" width="0.1" height="15.0" fill="rgb(212,173,20)" rx="2" ry="2" onmouseover="s('unix`resume (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="967.2" y="657" width="0.1" height="15.0" fill="rgb(234,207,19)" rx="2" ry="2" onmouseover="s('genunix`zmap (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="613.0" y="609" width="0.6" height="15.0" fill="rgb(216,41,26)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (182 samples, 0.05%)')" onmouseout="c()" />
<rect x="600.5" y="513" width="0.1" height="15.0" fill="rgb(234,104,48)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_process (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="963.3" y="689" width="2.1" height="15.0" fill="rgb(211,134,11)" rx="2" ry="2" onmouseover="s('genunix`open32 (702 samples, 0.18%)')" onmouseout="c()" />
<rect x="535.7" y="577" width="0.4" height="15.0" fill="rgb(254,64,23)" rx="2" ry="2" onmouseover="s('ip`tcp_build_hdrs (139 samples, 0.04%)')" onmouseout="c()" />
<rect x="551.2" y="689" width="0.6" height="15.0" fill="rgb(253,152,9)" rx="2" ry="2" onmouseover="s('sockfs`getsockopt (179 samples, 0.05%)')" onmouseout="c()" />
<rect x="95.8" y="625" width="0.8" height="15.0" fill="rgb(224,74,4)" rx="2" ry="2" onmouseover="s('genunix`port_close_pfd (256 samples, 0.07%)')" onmouseout="c()" />
<rect x="660.4" y="321" width="0.2" height="15.0" fill="rgb(251,108,16)" rx="2" ry="2" onmouseover="s('ip`tcp_timers_stop (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="216.3" y="609" width="0.4" height="15.0" fill="rgb(229,167,50)" rx="2" ry="2" onmouseover="s('unix`setbackdq (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="962.9" y="593" width="0.2" height="15.0" fill="rgb(251,185,12)" rx="2" ry="2" onmouseover="s('lofs`lo_map (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="70.9" y="593" width="0.4" height="15.0" fill="rgb(220,79,43)" rx="2" ry="2" onmouseover="s('unix`resume (127 samples, 0.03%)')" onmouseout="c()" />
<rect x="339.6" y="657" width="0.3" height="15.0" fill="rgb(208,60,9)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_write (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="213.3" y="673" width="2.2" height="15.0" fill="rgb(208,206,17)" rx="2" ry="2" onmouseover="s('genunix`lwp_park (718 samples, 0.19%)')" onmouseout="c()" />
<rect x="334.8" y="561" width="0.2" height="15.0" fill="rgb(230,53,37)" rx="2" ry="2" onmouseover="s('unix`bcopy (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="1027.8" y="641" width="0.1" height="15.0" fill="rgb(230,216,25)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="583.1" y="625" width="1.1" height="15.0" fill="rgb(221,191,32)" rx="2" ry="2" onmouseover="s('ip`tcp_create (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="311.7" y="529" width="0.6" height="15.0" fill="rgb(206,20,54)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (190 samples, 0.05%)')" onmouseout="c()" />
<rect x="164.9" y="497" width="0.1" height="15.0" fill="rgb(238,154,3)" rx="2" ry="2" onmouseover="s('unix`page_free (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="963.3" y="673" width="2.1" height="15.0" fill="rgb(248,53,32)" rx="2" ry="2" onmouseover="s('genunix`openat32 (702 samples, 0.18%)')" onmouseout="c()" />
<rect x="557.9" y="609" width="18.6" height="15.0" fill="rgb(235,151,21)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (6014 samples, 1.58%)')" onmouseout="c()" />
<rect x="1081.2" y="433" width="0.4" height="15.0" fill="rgb(242,195,25)" rx="2" ry="2" onmouseover="s('mac`mac_tx (135 samples, 0.04%)')" onmouseout="c()" />
<rect x="55.3" y="705" width="0.7" height="15.0" fill="rgb(254,132,29)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800ebb (233 samples, 0.06%)')" onmouseout="c()" />
<rect x="1070.3" y="529" width="0.1" height="15.0" fill="rgb(208,110,38)" rx="2" ry="2" onmouseover="s('genunix`dblk_decref (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="43.9" y="657" width="0.5" height="15.0" fill="rgb(215,138,45)" rx="2" ry="2" onmouseover="s('c2audit`auditme (136 samples, 0.04%)')" onmouseout="c()" />
<rect x="80.7" y="497" width="0.1" height="15.0" fill="rgb(228,53,30)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1047.3" y="561" width="0.3" height="15.0" fill="rgb(219,28,6)" rx="2" ry="2" onmouseover="s('ip`ip_squeue_get (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="949.8" y="625" width="0.5" height="15.0" fill="rgb(225,222,25)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (166 samples, 0.04%)')" onmouseout="c()" />
<rect x="673.6" y="209" width="0.1" height="15.0" fill="rgb(246,217,52)" rx="2" ry="2" onmouseover="s('mac`get_l3_info (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="317.0" y="433" width="0.2" height="15.0" fill="rgb(220,63,53)" rx="2" ry="2" onmouseover="s('mac`dhcpnospoof_check (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="1027.2" y="641" width="0.3" height="15.0" fill="rgb(248,137,8)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="1014.5" y="657" width="0.8" height="15.0" fill="rgb(205,6,21)" rx="2" ry="2" onmouseover="s('unix`swtch (247 samples, 0.06%)')" onmouseout="c()" />
<rect x="175.7" y="625" width="5.4" height="15.0" fill="rgb(221,56,46)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (1724 samples, 0.45%)')" onmouseout="c()" />
<rect x="335.7" y="609" width="0.1" height="15.0" fill="rgb(211,102,1)" rx="2" ry="2" onmouseover="s('unix`rw_exit (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1032.4" y="689" width="124.0" height="15.0" fill="rgb(216,106,35)" rx="2" ry="2" onmouseover="s('mac`mac_soft_ring_worker (40027 samples, 10.51%)')" onmouseout="c()" />
<text text-anchor="" x="1035.36881283658" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_soft_ring_worker (40027 samples, 10.51%)')" onmouseout="c()" >mac`mac_soft_r..</text>
<rect x="343.9" y="609" width="0.1" height="15.0" fill="rgb(250,122,45)" rx="2" ry="2" onmouseover="s('genunix`cv_wait (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1048.9" y="545" width="19.9" height="15.0" fill="rgb(211,169,22)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (6413 samples, 1.68%)')" onmouseout="c()" />
<rect x="990.4" y="625" width="0.2" height="15.0" fill="rgb(215,229,29)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="80.1" y="449" width="0.2" height="15.0" fill="rgb(252,80,44)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="972.3" y="673" width="23.4" height="15.0" fill="rgb(249,218,12)" rx="2" ry="2" onmouseover="s('portfs`portfs (7534 samples, 1.98%)')" onmouseout="c()" />
<rect x="754.4" y="433" width="0.2" height="15.0" fill="rgb(210,143,32)" rx="2" ry="2" onmouseover="s('unix`cpu_resched (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="140.2" y="593" width="0.2" height="15.0" fill="rgb(228,50,39)" rx="2" ry="2" onmouseover="s('zfs`dmu_buf_hold_array (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="1068.0" y="481" width="0.1" height="15.0" fill="rgb(254,219,36)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="604.8" y="609" width="0.5" height="15.0" fill="rgb(244,213,33)" rx="2" ry="2" onmouseover="s('genunix`ddi_fm_acc_err_get (146 samples, 0.04%)')" onmouseout="c()" />
<rect x="1027.6" y="641" width="0.1" height="15.0" fill="rgb(207,203,40)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="1079.6" y="401" width="0.1" height="15.0" fill="rgb(205,165,31)" rx="2" ry="2" onmouseover="s('unix`setfrontdq (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1081.4" y="369" width="0.2" height="15.0" fill="rgb(239,108,44)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="194.2" y="689" width="3.2" height="15.0" fill="rgb(217,80,39)" rx="2" ry="2" onmouseover="s('genunix`rexit (1043 samples, 0.27%)')" onmouseout="c()" />
<rect x="674.1" y="257" width="1.1" height="15.0" fill="rgb(212,211,37)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (346 samples, 0.09%)')" onmouseout="c()" />
<rect x="195.5" y="593" width="1.8" height="15.0" fill="rgb(251,42,22)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (573 samples, 0.15%)')" onmouseout="c()" />
<rect x="1159.8" y="481" width="0.2" height="15.0" fill="rgb(247,170,28)" rx="2" ry="2" onmouseover="s('ip`tcp_send (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="165.9" y="593" width="0.4" height="15.0" fill="rgb(225,173,45)" rx="2" ry="2" onmouseover="s('ip`tcp_kstat_update (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="1082.3" y="513" width="62.8" height="15.0" fill="rgb(232,142,35)" rx="2" ry="2" onmouseover="s('ip`conn_connect (20266 samples, 5.32%)')" onmouseout="c()" />
<text text-anchor="" x="1085.27998498092" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_connect (20266 samples, 5.32%)')" onmouseout="c()" >ip`conn..</text>
<rect x="680.5" y="225" width="0.1" height="15.0" fill="rgb(251,177,39)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="25.4" y="577" width="0.4" height="15.0" fill="rgb(210,168,33)" rx="2" ry="2" onmouseover="s('genunix`swap_getpage (128 samples, 0.03%)')" onmouseout="c()" />
<rect x="1050.6" y="433" width="0.2" height="15.0" fill="rgb(222,155,47)" rx="2" ry="2" onmouseover="s('mac`mac_tx (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="984.1" y="593" width="0.5" height="15.0" fill="rgb(212,10,11)" rx="2" ry="2" onmouseover="s('genunix`untimeout_generic (159 samples, 0.04%)')" onmouseout="c()" />
<rect x="1079.0" y="513" width="0.9" height="15.0" fill="rgb(236,81,43)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (291 samples, 0.08%)')" onmouseout="c()" />
<rect x="742.2" y="241" width="0.2" height="15.0" fill="rgb(235,210,5)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_v4 (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="992.9" y="609" width="0.1" height="15.0" fill="rgb(206,89,7)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_free (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="746.7" y="401" width="2.0" height="15.0" fill="rgb(231,176,22)" rx="2" ry="2" onmouseover="s('ip`rn_match_args (627 samples, 0.16%)')" onmouseout="c()" />
<rect x="744.6" y="385" width="0.4" height="15.0" fill="rgb(223,101,9)" rx="2" ry="2" onmouseover="s('ip`udp_ulp_recv (124 samples, 0.03%)')" onmouseout="c()" />
<rect x="679.7" y="369" width="0.3" height="15.0" fill="rgb(249,178,25)" rx="2" ry="2" onmouseover="s('ip`ixa_safe_copy (96 samples, 0.03%)')" onmouseout="c()" />
<rect x="753.1" y="449" width="0.2" height="15.0" fill="rgb(216,202,44)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="153.6" y="513" width="0.1" height="15.0" fill="rgb(213,184,49)" rx="2" ry="2" onmouseover="s('genunix`avl_find (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="949.8" y="609" width="0.4" height="15.0" fill="rgb(250,169,19)" rx="2" ry="2" onmouseover="s('ip`tcp_sendmsg (138 samples, 0.04%)')" onmouseout="c()" />
<rect x="945.9" y="641" width="0.1" height="15.0" fill="rgb(236,78,43)" rx="2" ry="2" onmouseover="s('genunix`cstat (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="337.8" y="657" width="0.3" height="15.0" fill="rgb(247,141,5)" rx="2" ry="2" onmouseover="s('genunix`fs_rwlock (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="671.0" y="369" width="1.1" height="15.0" fill="rgb(250,198,0)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (374 samples, 0.10%)')" onmouseout="c()" />
<rect x="770.7" y="513" width="1.1" height="15.0" fill="rgb(214,50,0)" rx="2" ry="2" onmouseover="s('FSS`fss_setrun (362 samples, 0.10%)')" onmouseout="c()" />
<rect x="1017.6" y="529" width="0.2" height="15.0" fill="rgb(237,216,13)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="76.3" y="689" width="76.1" height="15.0" fill="rgb(243,187,39)" rx="2" ry="2" onmouseover="s('genunix`dtrace_systrace_syscall32 (24560 samples, 6.45%)')" onmouseout="c()" />
<text text-anchor="" x="79.2766083145814" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`dtrace_systrace_syscall32 (24560 samples, 6.45%)')" onmouseout="c()" >genunix`d..</text>
<rect x="311.7" y="545" width="0.7" height="15.0" fill="rgb(242,39,41)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (215 samples, 0.06%)')" onmouseout="c()" />
<rect x="140.5" y="641" width="0.3" height="15.0" fill="rgb(224,184,45)" rx="2" ry="2" onmouseover="s('genunix`fop_rwlock (75 samples, 0.02%)')" onmouseout="c()" />
<rect x="672.3" y="353" width="3.4" height="15.0" fill="rgb(207,113,34)" rx="2" ry="2" onmouseover="s('ip`tcp_send (1103 samples, 0.29%)')" onmouseout="c()" />
<rect x="771.8" y="513" width="0.2" height="15.0" fill="rgb(218,215,53)" rx="2" ry="2" onmouseover="s('genunix`sleepq_unsleep (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="141.4" y="641" width="0.2" height="15.0" fill="rgb(221,125,51)" rx="2" ry="2" onmouseover="s('genunix`releasef (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="825.9" y="689" width="0.3" height="15.0" fill="rgb(222,49,1)" rx="2" ry="2" onmouseover="s('genunix`fstat (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="1049.5" y="529" width="0.3" height="15.0" fill="rgb(219,185,36)" rx="2" ry="2" onmouseover="s('ip`tcp_close_output (109 samples, 0.03%)')" onmouseout="c()" />
<rect x="762.4" y="657" width="0.3" height="15.0" fill="rgb(227,39,11)" rx="2" ry="2" onmouseover="s('unix`intr_get_time (101 samples, 0.03%)')" onmouseout="c()" />
<rect x="32.1" y="529" width="0.2" height="15.0" fill="rgb(216,132,40)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="82.8" y="369" width="0.1" height="15.0" fill="rgb(247,221,7)" rx="2" ry="2" onmouseover="s('mac`mac_client_check_flow_vid (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="84.9" y="417" width="0.9" height="15.0" fill="rgb(247,102,6)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (305 samples, 0.08%)')" onmouseout="c()" />
<rect x="84.9" y="385" width="0.7" height="15.0" fill="rgb(218,60,22)" rx="2" ry="2" onmouseover="s('ipf`fr_check (203 samples, 0.05%)')" onmouseout="c()" />
<rect x="622.2" y="481" width="0.1" height="15.0" fill="rgb(222,197,42)" rx="2" ry="2" onmouseover="s('mac`mac_header_info (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="309.3" y="561" width="3.1" height="15.0" fill="rgb(211,96,23)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (980 samples, 0.26%)')" onmouseout="c()" />
<rect x="936.2" y="657" width="0.4" height="15.0" fill="rgb(243,135,0)" rx="2" ry="2" onmouseover="s('genunix`set_active_fd (131 samples, 0.03%)')" onmouseout="c()" />
<rect x="990.6" y="641" width="0.2" height="15.0" fill="rgb(237,208,45)" rx="2" ry="2" onmouseover="s('genunix`gethrestime (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="152.6" y="577" width="0.3" height="15.0" fill="rgb(226,41,31)" rx="2" ry="2" onmouseover="s('genunix`segvn_fault (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="771.9" y="497" width="0.1" height="15.0" fill="rgb(249,124,34)" rx="2" ry="2" onmouseover="s('genunix`sleepq_dequeue (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="31.5" y="593" width="2.2" height="15.0" fill="rgb(218,123,37)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (702 samples, 0.18%)')" onmouseout="c()" />
<rect x="667.0" y="209" width="0.3" height="15.0" fill="rgb(225,109,6)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_copy (101 samples, 0.03%)')" onmouseout="c()" />
<rect x="533.7" y="529" width="1.0" height="15.0" fill="rgb(224,201,0)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_v4 (335 samples, 0.09%)')" onmouseout="c()" />
<rect x="311.8" y="497" width="0.5" height="15.0" fill="rgb(207,31,41)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (166 samples, 0.04%)')" onmouseout="c()" />
<rect x="625.8" y="497" width="127.2" height="15.0" fill="rgb(228,183,34)" rx="2" ry="2" onmouseover="s('mac`mac_rx_deliver (41063 samples, 10.78%)')" onmouseout="c()" />
<text text-anchor="" x="628.807629795536" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_deliver (41063 samples, 10.78%)')" onmouseout="c()" >mac`mac_rx_deli..</text>
<rect x="754.7" y="433" width="0.2" height="15.0" fill="rgb(247,37,38)" rx="2" ry="2" onmouseover="s('unix`lock_set (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="555.9" y="529" width="0.4" height="15.0" fill="rgb(227,205,18)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="64.3" y="705" width="3.4" height="15.0" fill="rgb(220,48,2)" rx="2" ry="2" onmouseover="s('unix`_resume_from_idle (1108 samples, 0.29%)')" onmouseout="c()" />
<rect x="582.2" y="673" width="2.9" height="15.0" fill="rgb(220,203,7)" rx="2" ry="2" onmouseover="s('sockfs`socket_create (927 samples, 0.24%)')" onmouseout="c()" />
<rect x="772.2" y="577" width="0.1" height="15.0" fill="rgb(217,60,41)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="535.1" y="529" width="0.2" height="15.0" fill="rgb(234,116,24)" rx="2" ry="2" onmouseover="s('ip`ire_nexthop_ill (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="665.0" y="273" width="0.8" height="15.0" fill="rgb(221,104,25)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (277 samples, 0.07%)')" onmouseout="c()" />
<rect x="937.7" y="625" width="0.1" height="15.0" fill="rgb(208,204,23)" rx="2" ry="2" onmouseover="s('unix`preempt (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="1052.0" y="417" width="0.1" height="15.0" fill="rgb(216,196,42)" rx="2" ry="2" onmouseover="s('mac`mac_tx (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="181.6" y="689" width="12.5" height="15.0" fill="rgb(226,51,6)" rx="2" ry="2" onmouseover="s('genunix`putmsg32 (4047 samples, 1.06%)')" onmouseout="c()" />
<rect x="945.5" y="625" width="0.2" height="15.0" fill="rgb(228,66,36)" rx="2" ry="2" onmouseover="s('genunix`as_free (80 samples, 0.02%)')" onmouseout="c()" />
<rect x="669.0" y="225" width="0.1" height="15.0" fill="rgb(210,203,48)" rx="2" ry="2" onmouseover="s('ipf`frpr_ipv4hdr (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="576.4" y="577" width="0.1" height="15.0" fill="rgb(213,192,5)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_v4 (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="554.1" y="497" width="0.2" height="15.0" fill="rgb(240,50,16)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="97.7" y="609" width="1.5" height="15.0" fill="rgb(233,36,36)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (474 samples, 0.12%)')" onmouseout="c()" />
<rect x="211.9" y="657" width="1.0" height="15.0" fill="rgb(252,146,2)" rx="2" ry="2" onmouseover="s('unix`tsc_read (303 samples, 0.08%)')" onmouseout="c()" />
<rect x="342.5" y="673" width="0.2" height="15.0" fill="rgb(217,117,31)" rx="2" ry="2" onmouseover="s('sockfs`getsonode (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="625.0" y="513" width="130.1" height="15.0" fill="rgb(211,186,26)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_process (41994 samples, 11.03%)')" onmouseout="c()" />
<text text-anchor="" x="627.989667821105" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_soft_ring_process (41994 samples, 11.03%)')" onmouseout="c()" >mac`mac_rx_soft..</text>
<rect x="646.8" y="353" width="0.1" height="15.0" fill="rgb(218,134,35)" rx="2" ry="2" onmouseover="s('genunix`cv_broadcast (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="41.7" y="561" width="0.1" height="15.0" fill="rgb(214,45,2)" rx="2" ry="2" onmouseover="s('unix`x86pte_set (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="973.4" y="641" width="0.3" height="15.0" fill="rgb(213,124,45)" rx="2" ry="2" onmouseover="s('genunix`set_active_fd (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="46.2" y="673" width="0.4" height="15.0" fill="rgb(237,42,50)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (146 samples, 0.04%)')" onmouseout="c()" />
<rect x="557.5" y="497" width="0.2" height="15.0" fill="rgb(248,193,46)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1077.7" y="353" width="0.1" height="15.0" fill="rgb(219,183,43)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_bind (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="783.0" y="545" width="0.1" height="15.0" fill="rgb(222,123,26)" rx="2" ry="2" onmouseover="s('FSS`fss_tick (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="33.2" y="529" width="0.5" height="15.0" fill="rgb(230,72,48)" rx="2" ry="2" onmouseover="s('unix`page_lookup_create (149 samples, 0.04%)')" onmouseout="c()" />
<rect x="136.8" y="481" width="0.4" height="15.0" fill="rgb(215,219,17)" rx="2" ry="2" onmouseover="s('genunix`gesballoc (136 samples, 0.04%)')" onmouseout="c()" />
<rect x="1011.1" y="641" width="1.1" height="15.0" fill="rgb(217,106,33)" rx="2" ry="2" onmouseover="s('unix`swtch (349 samples, 0.09%)')" onmouseout="c()" />
<rect x="616.5" y="609" width="143.1" height="15.0" fill="rgb(226,203,10)" rx="2" ry="2" onmouseover="s('mac`mac_rx_common (46195 samples, 12.13%)')" onmouseout="c()" />
<text text-anchor="" x="619.45373888339" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_common (46195 samples, 12.13%)')" onmouseout="c()" >mac`mac_rx_common</text>
<rect x="1158.1" y="545" width="0.5" height="15.0" fill="rgb(238,76,38)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_in (153 samples, 0.04%)')" onmouseout="c()" />
<rect x="770.7" y="529" width="1.3" height="15.0" fill="rgb(206,151,27)" rx="2" ry="2" onmouseover="s('genunix`cv_unsleep (423 samples, 0.11%)')" onmouseout="c()" />
<rect x="996.3" y="689" width="7.1" height="15.0" fill="rgb(252,113,51)" rx="2" ry="2" onmouseover="s('genunix`fsflush (2276 samples, 0.60%)')" onmouseout="c()" />
<rect x="943.2" y="593" width="0.3" height="15.0" fill="rgb(224,8,18)" rx="2" ry="2" onmouseover="s('genunix`segvn_faulta (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="153.6" y="497" width="0.1" height="15.0" fill="rgb(229,13,18)" rx="2" ry="2" onmouseover="s('unix`hment_compare (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1070.5" y="513" width="0.1" height="15.0" fill="rgb(247,202,48)" rx="2" ry="2" onmouseover="s('genunix`dblk_decref (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="950.3" y="657" width="0.4" height="15.0" fill="rgb(244,204,26)" rx="2" ry="2" onmouseover="s('zfs`zfs_write (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="663.2" y="369" width="6.7" height="15.0" fill="rgb(253,133,32)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (2160 samples, 0.57%)')" onmouseout="c()" />
<rect x="1158.1" y="561" width="0.5" height="15.0" fill="rgb(240,174,0)" rx="2" ry="2" onmouseover="s('hook`hook_run (157 samples, 0.04%)')" onmouseout="c()" />
<rect x="1080.9" y="513" width="0.2" height="15.0" fill="rgb(216,70,23)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="67.1" y="657" width="0.2" height="15.0" fill="rgb(229,39,48)" rx="2" ry="2" onmouseover="s('unix`gdt_ucode_model (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="153.1" y="593" width="0.9" height="15.0" fill="rgb(240,50,34)" rx="2" ry="2" onmouseover="s('genunix`as_free (275 samples, 0.07%)')" onmouseout="c()" />
<rect x="675.9" y="353" width="0.3" height="15.0" fill="rgb(222,191,5)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (114 samples, 0.03%)')" onmouseout="c()" />
<rect x="537.3" y="513" width="1.2" height="15.0" fill="rgb(246,182,48)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (404 samples, 0.11%)')" onmouseout="c()" />
<rect x="217.2" y="641" width="0.2" height="15.0" fill="rgb(206,94,9)" rx="2" ry="2" onmouseover="s('genunix`lwp_hash_lookup_and_lock (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="577.8" y="593" width="0.2" height="15.0" fill="rgb(241,215,52)" rx="2" ry="2" onmouseover="s('genunix`cv_block (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="648.6" y="289" width="5.3" height="15.0" fill="rgb(247,27,39)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (1699 samples, 0.45%)')" onmouseout="c()" />
<rect x="78.7" y="465" width="0.2" height="15.0" fill="rgb(240,105,20)" rx="2" ry="2" onmouseover="s('ip`dce_update_uinfo_v4 (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="1018.1" y="641" width="0.3" height="15.0" fill="rgb(252,127,53)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="323.6" y="497" width="0.3" height="15.0" fill="rgb(244,214,7)" rx="2" ry="2" onmouseover="s('ip`ip_xmit_attach_llhdr (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="748.7" y="401" width="0.1" height="15.0" fill="rgb(247,78,30)" rx="2" ry="2" onmouseover="s('unix`atomic_add_32 (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="964.3" y="609" width="1.1" height="15.0" fill="rgb(209,6,29)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (354 samples, 0.09%)')" onmouseout="c()" />
<rect x="784.9" y="689" width="0.9" height="15.0" fill="rgb(230,146,52)" rx="2" ry="2" onmouseover="s('genunix`dtrace_systrace_syscall (287 samples, 0.08%)')" onmouseout="c()" />
<rect x="958.3" y="641" width="4.9" height="15.0" fill="rgb(240,102,42)" rx="2" ry="2" onmouseover="s('unix`mmapobj_map_elf (1594 samples, 0.42%)')" onmouseout="c()" />
<rect x="199.3" y="657" width="6.3" height="15.0" fill="rgb(216,185,41)" rx="2" ry="2" onmouseover="s('genunix`cstatat64_32 (2060 samples, 0.54%)')" onmouseout="c()" />
<rect x="630.5" y="401" width="11.0" height="15.0" fill="rgb(241,1,47)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (3539 samples, 0.93%)')" onmouseout="c()" />
<rect x="770.6" y="545" width="1.4" height="15.0" fill="rgb(213,41,38)" rx="2" ry="2" onmouseover="s('genunix`setrun_locked (469 samples, 0.12%)')" onmouseout="c()" />
<rect x="133.1" y="577" width="0.1" height="15.0" fill="rgb(251,21,54)" rx="2" ry="2" onmouseover="s('ufs`rdip (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="193.7" y="529" width="0.4" height="15.0" fill="rgb(240,194,13)" rx="2" ry="2" onmouseover="s('bmc`kcs_write (127 samples, 0.03%)')" onmouseout="c()" />
<rect x="539.5" y="513" width="0.9" height="15.0" fill="rgb(253,135,23)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (291 samples, 0.08%)')" onmouseout="c()" />
<rect x="648.3" y="353" width="0.2" height="15.0" fill="rgb(227,18,13)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="537.0" y="561" width="1.6" height="15.0" fill="rgb(242,187,30)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (517 samples, 0.14%)')" onmouseout="c()" />
<rect x="39.2" y="561" width="0.1" height="15.0" fill="rgb(216,11,43)" rx="2" ry="2" onmouseover="s('unix`htable_create (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="329.1" y="449" width="0.2" height="15.0" fill="rgb(226,9,31)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="677.5" y="273" width="0.9" height="15.0" fill="rgb(229,57,2)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (279 samples, 0.07%)')" onmouseout="c()" />
<rect x="1009.5" y="609" width="1.0" height="15.0" fill="rgb(211,185,29)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (311 samples, 0.08%)')" onmouseout="c()" />
<rect x="302.0" y="593" width="1.6" height="15.0" fill="rgb(231,189,50)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (527 samples, 0.14%)')" onmouseout="c()" />
<rect x="1078.2" y="433" width="0.1" height="15.0" fill="rgb(243,225,24)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="647.8" y="321" width="0.2" height="15.0" fill="rgb(219,80,53)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="181.6" y="545" width="12.5" height="15.0" fill="rgb(239,90,26)" rx="2" ry="2" onmouseover="s('bmc`do_kcs2bmc (4037 samples, 1.06%)')" onmouseout="c()" />
<rect x="341.7" y="673" width="0.8" height="15.0" fill="rgb(245,91,25)" rx="2" ry="2" onmouseover="s('genunix`useracc (255 samples, 0.07%)')" onmouseout="c()" />
<rect x="774.9" y="497" width="0.7" height="15.0" fill="rgb(206,113,1)" rx="2" ry="2" onmouseover="s('unix`default_lock_delay (209 samples, 0.05%)')" onmouseout="c()" />
<rect x="1012.4" y="609" width="0.1" height="15.0" fill="rgb(253,114,43)" rx="2" ry="2" onmouseover="s('zfs`zio_done (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="10.4" y="705" width="0.2" height="15.0" fill="rgb(233,140,33)" rx="2" ry="2" onmouseover="s('genunix`restorectx (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="945.2" y="641" width="0.2" height="15.0" fill="rgb(234,167,44)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="327.3" y="513" width="0.2" height="15.0" fill="rgb(208,145,23)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="196.8" y="561" width="0.2" height="15.0" fill="rgb(206,220,38)" rx="2" ry="2" onmouseover="s('unix`page_numtopp_nolock (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="138.8" y="577" width="0.1" height="15.0" fill="rgb(232,194,30)" rx="2" ry="2" onmouseover="s('sockfs`sod_rcv_init (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.4" y="241" width="0.3" height="15.0" fill="rgb(236,158,13)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="181.6" y="561" width="12.5" height="15.0" fill="rgb(242,214,47)" rx="2" ry="2" onmouseover="s('bmc`bmc_process_msg (4040 samples, 1.06%)')" onmouseout="c()" />
<rect x="922.0" y="673" width="13.9" height="15.0" fill="rgb(219,132,34)" rx="2" ry="2" onmouseover="s('genunix`fop_ioctl (4461 samples, 1.17%)')" onmouseout="c()" />
<rect x="142.2" y="673" width="10.2" height="15.0" fill="rgb(207,172,18)" rx="2" ry="2" onmouseover="s('unix`dtrace_interrupt_enable (3275 samples, 0.86%)')" onmouseout="c()" />
<rect x="1033.0" y="641" width="0.1" height="15.0" fill="rgb(243,109,37)" rx="2" ry="2" onmouseover="s('unix`splr (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="541.5" y="593" width="0.3" height="15.0" fill="rgb(221,104,10)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="648.6" y="337" width="5.3" height="15.0" fill="rgb(231,66,3)" rx="2" ry="2" onmouseover="s('ip`conn_connect (1713 samples, 0.45%)')" onmouseout="c()" />
<rect x="1164.1" y="689" width="25.5" height="15.0" fill="rgb(252,47,19)" rx="2" ry="2" onmouseover="s('unix`idle (8222 samples, 2.16%)')" onmouseout="c()" />
<rect x="630.7" y="385" width="9.7" height="15.0" fill="rgb(217,0,21)" rx="2" ry="2" onmouseover="s('ipf`fr_check (3132 samples, 0.82%)')" onmouseout="c()" />
<rect x="172.1" y="625" width="0.6" height="15.0" fill="rgb(211,5,38)" rx="2" ry="2" onmouseover="s('unix`disp (198 samples, 0.05%)')" onmouseout="c()" />
<rect x="768.8" y="529" width="1.3" height="15.0" fill="rgb(205,2,22)" rx="2" ry="2" onmouseover="s('unix`do_splx (425 samples, 0.11%)')" onmouseout="c()" />
<rect x="1017.2" y="641" width="0.2" height="15.0" fill="rgb(230,99,19)" rx="2" ry="2" onmouseover="s('ip`tcp_newconn_notify (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="654.0" y="337" width="0.2" height="15.0" fill="rgb(232,142,23)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="180.1" y="529" width="0.1" height="15.0" fill="rgb(240,65,34)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="302.1" y="561" width="1.4" height="15.0" fill="rgb(206,198,0)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (431 samples, 0.11%)')" onmouseout="c()" />
<rect x="639.3" y="337" width="0.3" height="15.0" fill="rgb(227,128,11)" rx="2" ry="2" onmouseover="s('ipf`frpr_tcp (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="785.8" y="657" width="1.4" height="15.0" fill="rgb(247,227,10)" rx="2" ry="2" onmouseover="s('genunix`gexec (466 samples, 0.12%)')" onmouseout="c()" />
<rect x="1003.6" y="673" width="2.8" height="15.0" fill="rgb(231,98,8)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_enabling_reap (928 samples, 0.24%)')" onmouseout="c()" />
<rect x="647.8" y="289" width="0.1" height="15.0" fill="rgb(225,104,37)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="637.7" y="353" width="0.5" height="15.0" fill="rgb(228,222,38)" rx="2" ry="2" onmouseover="s('unix`bcmp (155 samples, 0.04%)')" onmouseout="c()" />
<rect x="204.4" y="545" width="0.6" height="15.0" fill="rgb(211,216,47)" rx="2" ry="2" onmouseover="s('lofs`lo_lookup (199 samples, 0.05%)')" onmouseout="c()" />
<rect x="949.9" y="545" width="0.3" height="15.0" fill="rgb(251,204,9)" rx="2" ry="2" onmouseover="s('ip`tcp_send (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="339.9" y="657" width="0.2" height="15.0" fill="rgb(236,167,17)" rx="2" ry="2" onmouseover="s('unix`_sys_rtt_ints_disabled (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="990.8" y="641" width="0.5" height="15.0" fill="rgb(224,113,48)" rx="2" ry="2" onmouseover="s('genunix`kmem_alloc (150 samples, 0.04%)')" onmouseout="c()" />
<rect x="40.1" y="545" width="0.7" height="15.0" fill="rgb(254,106,14)" rx="2" ry="2" onmouseover="s('unix`hment_insert (197 samples, 0.05%)')" onmouseout="c()" />
<rect x="641.8" y="433" width="0.1" height="15.0" fill="rgb(247,25,50)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_simple_v4 (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="988.9" y="593" width="1.2" height="15.0" fill="rgb(240,15,25)" rx="2" ry="2" onmouseover="s('unix`resume (390 samples, 0.10%)')" onmouseout="c()" />
<rect x="1070.8" y="513" width="0.1" height="15.0" fill="rgb(221,36,27)" rx="2" ry="2" onmouseover="s('genunix`allocb (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1049.6" y="481" width="0.1" height="15.0" fill="rgb(223,173,7)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="72.1" y="705" width="516.2" height="15.0" fill="rgb(222,81,35)" rx="2" ry="2" onmouseover="s('unix`_sys_sysenter_post_swapgs (166612 samples, 43.75%)')" onmouseout="c()" />
<text text-anchor="" x="75.0938482180602" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`_sys_sysenter_post_swapgs (166612 samples, 43.75%)')" onmouseout="c()" >unix`_sys_sysenter_post_swapgs</text>
<rect x="1042.9" y="449" width="0.9" height="15.0" fill="rgb(213,222,12)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (292 samples, 0.08%)')" onmouseout="c()" />
<rect x="137.4" y="417" width="0.1" height="15.0" fill="rgb(253,225,43)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="180.7" y="561" width="0.2" height="15.0" fill="rgb(206,94,30)" rx="2" ry="2" onmouseover="s('genunix`traverse (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1160.2" y="481" width="2.6" height="15.0" fill="rgb(206,39,32)" rx="2" ry="2" onmouseover="s('ip`conn_connect (839 samples, 0.22%)')" onmouseout="c()" />
<rect x="14.0" y="641" width="0.2" height="15.0" fill="rgb(216,47,43)" rx="2" ry="2" onmouseover="s('genunix`cpu_grow (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="175.4" y="609" width="0.1" height="15.0" fill="rgb(234,160,37)" rx="2" ry="2" onmouseover="s('genunix`fop_open (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="578.3" y="625" width="0.2" height="15.0" fill="rgb(224,20,19)" rx="2" ry="2" onmouseover="s('ip`squeue_synch_exit (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="618.3" y="545" width="0.5" height="15.0" fill="rgb(232,131,32)" rx="2" ry="2" onmouseover="s('mac`flow_ether_accept (161 samples, 0.04%)')" onmouseout="c()" />
<rect x="340.8" y="689" width="2.6" height="15.0" fill="rgb(213,181,33)" rx="2" ry="2" onmouseover="s('sockfs`accept (825 samples, 0.22%)')" onmouseout="c()" />
<rect x="1039.2" y="577" width="6.9" height="15.0" fill="rgb(253,126,29)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_in (2209 samples, 0.58%)')" onmouseout="c()" />
<rect x="642.2" y="417" width="103.7" height="15.0" fill="rgb(246,6,7)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (33478 samples, 8.79%)')" onmouseout="c()" />
<text text-anchor="" x="645.222639418772" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_fanout_v4 (33478 samples, 8.79%)')" onmouseout="c()" >ip`ip_fanout..</text>
<rect x="1158.1" y="529" width="0.5" height="15.0" fill="rgb(236,69,41)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (151 samples, 0.04%)')" onmouseout="c()" />
<rect x="964.0" y="625" width="0.1" height="15.0" fill="rgb(208,124,10)" rx="2" ry="2" onmouseover="s('genunix`fop_access (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1165.0" y="673" width="0.3" height="15.0" fill="rgb(209,45,32)" rx="2" ry="2" onmouseover="s('genunix`new_cpu_mstate (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="25.3" y="593" width="0.5" height="15.0" fill="rgb(235,179,16)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="1159.5" y="465" width="0.3" height="15.0" fill="rgb(211,149,14)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="648.6" y="353" width="5.4" height="15.0" fill="rgb(207,106,46)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (1719 samples, 0.45%)')" onmouseout="c()" />
<rect x="538.7" y="561" width="2.6" height="15.0" fill="rgb(225,176,43)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (848 samples, 0.22%)')" onmouseout="c()" />
<rect x="1068.3" y="513" width="0.4" height="15.0" fill="rgb(246,1,4)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="193.9" y="497" width="0.1" height="15.0" fill="rgb(226,63,48)" rx="2" ry="2" onmouseover="s('unix`inb (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="752.2" y="481" width="0.8" height="15.0" fill="rgb(207,130,43)" rx="2" ry="2" onmouseover="s('mac`mac_strip_vlan_tag_chain (254 samples, 0.07%)')" onmouseout="c()" />
<rect x="165.1" y="529" width="0.8" height="15.0" fill="rgb(228,94,1)" rx="2" ry="2" onmouseover="s('unix`segkmem_alloc (268 samples, 0.07%)')" onmouseout="c()" />
<rect x="617.1" y="577" width="142.2" height="15.0" fill="rgb(252,104,40)" rx="2" ry="2" onmouseover="s('mac`mac_rx_classify (45884 samples, 12.05%)')" onmouseout="c()" />
<text text-anchor="" x="620.138472202894" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_classify (45884 samples, 12.05%)')" onmouseout="c()" >mac`mac_rx_class..</text>
<rect x="78.0" y="577" width="16.7" height="15.0" fill="rgb(223,185,15)" rx="2" ry="2" onmouseover="s('sockfs`so_close (5402 samples, 1.42%)')" onmouseout="c()" />
<rect x="639.6" y="353" width="0.2" height="15.0" fill="rgb(215,113,17)" rx="2" ry="2" onmouseover="s('ipf`frpr_tcp (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1044.3" y="529" width="0.6" height="15.0" fill="rgb(233,128,22)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (214 samples, 0.06%)')" onmouseout="c()" />
<rect x="1036.1" y="673" width="119.9" height="15.0" fill="rgb(236,203,24)" rx="2" ry="2" onmouseover="s('mac`mac_rx_soft_ring_drain (38718 samples, 10.17%)')" onmouseout="c()" />
<text text-anchor="" x="1039.06203508477" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_soft_ring_drain (38718 samples, 10.17%)')" onmouseout="c()" >mac`mac_rx_sof..</text>
<rect x="37.7" y="609" width="0.6" height="15.0" fill="rgb(222,141,33)" rx="2" ry="2" onmouseover="s('unix`hat_memload (196 samples, 0.05%)')" onmouseout="c()" />
<rect x="1184.7" y="657" width="0.2" height="15.0" fill="rgb(230,50,49)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="71.4" y="641" width="0.3" height="15.0" fill="rgb(207,82,6)" rx="2" ry="2" onmouseover="s('FSS`fss_preempt (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="316.8" y="449" width="1.4" height="15.0" fill="rgb(229,110,32)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (439 samples, 0.12%)')" onmouseout="c()" />
<rect x="302.2" y="529" width="0.2" height="15.0" fill="rgb(245,92,27)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1028.1" y="625" width="0.4" height="15.0" fill="rgb(242,2,3)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_mp (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="599.8" y="673" width="0.3" height="15.0" fill="rgb(240,117,39)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="624.4" y="497" width="0.4" height="15.0" fill="rgb(233,10,47)" rx="2" ry="2" onmouseover="s('mac`i_mac_flow_vid (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="48.5" y="673" width="0.4" height="15.0" fill="rgb(234,219,52)" rx="2" ry="2" onmouseover="s('c2audit`audit_finish (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="629.0" y="449" width="121.3" height="15.0" fill="rgb(211,198,16)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (39126 samples, 10.27%)')" onmouseout="c()" />
<text text-anchor="" x="632.045395944324" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (39126 samples, 10.27%)')" onmouseout="c()" >ip`ill_input_s..</text>
<rect x="175.1" y="625" width="0.3" height="15.0" fill="rgb(246,127,38)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="1013.5" y="609" width="0.1" height="15.0" fill="rgb(210,35,53)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="641.6" y="433" width="0.2" height="15.0" fill="rgb(252,118,42)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="69.0" y="641" width="2.3" height="15.0" fill="rgb(211,29,54)" rx="2" ry="2" onmouseover="s('unix`kpreempt (737 samples, 0.19%)')" onmouseout="c()" />
<rect x="606.2" y="625" width="3.1" height="15.0" fill="rgb(221,53,1)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_rx_copy (1005 samples, 0.26%)')" onmouseout="c()" />
<rect x="654.6" y="257" width="0.3" height="15.0" fill="rgb(210,181,11)" rx="2" ry="2" onmouseover="s('mac`mac_tx (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="1027.3" y="593" width="0.2" height="15.0" fill="rgb(220,44,24)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1013.4" y="641" width="0.4" height="15.0" fill="rgb(238,53,12)" rx="2" ry="2" onmouseover="s('zfs`vdev_queue_io_done (119 samples, 0.03%)')" onmouseout="c()" />
<rect x="647.0" y="369" width="0.3" height="15.0" fill="rgb(233,24,21)" rx="2" ry="2" onmouseover="s('ip`tcp_close_output (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="1163.0" y="545" width="0.1" height="15.0" fill="rgb(230,130,52)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_simple_v4 (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="994.3" y="657" width="1.2" height="15.0" fill="rgb(249,86,40)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (383 samples, 0.10%)')" onmouseout="c()" />
<rect x="1081.7" y="417" width="0.1" height="15.0" fill="rgb(224,42,29)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="11.7" y="705" width="0.2" height="15.0" fill="rgb(240,229,23)" rx="2" ry="2" onmouseover="s('genunix`write32 (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="1154.1" y="609" width="0.1" height="15.0" fill="rgb(243,189,46)" rx="2" ry="2" onmouseover="s('mac`mac_header_info (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="165.1" y="449" width="0.1" height="15.0" fill="rgb(223,206,24)" rx="2" ry="2" onmouseover="s('unix`hati_pte_map (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="996.3" y="705" width="193.7" height="15.0" fill="rgb(218,56,50)" rx="2" ry="2" onmouseover="s('unix`thread_start (62526 samples, 16.42%)')" onmouseout="c()" />
<text text-anchor="" x="999.266945692387" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`thread_start (62526 samples, 16.42%)')" onmouseout="c()" >unix`thread_start</text>
<rect x="92.9" y="481" width="1.3" height="15.0" fill="rgb(219,72,26)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_mp (421 samples, 0.11%)')" onmouseout="c()" />
<rect x="181.2" y="641" width="0.1" height="15.0" fill="rgb(237,14,18)" rx="2" ry="2" onmouseover="s('genunix`cv_timedwait_sig_hires (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="83.6" y="353" width="0.9" height="15.0" fill="rgb(211,164,1)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (278 samples, 0.07%)')" onmouseout="c()" />
<rect x="165.1" y="465" width="0.2" height="15.0" fill="rgb(215,105,46)" rx="2" ry="2" onmouseover="s('unix`hati_load_common (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="678.7" y="321" width="0.2" height="15.0" fill="rgb(208,32,34)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="976.5" y="641" width="0.1" height="15.0" fill="rgb(223,132,21)" rx="2" ry="2" onmouseover="s('genunix`port_alloc_event_local (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="209.3" y="657" width="0.2" height="15.0" fill="rgb(229,224,7)" rx="2" ry="2" onmouseover="s('unix`copyin (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="179.0" y="545" width="0.3" height="15.0" fill="rgb(208,141,45)" rx="2" ry="2" onmouseover="s('dev`sdev_lookup (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="1077.1" y="433" width="1.0" height="15.0" fill="rgb(220,123,11)" rx="2" ry="2" onmouseover="s('mac`mac_tx (317 samples, 0.08%)')" onmouseout="c()" />
<rect x="556.8" y="465" width="0.1" height="15.0" fill="rgb(233,117,30)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_copy (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.0" y="577" width="0.1" height="15.0" fill="rgb(216,216,32)" rx="2" ry="2" onmouseover="s('unix`pagefault (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="946.0" y="577" width="0.1" height="15.0" fill="rgb(237,136,21)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="971.4" y="673" width="0.3" height="15.0" fill="rgb(219,70,19)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="931.2" y="593" width="2.5" height="15.0" fill="rgb(214,164,25)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (822 samples, 0.22%)')" onmouseout="c()" />
<rect x="207.0" y="657" width="2.1" height="15.0" fill="rgb(243,184,33)" rx="2" ry="2" onmouseover="s('genunix`copyin_nowatch (679 samples, 0.18%)')" onmouseout="c()" />
<rect x="600.5" y="529" width="0.1" height="15.0" fill="rgb(248,27,35)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_proto_fanout (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="600.5" y="641" width="0.1" height="15.0" fill="rgb(221,40,3)" rx="2" ry="2" onmouseover="s('bnx`bnx_intr_recv (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="39.5" y="609" width="0.2" height="15.0" fill="rgb(248,69,25)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="638.3" y="337" width="0.2" height="15.0" fill="rgb(219,168,19)" rx="2" ry="2" onmouseover="s('genunix`turnstile_lookup (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="40.1" y="561" width="0.8" height="15.0" fill="rgb(234,101,31)" rx="2" ry="2" onmouseover="s('unix`hment_assign (243 samples, 0.06%)')" onmouseout="c()" />
<rect x="174.9" y="625" width="0.2" height="15.0" fill="rgb(231,87,18)" rx="2" ry="2" onmouseover="s('genunix`fop_access (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="302.5" y="529" width="0.9" height="15.0" fill="rgb(236,177,20)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (283 samples, 0.07%)')" onmouseout="c()" />
<rect x="774.6" y="577" width="8.0" height="15.0" fill="rgb(209,132,14)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (2587 samples, 0.68%)')" onmouseout="c()" />
<rect x="328.4" y="369" width="0.4" height="15.0" fill="rgb(212,46,15)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_bind (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="15.4" y="641" width="2.0" height="15.0" fill="rgb(209,113,24)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (662 samples, 0.17%)')" onmouseout="c()" />
<rect x="199.5" y="641" width="6.0" height="15.0" fill="rgb(226,114,0)" rx="2" ry="2" onmouseover="s('genunix`cstatat_getvp (1943 samples, 0.51%)')" onmouseout="c()" />
<rect x="217.8" y="673" width="0.2" height="15.0" fill="rgb(250,95,23)" rx="2" ry="2" onmouseover="s('genunix`unlinkat (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="677.3" y="289" width="1.1" height="15.0" fill="rgb(229,96,8)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (350 samples, 0.09%)')" onmouseout="c()" />
<rect x="170.6" y="689" width="2.5" height="15.0" fill="rgb(230,216,2)" rx="2" ry="2" onmouseover="s('genunix`nanosleep (822 samples, 0.22%)')" onmouseout="c()" />
<rect x="195.8" y="561" width="0.2" height="15.0" fill="rgb(243,144,13)" rx="2" ry="2" onmouseover="s('unix`hati_sync_pte_to_page (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1050.3" y="513" width="0.2" height="15.0" fill="rgb(253,195,42)" rx="2" ry="2" onmouseover="s('ip`tcp_newconn_notify (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="140.8" y="641" width="0.1" height="15.0" fill="rgb(236,28,7)" rx="2" ry="2" onmouseover="s('genunix`fop_rwunlock (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="25.4" y="561" width="0.4" height="15.0" fill="rgb(236,51,39)" rx="2" ry="2" onmouseover="s('genunix`swap_getapage (126 samples, 0.03%)')" onmouseout="c()" />
<rect x="132.2" y="593" width="0.2" height="15.0" fill="rgb(252,70,8)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1159.5" y="497" width="0.3" height="15.0" fill="rgb(243,22,51)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="668.3" y="289" width="0.1" height="15.0" fill="rgb(235,91,44)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="939.4" y="577" width="1.4" height="15.0" fill="rgb(243,63,36)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (447 samples, 0.12%)')" onmouseout="c()" />
<rect x="1156.4" y="673" width="0.8" height="15.0" fill="rgb(210,126,47)" rx="2" ry="2" onmouseover="s('genunix`cv_wait (251 samples, 0.07%)')" onmouseout="c()" />
<rect x="619.1" y="513" width="0.1" height="15.0" fill="rgb(232,129,47)" rx="2" ry="2" onmouseover="s('genunix`memcmp (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="1040.4" y="529" width="3.9" height="15.0" fill="rgb(228,86,45)" rx="2" ry="2" onmouseover="s('ipf`fr_firewall (1257 samples, 0.33%)')" onmouseout="c()" />
<rect x="1081.2" y="417" width="0.2" height="15.0" fill="rgb(225,91,36)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="301.9" y="609" width="1.8" height="15.0" fill="rgb(247,228,25)" rx="2" ry="2" onmouseover="s('genunix`strpollwakeup (575 samples, 0.15%)')" onmouseout="c()" />
<rect x="556.3" y="529" width="0.9" height="15.0" fill="rgb(211,75,21)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (286 samples, 0.08%)')" onmouseout="c()" />
<rect x="646.5" y="385" width="8.7" height="15.0" fill="rgb(230,123,43)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (2807 samples, 0.74%)')" onmouseout="c()" />
<rect x="662.2" y="337" width="0.3" height="15.0" fill="rgb(252,93,45)" rx="2" ry="2" onmouseover="s('sockfs`socket_newconn (103 samples, 0.03%)')" onmouseout="c()" />
<rect x="211.7" y="673" width="1.2" height="15.0" fill="rgb(238,89,42)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (384 samples, 0.10%)')" onmouseout="c()" />
<rect x="1051.5" y="513" width="0.1" height="15.0" fill="rgb(235,10,5)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="984.7" y="609" width="0.2" height="15.0" fill="rgb(213,80,39)" rx="2" ry="2" onmouseover="s('unix`_resume_from_idle (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="1158.2" y="497" width="0.2" height="15.0" fill="rgb(233,110,51)" rx="2" ry="2" onmouseover="s('ipf`fr_firewall (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="961.9" y="577" width="0.9" height="15.0" fill="rgb(249,6,25)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (280 samples, 0.07%)')" onmouseout="c()" />
<rect x="674.8" y="193" width="0.1" height="15.0" fill="rgb(224,102,37)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="768.8" y="561" width="1.4" height="15.0" fill="rgb(224,195,49)" rx="2" ry="2" onmouseover="s('unix`xc_sync (426 samples, 0.11%)')" onmouseout="c()" />
<rect x="589.0" y="673" width="5.9" height="15.0" fill="rgb(216,59,24)" rx="2" ry="2" onmouseover="s('fasttrap`fasttrap_pid_probe (1923 samples, 0.50%)')" onmouseout="c()" />
<rect x="327.9" y="481" width="1.5" height="15.0" fill="rgb(211,165,27)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (474 samples, 0.12%)')" onmouseout="c()" />
<rect x="753.7" y="449" width="0.1" height="15.0" fill="rgb(251,207,39)" rx="2" ry="2" onmouseover="s('unix`membar_enter (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="159.7" y="609" width="10.1" height="15.0" fill="rgb(245,101,4)" rx="2" ry="2" onmouseover="s('kstat`read_kstat_data (3284 samples, 0.86%)')" onmouseout="c()" />
<rect x="1159.8" y="497" width="0.2" height="15.0" fill="rgb(220,162,4)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="1028.0" y="641" width="3.8" height="15.0" fill="rgb(244,127,23)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (1206 samples, 0.32%)')" onmouseout="c()" />
<rect x="630.0" y="433" width="11.5" height="15.0" fill="rgb(224,140,38)" rx="2" ry="2" onmouseover="s('hook`hook_run (3719 samples, 0.98%)')" onmouseout="c()" />
<rect x="1051.9" y="513" width="0.3" height="15.0" fill="rgb(243,100,14)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="1029.3" y="545" width="1.5" height="15.0" fill="rgb(210,218,35)" rx="2" ry="2" onmouseover="s('mac`mac_tx (513 samples, 0.13%)')" onmouseout="c()" />
<rect x="785.1" y="673" width="0.6" height="15.0" fill="rgb(240,182,52)" rx="2" ry="2" onmouseover="s('genunix`read (184 samples, 0.05%)')" onmouseout="c()" />
<rect x="978.4" y="657" width="15.8" height="15.0" fill="rgb(213,144,32)" rx="2" ry="2" onmouseover="s('portfs`port_getn (5102 samples, 1.34%)')" onmouseout="c()" />
<rect x="13.5" y="641" width="0.2" height="15.0" fill="rgb(206,147,18)" rx="2" ry="2" onmouseover="s('genunix`segvn_fault (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="1009.9" y="545" width="0.1" height="15.0" fill="rgb(216,176,43)" rx="2" ry="2" onmouseover="s('unix`setbackdq (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="152.5" y="625" width="0.6" height="15.0" fill="rgb(253,19,11)" rx="2" ry="2" onmouseover="s('elfexec`mapelfexec (183 samples, 0.05%)')" onmouseout="c()" />
<rect x="1182.1" y="657" width="0.1" height="15.0" fill="rgb(215,217,18)" rx="2" ry="2" onmouseover="s('genunix`new_cpu_mstate (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1078.2" y="417" width="0.1" height="15.0" fill="rgb(225,104,53)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="663.9" y="337" width="5.9" height="15.0" fill="rgb(239,50,13)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (1906 samples, 0.50%)')" onmouseout="c()" />
<rect x="213.7" y="561" width="0.1" height="15.0" fill="rgb(211,126,12)" rx="2" ry="2" onmouseover="s('unix`caps_charge_adjust (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="1031.8" y="625" width="0.2" height="15.0" fill="rgb(206,54,46)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="1013.8" y="593" width="0.1" height="15.0" fill="rgb(229,152,49)" rx="2" ry="2" onmouseover="s('zfs`zio_vdev_io_start (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1018.2" y="593" width="0.2" height="15.0" fill="rgb(210,22,51)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="670.2" y="305" width="0.1" height="15.0" fill="rgb(232,185,11)" rx="2" ry="2" onmouseover="s('genunix`untimeout_generic (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="1009.8" y="561" width="0.2" height="15.0" fill="rgb(216,162,17)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="664.1" y="321" width="0.1" height="15.0" fill="rgb(207,137,0)" rx="2" ry="2" onmouseover="s('ip`ip_output_cksum_v4 (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="173.1" y="689" width="8.1" height="15.0" fill="rgb(211,93,9)" rx="2" ry="2" onmouseover="s('genunix`open32 (2589 samples, 0.68%)')" onmouseout="c()" />
<rect x="761.1" y="657" width="1.3" height="15.0" fill="rgb(237,53,53)" rx="2" ry="2" onmouseover="s('unix`ddi_get32 (407 samples, 0.11%)')" onmouseout="c()" />
<rect x="680.2" y="337" width="0.7" height="15.0" fill="rgb(226,11,28)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (228 samples, 0.06%)')" onmouseout="c()" />
<rect x="743.6" y="337" width="0.8" height="15.0" fill="rgb(249,34,42)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_v4 (272 samples, 0.07%)')" onmouseout="c()" />
<rect x="1075.8" y="529" width="0.3" height="15.0" fill="rgb(242,185,21)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="31.7" y="561" width="2.0" height="15.0" fill="rgb(214,201,45)" rx="2" ry="2" onmouseover="s('genunix`swap_getapage (639 samples, 0.17%)')" onmouseout="c()" />
<rect x="787.3" y="689" width="38.3" height="15.0" fill="rgb(230,9,12)" rx="2" ry="2" onmouseover="s('genunix`fop_ioctl (12356 samples, 3.24%)')" onmouseout="c()" />
<rect x="1008.4" y="609" width="0.6" height="15.0" fill="rgb(219,180,34)" rx="2" ry="2" onmouseover="s('ip`tcp_cleanup (194 samples, 0.05%)')" onmouseout="c()" />
<rect x="648.6" y="321" width="5.3" height="15.0" fill="rgb(241,193,31)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (1711 samples, 0.45%)')" onmouseout="c()" />
<rect x="311.3" y="529" width="0.2" height="15.0" fill="rgb(244,130,2)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="660.5" y="289" width="0.1" height="15.0" fill="rgb(246,47,26)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="157.9" y="657" width="0.1" height="15.0" fill="rgb(222,40,0)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1165.3" y="657" width="0.2" height="15.0" fill="rgb(210,174,39)" rx="2" ry="2" onmouseover="s('unix`do_splx (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="1018.2" y="561" width="0.1" height="15.0" fill="rgb(227,178,27)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="782.9" y="593" width="0.3" height="15.0" fill="rgb(215,39,19)" rx="2" ry="2" onmouseover="s('unix`clock_tick_execute_common (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="655.2" y="385" width="24.0" height="15.0" fill="rgb(241,28,18)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (7730 samples, 2.03%)')" onmouseout="c()" />
<rect x="1042.0" y="497" width="0.1" height="15.0" fill="rgb(230,58,3)" rx="2" ry="2" onmouseover="s('unix`strncpy (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="985.9" y="561" width="0.2" height="15.0" fill="rgb(208,54,25)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="95.9" y="609" width="0.6" height="15.0" fill="rgb(253,47,29)" rx="2" ry="2" onmouseover="s('genunix`port_remove_fd_object (187 samples, 0.05%)')" onmouseout="c()" />
<rect x="302.2" y="545" width="0.2" height="15.0" fill="rgb(211,113,44)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="193.7" y="513" width="0.2" height="15.0" fill="rgb(221,137,4)" rx="2" ry="2" onmouseover="s('bmc`kcs_data_pending (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="742.2" y="257" width="0.2" height="15.0" fill="rgb(249,221,8)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_impl_v4 (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="33.8" y="593" width="1.3" height="15.0" fill="rgb(209,114,45)" rx="2" ry="2" onmouseover="s('unix`pagezero (407 samples, 0.11%)')" onmouseout="c()" />
<rect x="743.6" y="385" width="0.8" height="15.0" fill="rgb(249,187,31)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_early_reset (285 samples, 0.07%)')" onmouseout="c()" />
<rect x="670.1" y="337" width="0.2" height="15.0" fill="rgb(210,182,32)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="135.5" y="561" width="0.2" height="15.0" fill="rgb(235,153,47)" rx="2" ry="2" onmouseover="s('genunix`pullupmsg (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="311.3" y="497" width="0.2" height="15.0" fill="rgb(207,29,15)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="1038.4" y="609" width="115.2" height="15.0" fill="rgb(253,67,5)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (37170 samples, 9.76%)')" onmouseout="c()" />
<text text-anchor="" x="1041.40128239801" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ill_input_short_v4 (37170 samples, 9.76%)')" onmouseout="c()" >ip`ill_input_..</text>
<rect x="341.7" y="657" width="0.7" height="15.0" fill="rgb(229,89,50)" rx="2" ry="2" onmouseover="s('genunix`as_checkprot (224 samples, 0.06%)')" onmouseout="c()" />
<rect x="743.5" y="401" width="0.9" height="15.0" fill="rgb(243,218,26)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_listeners_reset (293 samples, 0.08%)')" onmouseout="c()" />
<rect x="67.5" y="689" width="0.2" height="15.0" fill="rgb(210,62,50)" rx="2" ry="2" onmouseover="s('unix`sep_restore (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="153.2" y="529" width="0.2" height="15.0" fill="rgb(253,157,43)" rx="2" ry="2" onmouseover="s('genunix`anon_free (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="578.9" y="673" width="0.6" height="15.0" fill="rgb(227,96,25)" rx="2" ry="2" onmouseover="s('genunix`falloc (162 samples, 0.04%)')" onmouseout="c()" />
<rect x="627.4" y="433" width="0.2" height="15.0" fill="rgb(208,74,4)" rx="2" ry="2" onmouseover="s('genunix`i_mod_hash (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="314.9" y="433" width="0.1" height="15.0" fill="rgb(225,215,29)" rx="2" ry="2" onmouseover="s('ip`rn_match_args (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1035.5" y="609" width="0.2" height="15.0" fill="rgb(208,40,20)" rx="2" ry="2" onmouseover="s('unix`disp_getbest (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="141.2" y="625" width="0.2" height="15.0" fill="rgb(207,62,3)" rx="2" ry="2" onmouseover="s('genunix`set_active_fd (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="554.8" y="657" width="22.3" height="15.0" fill="rgb(215,59,47)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (7188 samples, 1.89%)')" onmouseout="c()" />
<rect x="938.5" y="673" width="2.4" height="15.0" fill="rgb(223,16,24)" rx="2" ry="2" onmouseover="s('genunix`fstatat (786 samples, 0.21%)')" onmouseout="c()" />
<rect x="49.5" y="657" width="0.7" height="15.0" fill="rgb(246,93,0)" rx="2" ry="2" onmouseover="s('unix`do_splx (251 samples, 0.07%)')" onmouseout="c()" />
<rect x="1182.2" y="657" width="0.3" height="15.0" fill="rgb(210,181,22)" rx="2" ry="2" onmouseover="s('pcplusmp`apic_set_idlecpu (85 samples, 0.02%)')" onmouseout="c()" />
<rect x="953.6" y="625" width="0.2" height="15.0" fill="rgb(243,30,24)" rx="2" ry="2" onmouseover="s('unix`hat_clrattr (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="984.1" y="609" width="0.6" height="15.0" fill="rgb(251,126,23)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (202 samples, 0.05%)')" onmouseout="c()" />
<rect x="974.8" y="641" width="1.4" height="15.0" fill="rgb(216,6,28)" rx="2" ry="2" onmouseover="s('genunix`fop_poll (473 samples, 0.12%)')" onmouseout="c()" />
<rect x="53.9" y="689" width="1.0" height="15.0" fill="rgb(254,186,27)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (318 samples, 0.08%)')" onmouseout="c()" />
<rect x="160.1" y="561" width="4.9" height="15.0" fill="rgb(251,184,22)" rx="2" ry="2" onmouseover="s('genunix`vmem_xfree (1601 samples, 0.42%)')" onmouseout="c()" />
<rect x="1150.8" y="417" width="0.1" height="15.0" fill="rgb(216,131,1)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="751.2" y="433" width="0.2" height="15.0" fill="rgb(235,27,44)" rx="2" ry="2" onmouseover="s('mac_ether`mac_ether_header_info (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1027.8" y="657" width="0.1" height="15.0" fill="rgb(208,101,38)" rx="2" ry="2" onmouseover="s('ip`tcp_send_synack (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="942.4" y="641" width="0.5" height="15.0" fill="rgb(239,209,4)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (157 samples, 0.04%)')" onmouseout="c()" />
<rect x="607.9" y="593" width="0.2" height="15.0" fill="rgb(251,209,28)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_alloc (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="986.2" y="577" width="2.2" height="15.0" fill="rgb(218,39,24)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (714 samples, 0.19%)')" onmouseout="c()" />
<rect x="658.8" y="369" width="0.5" height="15.0" fill="rgb(213,223,42)" rx="2" ry="2" onmouseover="s('genunix`freeb (149 samples, 0.04%)')" onmouseout="c()" />
<rect x="39.9" y="625" width="2.2" height="15.0" fill="rgb(225,188,26)" rx="2" ry="2" onmouseover="s('unix`hat_memload_region (731 samples, 0.19%)')" onmouseout="c()" />
<rect x="946.7" y="689" width="2.6" height="15.0" fill="rgb(230,70,4)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (835 samples, 0.22%)')" onmouseout="c()" />
<rect x="964.3" y="593" width="1.1" height="15.0" fill="rgb(245,43,37)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (352 samples, 0.09%)')" onmouseout="c()" />
<rect x="155.8" y="689" width="0.8" height="15.0" fill="rgb(208,192,31)" rx="2" ry="2" onmouseover="s('genunix`fcntl (269 samples, 0.07%)')" onmouseout="c()" />
<rect x="98.8" y="529" width="0.2" height="15.0" fill="rgb(249,106,36)" rx="2" ry="2" onmouseover="s('lofs`lo_lookup (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="25.5" y="529" width="0.1" height="15.0" fill="rgb(234,150,29)" rx="2" ry="2" onmouseover="s('unix`page_get_freelist (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1176.7" y="625" width="0.2" height="15.0" fill="rgb(212,173,36)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="173.1" y="673" width="8.1" height="15.0" fill="rgb(234,66,45)" rx="2" ry="2" onmouseover="s('genunix`openat32 (2589 samples, 0.68%)')" onmouseout="c()" />
<rect x="84.1" y="337" width="0.2" height="15.0" fill="rgb(208,141,29)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="1030.3" y="449" width="0.1" height="15.0" fill="rgb(229,149,54)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="533.3" y="449" width="0.2" height="15.0" fill="rgb(214,63,9)" rx="2" ry="2" onmouseover="s('unix`swtch (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="343.9" y="625" width="0.1" height="15.0" fill="rgb(230,217,25)" rx="2" ry="2" onmouseover="s('ip`squeue_synch_enter (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="136.0" y="545" width="1.6" height="15.0" fill="rgb(212,69,45)" rx="2" ry="2" onmouseover="s('genunix`freeb (517 samples, 0.14%)')" onmouseout="c()" />
<rect x="973.2" y="657" width="0.5" height="15.0" fill="rgb(214,110,24)" rx="2" ry="2" onmouseover="s('genunix`getf (159 samples, 0.04%)')" onmouseout="c()" />
<rect x="22.1" y="593" width="0.2" height="15.0" fill="rgb(248,177,39)" rx="2" ry="2" onmouseover="s('ufs`ufs_lockfs_begin_getpage (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.1" y="593" width="4.9" height="15.0" fill="rgb(251,110,53)" rx="2" ry="2" onmouseover="s('genunix`kmem_free (1601 samples, 0.42%)')" onmouseout="c()" />
<rect x="197.8" y="657" width="0.2" height="15.0" fill="rgb(223,67,45)" rx="2" ry="2" onmouseover="s('genunix`zmap (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="943.9" y="609" width="0.8" height="15.0" fill="rgb(247,52,49)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (258 samples, 0.07%)')" onmouseout="c()" />
<rect x="176.6" y="577" width="4.4" height="15.0" fill="rgb(213,121,47)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (1397 samples, 0.37%)')" onmouseout="c()" />
<rect x="945.5" y="689" width="0.2" height="15.0" fill="rgb(225,59,40)" rx="2" ry="2" onmouseover="s('genunix`rexit (88 samples, 0.02%)')" onmouseout="c()" />
<rect x="31.9" y="545" width="1.2" height="15.0" fill="rgb(216,10,27)" rx="2" ry="2" onmouseover="s('unix`page_create_va (413 samples, 0.11%)')" onmouseout="c()" />
<rect x="958.5" y="609" width="0.6" height="15.0" fill="rgb(223,104,3)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb8001d6 (195 samples, 0.05%)')" onmouseout="c()" />
<rect x="157.3" y="641" width="0.2" height="15.0" fill="rgb(238,80,47)" rx="2" ry="2" onmouseover="s('lofs`lo_getattr (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="304.6" y="641" width="0.3" height="15.0" fill="rgb(211,215,50)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (106 samples, 0.03%)')" onmouseout="c()" />
<rect x="340.0" y="609" width="0.1" height="15.0" fill="rgb(226,197,52)" rx="2" ry="2" onmouseover="s('unix`preempt (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1075.2" y="433" width="0.3" height="15.0" fill="rgb(245,72,12)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="1081.2" y="401" width="0.1" height="15.0" fill="rgb(252,198,21)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1187.3" y="641" width="0.3" height="15.0" fill="rgb(242,151,41)" rx="2" ry="2" onmouseover="s('unix`atomic_add_32 (89 samples, 0.02%)')" onmouseout="c()" />
<rect x="81.3" y="481" width="4.9" height="15.0" fill="rgb(206,210,25)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (1591 samples, 0.42%)')" onmouseout="c()" />
<rect x="153.2" y="545" width="0.2" height="15.0" fill="rgb(232,27,38)" rx="2" ry="2" onmouseover="s('genunix`segvn_free (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="742.0" y="257" width="0.1" height="15.0" fill="rgb(205,25,54)" rx="2" ry="2" onmouseover="s('ip`ipif_lookup_addr_nondup (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="753.0" y="497" width="2.0" height="15.0" fill="rgb(229,123,28)" rx="2" ry="2" onmouseover="s('mac`mac_soft_ring_worker_wakeup (631 samples, 0.17%)')" onmouseout="c()" />
<rect x="551.6" y="657" width="0.1" height="15.0" fill="rgb(206,67,3)" rx="2" ry="2" onmouseover="s('sockfs`so_getsockopt (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="70.5" y="593" width="0.3" height="15.0" fill="rgb(232,1,28)" rx="2" ry="2" onmouseover="s('unix`disp (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="578.7" y="609" width="0.1" height="15.0" fill="rgb(245,133,35)" rx="2" ry="2" onmouseover="s('ip`conn_opt_set (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="533.1" y="465" width="0.4" height="15.0" fill="rgb(247,60,41)" rx="2" ry="2" onmouseover="s('unix`preempt (115 samples, 0.03%)')" onmouseout="c()" />
<rect x="553.0" y="625" width="0.8" height="15.0" fill="rgb(216,22,45)" rx="2" ry="2" onmouseover="s('sockfs`so_dequeue_msg (248 samples, 0.07%)')" onmouseout="c()" />
<rect x="949.9" y="497" width="0.3" height="15.0" fill="rgb(218,52,10)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="331.1" y="577" width="2.9" height="15.0" fill="rgb(206,59,10)" rx="2" ry="2" onmouseover="s('genunix`allocb (937 samples, 0.25%)')" onmouseout="c()" />
<rect x="181.6" y="593" width="12.5" height="15.0" fill="rgb(251,190,34)" rx="2" ry="2" onmouseover="s('genunix`runservice (4044 samples, 1.06%)')" onmouseout="c()" />
<rect x="963.0" y="577" width="0.1" height="15.0" fill="rgb(230,105,36)" rx="2" ry="2" onmouseover="s('genunix`fop_map (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="337.7" y="625" width="0.1" height="15.0" fill="rgb(231,224,40)" rx="2" ry="2" onmouseover="s('zfs`zfs_log_write (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="551.0" y="673" width="0.2" height="15.0" fill="rgb(235,109,34)" rx="2" ry="2" onmouseover="s('sockfs`socket_getpeername (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="647.3" y="369" width="1.2" height="15.0" fill="rgb(246,66,41)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (375 samples, 0.10%)')" onmouseout="c()" />
<rect x="173.1" y="657" width="8.0" height="15.0" fill="rgb(252,74,41)" rx="2" ry="2" onmouseover="s('genunix`copen (2586 samples, 0.68%)')" onmouseout="c()" />
<rect x="80.1" y="465" width="0.2" height="15.0" fill="rgb(228,113,29)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="45.8" y="689" width="0.8" height="15.0" fill="rgb(228,228,46)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (277 samples, 0.07%)')" onmouseout="c()" />
<rect x="1029.9" y="513" width="0.9" height="15.0" fill="rgb(205,49,17)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (311 samples, 0.08%)')" onmouseout="c()" />
<rect x="773.1" y="577" width="0.3" height="15.0" fill="rgb(229,57,3)" rx="2" ry="2" onmouseover="s('genunix`cyclic_reprogram_cyclic (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="194.7" y="465" width="0.1" height="15.0" fill="rgb(229,187,52)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.8" y="657" width="0.6" height="15.0" fill="rgb(229,131,18)" rx="2" ry="2" onmouseover="s('zfs`zio_ready (204 samples, 0.05%)')" onmouseout="c()" />
<rect x="962.0" y="561" width="0.8" height="15.0" fill="rgb(243,26,16)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage (264 samples, 0.07%)')" onmouseout="c()" />
<rect x="312.0" y="401" width="0.2" height="15.0" fill="rgb(238,102,31)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="662.0" y="321" width="0.2" height="15.0" fill="rgb(215,173,33)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="1150.7" y="449" width="0.2" height="15.0" fill="rgb(227,71,32)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="339.9" y="641" width="0.2" height="15.0" fill="rgb(208,160,25)" rx="2" ry="2" onmouseover="s('unix`sys_rtt_common (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="962.3" y="481" width="0.2" height="15.0" fill="rgb(237,158,46)" rx="2" ry="2" onmouseover="s('unix`page_get_mnode_freelist (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="152.4" y="689" width="3.4" height="15.0" fill="rgb(222,29,13)" rx="2" ry="2" onmouseover="s('genunix`exece (1091 samples, 0.29%)')" onmouseout="c()" />
<rect x="626.8" y="465" width="0.2" height="15.0" fill="rgb(230,8,5)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_subchain (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="152.7" y="561" width="0.2" height="15.0" fill="rgb(222,20,16)" rx="2" ry="2" onmouseover="s('genunix`segvn_faultpage (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="679.3" y="369" width="0.3" height="15.0" fill="rgb(238,167,14)" rx="2" ry="2" onmouseover="s('ip`conn_inherit_parent (93 samples, 0.02%)')" onmouseout="c()" />
<rect x="1030.9" y="561" width="0.5" height="15.0" fill="rgb(220,68,44)" rx="2" ry="2" onmouseover="s('hook`hook_run (173 samples, 0.05%)')" onmouseout="c()" />
<rect x="957.6" y="593" width="0.1" height="15.0" fill="rgb(225,212,5)" rx="2" ry="2" onmouseover="s('ufs`bmap_read (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="175.4" y="625" width="0.3" height="15.0" fill="rgb(251,171,42)" rx="2" ry="2" onmouseover="s('genunix`fop_open (119 samples, 0.03%)')" onmouseout="c()" />
<rect x="540.5" y="529" width="0.6" height="15.0" fill="rgb(249,128,54)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (187 samples, 0.05%)')" onmouseout="c()" />
<rect x="215.2" y="657" width="0.3" height="15.0" fill="rgb(241,95,21)" rx="2" ry="2" onmouseover="s('genunix`new_mstate (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="1006.9" y="577" width="0.3" height="15.0" fill="rgb(244,185,54)" rx="2" ry="2" onmouseover="s('unix`default_lock_delay (101 samples, 0.03%)')" onmouseout="c()" />
<rect x="321.0" y="433" width="0.4" height="15.0" fill="rgb(247,60,48)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="1017.2" y="625" width="0.2" height="15.0" fill="rgb(232,225,40)" rx="2" ry="2" onmouseover="s('sockfs`so_newconn (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1183.1" y="673" width="5.3" height="15.0" fill="rgb(245,34,35)" rx="2" ry="2" onmouseover="s('unix`swtch (1738 samples, 0.46%)')" onmouseout="c()" />
<rect x="957.9" y="689" width="5.3" height="15.0" fill="rgb(236,51,16)" rx="2" ry="2" onmouseover="s('genunix`mmapobjsys (1737 samples, 0.46%)')" onmouseout="c()" />
<rect x="964.3" y="625" width="1.1" height="15.0" fill="rgb(249,76,33)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="638.7" y="353" width="0.9" height="15.0" fill="rgb(225,128,13)" rx="2" ry="2" onmouseover="s('ipf`frpr_ipv4hdr (298 samples, 0.08%)')" onmouseout="c()" />
<rect x="742.0" y="273" width="0.2" height="15.0" fill="rgb(244,7,52)" rx="2" ry="2" onmouseover="s('ip`ip_select_src_ill (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="167.5" y="577" width="0.3" height="15.0" fill="rgb(212,58,46)" rx="2" ry="2" onmouseover="s('genunix`avl_walk (103 samples, 0.03%)')" onmouseout="c()" />
<rect x="578.4" y="609" width="0.1" height="15.0" fill="rgb(246,213,19)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="338.3" y="657" width="0.8" height="15.0" fill="rgb(226,218,7)" rx="2" ry="2" onmouseover="s('genunix`getf (271 samples, 0.07%)')" onmouseout="c()" />
<rect x="344.0" y="609" width="0.6" height="15.0" fill="rgb(247,87,33)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (168 samples, 0.04%)')" onmouseout="c()" />
<rect x="959.4" y="609" width="3.4" height="15.0" fill="rgb(236,102,11)" rx="2" ry="2" onmouseover="s('genunix`as_faulta (1111 samples, 0.29%)')" onmouseout="c()" />
<rect x="194.4" y="561" width="0.9" height="15.0" fill="rgb(240,123,43)" rx="2" ry="2" onmouseover="s('genunix`anon_free (302 samples, 0.08%)')" onmouseout="c()" />
<rect x="171.4" y="641" width="0.5" height="15.0" fill="rgb(218,5,25)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (131 samples, 0.03%)')" onmouseout="c()" />
<rect x="327.6" y="529" width="1.9" height="15.0" fill="rgb(242,69,32)" rx="2" ry="2" onmouseover="s('ip`tcp_send (597 samples, 0.16%)')" onmouseout="c()" />
<rect x="56.0" y="689" width="0.2" height="15.0" fill="rgb(231,14,54)" rx="2" ry="2" onmouseover="s('genunix`post_syscall (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="306.2" y="609" width="0.2" height="15.0" fill="rgb(230,218,2)" rx="2" ry="2" onmouseover="s('ip`tcp_sendmsg (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="638.3" y="289" width="0.2" height="15.0" fill="rgb(245,218,44)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="152.5" y="609" width="0.4" height="15.0" fill="rgb(252,103,29)" rx="2" ry="2" onmouseover="s('genunix`execmap (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="13.5" y="673" width="0.3" height="15.0" fill="rgb(220,38,16)" rx="2" ry="2" onmouseover="s('genunix`grow (96 samples, 0.03%)')" onmouseout="c()" />
<rect x="136.1" y="529" width="0.1" height="15.0" fill="rgb(249,5,15)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="1006.7" y="625" width="1.7" height="15.0" fill="rgb(223,208,8)" rx="2" ry="2" onmouseover="s('fasttrap`fasttrap_pid_cleanup_cb (546 samples, 0.14%)')" onmouseout="c()" />
<rect x="134.5" y="577" width="4.1" height="15.0" fill="rgb(207,5,35)" rx="2" ry="2" onmouseover="s('sockfs`so_dequeue_msg (1323 samples, 0.35%)')" onmouseout="c()" />
<rect x="669.4" y="305" width="0.2" height="15.0" fill="rgb(219,219,25)" rx="2" ry="2" onmouseover="s('ip`ip_xmit_attach_llhdr (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="660.2" y="369" width="0.5" height="15.0" fill="rgb(221,26,0)" rx="2" ry="2" onmouseover="s('ip`tcp_clean_death (150 samples, 0.04%)')" onmouseout="c()" />
<rect x="1077.5" y="417" width="0.6" height="15.0" fill="rgb(205,21,12)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (191 samples, 0.05%)')" onmouseout="c()" />
<rect x="1077.0" y="465" width="1.4" height="15.0" fill="rgb(234,199,54)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (436 samples, 0.11%)')" onmouseout="c()" />
<rect x="1018.9" y="641" width="8.2" height="15.0" fill="rgb(240,53,21)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (2651 samples, 0.70%)')" onmouseout="c()" />
<rect x="662.0" y="273" width="0.2" height="15.0" fill="rgb(237,116,17)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="335.5" y="609" width="0.2" height="15.0" fill="rgb(223,210,23)" rx="2" ry="2" onmouseover="s('unix`rw_enter (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="330.4" y="577" width="0.3" height="15.0" fill="rgb(235,81,45)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="1031.0" y="545" width="0.4" height="15.0" fill="rgb(219,78,31)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (143 samples, 0.04%)')" onmouseout="c()" />
<rect x="160.1" y="497" width="4.7" height="15.0" fill="rgb(231,20,53)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (1523 samples, 0.40%)')" onmouseout="c()" />
<rect x="138.0" y="529" width="0.3" height="15.0" fill="rgb(220,89,5)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb85a6ea (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="215.0" y="593" width="0.2" height="15.0" fill="rgb(211,57,48)" rx="2" ry="2" onmouseover="s('unix`resume (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="1145.1" y="513" width="0.2" height="15.0" fill="rgb(226,171,20)" rx="2" ry="2" onmouseover="s('ip`tcp_build_hdrs (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="158.1" y="673" width="0.1" height="15.0" fill="rgb(209,172,19)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="654.3" y="369" width="0.8" height="15.0" fill="rgb(232,129,22)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (267 samples, 0.07%)')" onmouseout="c()" />
<rect x="69.3" y="593" width="0.2" height="15.0" fill="rgb(215,27,8)" rx="2" ry="2" onmouseover="s('unix`cpucaps_charge (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1150.6" y="513" width="0.4" height="15.0" fill="rgb(225,70,13)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="343.7" y="657" width="206.9" height="15.0" fill="rgb(254,88,40)" rx="2" ry="2" onmouseover="s('sockfs`so_connect (66777 samples, 17.53%)')" onmouseout="c()" />
<text text-anchor="" x="346.706797182085" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`so_connect (66777 samples, 17.53%)')" onmouseout="c()" >sockfs`so_connect</text>
<rect x="967.6" y="609" width="1.7" height="15.0" fill="rgb(248,91,15)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (534 samples, 0.14%)')" onmouseout="c()" />
<rect x="1017.7" y="497" width="0.1" height="15.0" fill="rgb(218,46,34)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="750.7" y="449" width="0.1" height="15.0" fill="rgb(220,110,12)" rx="2" ry="2" onmouseover="s('unix`membar_enter (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="1157.9" y="609" width="5.3" height="15.0" fill="rgb(242,180,37)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_rx (1721 samples, 0.45%)')" onmouseout="c()" />
<rect x="755.5" y="513" width="0.3" height="15.0" fill="rgb(239,45,39)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="980.1" y="609" width="2.4" height="15.0" fill="rgb(244,79,46)" rx="2" ry="2" onmouseover="s('genunix`cv_block (765 samples, 0.20%)')" onmouseout="c()" />
<rect x="56.2" y="689" width="6.6" height="15.0" fill="rgb(229,41,37)" rx="2" ry="2" onmouseover="s('genunix`syscall_exit (2146 samples, 0.56%)')" onmouseout="c()" />
<rect x="135.9" y="561" width="2.6" height="15.0" fill="rgb(211,120,43)" rx="2" ry="2" onmouseover="s('sockfs`socopyoutuio (838 samples, 0.22%)')" onmouseout="c()" />
<rect x="755.1" y="513" width="0.3" height="15.0" fill="rgb(234,75,15)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_long_fanout (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="13.5" y="657" width="0.2" height="15.0" fill="rgb(228,227,42)" rx="2" ry="2" onmouseover="s('genunix`as_fault (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="82.4" y="417" width="2.3" height="15.0" fill="rgb(221,72,6)" rx="2" ry="2" onmouseover="s('mac`mac_tx (741 samples, 0.19%)')" onmouseout="c()" />
<rect x="1016.2" y="609" width="0.1" height="15.0" fill="rgb(220,200,54)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="1067.6" y="433" width="0.1" height="15.0" fill="rgb(212,71,15)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1018.5" y="641" width="0.1" height="15.0" fill="rgb(232,178,40)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="1078.7" y="513" width="0.2" height="15.0" fill="rgb(243,71,24)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_eof (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="299.3" y="657" width="0.7" height="15.0" fill="rgb(247,172,44)" rx="2" ry="2" onmouseover="s('genunix`fop_rwlock (234 samples, 0.06%)')" onmouseout="c()" />
<rect x="1070.5" y="497" width="0.1" height="15.0" fill="rgb(216,3,25)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.7" y="417" width="4.0" height="15.0" fill="rgb(220,91,52)" rx="2" ry="2" onmouseover="s('unix`xc_common (1282 samples, 0.34%)')" onmouseout="c()" />
<rect x="215.9" y="625" width="1.2" height="15.0" fill="rgb(248,139,12)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (373 samples, 0.10%)')" onmouseout="c()" />
<rect x="1185.9" y="625" width="0.6" height="15.0" fill="rgb(240,107,23)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (187 samples, 0.05%)')" onmouseout="c()" />
<rect x="41.0" y="545" width="0.3" height="15.0" fill="rgb(228,157,0)" rx="2" ry="2" onmouseover="s('unix`hment_alloc (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="1160.2" y="465" width="2.6" height="15.0" fill="rgb(212,178,0)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (839 samples, 0.22%)')" onmouseout="c()" />
<rect x="534.4" y="433" width="0.1" height="15.0" fill="rgb(206,215,10)" rx="2" ry="2" onmouseover="s('ip`ire_find_best_route (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="988.3" y="561" width="0.1" height="15.0" fill="rgb(230,219,6)" rx="2" ry="2" onmouseover="s('unix`disp_getbest (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1043.9" y="497" width="0.1" height="15.0" fill="rgb(233,141,4)" rx="2" ry="2" onmouseover="s('genunix`turnstile_lookup (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="541.8" y="609" width="0.3" height="15.0" fill="rgb(214,165,37)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_mp (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="1033.5" y="657" width="2.5" height="15.0" fill="rgb(228,123,39)" rx="2" ry="2" onmouseover="s('unix`swtch (799 samples, 0.21%)')" onmouseout="c()" />
<rect x="198.5" y="577" width="0.6" height="15.0" fill="rgb(226,128,40)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (195 samples, 0.05%)')" onmouseout="c()" />
<rect x="1043.9" y="481" width="0.1" height="15.0" fill="rgb(208,121,15)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1028.9" y="593" width="2.8" height="15.0" fill="rgb(247,3,40)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (890 samples, 0.23%)')" onmouseout="c()" />
<rect x="216.7" y="609" width="0.4" height="15.0" fill="rgb(233,25,10)" rx="2" ry="2" onmouseover="s('unix`setfrontdq (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="328.0" y="465" width="1.1" height="15.0" fill="rgb(247,22,24)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (348 samples, 0.09%)')" onmouseout="c()" />
<rect x="609.7" y="625" width="0.7" height="15.0" fill="rgb(211,150,28)" rx="2" ry="2" onmouseover="s('unix`bcopy (244 samples, 0.06%)')" onmouseout="c()" />
<rect x="53.4" y="673" width="0.1" height="15.0" fill="rgb(209,200,34)" rx="2" ry="2" onmouseover="s('unix`splr (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="1154.0" y="609" width="0.1" height="15.0" fill="rgb(207,56,9)" rx="2" ry="2" onmouseover="s('mac_ether`mac_ether_header_info (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="58.2" y="657" width="0.5" height="15.0" fill="rgb(218,6,22)" rx="2" ry="2" onmouseover="s('genunix`audit_getstate (162 samples, 0.04%)')" onmouseout="c()" />
<rect x="1014.1" y="657" width="0.2" height="15.0" fill="rgb(221,42,33)" rx="2" ry="2" onmouseover="s('genunix`cv_block (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="329.1" y="417" width="0.2" height="15.0" fill="rgb(217,196,25)" rx="2" ry="2" onmouseover="s('ipf`fr_check (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="744.8" y="305" width="0.2" height="15.0" fill="rgb(218,186,47)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="26.5" y="609" width="8.6" height="15.0" fill="rgb(215,8,6)" rx="2" ry="2" onmouseover="s('genunix`anon_zero (2783 samples, 0.73%)')" onmouseout="c()" />
<rect x="774.8" y="513" width="7.5" height="15.0" fill="rgb(252,109,50)" rx="2" ry="2" onmouseover="s('unix`lock_set_spin (2430 samples, 0.64%)')" onmouseout="c()" />
<rect x="958.7" y="529" width="0.4" height="15.0" fill="rgb(220,112,25)" rx="2" ry="2" onmouseover="s('genunix`segvn_faultpage (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="754.6" y="417" width="0.1" height="15.0" fill="rgb(244,114,28)" rx="2" ry="2" onmouseover="s('unix`bitset_in_set (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="1082.3" y="497" width="62.7" height="15.0" fill="rgb(206,50,16)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (20243 samples, 5.32%)')" onmouseout="c()" />
<text text-anchor="" x="1085.30477170742" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_attr_connect (20243 samples, 5.32%)')" onmouseout="c()" >ip`ip_a..</text>
<rect x="942.5" y="577" width="0.2" height="15.0" fill="rgb(225,150,11)" rx="2" ry="2" onmouseover="s('unix`page_create_va (86 samples, 0.02%)')" onmouseout="c()" />
<rect x="615.5" y="625" width="0.2" height="15.0" fill="rgb(251,60,32)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_put_free_list (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="654.5" y="289" width="0.5" height="15.0" fill="rgb(213,207,34)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (153 samples, 0.04%)')" onmouseout="c()" />
<rect x="949.3" y="689" width="0.1" height="15.0" fill="rgb(207,187,45)" rx="2" ry="2" onmouseover="s('genunix`syslwp_park (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="579.2" y="625" width="0.1" height="15.0" fill="rgb(239,87,8)" rx="2" ry="2" onmouseover="s('genunix`fd_find (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="786.1" y="609" width="0.6" height="15.0" fill="rgb(227,150,44)" rx="2" ry="2" onmouseover="s('genunix`relvm (183 samples, 0.05%)')" onmouseout="c()" />
<rect x="976.8" y="641" width="0.1" height="15.0" fill="rgb(226,21,36)" rx="2" ry="2" onmouseover="s('portfs`port_bind_pollhead (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.8" y="641" width="0.6" height="15.0" fill="rgb(236,111,7)" rx="2" ry="2" onmouseover="s('zfs`zio_notify_parent (200 samples, 0.05%)')" onmouseout="c()" />
<rect x="1163.3" y="625" width="0.5" height="15.0" fill="rgb(241,215,8)" rx="2" ry="2" onmouseover="s('mac`mac_soft_ring_worker_wakeup (158 samples, 0.04%)')" onmouseout="c()" />
<rect x="339.1" y="657" width="0.2" height="15.0" fill="rgb(214,145,27)" rx="2" ry="2" onmouseover="s('genunix`nbl_need_check (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="32.5" y="513" width="0.4" height="15.0" fill="rgb(223,114,16)" rx="2" ry="2" onmouseover="s('unix`page_get_mnode_freelist (148 samples, 0.04%)')" onmouseout="c()" />
<rect x="155.3" y="561" width="0.1" height="15.0" fill="rgb(206,132,39)" rx="2" ry="2" onmouseover="s('genunix`as_free (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="207.9" y="625" width="0.4" height="15.0" fill="rgb(218,29,51)" rx="2" ry="2" onmouseover="s('genunix`avl_numnodes (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="1080.0" y="545" width="65.6" height="15.0" fill="rgb(224,142,0)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (21179 samples, 5.56%)')" onmouseout="c()" />
<text text-anchor="" x="1083.02749121043" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_input_listener (21179 samples, 5.56%)')" onmouseout="c()" >ip`tcp_..</text>
<rect x="82.0" y="449" width="4.1" height="15.0" fill="rgb(247,195,44)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (1337 samples, 0.35%)')" onmouseout="c()" />
<rect x="37.8" y="577" width="0.3" height="15.0" fill="rgb(218,156,36)" rx="2" ry="2" onmouseover="s('unix`hati_pte_map (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="759.9" y="641" width="0.6" height="15.0" fill="rgb(248,223,54)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (189 samples, 0.05%)')" onmouseout="c()" />
<rect x="1042.6" y="465" width="0.2" height="15.0" fill="rgb(230,136,10)" rx="2" ry="2" onmouseover="s('unix`disp (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="786.1" y="577" width="0.6" height="15.0" fill="rgb(248,146,52)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (172 samples, 0.05%)')" onmouseout="c()" />
<rect x="63.7" y="673" width="0.6" height="15.0" fill="rgb(220,172,0)" rx="2" ry="2" onmouseover="s('unix`dispatch_hilevel (180 samples, 0.05%)')" onmouseout="c()" />
<rect x="95.1" y="609" width="0.5" height="15.0" fill="rgb(210,226,32)" rx="2" ry="2" onmouseover="s('genunix`fop_inactive (161 samples, 0.04%)')" onmouseout="c()" />
<rect x="55.3" y="689" width="0.6" height="15.0" fill="rgb(233,207,50)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (180 samples, 0.05%)')" onmouseout="c()" />
<rect x="154.5" y="625" width="0.4" height="15.0" fill="rgb(217,17,50)" rx="2" ry="2" onmouseover="s('genunix`exec_args (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="95.8" y="641" width="0.8" height="15.0" fill="rgb(245,106,53)" rx="2" ry="2" onmouseover="s('genunix`port_close_fd (285 samples, 0.07%)')" onmouseout="c()" />
<rect x="753.1" y="481" width="1.9" height="15.0" fill="rgb(241,202,17)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (605 samples, 0.16%)')" onmouseout="c()" />
<rect x="199.5" y="577" width="1.4" height="15.0" fill="rgb(231,128,1)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (446 samples, 0.12%)')" onmouseout="c()" />
<rect x="646.1" y="385" width="0.3" height="15.0" fill="rgb(249,153,37)" rx="2" ry="2" onmouseover="s('ip`ipcl_conn_destroy (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="1079.3" y="465" width="0.4" height="15.0" fill="rgb(224,173,41)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="336.3" y="609" width="0.2" height="15.0" fill="rgb(234,28,36)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_try_assign (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="557.9" y="593" width="18.6" height="15.0" fill="rgb(223,110,14)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (6011 samples, 1.58%)')" onmouseout="c()" />
<rect x="759.1" y="561" width="0.2" height="15.0" fill="rgb(205,183,50)" rx="2" ry="2" onmouseover="s('unix`rw_exit (75 samples, 0.02%)')" onmouseout="c()" />
<rect x="613.7" y="577" width="0.1" height="15.0" fill="rgb(207,190,52)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_coredma_unbindhdl (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="336.8" y="609" width="0.4" height="15.0" fill="rgb(247,68,9)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_count_write (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="647.0" y="353" width="0.3" height="15.0" fill="rgb(231,55,51)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_end (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="600.5" y="609" width="0.1" height="15.0" fill="rgb(212,159,52)" rx="2" ry="2" onmouseover="s('bnx`bnx_recv_ring_recv (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="950.0" y="449" width="0.1" height="15.0" fill="rgb(245,75,33)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="22.3" y="577" width="0.4" height="15.0" fill="rgb(219,167,18)" rx="2" ry="2" onmouseover="s('unix`page_lookup_create (127 samples, 0.03%)')" onmouseout="c()" />
<rect x="1073.5" y="481" width="2.2" height="15.0" fill="rgb(214,214,51)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (692 samples, 0.18%)')" onmouseout="c()" />
<rect x="744.6" y="369" width="0.4" height="15.0" fill="rgb(221,196,49)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="1012.6" y="641" width="0.2" height="15.0" fill="rgb(251,179,14)" rx="2" ry="2" onmouseover="s('zfs`metaslab_alloc (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="674.5" y="145" width="0.2" height="15.0" fill="rgb(211,119,53)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_coredma_bindhdl (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="935.9" y="673" width="0.7" height="15.0" fill="rgb(252,6,45)" rx="2" ry="2" onmouseover="s('genunix`getf (251 samples, 0.07%)')" onmouseout="c()" />
<rect x="45.2" y="673" width="0.2" height="15.0" fill="rgb(206,180,19)" rx="2" ry="2" onmouseover="s('genunix`au_zone_getstate (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="82.5" y="401" width="0.7" height="15.0" fill="rgb(226,181,23)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (224 samples, 0.06%)')" onmouseout="c()" />
<rect x="654.7" y="193" width="0.1" height="15.0" fill="rgb(226,45,23)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="96.0" y="593" width="0.2" height="15.0" fill="rgb(243,151,46)" rx="2" ry="2" onmouseover="s('genunix`port_free_event_local (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="1163.0" y="561" width="0.1" height="15.0" fill="rgb(245,199,5)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_dstonly_v4 (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="654.6" y="273" width="0.3" height="15.0" fill="rgb(218,228,30)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="197.6" y="689" width="0.4" height="15.0" fill="rgb(238,130,0)" rx="2" ry="2" onmouseover="s('genunix`smmap32 (146 samples, 0.04%)')" onmouseout="c()" />
<rect x="71.7" y="641" width="0.4" height="15.0" fill="rgb(211,136,9)" rx="2" ry="2" onmouseover="s('unix`swtch (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="165.1" y="513" width="0.8" height="15.0" fill="rgb(219,47,15)" rx="2" ry="2" onmouseover="s('unix`segkmem_alloc_vn (267 samples, 0.07%)')" onmouseout="c()" />
<rect x="43.4" y="673" width="1.8" height="15.0" fill="rgb(245,143,31)" rx="2" ry="2" onmouseover="s('c2audit`audit_start (577 samples, 0.15%)')" onmouseout="c()" />
<rect x="584.6" y="625" width="0.1" height="15.0" fill="rgb(251,78,39)" rx="2" ry="2" onmouseover="s('genunix`vn_reinit (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="619.2" y="561" width="0.2" height="15.0" fill="rgb(250,58,6)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_drain (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1006.4" y="657" width="4.3" height="15.0" fill="rgb(219,167,9)" rx="2" ry="2" onmouseover="s('genunix`callout_expire (1374 samples, 0.36%)')" onmouseout="c()" />
<rect x="94.0" y="417" width="0.1" height="15.0" fill="rgb(254,41,47)" rx="2" ry="2" onmouseover="s('genunix`callout_list_get (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="621.4" y="529" width="0.2" height="15.0" fill="rgb(236,105,36)" rx="2" ry="2" onmouseover="s('mac`mac_client_check_flow_vid (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="313.6" y="545" width="0.5" height="15.0" fill="rgb(251,142,20)" rx="2" ry="2" onmouseover="s('genunix`dupb (149 samples, 0.04%)')" onmouseout="c()" />
<rect x="751.1" y="449" width="0.1" height="15.0" fill="rgb(221,207,17)" rx="2" ry="2" onmouseover="s('mac_ether`mac_ether_header_info (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="314.1" y="545" width="10.2" height="15.0" fill="rgb(210,121,7)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (3302 samples, 0.87%)')" onmouseout="c()" />
<rect x="96.6" y="641" width="0.1" height="15.0" fill="rgb(226,182,6)" rx="2" ry="2" onmouseover="s('genunix`setf (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1158.8" y="513" width="0.4" height="15.0" fill="rgb(208,120,9)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="1074.4" y="385" width="0.5" height="15.0" fill="rgb(238,184,23)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (173 samples, 0.05%)')" onmouseout="c()" />
<rect x="669.6" y="305" width="0.1" height="15.0" fill="rgb(224,75,27)" rx="2" ry="2" onmouseover="s('unix`bcopy (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="153.0" y="577" width="0.1" height="15.0" fill="rgb(213,88,33)" rx="2" ry="2" onmouseover="s('unix`trap (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.4" y="337" width="0.4" height="15.0" fill="rgb(215,225,40)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_dma_bindhdl (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="1145.4" y="529" width="0.1" height="15.0" fill="rgb(227,191,46)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="341.8" y="625" width="0.4" height="15.0" fill="rgb(240,152,30)" rx="2" ry="2" onmouseover="s('genunix`avl_find (139 samples, 0.04%)')" onmouseout="c()" />
<rect x="537.2" y="529" width="1.3" height="15.0" fill="rgb(230,145,18)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (424 samples, 0.11%)')" onmouseout="c()" />
<rect x="925.2" y="561" width="5.6" height="15.0" fill="rgb(206,66,46)" rx="2" ry="2" onmouseover="s('unix`do_splx (1815 samples, 0.48%)')" onmouseout="c()" />
<rect x="63.4" y="689" width="0.3" height="15.0" fill="rgb(254,84,5)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="1177.3" y="609" width="3.6" height="15.0" fill="rgb(229,61,28)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (1139 samples, 0.30%)')" onmouseout="c()" />
<rect x="179.0" y="561" width="1.5" height="15.0" fill="rgb(218,210,54)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (486 samples, 0.13%)')" onmouseout="c()" />
<rect x="328.4" y="353" width="0.4" height="15.0" fill="rgb(217,34,50)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_addr_bind_handle (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="1075.2" y="449" width="0.3" height="15.0" fill="rgb(231,87,10)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="11.9" y="705" width="0.2" height="15.0" fill="rgb(250,186,39)" rx="2" ry="2" onmouseover="s('portfs`portfs32 (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="1012.2" y="641" width="0.2" height="15.0" fill="rgb(243,130,43)" rx="2" ry="2" onmouseover="s('zfs`zio_checksum_compute (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="38.3" y="609" width="1.1" height="15.0" fill="rgb(237,141,12)" rx="2" ry="2" onmouseover="s('unix`hat_memload_region (359 samples, 0.09%)')" onmouseout="c()" />
<rect x="1144.6" y="433" width="0.3" height="15.0" fill="rgb(212,9,41)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_v4 (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="667.7" y="209" width="0.1" height="15.0" fill="rgb(216,197,32)" rx="2" ry="2" onmouseover="s('unix`bcopy (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="949.8" y="593" width="0.4" height="15.0" fill="rgb(231,19,25)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="938.9" y="641" width="1.9" height="15.0" fill="rgb(213,5,46)" rx="2" ry="2" onmouseover="s('genunix`cstatat_getvp (612 samples, 0.16%)')" onmouseout="c()" />
<rect x="540.6" y="497" width="0.4" height="15.0" fill="rgb(206,3,43)" rx="2" ry="2" onmouseover="s('ipf`fr_check (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="680.3" y="273" width="0.4" height="15.0" fill="rgb(245,91,36)" rx="2" ry="2" onmouseover="s('mac`mac_tx (132 samples, 0.03%)')" onmouseout="c()" />
<rect x="774.7" y="561" width="7.9" height="15.0" fill="rgb(232,20,7)" rx="2" ry="2" onmouseover="s('unix`setbackdq (2563 samples, 0.67%)')" onmouseout="c()" />
<rect x="310.0" y="545" width="1.2" height="15.0" fill="rgb(254,176,50)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (375 samples, 0.10%)')" onmouseout="c()" />
<rect x="317.7" y="433" width="0.4" height="15.0" fill="rgb(231,224,28)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="63.2" y="657" width="0.2" height="15.0" fill="rgb(251,207,46)" rx="2" ry="2" onmouseover="s('unix`tsc_read (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="554.1" y="545" width="0.2" height="15.0" fill="rgb(215,155,21)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_send_icmp_v4 (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="1071.0" y="529" width="0.3" height="15.0" fill="rgb(224,168,28)" rx="2" ry="2" onmouseover="s('ip`tcp_clean_death (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="969.4" y="689" width="1.3" height="15.0" fill="rgb(239,185,17)" rx="2" ry="2" onmouseover="s('genunix`syscall_entry (423 samples, 0.11%)')" onmouseout="c()" />
<rect x="826.3" y="689" width="112.2" height="15.0" fill="rgb(225,151,27)" rx="2" ry="2" onmouseover="s('genunix`ioctl (36216 samples, 9.51%)')" onmouseout="c()" />
<text text-anchor="" x="829.298165414639" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`ioctl (36216 samples, 9.51%)')" onmouseout="c()" >genunix`ioctl</text>
<rect x="1033.0" y="657" width="0.1" height="15.0" fill="rgb(221,121,31)" rx="2" ry="2" onmouseover="s('genunix`thread_lock (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="942.6" y="561" width="0.1" height="15.0" fill="rgb(247,81,3)" rx="2" ry="2" onmouseover="s('unix`page_get_freelist (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="319.7" y="369" width="0.1" height="15.0" fill="rgb(210,121,28)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_dma_bindhdl (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="585.3" y="689" width="2.6" height="15.0" fill="rgb(235,19,8)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (867 samples, 0.23%)')" onmouseout="c()" />
<rect x="583.0" y="657" width="1.3" height="15.0" fill="rgb(212,201,31)" rx="2" ry="2" onmouseover="s('sockfs`so_init (451 samples, 0.12%)')" onmouseout="c()" />
<rect x="674.1" y="241" width="1.1" height="15.0" fill="rgb(219,72,33)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (328 samples, 0.09%)')" onmouseout="c()" />
<rect x="762.7" y="657" width="0.3" height="15.0" fill="rgb(246,19,52)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (75 samples, 0.02%)')" onmouseout="c()" />
<rect x="945.5" y="641" width="0.2" height="15.0" fill="rgb(218,209,26)" rx="2" ry="2" onmouseover="s('genunix`relvm (80 samples, 0.02%)')" onmouseout="c()" />
<rect x="741.9" y="305" width="0.5" height="15.0" fill="rgb(216,50,6)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_v4 (162 samples, 0.04%)')" onmouseout="c()" />
<rect x="1158.6" y="545" width="4.4" height="15.0" fill="rgb(231,6,47)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (1407 samples, 0.37%)')" onmouseout="c()" />
<rect x="310.1" y="513" width="1.1" height="15.0" fill="rgb(220,180,34)" rx="2" ry="2" onmouseover="s('ip`conn_connect (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="333.5" y="561" width="0.5" height="15.0" fill="rgb(218,121,17)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (160 samples, 0.04%)')" onmouseout="c()" />
<rect x="654.3" y="353" width="0.7" height="15.0" fill="rgb(243,93,30)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (231 samples, 0.06%)')" onmouseout="c()" />
<rect x="969.8" y="657" width="0.6" height="15.0" fill="rgb(205,101,52)" rx="2" ry="2" onmouseover="s('genunix`copyin_nowatch (192 samples, 0.05%)')" onmouseout="c()" />
<rect x="198.3" y="593" width="0.8" height="15.0" fill="rgb(209,101,1)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (264 samples, 0.07%)')" onmouseout="c()" />
<rect x="1072.4" y="481" width="0.2" height="15.0" fill="rgb(227,136,48)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1045.1" y="529" width="0.2" height="15.0" fill="rgb(218,140,30)" rx="2" ry="2" onmouseover="s('unix`rw_enter (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="319.6" y="401" width="0.2" height="15.0" fill="rgb(211,2,41)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_bind (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="785.9" y="625" width="0.2" height="15.0" fill="rgb(223,217,52)" rx="2" ry="2" onmouseover="s('elfexec`mapelfexec (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="968.7" y="561" width="0.4" height="15.0" fill="rgb(241,47,46)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (117 samples, 0.03%)')" onmouseout="c()" />
<rect x="59.7" y="657" width="1.3" height="15.0" fill="rgb(210,228,36)" rx="2" ry="2" onmouseover="s('genunix`thread_lock (422 samples, 0.11%)')" onmouseout="c()" />
<rect x="663.6" y="321" width="0.3" height="15.0" fill="rgb(240,165,46)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_pkt (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="31.3" y="577" width="0.1" height="15.0" fill="rgb(205,187,52)" rx="2" ry="2" onmouseover="s('genunix`swapfs_getvp (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.3" y="401" width="0.7" height="15.0" fill="rgb(205,226,46)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (233 samples, 0.06%)')" onmouseout="c()" />
<rect x="942.4" y="609" width="0.4" height="15.0" fill="rgb(250,156,15)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage_ra (125 samples, 0.03%)')" onmouseout="c()" />
<rect x="42.8" y="705" width="2.8" height="15.0" fill="rgb(219,146,25)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800c55 (902 samples, 0.24%)')" onmouseout="c()" />
<rect x="1160.2" y="497" width="2.6" height="15.0" fill="rgb(210,8,50)" rx="2" ry="2" onmouseover="s('ip`tcp_set_destination (841 samples, 0.22%)')" onmouseout="c()" />
<rect x="602.2" y="641" width="0.2" height="15.0" fill="rgb(232,33,5)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1081.4" y="385" width="0.2" height="15.0" fill="rgb(217,57,21)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="654.7" y="241" width="0.2" height="15.0" fill="rgb(210,64,42)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1146.0" y="561" width="4.5" height="15.0" fill="rgb(227,3,8)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_listeners_reset (1444 samples, 0.38%)')" onmouseout="c()" />
<rect x="1017.6" y="545" width="0.2" height="15.0" fill="rgb(239,144,6)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="1077.6" y="385" width="0.4" height="15.0" fill="rgb(220,197,35)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (144 samples, 0.04%)')" onmouseout="c()" />
<rect x="554.1" y="657" width="0.3" height="15.0" fill="rgb(212,216,30)" rx="2" ry="2" onmouseover="s('sockfs`sendit (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="534.7" y="529" width="0.4" height="15.0" fill="rgb(213,123,48)" rx="2" ry="2" onmouseover="s('ip`ip_select_source_v4 (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="1017.5" y="609" width="0.5" height="15.0" fill="rgb(244,90,36)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (162 samples, 0.04%)')" onmouseout="c()" />
<rect x="165.1" y="497" width="0.8" height="15.0" fill="rgb(211,22,22)" rx="2" ry="2" onmouseover="s('unix`segkmem_xalloc (264 samples, 0.07%)')" onmouseout="c()" />
<rect x="321.1" y="417" width="0.2" height="15.0" fill="rgb(207,133,47)" rx="2" ry="2" onmouseover="s('mac`mac_flow_lookup (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="785.8" y="641" width="1.0" height="15.0" fill="rgb(212,33,6)" rx="2" ry="2" onmouseover="s('elfexec`elf32exec (330 samples, 0.09%)')" onmouseout="c()" />
<rect x="958.0" y="657" width="5.2" height="15.0" fill="rgb(239,2,32)" rx="2" ry="2" onmouseover="s('unix`mmapobj_map_interpret (1700 samples, 0.45%)')" onmouseout="c()" />
<rect x="194.4" y="577" width="1.0" height="15.0" fill="rgb(221,71,12)" rx="2" ry="2" onmouseover="s('genunix`segvn_free (336 samples, 0.09%)')" onmouseout="c()" />
<rect x="648.6" y="305" width="5.3" height="15.0" fill="rgb(253,21,49)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (1711 samples, 0.45%)')" onmouseout="c()" />
<rect x="81.8" y="465" width="4.4" height="15.0" fill="rgb(207,188,30)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (1430 samples, 0.38%)')" onmouseout="c()" />
<rect x="774.2" y="545" width="0.2" height="15.0" fill="rgb(235,3,42)" rx="2" ry="2" onmouseover="s('unix`do_splx (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="1158.0" y="577" width="5.1" height="15.0" fill="rgb(253,16,6)" rx="2" ry="2" onmouseover="s('ip`ill_input_short_v4 (1653 samples, 0.43%)')" onmouseout="c()" />
<rect x="1158.9" y="433" width="0.2" height="15.0" fill="rgb(228,132,5)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="600.5" y="545" width="0.1" height="15.0" fill="rgb(215,169,27)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_drain (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="753.8" y="449" width="1.1" height="15.0" fill="rgb(251,182,46)" rx="2" ry="2" onmouseover="s('unix`setbackdq (356 samples, 0.09%)')" onmouseout="c()" />
<rect x="615.0" y="593" width="0.4" height="15.0" fill="rgb(254,111,44)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="620.0" y="545" width="137.6" height="15.0" fill="rgb(214,107,10)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_drain (44407 samples, 11.66%)')" onmouseout="c()" />
<text text-anchor="" x="623.007535795026" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_drain (44407 samples, 11.66%)')" onmouseout="c()" >mac`mac_rx_srs_d..</text>
<rect x="768.8" y="593" width="1.4" height="15.0" fill="rgb(205,20,13)" rx="2" ry="2" onmouseover="s('unix`dtrace_sync (427 samples, 0.11%)')" onmouseout="c()" />
<rect x="217.6" y="673" width="0.2" height="15.0" fill="rgb(243,159,6)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1076.5" y="513" width="1.9" height="15.0" fill="rgb(224,56,36)" rx="2" ry="2" onmouseover="s('ip`tcp_send (609 samples, 0.16%)')" onmouseout="c()" />
<rect x="554.1" y="513" width="0.2" height="15.0" fill="rgb(226,126,37)" rx="2" ry="2" onmouseover="s('ip`icmp_pkt (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.9" y="593" width="0.8" height="15.0" fill="rgb(208,62,51)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (256 samples, 0.07%)')" onmouseout="c()" />
<rect x="578.5" y="625" width="0.3" height="15.0" fill="rgb(252,189,22)" rx="2" ry="2" onmouseover="s('ip`tcp_opt_set (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="1032.5" y="673" width="3.5" height="15.0" fill="rgb(235,152,20)" rx="2" ry="2" onmouseover="s('genunix`cv_wait (1121 samples, 0.29%)')" onmouseout="c()" />
<rect x="555.9" y="561" width="1.4" height="15.0" fill="rgb(245,124,23)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (464 samples, 0.12%)')" onmouseout="c()" />
<rect x="1081.7" y="433" width="0.1" height="15.0" fill="rgb(246,85,4)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="1077.2" y="401" width="0.3" height="15.0" fill="rgb(223,61,20)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="942.5" y="593" width="0.3" height="15.0" fill="rgb(223,195,53)" rx="2" ry="2" onmouseover="s('genunix`pvn_read_kluster (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="925.0" y="609" width="5.8" height="15.0" fill="rgb(239,225,28)" rx="2" ry="2" onmouseover="s('unix`dtrace_xcall (1852 samples, 0.49%)')" onmouseout="c()" />
<rect x="577.8" y="609" width="0.5" height="15.0" fill="rgb(219,170,33)" rx="2" ry="2" onmouseover="s('genunix`cv_wait (156 samples, 0.04%)')" onmouseout="c()" />
<rect x="953.4" y="657" width="0.4" height="15.0" fill="rgb(220,42,8)" rx="2" ry="2" onmouseover="s('genunix`as_dup (124 samples, 0.03%)')" onmouseout="c()" />
<rect x="945.5" y="609" width="0.2" height="15.0" fill="rgb(249,120,52)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="578.9" y="689" width="6.3" height="15.0" fill="rgb(240,2,40)" rx="2" ry="2" onmouseover="s('sockfs`so_socket (2032 samples, 0.53%)')" onmouseout="c()" />
<rect x="957.1" y="577" width="0.4" height="15.0" fill="rgb(205,186,2)" rx="2" ry="2" onmouseover="s('unix`page_create_va (150 samples, 0.04%)')" onmouseout="c()" />
<rect x="1052.0" y="481" width="0.2" height="15.0" fill="rgb(216,103,20)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="1018.2" y="545" width="0.1" height="15.0" fill="rgb(235,41,52)" rx="2" ry="2" onmouseover="s('mac`mac_tx (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="985.2" y="609" width="4.9" height="15.0" fill="rgb(245,217,27)" rx="2" ry="2" onmouseover="s('unix`swtch (1575 samples, 0.41%)')" onmouseout="c()" />
<rect x="965.2" y="545" width="0.1" height="15.0" fill="rgb(238,84,53)" rx="2" ry="2" onmouseover="s('lofs`lo_lookup (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="773.9" y="593" width="0.3" height="15.0" fill="rgb(236,21,20)" rx="2" ry="2" onmouseover="s('genunix`callout_downheap (80 samples, 0.02%)')" onmouseout="c()" />
<rect x="1012.6" y="593" width="0.1" height="15.0" fill="rgb(211,159,48)" rx="2" ry="2" onmouseover="s('unix`mutex_vector_enter (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.5" y="305" width="0.3" height="15.0" fill="rgb(238,172,3)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_get_sgl (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="660.2" y="353" width="0.5" height="15.0" fill="rgb(254,184,44)" rx="2" ry="2" onmouseover="s('ip`tcp_close_detached (136 samples, 0.04%)')" onmouseout="c()" />
<rect x="79.3" y="465" width="0.7" height="15.0" fill="rgb(254,206,11)" rx="2" ry="2" onmouseover="s('ip`conn_connect (239 samples, 0.06%)')" onmouseout="c()" />
<rect x="675.9" y="321" width="0.3" height="15.0" fill="rgb(230,16,52)" rx="2" ry="2" onmouseover="s('genunix`untimeout_generic (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="53.5" y="689" width="0.2" height="15.0" fill="rgb(236,155,36)" rx="2" ry="2" onmouseover="s('genunix`thread_lock (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="533.2" y="449" width="0.1" height="15.0" fill="rgb(229,106,15)" rx="2" ry="2" onmouseover="s('FSS`fss_preempt (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="181.2" y="657" width="0.2" height="15.0" fill="rgb(229,202,23)" rx="2" ry="2" onmouseover="s('genunix`cv_waituntil_sig (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="993.6" y="609" width="0.1" height="15.0" fill="rgb(253,183,21)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="77.7" y="609" width="17.2" height="15.0" fill="rgb(226,62,31)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_close (5543 samples, 1.46%)')" onmouseout="c()" />
<rect x="1163.4" y="593" width="0.4" height="15.0" fill="rgb(243,36,16)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (122 samples, 0.03%)')" onmouseout="c()" />
<rect x="664.2" y="321" width="5.6" height="15.0" fill="rgb(212,86,32)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (1801 samples, 0.47%)')" onmouseout="c()" />
<rect x="1042.6" y="449" width="0.1" height="15.0" fill="rgb(206,181,1)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="946.1" y="577" width="0.6" height="15.0" fill="rgb(228,170,40)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (189 samples, 0.05%)')" onmouseout="c()" />
<rect x="217.4" y="641" width="0.1" height="15.0" fill="rgb(227,106,52)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="641.0" y="385" width="0.3" height="15.0" fill="rgb(227,37,50)" rx="2" ry="2" onmouseover="s('unix`rw_enter (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="967.6" y="577" width="0.4" height="15.0" fill="rgb(211,135,38)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (115 samples, 0.03%)')" onmouseout="c()" />
<rect x="205.0" y="545" width="0.2" height="15.0" fill="rgb(210,178,49)" rx="2" ry="2" onmouseover="s('ufs`ufs_lookup (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="556.4" y="513" width="0.8" height="15.0" fill="rgb(231,68,9)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (268 samples, 0.07%)')" onmouseout="c()" />
<rect x="174.9" y="609" width="0.2" height="15.0" fill="rgb(222,228,23)" rx="2" ry="2" onmouseover="s('lofs`lo_access (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="577.3" y="657" width="1.6" height="15.0" fill="rgb(250,19,2)" rx="2" ry="2" onmouseover="s('sockfs`so_setsockopt (500 samples, 0.13%)')" onmouseout="c()" />
<rect x="140.2" y="577" width="0.1" height="15.0" fill="rgb(218,125,54)" rx="2" ry="2" onmouseover="s('zfs`dmu_buf_hold_array_by_dnode (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="1003.2" y="673" width="0.2" height="15.0" fill="rgb(207,128,28)" rx="2" ry="2" onmouseover="s('unix`prefetch_page_r (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.1" y="481" width="4.6" height="15.0" fill="rgb(215,70,26)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (1482 samples, 0.39%)')" onmouseout="c()" />
<rect x="75.3" y="689" width="0.1" height="15.0" fill="rgb(218,105,39)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_probe (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.2" y="625" width="0.2" height="15.0" fill="rgb(227,129,7)" rx="2" ry="2" onmouseover="s('zfs`fletcher_4_native (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="973.9" y="657" width="0.2" height="15.0" fill="rgb(221,184,13)" rx="2" ry="2" onmouseover="s('genunix`releasef (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="139.4" y="593" width="0.2" height="15.0" fill="rgb(245,142,0)" rx="2" ry="2" onmouseover="s('genunix`vpm_data_copy (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="1145.5" y="529" width="0.1" height="15.0" fill="rgb(227,221,45)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_mp (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="79.3" y="449" width="0.7" height="15.0" fill="rgb(238,147,36)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (238 samples, 0.06%)')" onmouseout="c()" />
<rect x="159.4" y="609" width="0.2" height="15.0" fill="rgb(222,99,52)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_ioctl_helper (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="1186.1" y="609" width="0.4" height="15.0" fill="rgb(237,137,8)" rx="2" ry="2" onmouseover="s('unix`splr (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="338.1" y="657" width="0.2" height="15.0" fill="rgb(224,7,21)" rx="2" ry="2" onmouseover="s('genunix`fs_rwunlock (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="156.6" y="689" width="0.5" height="15.0" fill="rgb(216,20,7)" rx="2" ry="2" onmouseover="s('genunix`fop_ioctl (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="540.0" y="449" width="0.1" height="15.0" fill="rgb(233,60,26)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="214.3" y="593" width="0.6" height="15.0" fill="rgb(217,17,4)" rx="2" ry="2" onmouseover="s('unix`disp (196 samples, 0.05%)')" onmouseout="c()" />
<rect x="152.6" y="593" width="0.3" height="15.0" fill="rgb(236,179,3)" rx="2" ry="2" onmouseover="s('genunix`as_fault (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="673.2" y="321" width="2.4" height="15.0" fill="rgb(223,71,0)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (751 samples, 0.20%)')" onmouseout="c()" />
<rect x="1018.5" y="625" width="0.1" height="15.0" fill="rgb(231,88,47)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="976.3" y="641" width="0.1" height="15.0" fill="rgb(251,187,14)" rx="2" ry="2" onmouseover="s('genunix`kmem_zalloc (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="46.6" y="689" width="0.2" height="15.0" fill="rgb(219,66,28)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="609.3" y="625" width="0.2" height="15.0" fill="rgb(226,153,31)" rx="2" ry="2" onmouseover="s('mac`mac_hcksum_set (81 samples, 0.02%)')" onmouseout="c()" />
<rect x="579.0" y="657" width="0.1" height="15.0" fill="rgb(245,70,45)" rx="2" ry="2" onmouseover="s('genunix`audit_falloc (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.6" y="609" width="0.2" height="15.0" fill="rgb(246,106,11)" rx="2" ry="2" onmouseover="s('zfs`metaslab_group_alloc (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="942.9" y="673" width="0.7" height="15.0" fill="rgb(217,52,31)" rx="2" ry="2" onmouseover="s('unix`mmapobj (246 samples, 0.06%)')" onmouseout="c()" />
<rect x="316.4" y="481" width="5.0" height="15.0" fill="rgb(213,200,33)" rx="2" ry="2" onmouseover="s('mac`mac_tx (1599 samples, 0.42%)')" onmouseout="c()" />
<rect x="194.3" y="609" width="3.0" height="15.0" fill="rgb(246,6,30)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (951 samples, 0.25%)')" onmouseout="c()" />
<rect x="945.9" y="673" width="0.8" height="15.0" fill="rgb(221,191,52)" rx="2" ry="2" onmouseover="s('genunix`fstatat (280 samples, 0.07%)')" onmouseout="c()" />
<rect x="155.3" y="513" width="0.1" height="15.0" fill="rgb(229,64,18)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="154.3" y="641" width="0.6" height="15.0" fill="rgb(211,6,39)" rx="2" ry="2" onmouseover="s('elfexec`elfexec (181 samples, 0.05%)')" onmouseout="c()" />
<rect x="56.3" y="673" width="0.1" height="15.0" fill="rgb(240,223,30)" rx="2" ry="2" onmouseover="s('genunix`audit_getstate (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="596.5" y="705" width="186.9" height="15.0" fill="rgb(219,194,30)" rx="2" ry="2" onmouseover="s('unix`switch_sp_and_call (60327 samples, 15.84%)')" onmouseout="c()" />
<text text-anchor="" x="599.46324396283" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`switch_sp_and_call (60327 samples, 15.84%)')" onmouseout="c()" >unix`switch_sp_and_cal..</text>
<rect x="313.8" y="529" width="0.1" height="15.0" fill="rgb(218,24,18)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_alloc (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.7" y="257" width="0.1" height="15.0" fill="rgb(208,211,45)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.0" y="417" width="0.2" height="15.0" fill="rgb(205,51,23)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1080.2" y="529" width="0.3" height="15.0" fill="rgb(225,122,18)" rx="2" ry="2" onmouseover="s('ip`conn_inherit_parent (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="1157.4" y="657" width="6.5" height="15.0" fill="rgb(228,121,16)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_fanout (2073 samples, 0.54%)')" onmouseout="c()" />
<rect x="957.2" y="561" width="0.3" height="15.0" fill="rgb(230,178,34)" rx="2" ry="2" onmouseover="s('unix`page_get_freelist (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="165.0" y="593" width="0.9" height="15.0" fill="rgb(239,59,12)" rx="2" ry="2" onmouseover="s('genunix`kmem_zalloc (277 samples, 0.07%)')" onmouseout="c()" />
<rect x="302.6" y="513" width="0.1" height="15.0" fill="rgb(250,123,48)" rx="2" ry="2" onmouseover="s('FSS`fss_active (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="165.6" y="449" width="0.2" height="15.0" fill="rgb(215,191,23)" rx="2" ry="2" onmouseover="s('unix`page_get_freelist (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="746.3" y="417" width="2.5" height="15.0" fill="rgb(246,134,11)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_simple_v4 (803 samples, 0.21%)')" onmouseout="c()" />
<rect x="1070.6" y="513" width="0.1" height="15.0" fill="rgb(238,90,39)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="84.9" y="401" width="0.9" height="15.0" fill="rgb(235,134,17)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (294 samples, 0.08%)')" onmouseout="c()" />
<rect x="937.6" y="673" width="0.2" height="15.0" fill="rgb(225,205,51)" rx="2" ry="2" onmouseover="s('unix`_sys_rtt_ints_disabled (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="621.0" y="497" width="0.4" height="15.0" fill="rgb(222,210,14)" rx="2" ry="2" onmouseover="s('unix`setbackdq (100 samples, 0.03%)')" onmouseout="c()" />
<rect x="676.3" y="353" width="0.3" height="15.0" fill="rgb(218,153,35)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_eof (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="950.0" y="433" width="0.1" height="15.0" fill="rgb(227,204,16)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="1079.4" y="417" width="0.3" height="15.0" fill="rgb(211,48,20)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="21.8" y="625" width="2.0" height="15.0" fill="rgb(246,176,51)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (666 samples, 0.17%)')" onmouseout="c()" />
<rect x="1012.9" y="561" width="0.2" height="15.0" fill="rgb(244,159,46)" rx="2" ry="2" onmouseover="s('zfs`zio_ready (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="79.2" y="497" width="0.8" height="15.0" fill="rgb(230,200,12)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (252 samples, 0.07%)')" onmouseout="c()" />
<rect x="77.7" y="593" width="0.2" height="15.0" fill="rgb(218,225,19)" rx="2" ry="2" onmouseover="s('genunix`cleanlocks (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="141.0" y="641" width="0.1" height="15.0" fill="rgb(250,76,10)" rx="2" ry="2" onmouseover="s('genunix`fs_rwunlock (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="621.9" y="497" width="0.4" height="15.0" fill="rgb(250,196,54)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="80.1" y="417" width="0.2" height="15.0" fill="rgb(207,143,53)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1007.2" y="577" width="1.0" height="15.0" fill="rgb(227,49,21)" rx="2" ry="2" onmouseover="s('unix`mutex_delay_default (345 samples, 0.09%)')" onmouseout="c()" />
<rect x="21.9" y="609" width="1.8" height="15.0" fill="rgb(231,4,9)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage (591 samples, 0.16%)')" onmouseout="c()" />
<rect x="1047.1" y="561" width="0.1" height="15.0" fill="rgb(236,121,19)" rx="2" ry="2" onmouseover="s('ip`ip_input_cksum_v4 (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="760.8" y="657" width="0.2" height="15.0" fill="rgb(249,5,28)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_recycle_legacy (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="15.2" y="609" width="0.1" height="15.0" fill="rgb(233,1,31)" rx="2" ry="2" onmouseover="s('genunix`as_segcompar (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="958.2" y="641" width="0.1" height="15.0" fill="rgb(226,45,44)" rx="2" ry="2" onmouseover="s('unix`mmapobj_lookup_start_addr (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1145.2" y="497" width="0.1" height="15.0" fill="rgb(239,50,42)" rx="2" ry="2" onmouseover="s('ip`conn_build_hdr_template (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="753.5" y="465" width="1.4" height="15.0" fill="rgb(207,222,17)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (471 samples, 0.12%)')" onmouseout="c()" />
<rect x="993.3" y="641" width="0.6" height="15.0" fill="rgb(238,61,36)" rx="2" ry="2" onmouseover="s('portfs`port_queue_thread (209 samples, 0.05%)')" onmouseout="c()" />
<rect x="66.8" y="673" width="0.2" height="15.0" fill="rgb(251,102,34)" rx="2" ry="2" onmouseover="s('genunix`schedctl_restore (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="311.3" y="513" width="0.2" height="15.0" fill="rgb(230,5,20)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="328.2" y="417" width="0.8" height="15.0" fill="rgb(232,29,45)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (257 samples, 0.07%)')" onmouseout="c()" />
<rect x="160.1" y="513" width="4.7" height="15.0" fill="rgb(214,118,33)" rx="2" ry="2" onmouseover="s('unix`hat_unload (1524 samples, 0.40%)')" onmouseout="c()" />
<rect x="69.4" y="577" width="0.1" height="15.0" fill="rgb(252,214,25)" rx="2" ry="2" onmouseover="s('unix`caps_charge_adjust (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="953.4" y="689" width="0.6" height="15.0" fill="rgb(217,52,3)" rx="2" ry="2" onmouseover="s('genunix`forksys (194 samples, 0.05%)')" onmouseout="c()" />
<rect x="1049.3" y="529" width="0.2" height="15.0" fill="rgb(205,111,43)" rx="2" ry="2" onmouseover="s('ip`squeue_wakeup_conn (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="1081.2" y="449" width="0.4" height="15.0" fill="rgb(249,0,51)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (147 samples, 0.04%)')" onmouseout="c()" />
<rect x="319.5" y="401" width="0.1" height="15.0" fill="rgb(225,203,36)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_get_free_list (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="15.1" y="625" width="0.2" height="15.0" fill="rgb(214,149,4)" rx="2" ry="2" onmouseover="s('genunix`avl_find (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="209.7" y="673" width="0.4" height="15.0" fill="rgb(238,188,21)" rx="2" ry="2" onmouseover="s('genunix`pre_syscall (127 samples, 0.03%)')" onmouseout="c()" />
<rect x="181.2" y="673" width="0.3" height="15.0" fill="rgb(230,96,33)" rx="2" ry="2" onmouseover="s('genunix`poll_common (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="181.6" y="641" width="12.5" height="15.0" fill="rgb(242,92,15)" rx="2" ry="2" onmouseover="s('genunix`strput (4046 samples, 1.06%)')" onmouseout="c()" />
<rect x="966.6" y="609" width="0.2" height="15.0" fill="rgb(229,166,27)" rx="2" ry="2" onmouseover="s('lofs`lo_lookup (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="214.5" y="577" width="0.4" height="15.0" fill="rgb(211,1,47)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (141 samples, 0.04%)')" onmouseout="c()" />
<rect x="1153.7" y="609" width="0.1" height="15.0" fill="rgb(205,62,53)" rx="2" ry="2" onmouseover="s('unix`atomic_add_32_nv (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="751.5" y="465" width="0.4" height="15.0" fill="rgb(231,21,0)" rx="2" ry="2" onmouseover="s('unix`rw_enter (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="1082.4" y="465" width="62.0" height="15.0" fill="rgb(226,47,15)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (20017 samples, 5.26%)')" onmouseout="c()" />
<text text-anchor="" x="1085.3760335461" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`dce_lookup_and_add_v4 (20017 samples, 5.26%)')" onmouseout="c()" >ip`dce_..</text>
<rect x="583.7" y="577" width="0.1" height="15.0" fill="rgb(244,19,38)" rx="2" ry="2" onmouseover="s('genunix`allocb (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="307.4" y="593" width="23.5" height="15.0" fill="rgb(251,88,48)" rx="2" ry="2" onmouseover="s('ip`tcp_sendmsg (7569 samples, 1.99%)')" onmouseout="c()" />
<rect x="179.5" y="545" width="0.8" height="15.0" fill="rgb(222,129,34)" rx="2" ry="2" onmouseover="s('lofs`lo_lookup (265 samples, 0.07%)')" onmouseout="c()" />
<rect x="133.3" y="625" width="6.1" height="15.0" fill="rgb(238,229,37)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_read (1941 samples, 0.51%)')" onmouseout="c()" />
<rect x="1159.2" y="513" width="0.9" height="15.0" fill="rgb(233,68,47)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (302 samples, 0.08%)')" onmouseout="c()" />
<rect x="302.1" y="577" width="1.4" height="15.0" fill="rgb(209,229,44)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (448 samples, 0.12%)')" onmouseout="c()" />
<rect x="659.0" y="321" width="0.1" height="15.0" fill="rgb(238,117,51)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_free (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="1187.7" y="657" width="0.7" height="15.0" fill="rgb(248,92,41)" rx="2" ry="2" onmouseover="s('unix`resume (242 samples, 0.06%)')" onmouseout="c()" />
<rect x="1081.6" y="449" width="0.2" height="15.0" fill="rgb(247,123,40)" rx="2" ry="2" onmouseover="s('hook`hook_run (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="194.3" y="593" width="1.1" height="15.0" fill="rgb(242,0,34)" rx="2" ry="2" onmouseover="s('genunix`seg_free (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="680.2" y="321" width="0.7" height="15.0" fill="rgb(209,187,52)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (218 samples, 0.06%)')" onmouseout="c()" />
<rect x="198.1" y="641" width="0.2" height="15.0" fill="rgb(252,163,29)" rx="2" ry="2" onmouseover="s('genunix`cstat32 (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="338.5" y="641" width="0.6" height="15.0" fill="rgb(232,215,18)" rx="2" ry="2" onmouseover="s('genunix`set_active_fd (189 samples, 0.05%)')" onmouseout="c()" />
<rect x="1078.7" y="529" width="0.2" height="15.0" fill="rgb(219,79,53)" rx="2" ry="2" onmouseover="s('sockfs`so_opctl (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="662.6" y="369" width="0.1" height="15.0" fill="rgb(220,146,14)" rx="2" ry="2" onmouseover="s('ip`tcp_paws_check (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1079.3" y="449" width="0.4" height="15.0" fill="rgb(249,33,40)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (117 samples, 0.03%)')" onmouseout="c()" />
<rect x="153.5" y="545" width="0.4" height="15.0" fill="rgb(217,146,9)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (139 samples, 0.04%)')" onmouseout="c()" />
<rect x="1076.7" y="497" width="0.2" height="15.0" fill="rgb(243,197,30)" rx="2" ry="2" onmouseover="s('genunix`dupb (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="56.0" y="705" width="6.8" height="15.0" fill="rgb(238,178,20)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800ec9 (2204 samples, 0.58%)')" onmouseout="c()" />
<rect x="615.7" y="625" width="0.2" height="15.0" fill="rgb(230,189,51)" rx="2" ry="2" onmouseover="s('unix`atomic_add_32 (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="744.7" y="337" width="0.3" height="15.0" fill="rgb(246,129,11)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_data (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="1073.7" y="465" width="1.4" height="15.0" fill="rgb(206,171,2)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (464 samples, 0.12%)')" onmouseout="c()" />
<rect x="343.7" y="673" width="206.9" height="15.0" fill="rgb(206,177,15)" rx="2" ry="2" onmouseover="s('sockfs`socket_connect (66803 samples, 17.54%)')" onmouseout="c()" />
<text text-anchor="" x="346.660322069902" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`socket_connect (66803 samples, 17.54%)')" onmouseout="c()" >sockfs`socket_connect</text>
<rect x="660.3" y="337" width="0.3" height="15.0" fill="rgb(242,74,23)" rx="2" ry="2" onmouseover="s('ip`tcp_closei_local (121 samples, 0.03%)')" onmouseout="c()" />
<rect x="943.1" y="593" width="0.1" height="15.0" fill="rgb(225,17,36)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="179.3" y="545" width="0.2" height="15.0" fill="rgb(236,21,36)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="972.0" y="689" width="23.7" height="15.0" fill="rgb(232,30,11)" rx="2" ry="2" onmouseover="s('portfs`portfs32 (7649 samples, 2.01%)')" onmouseout="c()" />
<rect x="670.1" y="353" width="0.2" height="15.0" fill="rgb(211,182,1)" rx="2" ry="2" onmouseover="s('ip`tcp_timers_stop (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="667.1" y="193" width="0.2" height="15.0" fill="rgb(253,222,10)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="1018.5" y="593" width="0.1" height="15.0" fill="rgb(250,78,50)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="304.1" y="625" width="0.1" height="15.0" fill="rgb(238,90,2)" rx="2" ry="2" onmouseover="s('genunix`uiomove (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="533.1" y="497" width="0.4" height="15.0" fill="rgb(230,75,38)" rx="2" ry="2" onmouseover="s('unix`sys_rtt_common (133 samples, 0.03%)')" onmouseout="c()" />
<rect x="33.9" y="561" width="1.2" height="15.0" fill="rgb(241,173,4)" rx="2" ry="2" onmouseover="s('unix`hwblkclr (383 samples, 0.10%)')" onmouseout="c()" />
<rect x="80.8" y="481" width="0.2" height="15.0" fill="rgb(221,34,53)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="1156.7" y="641" width="0.5" height="15.0" fill="rgb(230,157,44)" rx="2" ry="2" onmouseover="s('unix`disp (154 samples, 0.04%)')" onmouseout="c()" />
<rect x="1010.1" y="561" width="0.2" height="15.0" fill="rgb(240,223,30)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (85 samples, 0.02%)')" onmouseout="c()" />
<rect x="336.6" y="609" width="0.2" height="15.0" fill="rgb(225,143,36)" rx="2" ry="2" onmouseover="s('zfs`dmu_tx_hold_bonus (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.7" y="401" width="4.0" height="15.0" fill="rgb(237,159,39)" rx="2" ry="2" onmouseover="s('unix`do_splx (1280 samples, 0.34%)')" onmouseout="c()" />
<rect x="648.1" y="353" width="0.2" height="15.0" fill="rgb(253,199,2)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="139.7" y="625" width="0.8" height="15.0" fill="rgb(224,27,6)" rx="2" ry="2" onmouseover="s('zfs`zfs_read (266 samples, 0.07%)')" onmouseout="c()" />
<rect x="542.3" y="641" width="8.2" height="15.0" fill="rgb(252,3,12)" rx="2" ry="2" onmouseover="s('ip`udp_connect (2667 samples, 0.70%)')" onmouseout="c()" />
<rect x="945.1" y="673" width="0.3" height="15.0" fill="rgb(217,208,12)" rx="2" ry="2" onmouseover="s('genunix`lookuppn (105 samples, 0.03%)')" onmouseout="c()" />
<rect x="197.4" y="689" width="0.2" height="15.0" fill="rgb(251,125,20)" rx="2" ry="2" onmouseover="s('genunix`schedctl (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="76.1" y="689" width="0.2" height="15.0" fill="rgb(254,223,2)" rx="2" ry="2" onmouseover="s('genunix`copyin_args32 (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="666.8" y="209" width="0.2" height="15.0" fill="rgb(229,217,25)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_get_free_list (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="991.5" y="641" width="0.3" height="15.0" fill="rgb(208,188,39)" rx="2" ry="2" onmouseover="s('genunix`list_move_tail (93 samples, 0.02%)')" onmouseout="c()" />
<rect x="966.6" y="625" width="0.2" height="15.0" fill="rgb(223,207,13)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="328.2" y="433" width="0.8" height="15.0" fill="rgb(235,144,16)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (261 samples, 0.07%)')" onmouseout="c()" />
<rect x="630.4" y="417" width="11.1" height="15.0" fill="rgb(232,69,0)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_in (3580 samples, 0.94%)')" onmouseout="c()" />
<rect x="785.7" y="673" width="0.1" height="15.0" fill="rgb(247,147,9)" rx="2" ry="2" onmouseover="s('unix`dtrace_interrupt_enable (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="954.1" y="673" width="3.8" height="15.0" fill="rgb(247,187,37)" rx="2" ry="2" onmouseover="s('genunix`as_faulta (1216 samples, 0.32%)')" onmouseout="c()" />
<rect x="974.2" y="657" width="4.0" height="15.0" fill="rgb(235,37,6)" rx="2" ry="2" onmouseover="s('portfs`port_associate_fd (1290 samples, 0.34%)')" onmouseout="c()" />
<rect x="14.4" y="673" width="0.1" height="15.0" fill="rgb(218,40,12)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1028.7" y="593" width="0.1" height="15.0" fill="rgb(209,9,36)" rx="2" ry="2" onmouseover="s('ip`ip_verify_ire (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="194.3" y="641" width="3.0" height="15.0" fill="rgb(247,90,28)" rx="2" ry="2" onmouseover="s('genunix`relvm (967 samples, 0.25%)')" onmouseout="c()" />
<rect x="960.2" y="593" width="2.6" height="15.0" fill="rgb(222,124,23)" rx="2" ry="2" onmouseover="s('genunix`segvn_faulta (846 samples, 0.22%)')" onmouseout="c()" />
<rect x="771.4" y="481" width="0.3" height="15.0" fill="rgb(227,41,39)" rx="2" ry="2" onmouseover="s('unix`cmt_balance (102 samples, 0.03%)')" onmouseout="c()" />
<rect x="993.7" y="625" width="0.2" height="15.0" fill="rgb(227,41,14)" rx="2" ry="2" onmouseover="s('unix`bzero (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="13.8" y="673" width="0.6" height="15.0" fill="rgb(235,216,24)" rx="2" ry="2" onmouseover="s('genunix`new_mstate (208 samples, 0.05%)')" onmouseout="c()" />
<rect x="44.4" y="657" width="0.2" height="15.0" fill="rgb(239,205,49)" rx="2" ry="2" onmouseover="s('genunix`audit_update_context (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="534.1" y="449" width="0.5" height="15.0" fill="rgb(221,209,21)" rx="2" ry="2" onmouseover="s('ip`rn_match_args (149 samples, 0.04%)')" onmouseout="c()" />
<rect x="1006.4" y="673" width="4.3" height="15.0" fill="rgb(212,145,48)" rx="2" ry="2" onmouseover="s('genunix`callout_execute (1377 samples, 0.36%)')" onmouseout="c()" />
<rect x="1150.6" y="529" width="0.4" height="15.0" fill="rgb(212,208,40)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg (127 samples, 0.03%)')" onmouseout="c()" />
<rect x="1018.9" y="577" width="8.1" height="15.0" fill="rgb(245,69,6)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (2605 samples, 0.68%)')" onmouseout="c()" />
<rect x="197.9" y="641" width="0.1" height="15.0" fill="rgb(249,144,11)" rx="2" ry="2" onmouseover="s('genunix`choose_addr (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="343.4" y="689" width="207.3" height="15.0" fill="rgb(222,180,27)" rx="2" ry="2" onmouseover="s('sockfs`connect (66921 samples, 17.57%)')" onmouseout="c()" />
<text text-anchor="" x="346.35358632949" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('sockfs`connect (66921 samples, 17.57%)')" onmouseout="c()" >sockfs`connect</text>
<rect x="71.4" y="657" width="0.7" height="15.0" fill="rgb(222,37,40)" rx="2" ry="2" onmouseover="s('unix`preempt (219 samples, 0.06%)')" onmouseout="c()" />
<rect x="537.3" y="497" width="1.2" height="15.0" fill="rgb(247,43,47)" rx="2" ry="2" onmouseover="s('ip`conn_connect (400 samples, 0.11%)')" onmouseout="c()" />
<rect x="347.1" y="561" width="188.4" height="15.0" fill="rgb(250,83,27)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (60802 samples, 15.96%)')" onmouseout="c()" />
<text text-anchor="" x="350.111873734735" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_attr_connect (60802 samples, 15.96%)')" onmouseout="c()" >ip`ip_attr_connect</text>
<rect x="979.6" y="625" width="10.5" height="15.0" fill="rgb(236,165,32)" rx="2" ry="2" onmouseover="s('genunix`cv_timedwait_sig_hires (3375 samples, 0.89%)')" onmouseout="c()" />
<rect x="329.9" y="561" width="0.1" height="15.0" fill="rgb(219,81,38)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.6" y="257" width="0.1" height="15.0" fill="rgb(233,228,48)" rx="2" ry="2" onmouseover="s('unix`htable_getpte (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="325.6" y="513" width="0.4" height="15.0" fill="rgb(206,156,40)" rx="2" ry="2" onmouseover="s('genunix`untimeout_generic (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="1051.5" y="497" width="0.1" height="15.0" fill="rgb(250,122,24)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="661.2" y="369" width="1.4" height="15.0" fill="rgb(230,58,51)" rx="2" ry="2" onmouseover="s('ip`tcp_newconn_notify (454 samples, 0.12%)')" onmouseout="c()" />
<rect x="631.9" y="369" width="0.2" height="15.0" fill="rgb(225,176,12)" rx="2" ry="2" onmouseover="s('ipf`fr_checknatin (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="937.9" y="673" width="0.5" height="15.0" fill="rgb(242,218,42)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (171 samples, 0.04%)')" onmouseout="c()" />
<rect x="557.1" y="497" width="0.1" height="15.0" fill="rgb(242,166,43)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="62.1" y="641" width="0.1" height="15.0" fill="rgb(222,194,36)" rx="2" ry="2" onmouseover="s('FSS`fss_preempt (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="773.7" y="609" width="0.7" height="15.0" fill="rgb(247,64,3)" rx="2" ry="2" onmouseover="s('genunix`callout_heap_delete (251 samples, 0.07%)')" onmouseout="c()" />
<rect x="302.3" y="513" width="0.1" height="15.0" fill="rgb(210,191,19)" rx="2" ry="2" onmouseover="s('unix`splr (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="666.3" y="241" width="1.7" height="15.0" fill="rgb(214,82,5)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (564 samples, 0.15%)')" onmouseout="c()" />
<rect x="943.6" y="657" width="1.1" height="15.0" fill="rgb(243,22,32)" rx="2" ry="2" onmouseover="s('genunix`copen (354 samples, 0.09%)')" onmouseout="c()" />
<rect x="595.3" y="689" width="0.3" height="15.0" fill="rgb(227,18,37)" rx="2" ry="2" onmouseover="s('unix`atomic_and_64 (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="1011.3" y="593" width="0.6" height="15.0" fill="rgb(244,76,51)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (213 samples, 0.06%)')" onmouseout="c()" />
<rect x="1033.9" y="625" width="0.2" height="15.0" fill="rgb(211,44,24)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="334.1" y="577" width="1.1" height="15.0" fill="rgb(222,0,30)" rx="2" ry="2" onmouseover="s('genunix`uiomove (350 samples, 0.09%)')" onmouseout="c()" />
<rect x="217.8" y="689" width="0.2" height="15.0" fill="rgb(240,33,26)" rx="2" ry="2" onmouseover="s('genunix`unlink (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="946.0" y="641" width="0.7" height="15.0" fill="rgb(211,71,30)" rx="2" ry="2" onmouseover="s('genunix`cstatat_getvp (236 samples, 0.06%)')" onmouseout="c()" />
<rect x="1071.4" y="529" width="1.7" height="15.0" fill="rgb(229,60,18)" rx="2" ry="2" onmouseover="s('ip`tcp_newconn_notify (550 samples, 0.14%)')" onmouseout="c()" />
<rect x="774.5" y="593" width="8.1" height="15.0" fill="rgb(237,22,13)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (2623 samples, 0.69%)')" onmouseout="c()" />
<rect x="1144.7" y="385" width="0.2" height="15.0" fill="rgb(211,185,50)" rx="2" ry="2" onmouseover="s('ip`rn_match_args (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="1017.5" y="577" width="0.3" height="15.0" fill="rgb(244,229,26)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (100 samples, 0.03%)')" onmouseout="c()" />
<rect x="557.3" y="545" width="0.4" height="15.0" fill="rgb(234,207,27)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (131 samples, 0.03%)')" onmouseout="c()" />
<rect x="972.0" y="673" width="0.1" height="15.0" fill="rgb(249,81,23)" rx="2" ry="2" onmouseover="s('genunix`getf (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1051.2" y="465" width="0.2" height="15.0" fill="rgb(208,57,39)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="937.6" y="657" width="0.2" height="15.0" fill="rgb(254,37,9)" rx="2" ry="2" onmouseover="s('unix`sys_rtt_common (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="1031.2" y="497" width="0.1" height="15.0" fill="rgb(222,187,42)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="1043.9" y="513" width="0.3" height="15.0" fill="rgb(247,204,39)" rx="2" ry="2" onmouseover="s('unix`rw_exit_wakeup (124 samples, 0.03%)')" onmouseout="c()" />
<rect x="768.8" y="545" width="1.4" height="15.0" fill="rgb(212,195,54)" rx="2" ry="2" onmouseover="s('unix`xc_common (426 samples, 0.11%)')" onmouseout="c()" />
<rect x="583.0" y="641" width="1.3" height="15.0" fill="rgb(217,94,14)" rx="2" ry="2" onmouseover="s('sockfs`socket_init_common (434 samples, 0.11%)')" onmouseout="c()" />
<rect x="304.9" y="641" width="30.9" height="15.0" fill="rgb(229,217,8)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_write (9964 samples, 2.62%)')" onmouseout="c()" />
<rect x="83.3" y="385" width="1.4" height="15.0" fill="rgb(240,158,7)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (431 samples, 0.11%)')" onmouseout="c()" />
<rect x="671.9" y="353" width="0.2" height="15.0" fill="rgb(218,33,4)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_free (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="676.3" y="337" width="0.2" height="15.0" fill="rgb(241,12,26)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (76 samples, 0.02%)')" onmouseout="c()" />
<rect x="315.1" y="529" width="9.2" height="15.0" fill="rgb(230,130,21)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (2958 samples, 0.78%)')" onmouseout="c()" />
<rect x="1176.7" y="641" width="4.2" height="15.0" fill="rgb(230,44,12)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (1374 samples, 0.36%)')" onmouseout="c()" />
<rect x="343.8" y="641" width="198.5" height="15.0" fill="rgb(228,70,20)" rx="2" ry="2" onmouseover="s('ip`tcp_connect (64068 samples, 16.82%)')" onmouseout="c()" />
<text text-anchor="" x="346.759468975893" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_connect (64068 samples, 16.82%)')" onmouseout="c()" >ip`tcp_connect</text>
<rect x="622.4" y="529" width="133.8" height="15.0" fill="rgb(243,47,5)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_fanout (43191 samples, 11.34%)')" onmouseout="c()" />
<text text-anchor="" x="625.396356561262" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_fanout (43191 samples, 11.34%)')" onmouseout="c()" >mac`mac_rx_srs_..</text>
<rect x="81.9" y="449" width="0.1" height="15.0" fill="rgb(230,137,52)" rx="2" ry="2" onmouseover="s('ip`ip_output_cksum_v4 (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="154.5" y="609" width="0.3" height="15.0" fill="rgb(207,123,21)" rx="2" ry="2" onmouseover="s('genunix`relvm (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="553.6" y="577" width="0.1" height="15.0" fill="rgb(215,48,32)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="786.1" y="561" width="0.2" height="15.0" fill="rgb(231,6,26)" rx="2" ry="2" onmouseover="s('genunix`seg_free (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="77.2" y="609" width="0.1" height="15.0" fill="rgb(205,210,24)" rx="2" ry="2" onmouseover="s('genunix`kmem_free (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="46.8" y="705" width="7.0" height="15.0" fill="rgb(208,219,21)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800c91 (2259 samples, 0.59%)')" onmouseout="c()" />
<rect x="319.7" y="353" width="0.1" height="15.0" fill="rgb(229,7,34)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_coredma_bindhdl (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="969.6" y="673" width="0.9" height="15.0" fill="rgb(229,47,22)" rx="2" ry="2" onmouseover="s('genunix`copyin_args32 (314 samples, 0.08%)')" onmouseout="c()" />
<rect x="1071.4" y="513" width="1.6" height="15.0" fill="rgb(205,202,6)" rx="2" ry="2" onmouseover="s('sockfs`so_newconn (511 samples, 0.13%)')" onmouseout="c()" />
<rect x="1033.7" y="641" width="0.1" height="15.0" fill="rgb(228,17,22)" rx="2" ry="2" onmouseover="s('genunix`restore_mstate (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="159.7" y="625" width="10.2" height="15.0" fill="rgb(234,66,14)" rx="2" ry="2" onmouseover="s('kstat`kstat_ioctl (3316 samples, 0.87%)')" onmouseout="c()" />
<rect x="1159.5" y="433" width="0.2" height="15.0" fill="rgb(247,3,23)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="785.9" y="577" width="0.1" height="15.0" fill="rgb(240,98,47)" rx="2" ry="2" onmouseover="s('genunix`segvn_fault (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="217.8" y="657" width="0.2" height="15.0" fill="rgb(212,73,44)" rx="2" ry="2" onmouseover="s('genunix`vn_removeat (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="664.7" y="305" width="3.7" height="15.0" fill="rgb(213,180,37)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (1185 samples, 0.31%)')" onmouseout="c()" />
<rect x="342.8" y="641" width="0.2" height="15.0" fill="rgb(235,223,31)" rx="2" ry="2" onmouseover="s('ip`tcp_accept (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.1" y="577" width="4.9" height="15.0" fill="rgb(208,225,25)" rx="2" ry="2" onmouseover="s('genunix`vmem_free (1601 samples, 0.42%)')" onmouseout="c()" />
<rect x="976.9" y="641" width="0.7" height="15.0" fill="rgb(244,13,54)" rx="2" ry="2" onmouseover="s('portfs`port_cache_lookup_fp (232 samples, 0.06%)')" onmouseout="c()" />
<rect x="637.7" y="337" width="0.5" height="15.0" fill="rgb(210,110,28)" rx="2" ry="2" onmouseover="s('genunix`memcmp (154 samples, 0.04%)')" onmouseout="c()" />
<rect x="135.8" y="561" width="0.1" height="15.0" fill="rgb(230,93,49)" rx="2" ry="2" onmouseover="s('sockfs`so_process_new_message (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1042.8" y="465" width="1.0" height="15.0" fill="rgb(219,226,5)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl_spin (331 samples, 0.09%)')" onmouseout="c()" />
<rect x="341.3" y="641" width="0.3" height="15.0" fill="rgb(218,52,20)" rx="2" ry="2" onmouseover="s('genunix`fd_find (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="660.1" y="353" width="0.1" height="15.0" fill="rgb(218,128,48)" rx="2" ry="2" onmouseover="s('ip`tcp_xmit_mp (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="608.1" y="593" width="0.2" height="15.0" fill="rgb(214,76,36)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="155.1" y="625" width="0.4" height="15.0" fill="rgb(209,1,36)" rx="2" ry="2" onmouseover="s('genunix`gexec (131 samples, 0.03%)')" onmouseout="c()" />
<rect x="330.9" y="593" width="4.3" height="15.0" fill="rgb(230,95,6)" rx="2" ry="2" onmouseover="s('sockfs`socopyinuio (1391 samples, 0.37%)')" onmouseout="c()" />
<rect x="674.5" y="177" width="0.2" height="15.0" fill="rgb(233,29,54)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_addr_bind_handle (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="170.8" y="641" width="0.6" height="15.0" fill="rgb(238,34,37)" rx="2" ry="2" onmouseover="s('genunix`cv_block (165 samples, 0.04%)')" onmouseout="c()" />
<rect x="329.6" y="545" width="0.1" height="15.0" fill="rgb(234,140,3)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1037.5" y="593" width="0.2" height="15.0" fill="rgb(239,163,0)" rx="2" ry="2" onmouseover="s('genunix`i_mod_hash (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1032.6" y="657" width="0.2" height="15.0" fill="rgb(253,185,35)" rx="2" ry="2" onmouseover="s('genunix`cv_block (84 samples, 0.02%)')" onmouseout="c()" />
<rect x="194.2" y="657" width="3.2" height="15.0" fill="rgb(222,178,21)" rx="2" ry="2" onmouseover="s('genunix`proc_exit (1038 samples, 0.27%)')" onmouseout="c()" />
<rect x="173.2" y="641" width="0.2" height="15.0" fill="rgb(252,212,35)" rx="2" ry="2" onmouseover="s('genunix`fop_access (81 samples, 0.02%)')" onmouseout="c()" />
<rect x="59.1" y="641" width="0.5" height="15.0" fill="rgb(241,77,45)" rx="2" ry="2" onmouseover="s('unix`do_splx (175 samples, 0.05%)')" onmouseout="c()" />
<rect x="1030.2" y="465" width="0.2" height="15.0" fill="rgb(252,71,2)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_copy (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="977.8" y="641" width="0.2" height="15.0" fill="rgb(230,184,36)" rx="2" ry="2" onmouseover="s('unix`bzero (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="310.1" y="481" width="1.1" height="15.0" fill="rgb(236,85,6)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (355 samples, 0.09%)')" onmouseout="c()" />
<rect x="680.2" y="353" width="0.7" height="15.0" fill="rgb(217,37,48)" rx="2" ry="2" onmouseover="s('ip`tcp_send_synack (232 samples, 0.06%)')" onmouseout="c()" />
<rect x="96.3" y="577" width="0.1" height="15.0" fill="rgb(227,169,29)" rx="2" ry="2" onmouseover="s('genunix`kmem_free (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="579.1" y="641" width="0.2" height="15.0" fill="rgb(251,125,3)" rx="2" ry="2" onmouseover="s('genunix`ufalloc_file (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="654.0" y="353" width="0.2" height="15.0" fill="rgb(235,28,41)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="139.4" y="609" width="0.2" height="15.0" fill="rgb(237,180,46)" rx="2" ry="2" onmouseover="s('ufs`rdip (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="140.4" y="593" width="0.1" height="15.0" fill="rgb(232,107,9)" rx="2" ry="2" onmouseover="s('zfs`dmu_read_uio (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="322.6" y="433" width="0.5" height="15.0" fill="rgb(224,107,33)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (148 samples, 0.04%)')" onmouseout="c()" />
<rect x="944.1" y="577" width="0.6" height="15.0" fill="rgb(214,128,21)" rx="2" ry="2" onmouseover="s('genunix`lookuppnvp (199 samples, 0.05%)')" onmouseout="c()" />
<rect x="326.0" y="529" width="0.2" height="15.0" fill="rgb(248,30,27)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_free (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="98.8" y="545" width="0.2" height="15.0" fill="rgb(220,80,46)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="344.0" y="625" width="0.6" height="15.0" fill="rgb(225,99,40)" rx="2" ry="2" onmouseover="s('ip`squeue_synch_exit (184 samples, 0.05%)')" onmouseout="c()" />
<rect x="311.9" y="481" width="0.4" height="15.0" fill="rgb(239,135,14)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="1068.4" y="465" width="0.3" height="15.0" fill="rgb(251,75,47)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (96 samples, 0.03%)')" onmouseout="c()" />
<rect x="676.9" y="337" width="0.2" height="15.0" fill="rgb(251,138,38)" rx="2" ry="2" onmouseover="s('sockfs`so_enqueue_msg (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="314.8" y="465" width="0.2" height="15.0" fill="rgb(233,225,6)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_impl_v4 (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="47.2" y="689" width="6.3" height="15.0" fill="rgb(235,223,22)" rx="2" ry="2" onmouseover="s('genunix`post_syscall (2041 samples, 0.54%)')" onmouseout="c()" />
<rect x="23.8" y="625" width="16.0" height="15.0" fill="rgb(221,55,20)" rx="2" ry="2" onmouseover="s('genunix`segvn_faultpage (5162 samples, 1.36%)')" onmouseout="c()" />
<rect x="666.0" y="257" width="2.3" height="15.0" fill="rgb(225,76,3)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (720 samples, 0.19%)')" onmouseout="c()" />
<rect x="620.9" y="529" width="0.5" height="15.0" fill="rgb(227,110,10)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (158 samples, 0.04%)')" onmouseout="c()" />
<rect x="1029.8" y="529" width="1.0" height="15.0" fill="rgb(237,80,36)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (324 samples, 0.09%)')" onmouseout="c()" />
<rect x="197.9" y="609" width="0.1" height="15.0" fill="rgb(238,88,48)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="160.7" y="433" width="4.0" height="15.0" fill="rgb(228,162,1)" rx="2" ry="2" onmouseover="s('unix`xc_call (1283 samples, 0.34%)')" onmouseout="c()" />
<rect x="307.2" y="593" width="0.2" height="15.0" fill="rgb(224,158,48)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1076.2" y="513" width="0.2" height="15.0" fill="rgb(244,24,14)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="195.1" y="529" width="0.1" height="15.0" fill="rgb(223,25,38)" rx="2" ry="2" onmouseover="s('unix`page_lookup (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="70.3" y="609" width="1.0" height="15.0" fill="rgb(229,211,1)" rx="2" ry="2" onmouseover="s('unix`swtch (322 samples, 0.08%)')" onmouseout="c()" />
<rect x="311.8" y="513" width="0.5" height="15.0" fill="rgb(234,93,28)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (167 samples, 0.04%)')" onmouseout="c()" />
<rect x="978.0" y="641" width="0.1" height="15.0" fill="rgb(240,168,26)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="679.2" y="385" width="63.7" height="15.0" fill="rgb(254,168,42)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (20579 samples, 5.40%)')" onmouseout="c()" />
<text text-anchor="" x="682.173451945522" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`tcp_input_listener (20579 samples, 5.40%)')" onmouseout="c()" >ip`tcp_..</text>
<rect x="140.4" y="609" width="0.1" height="15.0" fill="rgb(217,82,29)" rx="2" ry="2" onmouseover="s('zfs`mappedread (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1156.6" y="657" width="0.6" height="15.0" fill="rgb(253,8,43)" rx="2" ry="2" onmouseover="s('unix`swtch (197 samples, 0.05%)')" onmouseout="c()" />
<rect x="647.1" y="321" width="0.2" height="15.0" fill="rgb(237,221,33)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="1037.3" y="625" width="0.5" height="15.0" fill="rgb(252,24,40)" rx="2" ry="2" onmouseover="s('genunix`mod_hash_find_cb_rval (156 samples, 0.04%)')" onmouseout="c()" />
<rect x="153.0" y="609" width="0.1" height="15.0" fill="rgb(225,4,43)" rx="2" ry="2" onmouseover="s('unix`bzero (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1165.9" y="673" width="15.4" height="15.0" fill="rgb(224,76,41)" rx="2" ry="2" onmouseover="s('unix`disp_getwork (4988 samples, 1.31%)')" onmouseout="c()" />
<rect x="38.5" y="497" width="0.2" height="15.0" fill="rgb(240,172,53)" rx="2" ry="2" onmouseover="s('genunix`avl_find (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="1017.4" y="641" width="0.6" height="15.0" fill="rgb(217,88,51)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (186 samples, 0.05%)')" onmouseout="c()" />
<rect x="1012.9" y="545" width="0.2" height="15.0" fill="rgb(238,100,39)" rx="2" ry="2" onmouseover="s('zfs`zio_notify_parent (55 samples, 0.01%)')" onmouseout="c()" />
<rect x="746.2" y="433" width="3.4" height="15.0" fill="rgb(243,110,26)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_dstonly_v4 (1082 samples, 0.28%)')" onmouseout="c()" />
<rect x="314.7" y="465" width="0.1" height="15.0" fill="rgb(221,64,50)" rx="2" ry="2" onmouseover="s('ip`ipif_lookup_addr_nondup (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="77.3" y="625" width="17.7" height="15.0" fill="rgb(230,6,50)" rx="2" ry="2" onmouseover="s('genunix`fop_close (5690 samples, 1.49%)')" onmouseout="c()" />
<rect x="309.7" y="545" width="0.3" height="15.0" fill="rgb(228,159,32)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (122 samples, 0.03%)')" onmouseout="c()" />
<rect x="681.3" y="337" width="61.2" height="15.0" fill="rgb(237,59,8)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (19763 samples, 5.19%)')" onmouseout="c()" />
<text text-anchor="" x="684.264831993782" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`ip_attr_connect (19763 samples, 5.19%)')" onmouseout="c()" >ip`ip_a..</text>
<rect x="1048.5" y="529" width="0.2" height="15.0" fill="rgb(237,227,48)" rx="2" ry="2" onmouseover="s('genunix`allocb (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="68.6" y="657" width="2.7" height="15.0" fill="rgb(228,138,7)" rx="2" ry="2" onmouseover="s('genunix`new_mstate (874 samples, 0.23%)')" onmouseout="c()" />
<rect x="152.8" y="545" width="0.1" height="15.0" fill="rgb(209,211,0)" rx="2" ry="2" onmouseover="s('unix`hat_memload_region (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="600.6" y="657" width="0.2" height="15.0" fill="rgb(246,160,9)" rx="2" ry="2" onmouseover="s('genunix`bt_getlowbit (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="539.7" y="481" width="0.6" height="15.0" fill="rgb(235,12,16)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (196 samples, 0.05%)')" onmouseout="c()" />
<rect x="152.4" y="641" width="1.9" height="15.0" fill="rgb(240,139,11)" rx="2" ry="2" onmouseover="s('elfexec`elf32exec (625 samples, 0.16%)')" onmouseout="c()" />
<rect x="209.1" y="657" width="0.2" height="15.0" fill="rgb(230,158,32)" rx="2" ry="2" onmouseover="s('genunix`watch_disable_addr (80 samples, 0.02%)')" onmouseout="c()" />
<rect x="554.1" y="673" width="0.3" height="15.0" fill="rgb(213,25,44)" rx="2" ry="2" onmouseover="s('sockfs`send (97 samples, 0.03%)')" onmouseout="c()" />
<rect x="82.9" y="369" width="0.3" height="15.0" fill="rgb(221,97,36)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="938.9" y="593" width="1.9" height="15.0" fill="rgb(224,206,29)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (598 samples, 0.16%)')" onmouseout="c()" />
<rect x="95.2" y="561" width="0.2" height="15.0" fill="rgb(229,72,38)" rx="2" ry="2" onmouseover="s('sockfs`socket_sonode_destroy (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="302.9" y="513" width="0.3" height="15.0" fill="rgb(234,176,23)" rx="2" ry="2" onmouseover="s('unix`setbackdq (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="1018.9" y="625" width="8.2" height="15.0" fill="rgb(240,150,25)" rx="2" ry="2" onmouseover="s('ip`conn_connect (2642 samples, 0.69%)')" onmouseout="c()" />
<rect x="761.0" y="657" width="0.1" height="15.0" fill="rgb(229,7,13)" rx="2" ry="2" onmouseover="s('mac`mac_rx_ring (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="630.4" y="401" width="0.1" height="15.0" fill="rgb(249,107,4)" rx="2" ry="2" onmouseover="s('ipf`fr_check (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1027.4" y="545" width="0.1" height="15.0" fill="rgb(215,91,49)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="1049.8" y="529" width="1.9" height="15.0" fill="rgb(219,157,0)" rx="2" ry="2" onmouseover="s('ip`tcp_input_data (599 samples, 0.16%)')" onmouseout="c()" />
<rect x="1072.4" y="465" width="0.1" height="15.0" fill="rgb(211,154,52)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="632.4" y="369" width="6.2" height="15.0" fill="rgb(207,18,23)" rx="2" ry="2" onmouseover="s('ipf`fr_firewall (1994 samples, 0.52%)')" onmouseout="c()" />
<rect x="26.1" y="577" width="0.3" height="15.0" fill="rgb(212,182,35)" rx="2" ry="2" onmouseover="s('unix`hwblkpagecopy (106 samples, 0.03%)')" onmouseout="c()" />
<rect x="786.3" y="545" width="0.3" height="15.0" fill="rgb(220,200,5)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="1010.8" y="641" width="0.1" height="15.0" fill="rgb(213,85,0)" rx="2" ry="2" onmouseover="s('genunix`cv_block (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="981.8" y="577" width="0.2" height="15.0" fill="rgb(211,96,14)" rx="2" ry="2" onmouseover="s('genunix`cpu_update_pct (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="967.3" y="673" width="2.1" height="15.0" fill="rgb(250,69,16)" rx="2" ry="2" onmouseover="s('genunix`fstatat64_32 (671 samples, 0.18%)')" onmouseout="c()" />
<rect x="750.3" y="449" width="0.1" height="15.0" fill="rgb(252,167,41)" rx="2" ry="2" onmouseover="s('ip`ire_recv_local_v4 (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="631.7" y="369" width="0.2" height="15.0" fill="rgb(236,0,37)" rx="2" ry="2" onmouseover="s('ipf`fr_acctpkt (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="314.7" y="481" width="0.1" height="15.0" fill="rgb(244,130,46)" rx="2" ry="2" onmouseover="s('ip`ip_select_src_ill (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="345.0" y="593" width="1.9" height="15.0" fill="rgb(220,169,49)" rx="2" ry="2" onmouseover="s('ip`tcp_bindi (618 samples, 0.16%)')" onmouseout="c()" />
<rect x="317.5" y="417" width="0.1" height="15.0" fill="rgb(254,2,50)" rx="2" ry="2" onmouseover="s('mac`i_mac_flow_vid (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="540.6" y="513" width="0.5" height="15.0" fill="rgb(244,228,1)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (179 samples, 0.05%)')" onmouseout="c()" />
<rect x="322.7" y="417" width="0.4" height="15.0" fill="rgb(214,36,36)" rx="2" ry="2" onmouseover="s('ipf`frpr_ipv4hdr (125 samples, 0.03%)')" onmouseout="c()" />
<rect x="71.5" y="625" width="0.2" height="15.0" fill="rgb(215,167,38)" rx="2" ry="2" onmouseover="s('unix`setfrontdq (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="662.0" y="337" width="0.2" height="15.0" fill="rgb(253,221,54)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_newconn (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="588.1" y="689" width="0.2" height="15.0" fill="rgb(249,203,48)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (65 samples, 0.02%)')" onmouseout="c()" />
<rect x="640.3" y="369" width="0.1" height="15.0" fill="rgb(251,138,33)" rx="2" ry="2" onmouseover="s('unix`rw_exit (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="950.0" y="401" width="0.1" height="15.0" fill="rgb(253,67,8)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="1017.5" y="561" width="0.3" height="15.0" fill="rgb(238,81,53)" rx="2" ry="2" onmouseover="s('mac`mac_tx (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="554.5" y="673" width="22.6" height="15.0" fill="rgb(234,176,30)" rx="2" ry="2" onmouseover="s('sockfs`sendit (7286 samples, 1.91%)')" onmouseout="c()" />
<rect x="63.7" y="689" width="0.6" height="15.0" fill="rgb(224,22,36)" rx="2" ry="2" onmouseover="s('unix`do_interrupt (186 samples, 0.05%)')" onmouseout="c()" />
<rect x="750.9" y="465" width="0.5" height="15.0" fill="rgb(251,27,28)" rx="2" ry="2" onmouseover="s('mac`mac_vlan_header_info (183 samples, 0.05%)')" onmouseout="c()" />
<rect x="965.5" y="689" width="1.6" height="15.0" fill="rgb(225,135,54)" rx="2" ry="2" onmouseover="s('genunix`resolvepath (509 samples, 0.13%)')" onmouseout="c()" />
<rect x="555.3" y="609" width="2.6" height="15.0" fill="rgb(221,1,11)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (835 samples, 0.22%)')" onmouseout="c()" />
<rect x="537.0" y="433" width="0.2" height="15.0" fill="rgb(215,153,4)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="963.6" y="641" width="1.8" height="15.0" fill="rgb(215,122,38)" rx="2" ry="2" onmouseover="s('genunix`vn_openat (588 samples, 0.15%)')" onmouseout="c()" />
<rect x="946.0" y="609" width="0.7" height="15.0" fill="rgb(223,49,45)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (235 samples, 0.06%)')" onmouseout="c()" />
<rect x="659.6" y="369" width="0.6" height="15.0" fill="rgb(210,220,0)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_mp (210 samples, 0.06%)')" onmouseout="c()" />
<rect x="195.8" y="545" width="0.1" height="15.0" fill="rgb(229,18,13)" rx="2" ry="2" onmouseover="s('unix`hat_page_setattr (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="658.8" y="337" width="0.4" height="15.0" fill="rgb(252,191,43)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (121 samples, 0.03%)')" onmouseout="c()" />
<rect x="32.9" y="529" width="0.2" height="15.0" fill="rgb(237,64,7)" rx="2" ry="2" onmouseover="s('unix`page_hashin (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="958.5" y="577" width="0.6" height="15.0" fill="rgb(228,218,38)" rx="2" ry="2" onmouseover="s('unix`pagefault (188 samples, 0.05%)')" onmouseout="c()" />
<rect x="1144.7" y="401" width="0.2" height="15.0" fill="rgb(252,184,49)" rx="2" ry="2" onmouseover="s('ip`ire_ftable_lookup_v4 (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="945.1" y="689" width="0.4" height="15.0" fill="rgb(234,99,22)" rx="2" ry="2" onmouseover="s('genunix`resolvepath (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="1016.1" y="657" width="0.6" height="15.0" fill="rgb(232,54,9)" rx="2" ry="2" onmouseover="s('ip`tcp_close_output (182 samples, 0.05%)')" onmouseout="c()" />
<rect x="69.2" y="609" width="0.7" height="15.0" fill="rgb(220,68,40)" rx="2" ry="2" onmouseover="s('FSS`fss_preempt (249 samples, 0.07%)')" onmouseout="c()" />
<rect x="1012.2" y="673" width="1.8" height="15.0" fill="rgb(234,220,28)" rx="2" ry="2" onmouseover="s('zfs`zio_execute (574 samples, 0.15%)')" onmouseout="c()" />
<rect x="336.3" y="641" width="1.5" height="15.0" fill="rgb(245,159,29)" rx="2" ry="2" onmouseover="s('zfs`zfs_write (501 samples, 0.13%)')" onmouseout="c()" />
<rect x="533.9" y="481" width="0.8" height="15.0" fill="rgb(219,1,22)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_impl_v4 (264 samples, 0.07%)')" onmouseout="c()" />
<rect x="536.2" y="577" width="0.2" height="15.0" fill="rgb(244,67,51)" rx="2" ry="2" onmouseover="s('ip`tcp_update_lso (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="555.7" y="577" width="2.2" height="15.0" fill="rgb(226,15,21)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (715 samples, 0.19%)')" onmouseout="c()" />
<rect x="957.3" y="545" width="0.2" height="15.0" fill="rgb(207,176,35)" rx="2" ry="2" onmouseover="s('unix`page_get_mnode_freelist (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="533.1" y="481" width="0.4" height="15.0" fill="rgb(216,153,53)" rx="2" ry="2" onmouseover="s('unix`kpreempt (122 samples, 0.03%)')" onmouseout="c()" />
<rect x="1155.2" y="625" width="0.4" height="15.0" fill="rgb(230,194,53)" rx="2" ry="2" onmouseover="s('mac`mac_strip_vlan_tag (138 samples, 0.04%)')" onmouseout="c()" />
<rect x="606.9" y="609" width="1.4" height="15.0" fill="rgb(227,82,33)" rx="2" ry="2" onmouseover="s('genunix`allocb (446 samples, 0.12%)')" onmouseout="c()" />
<rect x="1150.7" y="497" width="0.3" height="15.0" fill="rgb(248,181,54)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_data (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="1073.2" y="513" width="2.5" height="15.0" fill="rgb(240,131,3)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (797 samples, 0.21%)')" onmouseout="c()" />
<rect x="170.2" y="673" width="0.4" height="15.0" fill="rgb(246,213,27)" rx="2" ry="2" onmouseover="s('genunix`as_unmap (119 samples, 0.03%)')" onmouseout="c()" />
<rect x="962.3" y="497" width="0.2" height="15.0" fill="rgb(249,33,17)" rx="2" ry="2" onmouseover="s('unix`page_get_freelist (74 samples, 0.02%)')" onmouseout="c()" />
<rect x="677.6" y="257" width="0.8" height="15.0" fill="rgb(246,170,54)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (238 samples, 0.06%)')" onmouseout="c()" />
<rect x="315.6" y="513" width="8.6" height="15.0" fill="rgb(252,2,33)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (2777 samples, 0.73%)')" onmouseout="c()" />
<rect x="1018.9" y="593" width="8.2" height="15.0" fill="rgb(229,181,33)" rx="2" ry="2" onmouseover="s('ip`ip_set_destination_v4 (2636 samples, 0.69%)')" onmouseout="c()" />
<rect x="1013.8" y="657" width="0.1" height="15.0" fill="rgb(243,116,7)" rx="2" ry="2" onmouseover="s('zfs`zio_vdev_io_start (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="86.2" y="465" width="6.7" height="15.0" fill="rgb(213,176,29)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (2150 samples, 0.56%)')" onmouseout="c()" />
<rect x="71.9" y="625" width="0.1" height="15.0" fill="rgb(206,52,39)" rx="2" ry="2" onmouseover="s('unix`disp (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="62.1" y="657" width="0.2" height="15.0" fill="rgb(251,101,19)" rx="2" ry="2" onmouseover="s('unix`preempt (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="1031.8" y="609" width="0.2" height="15.0" fill="rgb(244,229,6)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="1081.1" y="481" width="0.8" height="15.0" fill="rgb(245,78,32)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (251 samples, 0.07%)')" onmouseout="c()" />
<rect x="670.0" y="369" width="0.3" height="15.0" fill="rgb(253,150,12)" rx="2" ry="2" onmouseover="s('ip`tcp_time_wait_append (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="1189.9" y="689" width="0.1" height="15.0" fill="rgb(235,65,51)" rx="2" ry="2" onmouseover="s('zfs`txg_sync_thread (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.7" y="273" width="0.1" height="15.0" fill="rgb(213,27,45)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook4_out (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1006.7" y="609" width="1.7" height="15.0" fill="rgb(224,178,5)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_unregister (545 samples, 0.14%)')" onmouseout="c()" />
<rect x="962.6" y="529" width="0.1" height="15.0" fill="rgb(246,137,52)" rx="2" ry="2" onmouseover="s('ufs`bmap_read (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="329.5" y="545" width="0.1" height="15.0" fill="rgb(214,22,9)" rx="2" ry="2" onmouseover="s('unix`atomic_cas_32 (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="340.0" y="625" width="0.1" height="15.0" fill="rgb(235,23,15)" rx="2" ry="2" onmouseover="s('unix`kpreempt (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="1012.6" y="625" width="0.2" height="15.0" fill="rgb(207,8,28)" rx="2" ry="2" onmouseover="s('zfs`metaslab_alloc_dva (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="330.2" y="577" width="0.2" height="15.0" fill="rgb(205,213,36)" rx="2" ry="2" onmouseover="s('ip`tcp_output (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="616.1" y="641" width="143.7" height="15.0" fill="rgb(222,229,24)" rx="2" ry="2" onmouseover="s('mac`mac_rx_ring (46361 samples, 12.17%)')" onmouseout="c()" />
<text text-anchor="" x="619.109823053231" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_ring (46361 samples, 12.17%)')" onmouseout="c()" >mac`mac_rx_ring</text>
<rect x="619.4" y="561" width="138.5" height="15.0" fill="rgb(241,160,36)" rx="2" ry="2" onmouseover="s('mac`mac_rx_srs_process (44717 samples, 11.74%)')" onmouseout="c()" />
<text text-anchor="" x="622.356884224456" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('mac`mac_rx_srs_process (44717 samples, 11.74%)')" onmouseout="c()" >mac`mac_rx_srs_p..</text>
<rect x="985.8" y="577" width="0.3" height="15.0" fill="rgb(206,200,49)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (77 samples, 0.02%)')" onmouseout="c()" />
<rect x="63.7" y="705" width="0.6" height="15.0" fill="rgb(238,57,9)" rx="2" ry="2" onmouseover="s('unix`_interrupt (186 samples, 0.05%)')" onmouseout="c()" />
<rect x="208.8" y="641" width="0.3" height="15.0" fill="rgb(244,119,30)" rx="2" ry="2" onmouseover="s('unix`kcopy (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="59.9" y="641" width="1.1" height="15.0" fill="rgb(224,155,9)" rx="2" ry="2" onmouseover="s('unix`splr (364 samples, 0.10%)')" onmouseout="c()" />
<rect x="995.7" y="689" width="0.5" height="15.0" fill="rgb(232,41,11)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (187 samples, 0.05%)')" onmouseout="c()" />
<rect x="1067.9" y="529" width="0.3" height="15.0" fill="rgb(206,109,16)" rx="2" ry="2" onmouseover="s('ip`tcp_output (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="157.3" y="657" width="0.2" height="15.0" fill="rgb(221,208,16)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="600.1" y="673" width="162.9" height="15.0" fill="rgb(211,79,34)" rx="2" ry="2" onmouseover="s('unix`av_dispatch_autovect (52579 samples, 13.81%)')" onmouseout="c()" />
<text text-anchor="" x="603.088302713149" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`av_dispatch_autovect (52579 samples, 13.81%)')" onmouseout="c()" >unix`av_dispatch_au..</text>
<rect x="204.3" y="561" width="1.0" height="15.0" fill="rgb(209,7,42)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (328 samples, 0.09%)')" onmouseout="c()" />
<rect x="1009.1" y="625" width="1.4" height="15.0" fill="rgb(214,65,1)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_callback (466 samples, 0.12%)')" onmouseout="c()" />
<rect x="965.5" y="641" width="0.3" height="15.0" fill="rgb(211,162,18)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="995.5" y="657" width="0.2" height="15.0" fill="rgb(254,143,1)" rx="2" ry="2" onmouseover="s('unix`mutex_exit (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="76.9" y="657" width="20.0" height="15.0" fill="rgb(216,161,37)" rx="2" ry="2" onmouseover="s('genunix`closeandsetf (6450 samples, 1.69%)')" onmouseout="c()" />
<rect x="993.9" y="641" width="0.2" height="15.0" fill="rgb(246,211,35)" rx="2" ry="2" onmouseover="s('unix`bcopy (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="153.1" y="609" width="0.9" height="15.0" fill="rgb(211,91,3)" rx="2" ry="2" onmouseover="s('genunix`relvm (276 samples, 0.07%)')" onmouseout="c()" />
<rect x="1050.5" y="513" width="0.4" height="15.0" fill="rgb(226,58,31)" rx="2" ry="2" onmouseover="s('ip`tcp_send_data (134 samples, 0.04%)')" onmouseout="c()" />
<rect x="990.9" y="625" width="0.2" height="15.0" fill="rgb(247,228,22)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_alloc (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="1068.4" y="449" width="0.3" height="15.0" fill="rgb(232,118,46)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (91 samples, 0.02%)')" onmouseout="c()" />
<rect x="206.5" y="673" width="3.0" height="15.0" fill="rgb(217,156,45)" rx="2" ry="2" onmouseover="s('genunix`copyin_args32 (971 samples, 0.25%)')" onmouseout="c()" />
<rect x="626.4" y="465" width="0.4" height="15.0" fill="rgb(219,121,23)" rx="2" ry="2" onmouseover="s('dls`dls_accept (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="614.7" y="593" width="0.3" height="15.0" fill="rgb(244,209,35)" rx="2" ry="2" onmouseover="s('genunix`dblk_decref (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="967.3" y="641" width="0.3" height="15.0" fill="rgb(231,48,15)" rx="2" ry="2" onmouseover="s('genunix`cstat64_32 (94 samples, 0.02%)')" onmouseout="c()" />
<rect x="175.5" y="609" width="0.2" height="15.0" fill="rgb(215,106,48)" rx="2" ry="2" onmouseover="s('specfs`spec_open (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="938.9" y="609" width="1.9" height="15.0" fill="rgb(218,116,34)" rx="2" ry="2" onmouseover="s('genunix`lookupnameatcred (610 samples, 0.16%)')" onmouseout="c()" />
<rect x="1184.0" y="641" width="0.2" height="15.0" fill="rgb(249,203,52)" rx="2" ry="2" onmouseover="s('genunix`cpu_update_pct (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="665.1" y="257" width="0.7" height="15.0" fill="rgb(233,28,4)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (241 samples, 0.06%)')" onmouseout="c()" />
<rect x="1185.9" y="641" width="0.7" height="15.0" fill="rgb(206,66,44)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (219 samples, 0.06%)')" onmouseout="c()" />
<rect x="648.5" y="369" width="5.5" height="15.0" fill="rgb(217,7,45)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (1777 samples, 0.47%)')" onmouseout="c()" />
<rect x="181.6" y="609" width="12.5" height="15.0" fill="rgb(230,172,46)" rx="2" ry="2" onmouseover="s('genunix`queue_service (4044 samples, 1.06%)')" onmouseout="c()" />
<rect x="75.8" y="609" width="0.1" height="15.0" fill="rgb(205,131,19)" rx="2" ry="2" onmouseover="s('genunix`segvn_extend_prev (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="741.9" y="289" width="0.5" height="15.0" fill="rgb(235,75,51)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (160 samples, 0.04%)')" onmouseout="c()" />
<rect x="676.3" y="273" width="0.2" height="15.0" fill="rgb(231,109,24)" rx="2" ry="2" onmouseover="s('FSS`fss_wakeup (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="675.7" y="337" width="0.2" height="15.0" fill="rgb(250,45,37)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="985.6" y="593" width="2.9" height="15.0" fill="rgb(212,12,50)" rx="2" ry="2" onmouseover="s('unix`disp (922 samples, 0.24%)')" onmouseout="c()" />
<rect x="668.6" y="257" width="0.6" height="15.0" fill="rgb(227,114,46)" rx="2" ry="2" onmouseover="s('ipf`fr_check (173 samples, 0.05%)')" onmouseout="c()" />
<rect x="132.7" y="625" width="0.1" height="15.0" fill="rgb(212,47,21)" rx="2" ry="2" onmouseover="s('genunix`crgetmapped (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="172.0" y="641" width="0.9" height="15.0" fill="rgb(247,135,43)" rx="2" ry="2" onmouseover="s('unix`swtch (291 samples, 0.08%)')" onmouseout="c()" />
<rect x="133.9" y="593" width="5.1" height="15.0" fill="rgb(232,99,0)" rx="2" ry="2" onmouseover="s('sockfs`so_recvmsg (1649 samples, 0.43%)')" onmouseout="c()" />
<rect x="218.2" y="689" width="122.5" height="15.0" fill="rgb(228,119,26)" rx="2" ry="2" onmouseover="s('genunix`write32 (39539 samples, 10.38%)')" onmouseout="c()" />
<text text-anchor="" x="221.214699264013" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('genunix`write32 (39539 samples, 10.38%)')" onmouseout="c()" >genunix`write3..</text>
<rect x="78.4" y="529" width="16.1" height="15.0" fill="rgb(210,93,32)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (5178 samples, 1.36%)')" onmouseout="c()" />
<rect x="153.6" y="529" width="0.2" height="15.0" fill="rgb(220,189,39)" rx="2" ry="2" onmouseover="s('unix`hment_remove (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="786.9" y="641" width="0.3" height="15.0" fill="rgb(231,200,53)" rx="2" ry="2" onmouseover="s('intpexec`intpexec (109 samples, 0.03%)')" onmouseout="c()" />
<rect x="53.8" y="705" width="1.3" height="15.0" fill="rgb(242,148,11)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb800ca0 (399 samples, 0.10%)')" onmouseout="c()" />
<rect x="662.0" y="305" width="0.2" height="15.0" fill="rgb(214,48,31)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="76.9" y="673" width="20.1" height="15.0" fill="rgb(238,137,32)" rx="2" ry="2" onmouseover="s('genunix`close (6488 samples, 1.70%)')" onmouseout="c()" />
<rect x="55.9" y="689" width="0.1" height="15.0" fill="rgb(250,99,33)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (48 samples, 0.01%)')" onmouseout="c()" />
<rect x="1068.5" y="417" width="0.1" height="15.0" fill="rgb(233,173,10)" rx="2" ry="2" onmouseover="s('mac`mac_tx (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="336.7" y="577" width="0.1" height="15.0" fill="rgb(217,2,19)" rx="2" ry="2" onmouseover="s('zfs`dnode_hold (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1076.1" y="529" width="0.3" height="15.0" fill="rgb(234,175,48)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout_cancel (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="924.7" y="609" width="0.2" height="15.0" fill="rgb(221,92,15)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb85a6ea (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="595.6" y="689" width="0.8" height="15.0" fill="rgb(254,154,19)" rx="2" ry="2" onmouseover="s('unix`hat_switch (250 samples, 0.07%)')" onmouseout="c()" />
<rect x="1144.5" y="433" width="0.1" height="15.0" fill="rgb(206,118,7)" rx="2" ry="2" onmouseover="s('ip`ip_select_src_ill (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="155.3" y="577" width="0.1" height="15.0" fill="rgb(249,218,27)" rx="2" ry="2" onmouseover="s('genunix`relvm (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="953.4" y="673" width="0.6" height="15.0" fill="rgb(236,72,35)" rx="2" ry="2" onmouseover="s('genunix`cfork (194 samples, 0.05%)')" onmouseout="c()" />
<rect x="1018.7" y="657" width="8.5" height="15.0" fill="rgb(217,170,10)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (2744 samples, 0.72%)')" onmouseout="c()" />
<rect x="97.0" y="657" width="2.4" height="15.0" fill="rgb(226,194,9)" rx="2" ry="2" onmouseover="s('genunix`openat64 (775 samples, 0.20%)')" onmouseout="c()" />
<rect x="1027.9" y="657" width="4.3" height="15.0" fill="rgb(205,215,1)" rx="2" ry="2" onmouseover="s('ip`tcp_timer_handler (1369 samples, 0.36%)')" onmouseout="c()" />
<rect x="963.0" y="561" width="0.1" height="15.0" fill="rgb(247,30,24)" rx="2" ry="2" onmouseover="s('ufs`ufs_map (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="660.9" y="337" width="0.2" height="15.0" fill="rgb(235,174,19)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_connected (73 samples, 0.02%)')" onmouseout="c()" />
<rect x="154.4" y="625" width="0.1" height="15.0" fill="rgb(241,75,16)" rx="2" ry="2" onmouseover="s('elfexec`mapelfexec (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="10.8" y="705" width="0.8" height="15.0" fill="rgb(248,103,18)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (275 samples, 0.07%)')" onmouseout="c()" />
<rect x="10.0" y="721" width="1180.0" height="15.0" fill="rgb(223,78,33)" rx="2" ry="2" onmouseover="s('all samples (380849 samples, 100%)')" onmouseout="c()" />
<text text-anchor="" x="13" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('all samples (380849 samples, 100%)')" onmouseout="c()" ></text>
<rect x="557.4" y="513" width="0.3" height="15.0" fill="rgb(243,72,53)" rx="2" ry="2" onmouseover="s('ipf`fr_check (92 samples, 0.02%)')" onmouseout="c()" />
<rect x="1016.3" y="625" width="0.3" height="15.0" fill="rgb(214,84,12)" rx="2" ry="2" onmouseover="s('ip`dce_update_uinfo_v4 (110 samples, 0.03%)')" onmouseout="c()" />
<rect x="168.0" y="577" width="0.8" height="15.0" fill="rgb(243,167,29)" rx="2" ry="2" onmouseover="s('genunix`avl_walk (258 samples, 0.07%)')" onmouseout="c()" />
<rect x="51.7" y="673" width="1.5" height="15.0" fill="rgb(214,13,21)" rx="2" ry="2" onmouseover="s('unix`lock_try (487 samples, 0.13%)')" onmouseout="c()" />
<rect x="577.7" y="625" width="0.6" height="15.0" fill="rgb(253,227,10)" rx="2" ry="2" onmouseover="s('ip`squeue_synch_enter (210 samples, 0.06%)')" onmouseout="c()" />
<rect x="1080.6" y="529" width="0.3" height="15.0" fill="rgb(238,122,40)" rx="2" ry="2" onmouseover="s('ip`ixa_safe_copy (101 samples, 0.03%)')" onmouseout="c()" />
<rect x="554.1" y="577" width="0.2" height="15.0" fill="rgb(248,191,23)" rx="2" ry="2" onmouseover="s('ip`ire_send_local_v4 (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="1051.1" y="497" width="0.3" height="15.0" fill="rgb(223,199,13)" rx="2" ry="2" onmouseover="s('ip`tcp_send (106 samples, 0.03%)')" onmouseout="c()" />
<rect x="959.1" y="625" width="4.1" height="15.0" fill="rgb(248,201,1)" rx="2" ry="2" onmouseover="s('unix`mmapobj_map_ptload (1335 samples, 0.35%)')" onmouseout="c()" />
<rect x="1182.6" y="657" width="0.1" height="15.0" fill="rgb(249,221,35)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="754.6" y="433" width="0.1" height="15.0" fill="rgb(240,25,41)" rx="2" ry="2" onmouseover="s('unix`cpu_wakeup_mwait (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1185.0" y="657" width="2.1" height="15.0" fill="rgb(216,145,11)" rx="2" ry="2" onmouseover="s('unix`disp (678 samples, 0.18%)')" onmouseout="c()" />
<rect x="317.4" y="433" width="0.3" height="15.0" fill="rgb(243,26,20)" rx="2" ry="2" onmouseover="s('mac`mac_client_check_flow_vid (89 samples, 0.02%)')" onmouseout="c()" />
<rect x="770.5" y="561" width="1.7" height="15.0" fill="rgb(254,117,49)" rx="2" ry="2" onmouseover="s('genunix`setrun (541 samples, 0.14%)')" onmouseout="c()" />
<rect x="319.7" y="337" width="0.1" height="15.0" fill="rgb(222,178,51)" rx="2" ry="2" onmouseover="s('rootnex`rootnex_get_sgl (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="619.1" y="529" width="0.1" height="15.0" fill="rgb(245,134,32)" rx="2" ry="2" onmouseover="s('unix`bcmp (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="347.1" y="577" width="188.5" height="15.0" fill="rgb(221,96,20)" rx="2" ry="2" onmouseover="s('ip`conn_connect (60859 samples, 15.98%)')" onmouseout="c()" />
<text text-anchor="" x="350.056103600115" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`conn_connect (60859 samples, 15.98%)')" onmouseout="c()" >ip`conn_connect</text>
<rect x="665.9" y="273" width="2.4" height="15.0" fill="rgb(219,39,15)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (763 samples, 0.20%)')" onmouseout="c()" />
<rect x="538.9" y="545" width="1.6" height="15.0" fill="rgb(216,224,28)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (518 samples, 0.14%)')" onmouseout="c()" />
<rect x="942.9" y="657" width="0.7" height="15.0" fill="rgb(228,66,19)" rx="2" ry="2" onmouseover="s('unix`mmapobj_map_interpret (243 samples, 0.06%)')" onmouseout="c()" />
<rect x="940.6" y="561" width="0.1" height="15.0" fill="rgb(210,166,30)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="109.9" y="657" width="32.3" height="15.0" fill="rgb(246,121,4)" rx="2" ry="2" onmouseover="s('genunix`read (10418 samples, 2.74%)')" onmouseout="c()" />
<rect x="627.0" y="465" width="0.9" height="15.0" fill="rgb(231,179,52)" rx="2" ry="2" onmouseover="s('genunix`mod_hash_find_cb_rval (295 samples, 0.08%)')" onmouseout="c()" />
<rect x="772.9" y="593" width="0.2" height="15.0" fill="rgb(254,66,51)" rx="2" ry="2" onmouseover="s('genunix`callout_downheap (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="215.6" y="609" width="0.2" height="15.0" fill="rgb(243,229,19)" rx="2" ry="2" onmouseover="s('unix`splr (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1144.3" y="449" width="0.1" height="15.0" fill="rgb(218,105,36)" rx="2" ry="2" onmouseover="s('unix`rw_exit_wakeup (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1052.0" y="449" width="0.2" height="15.0" fill="rgb(241,187,11)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="662.9" y="337" width="0.1" height="15.0" fill="rgb(222,32,33)" rx="2" ry="2" onmouseover="s('ip`tcp_maxpsz_set (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="31.7" y="577" width="2.0" height="15.0" fill="rgb(237,64,0)" rx="2" ry="2" onmouseover="s('genunix`swap_getpage (646 samples, 0.17%)')" onmouseout="c()" />
<rect x="1013.2" y="609" width="0.2" height="15.0" fill="rgb(233,47,44)" rx="2" ry="2" onmouseover="s('zfs`zio_write_bp_init (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1011.2" y="609" width="0.7" height="15.0" fill="rgb(247,65,46)" rx="2" ry="2" onmouseover="s('unix`disp_getbest (227 samples, 0.06%)')" onmouseout="c()" />
<rect x="199.3" y="673" width="6.3" height="15.0" fill="rgb(253,226,47)" rx="2" ry="2" onmouseover="s('genunix`fstatat64_32 (2060 samples, 0.54%)')" onmouseout="c()" />
<rect x="25.8" y="561" width="0.1" height="15.0" fill="rgb(227,125,8)" rx="2" ry="2" onmouseover="s('unix`hat_pte_unmap (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="954.0" y="689" width="3.9" height="15.0" fill="rgb(241,93,7)" rx="2" ry="2" onmouseover="s('genunix`memcntl (1234 samples, 0.32%)')" onmouseout="c()" />
<rect x="167.9" y="593" width="1.1" height="15.0" fill="rgb(252,45,48)" rx="2" ry="2" onmouseover="s('unix`header_kstat_update (370 samples, 0.10%)')" onmouseout="c()" />
<rect x="608.3" y="609" width="0.9" height="15.0" fill="rgb(228,126,38)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (290 samples, 0.08%)')" onmouseout="c()" />
<rect x="1029.0" y="577" width="2.7" height="15.0" fill="rgb(249,207,4)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (854 samples, 0.22%)')" onmouseout="c()" />
<rect x="1077.0" y="481" width="1.4" height="15.0" fill="rgb(216,112,30)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (455 samples, 0.12%)')" onmouseout="c()" />
<rect x="641.3" y="385" width="0.2" height="15.0" fill="rgb(210,204,27)" rx="2" ry="2" onmouseover="s('unix`rw_exit (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="1078.4" y="497" width="0.1" height="15.0" fill="rgb(220,152,0)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="681.4" y="305" width="60.5" height="15.0" fill="rgb(235,158,4)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (19543 samples, 5.13%)')" onmouseout="c()" />
<text text-anchor="" x="684.351585536525" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`dce_lookup_and_add_v4 (19543 samples, 5.13%)')" onmouseout="c()" >ip`dce_..</text>
<rect x="1029.4" y="513" width="0.4" height="15.0" fill="rgb(249,7,5)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (137 samples, 0.04%)')" onmouseout="c()" />
<rect x="39.2" y="545" width="0.1" height="15.0" fill="rgb(241,97,31)" rx="2" ry="2" onmouseover="s('unix`htable_alloc (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="32.6" y="481" width="0.2" height="15.0" fill="rgb(220,107,20)" rx="2" ry="2" onmouseover="s('unix`page_ctr_sub_internal (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="1037.4" y="609" width="0.3" height="15.0" fill="rgb(211,164,12)" rx="2" ry="2" onmouseover="s('genunix`i_mod_hash_find_nosync (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="335.9" y="641" width="0.3" height="15.0" fill="rgb(247,33,29)" rx="2" ry="2" onmouseover="s('unix`bzero (104 samples, 0.03%)')" onmouseout="c()" />
<rect x="993.4" y="625" width="0.3" height="15.0" fill="rgb(219,69,14)" rx="2" ry="2" onmouseover="s('genunix`kmem_zalloc (117 samples, 0.03%)')" onmouseout="c()" />
<rect x="554.1" y="641" width="0.3" height="15.0" fill="rgb(223,73,27)" rx="2" ry="2" onmouseover="s('sockfs`socket_sendmsg (96 samples, 0.03%)')" onmouseout="c()" />
<rect x="1028.3" y="609" width="0.2" height="15.0" fill="rgb(252,8,45)" rx="2" ry="2" onmouseover="s('genunix`allocb (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="600.5" y="577" width="0.1" height="15.0" fill="rgb(246,50,14)" rx="2" ry="2" onmouseover="s('mac`mac_rx_common (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="50.5" y="657" width="1.0" height="15.0" fill="rgb(234,93,3)" rx="2" ry="2" onmouseover="s('unix`splr (328 samples, 0.09%)')" onmouseout="c()" />
<rect x="137.4" y="529" width="0.2" height="15.0" fill="rgb(233,201,5)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree_oversize (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="783.0" y="561" width="0.2" height="15.0" fill="rgb(224,225,18)" rx="2" ry="2" onmouseover="s('genunix`clock_tick (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="155.3" y="545" width="0.1" height="15.0" fill="rgb(215,78,53)" rx="2" ry="2" onmouseover="s('genunix`segvn_unmap (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="56.6" y="673" width="5.9" height="15.0" fill="rgb(228,106,18)" rx="2" ry="2" onmouseover="s('genunix`post_syscall (1919 samples, 0.50%)')" onmouseout="c()" />
<rect x="1068.5" y="433" width="0.1" height="15.0" fill="rgb(227,103,11)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="157.1" y="673" width="0.4" height="15.0" fill="rgb(248,25,53)" rx="2" ry="2" onmouseover="s('genunix`cstat32 (126 samples, 0.03%)')" onmouseout="c()" />
<rect x="40.8" y="545" width="0.1" height="15.0" fill="rgb(249,160,30)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="153.2" y="561" width="0.2" height="15.0" fill="rgb(234,83,21)" rx="2" ry="2" onmouseover="s('genunix`seg_free (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="1158.9" y="497" width="0.2" height="15.0" fill="rgb(252,213,37)" rx="2" ry="2" onmouseover="s('ip`tcp_input_listener (75 samples, 0.02%)')" onmouseout="c()" />
<rect x="553.7" y="593" width="0.1" height="15.0" fill="rgb(232,25,13)" rx="2" ry="2" onmouseover="s('genunix`uiomove (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="746.0" y="417" width="0.1" height="15.0" fill="rgb(234,34,31)" rx="2" ry="2" onmouseover="s('ip`ip_squeue_get (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="925.1" y="593" width="5.7" height="15.0" fill="rgb(234,134,26)" rx="2" ry="2" onmouseover="s('unix`xc_sync (1824 samples, 0.48%)')" onmouseout="c()" />
<rect x="65.1" y="689" width="2.4" height="15.0" fill="rgb(234,139,12)" rx="2" ry="2" onmouseover="s('genunix`restorectx (763 samples, 0.20%)')" onmouseout="c()" />
<rect x="328.5" y="289" width="0.3" height="15.0" fill="rgb(209,0,42)" rx="2" ry="2" onmouseover="s('unix`hat_getpfnum (75 samples, 0.02%)')" onmouseout="c()" />
<rect x="1018.2" y="609" width="0.2" height="15.0" fill="rgb(234,222,0)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="132.4" y="609" width="0.2" height="15.0" fill="rgb(236,187,26)" rx="2" ry="2" onmouseover="s('genunix`uiomove (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="552.7" y="641" width="1.2" height="15.0" fill="rgb(248,220,15)" rx="2" ry="2" onmouseover="s('sockfs`so_recvmsg (407 samples, 0.11%)')" onmouseout="c()" />
<rect x="50.3" y="673" width="1.2" height="15.0" fill="rgb(249,224,11)" rx="2" ry="2" onmouseover="s('genunix`thread_lock (400 samples, 0.11%)')" onmouseout="c()" />
<rect x="677.1" y="337" width="1.8" height="15.0" fill="rgb(232,37,49)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_data (585 samples, 0.15%)')" onmouseout="c()" />
<rect x="1075.0" y="401" width="0.1" height="15.0" fill="rgb(237,12,16)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="938.4" y="673" width="0.1" height="15.0" fill="rgb(218,133,51)" rx="2" ry="2" onmouseover="s('unix`mutex_exit (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="166.7" y="593" width="0.3" height="15.0" fill="rgb(235,48,9)" rx="2" ry="2" onmouseover="s('unix`0xfffffffffb85a6ea (105 samples, 0.03%)')" onmouseout="c()" />
<rect x="332.9" y="561" width="0.6" height="15.0" fill="rgb(225,190,44)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_alloc (188 samples, 0.05%)')" onmouseout="c()" />
<rect x="557.3" y="561" width="0.4" height="15.0" fill="rgb(237,120,45)" rx="2" ry="2" onmouseover="s('hook`hook_run (145 samples, 0.04%)')" onmouseout="c()" />
<rect x="537.3" y="449" width="1.2" height="15.0" fill="rgb(215,50,8)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (395 samples, 0.10%)')" onmouseout="c()" />
<rect x="946.0" y="625" width="0.7" height="15.0" fill="rgb(238,184,30)" rx="2" ry="2" onmouseover="s('genunix`lookupnameat (235 samples, 0.06%)')" onmouseout="c()" />
<rect x="328.0" y="433" width="0.2" height="15.0" fill="rgb(250,36,28)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1044.1" y="481" width="0.1" height="15.0" fill="rgb(249,196,5)" rx="2" ry="2" onmouseover="s('unix`setbackdq (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="675.7" y="353" width="0.2" height="15.0" fill="rgb(245,47,54)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="753.3" y="465" width="0.2" height="15.0" fill="rgb(227,45,42)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_exit (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="1048.5" y="545" width="0.2" height="15.0" fill="rgb(224,132,17)" rx="2" ry="2" onmouseover="s('ip`ip_recv_attr_to_mblk (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="554.4" y="689" width="22.7" height="15.0" fill="rgb(247,54,41)" rx="2" ry="2" onmouseover="s('sockfs`sendmsg (7316 samples, 1.92%)')" onmouseout="c()" />
<rect x="1014.1" y="673" width="1.2" height="15.0" fill="rgb(240,170,43)" rx="2" ry="2" onmouseover="s('genunix`cv_wait (375 samples, 0.10%)')" onmouseout="c()" />
<rect x="945.5" y="593" width="0.2" height="15.0" fill="rgb(248,21,1)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="673.6" y="241" width="0.5" height="15.0" fill="rgb(226,0,5)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (168 samples, 0.04%)')" onmouseout="c()" />
<rect x="196.2" y="545" width="0.4" height="15.0" fill="rgb(219,204,31)" rx="2" ry="2" onmouseover="s('genunix`avl_find (139 samples, 0.04%)')" onmouseout="c()" />
<rect x="743.6" y="369" width="0.8" height="15.0" fill="rgb(227,10,4)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple (277 samples, 0.07%)')" onmouseout="c()" />
<rect x="744.7" y="353" width="0.3" height="15.0" fill="rgb(223,109,45)" rx="2" ry="2" onmouseover="s('sockfs`so_queue_msg_impl (113 samples, 0.03%)')" onmouseout="c()" />
<rect x="1146.1" y="513" width="4.3" height="15.0" fill="rgb(217,33,35)" rx="2" ry="2" onmouseover="s('ip`ip_output_simple_v4 (1413 samples, 0.37%)')" onmouseout="c()" />
<rect x="69.1" y="625" width="2.2" height="15.0" fill="rgb(221,167,23)" rx="2" ry="2" onmouseover="s('unix`preempt (685 samples, 0.18%)')" onmouseout="c()" />
<rect x="1157.3" y="641" width="0.1" height="15.0" fill="rgb(207,141,20)" rx="2" ry="2" onmouseover="s('mac`mac_protect_intercept_dhcp_one (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="669.0" y="241" width="0.2" height="15.0" fill="rgb(216,226,37)" rx="2" ry="2" onmouseover="s('ipf`fr_makefrip (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="42.2" y="609" width="0.1" height="15.0" fill="rgb(238,62,53)" rx="2" ry="2" onmouseover="s('unix`htable_getpage (49 samples, 0.01%)')" onmouseout="c()" />
<rect x="31.1" y="593" width="0.4" height="15.0" fill="rgb(227,147,3)" rx="2" ry="2" onmouseover="s('genunix`anon_alloc (115 samples, 0.03%)')" onmouseout="c()" />
<rect x="78.1" y="561" width="16.5" height="15.0" fill="rgb(214,167,39)" rx="2" ry="2" onmouseover="s('ip`tcp_close (5335 samples, 1.40%)')" onmouseout="c()" />
<rect x="539.0" y="513" width="0.5" height="15.0" fill="rgb(219,199,40)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check (163 samples, 0.04%)')" onmouseout="c()" />
<rect x="96.2" y="593" width="0.2" height="15.0" fill="rgb(244,100,35)" rx="2" ry="2" onmouseover="s('genunix`port_pcache_remove_fd (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="83.2" y="401" width="1.5" height="15.0" fill="rgb(212,83,49)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (462 samples, 0.12%)')" onmouseout="c()" />
<rect x="211.7" y="657" width="0.2" height="15.0" fill="rgb(252,192,6)" rx="2" ry="2" onmouseover="s('unix`tsc_gethrtimeunscaled (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="1160.2" y="433" width="2.6" height="15.0" fill="rgb(247,71,50)" rx="2" ry="2" onmouseover="s('ip`dce_lookup_and_add_v4 (825 samples, 0.22%)')" onmouseout="c()" />
<rect x="1016.2" y="625" width="0.1" height="15.0" fill="rgb(253,21,41)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="38.3" y="577" width="1.1" height="15.0" fill="rgb(226,192,51)" rx="2" ry="2" onmouseover="s('unix`hati_load_common (343 samples, 0.09%)')" onmouseout="c()" />
<rect x="312.0" y="417" width="0.2" height="15.0" fill="rgb(227,56,40)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="1011.3" y="577" width="0.6" height="15.0" fill="rgb(231,10,43)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl_spin (206 samples, 0.05%)')" onmouseout="c()" />
<rect x="320.3" y="385" width="0.2" height="15.0" fill="rgb(207,213,38)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_check_acc_handle (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="982.1" y="593" width="0.1" height="15.0" fill="rgb(212,57,26)" rx="2" ry="2" onmouseover="s('genunix`sleepq_insert (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="673.3" y="305" width="2.3" height="15.0" fill="rgb(217,199,39)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (720 samples, 0.19%)')" onmouseout="c()" />
<rect x="674.2" y="225" width="0.9" height="15.0" fill="rgb(246,186,29)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (295 samples, 0.08%)')" onmouseout="c()" />
<rect x="199.1" y="641" width="0.1" height="15.0" fill="rgb(234,154,19)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="539.7" y="465" width="0.5" height="15.0" fill="rgb(215,54,31)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (173 samples, 0.05%)')" onmouseout="c()" />
<rect x="165.0" y="545" width="0.9" height="15.0" fill="rgb(212,45,47)" rx="2" ry="2" onmouseover="s('genunix`vmem_xalloc (272 samples, 0.07%)')" onmouseout="c()" />
<rect x="83.5" y="369" width="1.0" height="15.0" fill="rgb(210,12,40)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (321 samples, 0.08%)')" onmouseout="c()" />
<rect x="757.9" y="561" width="0.7" height="15.0" fill="rgb(246,92,51)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (225 samples, 0.06%)')" onmouseout="c()" />
<rect x="941.3" y="657" width="1.6" height="15.0" fill="rgb(208,130,12)" rx="2" ry="2" onmouseover="s('genunix`segvn_faulta (493 samples, 0.13%)')" onmouseout="c()" />
<rect x="614.8" y="577" width="0.1" height="15.0" fill="rgb(223,11,34)" rx="2" ry="2" onmouseover="s('genunix`kmem_cache_free (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="136.7" y="497" width="0.5" height="15.0" fill="rgb(209,123,42)" rx="2" ry="2" onmouseover="s('genunix`desballoc (140 samples, 0.04%)')" onmouseout="c()" />
<rect x="155.1" y="593" width="0.1" height="15.0" fill="rgb(231,129,48)" rx="2" ry="2" onmouseover="s('elfexec`mapelfexec (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="67.3" y="657" width="0.1" height="15.0" fill="rgb(213,82,25)" rx="2" ry="2" onmouseover="s('unix`gdt_update_usegd (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="39.9" y="609" width="2.2" height="15.0" fill="rgb(206,142,9)" rx="2" ry="2" onmouseover="s('unix`hat_memload (720 samples, 0.19%)')" onmouseout="c()" />
<rect x="552.3" y="657" width="0.2" height="15.0" fill="rgb(218,6,12)" rx="2" ry="2" onmouseover="s('sockfs`getsonode (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="539.0" y="497" width="0.5" height="15.0" fill="rgb(231,40,4)" rx="2" ry="2" onmouseover="s('mac`mac_protect_check_one (142 samples, 0.04%)')" onmouseout="c()" />
<rect x="80.1" y="481" width="0.2" height="15.0" fill="rgb(230,135,13)" rx="2" ry="2" onmouseover="s('ip`tcp_ack_timer (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="596.5" y="689" width="166.6" height="15.0" fill="rgb(209,72,53)" rx="2" ry="2" onmouseover="s('unix`dispatch_hardint (53780 samples, 14.12%)')" onmouseout="c()" />
<text text-anchor="" x="599.491129030141" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('unix`dispatch_hardint (53780 samples, 14.12%)')" onmouseout="c()" >unix`dispatch_hardi..</text>
<rect x="300.0" y="657" width="0.4" height="15.0" fill="rgb(210,143,39)" rx="2" ry="2" onmouseover="s('genunix`fop_rwunlock (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="942.9" y="689" width="0.7" height="15.0" fill="rgb(250,154,24)" rx="2" ry="2" onmouseover="s('genunix`mmapobjsys (246 samples, 0.06%)')" onmouseout="c()" />
<rect x="584.8" y="657" width="0.2" height="15.0" fill="rgb(225,188,15)" rx="2" ry="2" onmouseover="s('sockfs`solookup (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="159.4" y="641" width="10.6" height="15.0" fill="rgb(225,13,45)" rx="2" ry="2" onmouseover="s('genunix`cdev_ioctl (3399 samples, 0.89%)')" onmouseout="c()" />
<rect x="197.6" y="673" width="0.4" height="15.0" fill="rgb(232,179,32)" rx="2" ry="2" onmouseover="s('genunix`smmap_common (131 samples, 0.03%)')" onmouseout="c()" />
<rect x="662.0" y="289" width="0.2" height="15.0" fill="rgb(249,128,40)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="25.8" y="593" width="0.2" height="15.0" fill="rgb(210,16,6)" rx="2" ry="2" onmouseover="s('unix`hat_unload (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="318.5" y="449" width="2.9" height="15.0" fill="rgb(229,173,21)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (937 samples, 0.25%)')" onmouseout="c()" />
<rect x="942.4" y="625" width="0.5" height="15.0" fill="rgb(254,67,51)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage (143 samples, 0.04%)')" onmouseout="c()" />
<rect x="70.9" y="577" width="0.2" height="15.0" fill="rgb(254,212,46)" rx="2" ry="2" onmouseover="s('genunix`savectx (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="216.5" y="593" width="0.1" height="15.0" fill="rgb(249,27,21)" rx="2" ry="2" onmouseover="s('unix`cmt_balance (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="157.6" y="689" width="0.7" height="15.0" fill="rgb(246,50,29)" rx="2" ry="2" onmouseover="s('genunix`fstat64_32 (218 samples, 0.06%)')" onmouseout="c()" />
<rect x="760.5" y="641" width="0.2" height="15.0" fill="rgb(214,77,4)" rx="2" ry="2" onmouseover="s('unix`mutex_exit (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="1081.4" y="401" width="0.2" height="15.0" fill="rgb(253,128,8)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (81 samples, 0.02%)')" onmouseout="c()" />
<rect x="964.1" y="625" width="0.1" height="15.0" fill="rgb(253,11,43)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="328.0" y="449" width="1.0" height="15.0" fill="rgb(208,3,49)" rx="2" ry="2" onmouseover="s('mac`mac_tx (332 samples, 0.09%)')" onmouseout="c()" />
<rect x="637.5" y="337" width="0.2" height="15.0" fill="rgb(233,191,11)" rx="2" ry="2" onmouseover="s('unix`strncpy (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="137.4" y="497" width="0.2" height="15.0" fill="rgb(218,78,41)" rx="2" ry="2" onmouseover="s('genunix`vmem_free (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="654.7" y="225" width="0.2" height="15.0" fill="rgb(205,5,23)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="667.3" y="209" width="0.4" height="15.0" fill="rgb(237,191,27)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (119 samples, 0.03%)')" onmouseout="c()" />
<rect x="670.3" y="369" width="0.7" height="15.0" fill="rgb(216,23,28)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (209 samples, 0.05%)')" onmouseout="c()" />
<rect x="654.5" y="305" width="0.5" height="15.0" fill="rgb(216,205,31)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (161 samples, 0.04%)')" onmouseout="c()" />
<rect x="153.2" y="513" width="0.1" height="15.0" fill="rgb(217,132,20)" rx="2" ry="2" onmouseover="s('genunix`anon_decref (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="676.3" y="321" width="0.2" height="15.0" fill="rgb(231,3,25)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (60 samples, 0.02%)')" onmouseout="c()" />
<rect x="537.0" y="577" width="1.6" height="15.0" fill="rgb(213,4,33)" rx="2" ry="2" onmouseover="s('ip`ire_send_local_v4 (517 samples, 0.14%)')" onmouseout="c()" />
<rect x="1144.5" y="449" width="0.4" height="15.0" fill="rgb(223,223,53)" rx="2" ry="2" onmouseover="s('ip`ip_select_route (156 samples, 0.04%)')" onmouseout="c()" />
<rect x="1158.1" y="513" width="0.4" height="15.0" fill="rgb(229,43,39)" rx="2" ry="2" onmouseover="s('ipf`fr_check (129 samples, 0.03%)')" onmouseout="c()" />
<rect x="194.8" y="465" width="0.2" height="15.0" fill="rgb(228,53,21)" rx="2" ry="2" onmouseover="s('unix`page_free (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="99.4" y="673" width="42.8" height="15.0" fill="rgb(223,99,23)" rx="2" ry="2" onmouseover="s('genunix`read32 (13788 samples, 3.62%)')" onmouseout="c()" />
<rect x="1016.2" y="593" width="0.1" height="15.0" fill="rgb(211,72,38)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="785.8" y="673" width="1.5" height="15.0" fill="rgb(242,189,4)" rx="2" ry="2" onmouseover="s('genunix`exec_common (494 samples, 0.13%)')" onmouseout="c()" />
<rect x="963.5" y="641" width="0.1" height="15.0" fill="rgb(242,201,29)" rx="2" ry="2" onmouseover="s('genunix`fop_open (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1144.4" y="465" width="0.6" height="15.0" fill="rgb(222,60,8)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_v4 (163 samples, 0.04%)')" onmouseout="c()" />
<rect x="205.3" y="561" width="0.1" height="15.0" fill="rgb(253,82,38)" rx="2" ry="2" onmouseover="s('genunix`traverse (33 samples, 0.01%)')" onmouseout="c()" />
<rect x="171.7" y="625" width="0.1" height="15.0" fill="rgb(247,132,43)" rx="2" ry="2" onmouseover="s('genunix`callout_heap_insert (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="537.0" y="529" width="0.2" height="15.0" fill="rgb(246,133,26)" rx="2" ry="2" onmouseover="s('ip`squeue_drain (79 samples, 0.02%)')" onmouseout="c()" />
<rect x="578.0" y="593" width="0.3" height="15.0" fill="rgb(227,8,39)" rx="2" ry="2" onmouseover="s('unix`swtch (88 samples, 0.02%)')" onmouseout="c()" />
<rect x="787.0" y="529" width="0.2" height="15.0" fill="rgb(239,3,11)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="173.9" y="641" width="7.2" height="15.0" fill="rgb(247,191,7)" rx="2" ry="2" onmouseover="s('genunix`vn_openat (2325 samples, 0.61%)')" onmouseout="c()" />
<rect x="1042.4" y="497" width="0.4" height="15.0" fill="rgb(242,147,24)" rx="2" ry="2" onmouseover="s('genunix`turnstile_block (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="533.6" y="529" width="0.1" height="15.0" fill="rgb(209,99,17)" rx="2" ry="2" onmouseover="s('ip`ip_get_pmtu (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="556.5" y="497" width="0.6" height="15.0" fill="rgb(241,204,43)" rx="2" ry="2" onmouseover="s('mac`mac_hwring_tx (205 samples, 0.05%)')" onmouseout="c()" />
<rect x="133.5" y="609" width="5.9" height="15.0" fill="rgb(254,7,48)" rx="2" ry="2" onmouseover="s('sockfs`socket_recvmsg (1881 samples, 0.49%)')" onmouseout="c()" />
<rect x="196.2" y="529" width="0.4" height="15.0" fill="rgb(251,168,50)" rx="2" ry="2" onmouseover="s('unix`hment_compare (130 samples, 0.03%)')" onmouseout="c()" />
<rect x="1039.3" y="561" width="6.8" height="15.0" fill="rgb(252,174,13)" rx="2" ry="2" onmouseover="s('ipf`ipf_hook (2181 samples, 0.57%)')" onmouseout="c()" />
<rect x="659.3" y="353" width="0.2" height="15.0" fill="rgb(226,107,12)" rx="2" ry="2" onmouseover="s('genunix`dblk_lastfree (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="1182.5" y="673" width="0.5" height="15.0" fill="rgb(227,145,25)" rx="2" ry="2" onmouseover="s('unix`idle_exit (145 samples, 0.04%)')" onmouseout="c()" />
<rect x="1068.3" y="481" width="0.4" height="15.0" fill="rgb(231,48,14)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (116 samples, 0.03%)')" onmouseout="c()" />
<rect x="554.1" y="561" width="0.2" height="15.0" fill="rgb(206,219,41)" rx="2" ry="2" onmouseover="s('ip`ip_fanout_v4 (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="605.3" y="625" width="0.9" height="15.0" fill="rgb(208,27,14)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_rx_bind (266 samples, 0.07%)')" onmouseout="c()" />
<rect x="643.5" y="385" width="0.2" height="15.0" fill="rgb(230,176,35)" rx="2" ry="2" onmouseover="s('ip`ip_squeue_random (78 samples, 0.02%)')" onmouseout="c()" />
<rect x="95.2" y="593" width="0.3" height="15.0" fill="rgb(239,115,31)" rx="2" ry="2" onmouseover="s('sockfs`socket_vop_inactive (112 samples, 0.03%)')" onmouseout="c()" />
<rect x="320.1" y="401" width="0.4" height="15.0" fill="rgb(224,159,51)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (143 samples, 0.04%)')" onmouseout="c()" />
<rect x="197.1" y="577" width="0.1" height="15.0" fill="rgb(210,122,28)" rx="2" ry="2" onmouseover="s('unix`htable_walk (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="197.9" y="625" width="0.1" height="15.0" fill="rgb(243,144,41)" rx="2" ry="2" onmouseover="s('genunix`as_unmap (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="957.0" y="593" width="0.5" height="15.0" fill="rgb(248,121,32)" rx="2" ry="2" onmouseover="s('genunix`pvn_read_kluster (176 samples, 0.05%)')" onmouseout="c()" />
<rect x="645.5" y="401" width="97.9" height="15.0" fill="rgb(246,136,52)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (31612 samples, 8.30%)')" onmouseout="c()" />
<text text-anchor="" x="648.451110545124" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('ip`squeue_enter (31612 samples, 8.30%)')" onmouseout="c()" >ip`squeue_e..</text>
<rect x="673.5" y="289" width="1.7" height="15.0" fill="rgb(219,140,12)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (555 samples, 0.15%)')" onmouseout="c()" />
<rect x="963.3" y="657" width="2.1" height="15.0" fill="rgb(240,169,9)" rx="2" ry="2" onmouseover="s('genunix`copen (701 samples, 0.18%)')" onmouseout="c()" />
<rect x="1163.3" y="609" width="0.5" height="15.0" fill="rgb(216,10,25)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (149 samples, 0.04%)')" onmouseout="c()" />
<rect x="159.4" y="657" width="10.7" height="15.0" fill="rgb(250,177,19)" rx="2" ry="2" onmouseover="s('specfs`spec_ioctl (3439 samples, 0.90%)')" onmouseout="c()" />
<rect x="312.4" y="561" width="17.4" height="15.0" fill="rgb(242,229,14)" rx="2" ry="2" onmouseover="s('ip`tcp_output (5613 samples, 1.47%)')" onmouseout="c()" />
<rect x="1077.5" y="401" width="0.6" height="15.0" fill="rgb(242,21,49)" rx="2" ry="2" onmouseover="s('mac`mac_tx_send (183 samples, 0.05%)')" onmouseout="c()" />
<rect x="182.4" y="513" width="0.2" height="15.0" fill="rgb(217,27,35)" rx="2" ry="2" onmouseover="s('genunix`issig (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="654.4" y="321" width="0.6" height="15.0" fill="rgb(218,162,26)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (201 samples, 0.05%)')" onmouseout="c()" />
<rect x="175.7" y="593" width="5.3" height="15.0" fill="rgb(248,47,41)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (1700 samples, 0.45%)')" onmouseout="c()" />
<rect x="1050.7" y="417" width="0.1" height="15.0" fill="rgb(244,184,0)" rx="2" ry="2" onmouseover="s('mac`mac_tx_single_ring_mode (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="78.6" y="497" width="0.3" height="15.0" fill="rgb(212,71,31)" rx="2" ry="2" onmouseover="s('ip`tcp_close_output (108 samples, 0.03%)')" onmouseout="c()" />
<rect x="958.6" y="545" width="0.5" height="15.0" fill="rgb(210,64,13)" rx="2" ry="2" onmouseover="s('genunix`segvn_fault (166 samples, 0.04%)')" onmouseout="c()" />
<rect x="80.3" y="513" width="14.0" height="15.0" fill="rgb(240,172,9)" rx="2" ry="2" onmouseover="s('ip`tcp_close_output (4515 samples, 1.19%)')" onmouseout="c()" />
<rect x="137.3" y="513" width="0.1" height="15.0" fill="rgb(251,43,33)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="553.5" y="593" width="0.2" height="15.0" fill="rgb(238,16,13)" rx="2" ry="2" onmouseover="s('genunix`freeb (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="1075.8" y="513" width="0.2" height="15.0" fill="rgb(238,133,32)" rx="2" ry="2" onmouseover="s('genunix`timeout_generic (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="660.7" y="369" width="0.5" height="15.0" fill="rgb(246,183,49)" rx="2" ry="2" onmouseover="s('ip`tcp_conn_con (163 samples, 0.04%)')" onmouseout="c()" />
<rect x="1071.0" y="497" width="0.3" height="15.0" fill="rgb(221,19,16)" rx="2" ry="2" onmouseover="s('ip`tcp_closei_local (75 samples, 0.02%)')" onmouseout="c()" />
<rect x="660.9" y="353" width="0.3" height="15.0" fill="rgb(243,125,48)" rx="2" ry="2" onmouseover="s('sockfs`so_connected (81 samples, 0.02%)')" onmouseout="c()" />
<rect x="924.9" y="609" width="0.1" height="15.0" fill="rgb(237,193,6)" rx="2" ry="2" onmouseover="s('unix`bcopy (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="825.6" y="689" width="0.2" height="15.0" fill="rgb(225,204,42)" rx="2" ry="2" onmouseover="s('genunix`fop_write (52 samples, 0.01%)')" onmouseout="c()" />
<rect x="943.0" y="561" width="0.1" height="15.0" fill="rgb(211,86,27)" rx="2" ry="2" onmouseover="s('genunix`as_fault (34 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.2" y="305" width="0.7" height="15.0" fill="rgb(225,132,32)" rx="2" ry="2" onmouseover="s('ip`ip_xmit (210 samples, 0.06%)')" onmouseout="c()" />
<rect x="1036.6" y="641" width="118.0" height="15.0" fill="rgb(227,206,8)" rx="2" ry="2" onmouseover="s('dls`i_dls_link_rx (38064 samples, 9.99%)')" onmouseout="c()" />
<text text-anchor="" x="1039.63212979422" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" onmouseover="s('dls`i_dls_link_rx (38064 samples, 9.99%)')" onmouseout="c()" >dls`i_dls_link..</text>
<rect x="971.5" y="657" width="0.2" height="15.0" fill="rgb(240,176,30)" rx="2" ry="2" onmouseover="s('unix`tsc_read (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="325.6" y="529" width="0.4" height="15.0" fill="rgb(237,88,43)" rx="2" ry="2" onmouseover="s('genunix`untimeout_default (126 samples, 0.03%)')" onmouseout="c()" />
<rect x="1068.0" y="465" width="0.1" height="15.0" fill="rgb(219,40,44)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="825.8" y="689" width="0.1" height="15.0" fill="rgb(225,169,27)" rx="2" ry="2" onmouseover="s('genunix`forksys (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="992.8" y="641" width="0.3" height="15.0" fill="rgb(214,189,1)" rx="2" ry="2" onmouseover="s('portfs`port_dequeue_thread (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="155.5" y="641" width="0.2" height="15.0" fill="rgb(210,4,7)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="152.8" y="529" width="0.1" height="15.0" fill="rgb(249,103,49)" rx="2" ry="2" onmouseover="s('unix`hat_memload (37 samples, 0.01%)')" onmouseout="c()" />
<rect x="209.9" y="641" width="0.2" height="15.0" fill="rgb(207,206,34)" rx="2" ry="2" onmouseover="s('genunix`copyin_args32 (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="170.9" y="625" width="0.3" height="15.0" fill="rgb(214,2,13)" rx="2" ry="2" onmouseover="s('FSS`fss_sleep (98 samples, 0.03%)')" onmouseout="c()" />
<rect x="62.9" y="689" width="0.5" height="15.0" fill="rgb(213,115,40)" rx="2" ry="2" onmouseover="s('genunix`syscall_mstate (184 samples, 0.05%)')" onmouseout="c()" />
<rect x="1052.0" y="497" width="0.2" height="15.0" fill="rgb(238,186,20)" rx="2" ry="2" onmouseover="s('ip`tcp_send_synack (64 samples, 0.02%)')" onmouseout="c()" />
<rect x="84.5" y="369" width="0.2" height="15.0" fill="rgb(217,90,26)" rx="2" ry="2" onmouseover="s('mac`mac_tx_classify (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="95.1" y="625" width="0.5" height="15.0" fill="rgb(213,31,8)" rx="2" ry="2" onmouseover="s('genunix`vn_rele (173 samples, 0.05%)')" onmouseout="c()" />
<rect x="1157.3" y="657" width="0.1" height="15.0" fill="rgb(210,200,5)" rx="2" ry="2" onmouseover="s('mac`mac_protect_intercept_dhcp (40 samples, 0.01%)')" onmouseout="c()" />
<rect x="626.4" y="449" width="0.1" height="15.0" fill="rgb(235,54,31)" rx="2" ry="2" onmouseover="s('dls`dls_accept_common (53 samples, 0.01%)')" onmouseout="c()" />
<rect x="25.8" y="577" width="0.2" height="15.0" fill="rgb(210,86,22)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="1018.5" y="609" width="0.1" height="15.0" fill="rgb(252,95,19)" rx="2" ry="2" onmouseover="s('sockfs`so_notify_data (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="941.0" y="673" width="1.9" height="15.0" fill="rgb(231,12,0)" rx="2" ry="2" onmouseover="s('genunix`as_faulta (619 samples, 0.16%)')" onmouseout="c()" />
<rect x="621.8" y="497" width="0.1" height="15.0" fill="rgb(216,92,38)" rx="2" ry="2" onmouseover="s('mac`intercept_dhcpv4_inbound (47 samples, 0.01%)')" onmouseout="c()" />
<rect x="1080.9" y="529" width="1.0" height="15.0" fill="rgb(221,28,30)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (323 samples, 0.08%)')" onmouseout="c()" />
<rect x="1155.6" y="625" width="0.3" height="15.0" fill="rgb(219,111,15)" rx="2" ry="2" onmouseover="s('unix`ovbcopy (99 samples, 0.03%)')" onmouseout="c()" />
<rect x="1074.7" y="369" width="0.1" height="15.0" fill="rgb(246,143,26)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_fill_ring (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1152.9" y="577" width="0.3" height="15.0" fill="rgb(243,0,51)" rx="2" ry="2" onmouseover="s('unix`rw_enter (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="613.8" y="609" width="1.7" height="15.0" fill="rgb(235,14,15)" rx="2" ry="2" onmouseover="s('genunix`freemsg (543 samples, 0.14%)')" onmouseout="c()" />
<rect x="956.9" y="625" width="0.9" height="15.0" fill="rgb(220,197,11)" rx="2" ry="2" onmouseover="s('ufs`ufs_getpage (276 samples, 0.07%)')" onmouseout="c()" />
<rect x="1013.4" y="625" width="0.2" height="15.0" fill="rgb(251,1,4)" rx="2" ry="2" onmouseover="s('unix`mutex_vector_enter (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="537.0" y="545" width="1.5" height="15.0" fill="rgb(215,93,25)" rx="2" ry="2" onmouseover="s('ip`squeue_enter (503 samples, 0.13%)')" onmouseout="c()" />
<rect x="755.0" y="497" width="0.1" height="15.0" fill="rgb(239,34,23)" rx="2" ry="2" onmouseover="s('mac`mac_strip_vlan_tag_chain (35 samples, 0.01%)')" onmouseout="c()" />
<rect x="1150.7" y="433" width="0.2" height="15.0" fill="rgb(221,31,28)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (58 samples, 0.02%)')" onmouseout="c()" />
<rect x="80.1" y="433" width="0.2" height="15.0" fill="rgb(222,195,9)" rx="2" ry="2" onmouseover="s('ip`ire_send_wire_v4 (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="83.9" y="337" width="0.2" height="15.0" fill="rgb(245,58,30)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_copy (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="954.9" y="657" width="2.9" height="15.0" fill="rgb(241,89,45)" rx="2" ry="2" onmouseover="s('genunix`segvn_faulta (935 samples, 0.25%)')" onmouseout="c()" />
<rect x="766.6" y="657" width="7.0" height="15.0" fill="rgb(214,152,45)" rx="2" ry="2" onmouseover="s('unix`cbe_low_level (2256 samples, 0.59%)')" onmouseout="c()" />
<rect x="1029.2" y="561" width="1.7" height="15.0" fill="rgb(219,24,44)" rx="2" ry="2" onmouseover="s('dld`str_mdata_fastpath_put (539 samples, 0.14%)')" onmouseout="c()" />
<rect x="766.8" y="609" width="3.4" height="15.0" fill="rgb(221,37,4)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_dynvar_clean (1068 samples, 0.28%)')" onmouseout="c()" />
<rect x="314.6" y="513" width="0.4" height="15.0" fill="rgb(251,29,47)" rx="2" ry="2" onmouseover="s('ip`ip_select_route_pkt (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="940.8" y="641" width="0.1" height="15.0" fill="rgb(231,0,33)" rx="2" ry="2" onmouseover="s('genunix`fop_getattr (50 samples, 0.01%)')" onmouseout="c()" />
<rect x="194.4" y="545" width="0.8" height="15.0" fill="rgb(250,161,24)" rx="2" ry="2" onmouseover="s('genunix`anon_decref (273 samples, 0.07%)')" onmouseout="c()" />
<rect x="1051.1" y="513" width="0.3" height="15.0" fill="rgb(222,33,41)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (125 samples, 0.03%)')" onmouseout="c()" />
<rect x="215.6" y="641" width="0.2" height="15.0" fill="rgb(251,1,17)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="170.4" y="641" width="0.2" height="15.0" fill="rgb(206,53,39)" rx="2" ry="2" onmouseover="s('unix`hat_unload_callback (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="988.5" y="593" width="0.4" height="15.0" fill="rgb(211,142,30)" rx="2" ry="2" onmouseover="s('unix`pg_ev_thread_swtch (107 samples, 0.03%)')" onmouseout="c()" />
<rect x="672.1" y="369" width="4.1" height="15.0" fill="rgb(254,166,49)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (1321 samples, 0.35%)')" onmouseout="c()" />
<rect x="555.9" y="545" width="1.3" height="15.0" fill="rgb(230,62,47)" rx="2" ry="2" onmouseover="s('mac`mac_tx (433 samples, 0.11%)')" onmouseout="c()" />
<rect x="676.3" y="305" width="0.2" height="15.0" fill="rgb(206,16,49)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="936.6" y="673" width="0.7" height="15.0" fill="rgb(246,88,5)" rx="2" ry="2" onmouseover="s('genunix`releasef (202 samples, 0.05%)')" onmouseout="c()" />
<rect x="967.1" y="689" width="0.2" height="15.0" fill="rgb(250,155,11)" rx="2" ry="2" onmouseover="s('genunix`smmap32 (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="1041.3" y="497" width="0.7" height="15.0" fill="rgb(250,208,43)" rx="2" ry="2" onmouseover="s('ipf`fr_ipfcheck (226 samples, 0.06%)')" onmouseout="c()" />
<rect x="169.2" y="593" width="0.6" height="15.0" fill="rgb(216,11,8)" rx="2" ry="2" onmouseover="s('unix`strcpy (217 samples, 0.06%)')" onmouseout="c()" />
<rect x="774.5" y="609" width="8.2" height="15.0" fill="rgb(248,32,54)" rx="2" ry="2" onmouseover="s('genunix`taskq_dispatch (2662 samples, 0.70%)')" onmouseout="c()" />
<rect x="537.3" y="481" width="1.2" height="15.0" fill="rgb(213,212,28)" rx="2" ry="2" onmouseover="s('ip`ip_attr_connect (400 samples, 0.11%)')" onmouseout="c()" />
<rect x="215.6" y="625" width="0.2" height="15.0" fill="rgb(242,51,15)" rx="2" ry="2" onmouseover="s('unix`lock_set_spl (62 samples, 0.02%)')" onmouseout="c()" />
<rect x="1075.2" y="417" width="0.2" height="15.0" fill="rgb(235,226,42)" rx="2" ry="2" onmouseover="s('ipf`fr_check (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="949.9" y="529" width="0.3" height="15.0" fill="rgb(236,152,43)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (90 samples, 0.02%)')" onmouseout="c()" />
<rect x="1076.4" y="529" width="2.3" height="15.0" fill="rgb(253,97,3)" rx="2" ry="2" onmouseover="s('ip`tcp_wput_data (734 samples, 0.19%)')" onmouseout="c()" />
<rect x="97.7" y="561" width="0.4" height="15.0" fill="rgb(229,39,31)" rx="2" ry="2" onmouseover="s('genunix`fop_lookup (111 samples, 0.03%)')" onmouseout="c()" />
<rect x="97.7" y="577" width="1.5" height="15.0" fill="rgb(242,217,7)" rx="2" ry="2" onmouseover="s('genunix`lookuppnatcred (473 samples, 0.12%)')" onmouseout="c()" />
<rect x="786.1" y="625" width="0.6" height="15.0" fill="rgb(250,200,4)" rx="2" ry="2" onmouseover="s('genunix`exec_args (201 samples, 0.05%)')" onmouseout="c()" />
<rect x="341.3" y="657" width="0.3" height="15.0" fill="rgb(227,156,21)" rx="2" ry="2" onmouseover="s('genunix`ufalloc_file (115 samples, 0.03%)')" onmouseout="c()" />
<rect x="773.1" y="561" width="0.3" height="15.0" fill="rgb(229,175,18)" rx="2" ry="2" onmouseover="s('unix`cbe_restore_level (83 samples, 0.02%)')" onmouseout="c()" />
<rect x="770.5" y="577" width="1.7" height="15.0" fill="rgb(244,54,53)" rx="2" ry="2" onmouseover="s('genunix`cv_wakeup (549 samples, 0.14%)')" onmouseout="c()" />
<rect x="950.7" y="689" width="0.6" height="15.0" fill="rgb(249,6,36)" rx="2" ry="2" onmouseover="s('unix`atomic_add_64 (191 samples, 0.05%)')" onmouseout="c()" />
<rect x="992.0" y="641" width="0.8" height="15.0" fill="rgb(221,37,31)" rx="2" ry="2" onmouseover="s('portfs`port_copy_event32 (257 samples, 0.07%)')" onmouseout="c()" />
<rect x="344.2" y="593" width="0.3" height="15.0" fill="rgb(218,188,27)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (123 samples, 0.03%)')" onmouseout="c()" />
<rect x="155.5" y="657" width="0.2" height="15.0" fill="rgb(250,96,37)" rx="2" ry="2" onmouseover="s('genunix`lookuppn (61 samples, 0.02%)')" onmouseout="c()" />
<rect x="42.1" y="625" width="0.2" height="15.0" fill="rgb(250,129,26)" rx="2" ry="2" onmouseover="s('unix`hat_probe (57 samples, 0.01%)')" onmouseout="c()" />
<rect x="941.0" y="657" width="0.3" height="15.0" fill="rgb(227,82,29)" rx="2" ry="2" onmouseover="s('genunix`fop_getpage (121 samples, 0.03%)')" onmouseout="c()" />
<rect x="1045.8" y="545" width="0.2" height="15.0" fill="rgb(242,84,53)" rx="2" ry="2" onmouseover="s('unix`rw_enter (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="535.8" y="545" width="0.1" height="15.0" fill="rgb(225,44,39)" rx="2" ry="2" onmouseover="s('genunix`kmem_alloc (43 samples, 0.01%)')" onmouseout="c()" />
<rect x="86.2" y="481" width="6.7" height="15.0" fill="rgb(242,145,48)" rx="2" ry="2" onmouseover="s('ip`dce_update_uinfo_v4 (2150 samples, 0.56%)')" onmouseout="c()" />
<rect x="1006.4" y="641" width="4.2" height="15.0" fill="rgb(233,156,20)" rx="2" ry="2" onmouseover="s('genunix`callout_list_expire (1353 samples, 0.36%)')" onmouseout="c()" />
<rect x="991.3" y="641" width="0.1" height="15.0" fill="rgb(250,109,39)" rx="2" ry="2" onmouseover="s('genunix`kmem_free (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="66.6" y="673" width="0.2" height="15.0" fill="rgb(234,82,21)" rx="2" ry="2" onmouseover="s('genunix`gethrtime_unscaled (68 samples, 0.02%)')" onmouseout="c()" />
<rect x="1151.0" y="561" width="0.4" height="15.0" fill="rgb(235,99,53)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="773.6" y="657" width="9.8" height="15.0" fill="rgb(205,40,10)" rx="2" ry="2" onmouseover="s('unix`cbe_softclock (3153 samples, 0.83%)')" onmouseout="c()" />
<rect x="330.7" y="577" width="0.2" height="15.0" fill="rgb(225,112,23)" rx="2" ry="2" onmouseover="s('unix`mutex_exit (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="341.7" y="641" width="0.5" height="15.0" fill="rgb(221,75,7)" rx="2" ry="2" onmouseover="s('genunix`as_segat (154 samples, 0.04%)')" onmouseout="c()" />
<rect x="673.8" y="225" width="0.2" height="15.0" fill="rgb(238,223,19)" rx="2" ry="2" onmouseover="s('mac`ipnospoof_check (66 samples, 0.02%)')" onmouseout="c()" />
<rect x="1072.4" y="449" width="0.1" height="15.0" fill="rgb(218,31,54)" rx="2" ry="2" onmouseover="s('genunix`cv_signal (45 samples, 0.01%)')" onmouseout="c()" />
<rect x="32.6" y="497" width="0.2" height="15.0" fill="rgb(231,175,54)" rx="2" ry="2" onmouseover="s('unix`page_ctr_sub (42 samples, 0.01%)')" onmouseout="c()" />
<rect x="949.9" y="465" width="0.2" height="15.0" fill="rgb(250,18,19)" rx="2" ry="2" onmouseover="s('mac`mac_tx (69 samples, 0.02%)')" onmouseout="c()" />
<rect x="155.2" y="593" width="0.3" height="15.0" fill="rgb(230,131,43)" rx="2" ry="2" onmouseover="s('genunix`exec_args (71 samples, 0.02%)')" onmouseout="c()" />
<rect x="612.4" y="625" width="3.1" height="15.0" fill="rgb(252,215,28)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_free_tcb (981 samples, 0.26%)')" onmouseout="c()" />
<rect x="674.5" y="193" width="0.2" height="15.0" fill="rgb(248,74,49)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_tx_bind (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="668.4" y="305" width="1.0" height="15.0" fill="rgb(215,116,43)" rx="2" ry="2" onmouseover="s('hook`hook_run (319 samples, 0.08%)')" onmouseout="c()" />
<rect x="181.6" y="577" width="12.5" height="15.0" fill="rgb(206,62,18)" rx="2" ry="2" onmouseover="s('bmc`bmc_wsrv (4042 samples, 1.06%)')" onmouseout="c()" />
<rect x="768.8" y="577" width="1.4" height="15.0" fill="rgb(212,109,53)" rx="2" ry="2" onmouseover="s('unix`dtrace_xcall (427 samples, 0.11%)')" onmouseout="c()" />
<rect x="1158.0" y="593" width="5.2" height="15.0" fill="rgb(218,133,16)" rx="2" ry="2" onmouseover="s('ip`ip_input (1679 samples, 0.44%)')" onmouseout="c()" />
<rect x="1078.4" y="513" width="0.1" height="15.0" fill="rgb(232,177,28)" rx="2" ry="2" onmouseover="s('ip`tcp_timeout (39 samples, 0.01%)')" onmouseout="c()" />
<rect x="554.1" y="625" width="0.3" height="15.0" fill="rgb(248,212,48)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (95 samples, 0.02%)')" onmouseout="c()" />
<rect x="1048.7" y="545" width="0.2" height="15.0" fill="rgb(249,136,10)" rx="2" ry="2" onmouseover="s('ip`ipcl_conn_destroy (87 samples, 0.02%)')" onmouseout="c()" />
<rect x="552.1" y="673" width="2.0" height="15.0" fill="rgb(207,140,28)" rx="2" ry="2" onmouseover="s('sockfs`recvit (639 samples, 0.17%)')" onmouseout="c()" />
<rect x="782.7" y="625" width="0.6" height="15.0" fill="rgb(239,51,15)" rx="2" ry="2" onmouseover="s('genunix`clock (175 samples, 0.05%)')" onmouseout="c()" />
<rect x="537.0" y="481" width="0.2" height="15.0" fill="rgb(230,160,45)" rx="2" ry="2" onmouseover="s('ip`conn_connect (63 samples, 0.02%)')" onmouseout="c()" />
<rect x="1042.3" y="513" width="1.6" height="15.0" fill="rgb(244,134,2)" rx="2" ry="2" onmouseover="s('unix`rw_enter_sleep (500 samples, 0.13%)')" onmouseout="c()" />
<rect x="194.6" y="529" width="0.5" height="15.0" fill="rgb(230,157,5)" rx="2" ry="2" onmouseover="s('genunix`fop_dispose (180 samples, 0.05%)')" onmouseout="c()" />
<rect x="677.3" y="305" width="1.1" height="15.0" fill="rgb(229,171,12)" rx="2" ry="2" onmouseover="s('genunix`port_send_event (372 samples, 0.10%)')" onmouseout="c()" />
<rect x="302.4" y="545" width="1.1" height="15.0" fill="rgb(211,109,27)" rx="2" ry="2" onmouseover="s('genunix`sleepq_wakeone_chan (326 samples, 0.09%)')" onmouseout="c()" />
<rect x="218.0" y="689" width="0.2" height="15.0" fill="rgb(230,112,52)" rx="2" ry="2" onmouseover="s('genunix`write (56 samples, 0.01%)')" onmouseout="c()" />
<rect x="680.5" y="209" width="0.1" height="15.0" fill="rgb(236,206,16)" rx="2" ry="2" onmouseover="s('ixgbe`ixgbe_ring_tx (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="673.6" y="225" width="0.1" height="15.0" fill="rgb(225,135,37)" rx="2" ry="2" onmouseover="s('mac`dhcpnospoof_check (54 samples, 0.01%)')" onmouseout="c()" />
<rect x="744.5" y="385" width="0.1" height="15.0" fill="rgb(242,20,20)" rx="2" ry="2" onmouseover="s('genunix`allocb (36 samples, 0.01%)')" onmouseout="c()" />
<rect x="579.1" y="657" width="0.3" height="15.0" fill="rgb(254,46,1)" rx="2" ry="2" onmouseover="s('genunix`ufalloc (72 samples, 0.02%)')" onmouseout="c()" />
<rect x="612.2" y="625" width="0.2" height="15.0" fill="rgb(222,1,49)" rx="2" ry="2" onmouseover="s('genunix`ddi_dma_sync (51 samples, 0.01%)')" onmouseout="c()" />
<rect x="75.6" y="625" width="0.1" height="15.0" fill="rgb(254,167,48)" rx="2" ry="2" onmouseover="s('genunix`seg_alloc (46 samples, 0.01%)')" onmouseout="c()" />
<rect x="674.6" y="113" width="0.1" height="15.0" fill="rgb(234,110,33)" rx="2" ry="2" onmouseover="s('unix`hat_getpfnum (38 samples, 0.01%)')" onmouseout="c()" />
<rect x="170.2" y="689" width="0.4" height="15.0" fill="rgb(216,193,25)" rx="2" ry="2" onmouseover="s('genunix`munmap (120 samples, 0.03%)')" onmouseout="c()" />
<rect x="38.8" y="545" width="0.2" height="15.0" fill="rgb(248,71,20)" rx="2" ry="2" onmouseover="s('unix`hment_prepare (67 samples, 0.02%)')" onmouseout="c()" />
<rect x="757.7" y="545" width="0.1" height="15.0" fill="rgb(253,174,22)" rx="2" ry="2" onmouseover="s('unix`mutex_enter (41 samples, 0.01%)')" onmouseout="c()" />
<rect x="314.8" y="481" width="0.2" height="15.0" fill="rgb(210,89,21)" rx="2" ry="2" onmouseover="s('ip`ire_route_recursive_v4 (59 samples, 0.02%)')" onmouseout="c()" />
<rect x="1150.7" y="481" width="0.2" height="15.0" fill="rgb(213,27,51)" rx="2" ry="2" onmouseover="s('genunix`pollwakeup (82 samples, 0.02%)')" onmouseout="c()" />
<rect x="159.5" y="561" width="0.1" height="15.0" fill="rgb(213,77,31)" rx="2" ry="2" onmouseover="s('dtrace`dtrace_helper_provide (44 samples, 0.01%)')" onmouseout="c()" />
<rect x="306.4" y="609" width="29.1" height="15.0" fill="rgb(230,110,50)" rx="2" ry="2" onmouseover="s('sockfs`so_sendmsg (9385 samples, 2.46%)')" onmouseout="c()" />
<rect x="1177.0" y="609" width="0.3" height="15.0" fill="rgb(213,83,5)" rx="2" ry="2" onmouseover="s('unix`default_lock_delay (103 samples, 0.03%)')" onmouseout="c()" />
<rect x="948.5" y="657" width="0.7" height="15.0" fill="rgb(229,76,52)" rx="2" ry="2" onmouseover="s('unix`tsc_read (233 samples, 0.06%)')" onmouseout="c()" />
<rect x="638.3" y="321" width="0.2" height="15.0" fill="rgb(208,176,54)" rx="2" ry="2" onmouseover="s('genunix`disp_lock_enter (70 samples, 0.02%)')" onmouseout="c()" />
<rect x="647.7" y="337" width="0.3" height="15.0" fill="rgb(228,9,28)" rx="2" ry="2" onmouseover="s('ip`conn_ip_output (90 samples, 0.02%)')" onmouseout="c()" />
</svg>
