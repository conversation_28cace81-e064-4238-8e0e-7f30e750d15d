<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="1266" onload="init(evt)" viewBox="0 0 1200 1266" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details, svg;
	function init(evt) { 
		details = document.getElementById("details").firstChild; 
		svg = document.getElementsByTagName("svg")[0];
	}
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }
	function find_child(parent, name, attr) {
		var children = parent.childNodes;
		for (var i=0; i<children.length;i++) {
			if (children[i].tagName == name)
				return (attr != undefined) ? children[i].attributes[attr].value : children[i];
		}
		return;
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_"+attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_"+attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_"+attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes["width"].value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)/,"");
		t.attributes["x"].value = parseFloat(r.attributes["x"].value) +3;
		
		// Smaller than this size won't fit anything
		if (w < 2*12*0.59) {
			t.textContent = "";
			return;
		}
		
		t.textContent = txt;
		// Fit in full text width
		if (/^ *$/.test(txt) || t.getSubStringLength(0, txt.length) < w)
			return;
		
		for (var x=txt.length-2; x>0; x--) {
			if (t.getSubStringLength(0, x+2) <= w) { 
				t.textContent = txt.substring(0,x) + "..";
				return;
			}
		}
		t.textContent = "";
	}
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = (parseFloat(e.attributes["x"].value) - x - 10) * ratio + 10;
				if(e.tagName == "text") e.attributes["x"].value = find_child(e.parentNode, "rect", "x") + 3;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseFloat(e.attributes["width"].value) * ratio;
			}
		}
		
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_child(c[i], x-10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = 10;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseInt(svg.width.baseVal.value) - (10*2);
			}
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) { 
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr["width"].value);
		var xmin = parseFloat(attr["x"].value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr["y"].value);
		var ratio = (svg.width.baseVal.value - 2*10) / width;
		
		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;
		
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "1.0";
		
		var el = document.getElementsByTagName("g");
		for(var i=0;i<el.length;i++){
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a["x"].value);
			var ew = parseFloat(a["width"].value);
			// Is it an ancestor
			if (0 == 0) {
				var upstack = parseFloat(a["y"].value) > ymin;
			} else {
				var upstack = parseFloat(a["y"].value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.style["opacity"] = "0.5";
					zoom_parent(e);
					e.onclick = function(e){unzoom(); zoom(this);};
					update_text(e);
				}
				// not in current path
				else
					e.style["display"] = "none";
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.style["display"] = "none";
				}
				else {
					zoom_child(e, xmin, ratio);
					e.onclick = function(e){zoom(this);};
					update_text(e);
				}
			}
		}
	}
	function unzoom() {
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "0.0";
		
		var el = document.getElementsByTagName("g");
		for(i=0;i<el.length;i++) {
			el[i].style["display"] = "block";
			el[i].style["opacity"] = "1";
			zoom_reset(el[i]);
			update_text(el[i]);
		}
	}	
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="1266.0" fill="url(#background)"  />
<text text-anchor="middle" x="600.00" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >CPU Mixed-Mode Flame Graph: green == Java, yellow == C++, red == system</text>
<text text-anchor="" x="10.00" y="1249" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<text text-anchor="" x="10.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="unzoom" onclick="unzoom()" style="opacity:0.0;cursor:pointer" >Reset Zoom</text>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)</title><rect x="400.2" y="737" width="1.2" height="15.0" fill="rgb(69,217,69)" rx="2" ry="2" />
<text text-anchor="" x="403.17" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('mprotect_fixup (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>mprotect_fixup (1 samples, 0.10%)</title><rect x="1188.8" y="1057" width="1.2" height="15.0" fill="rgb(208,61,61)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (1 samples, 0.10%)</title><rect x="441.7" y="721" width="1.2" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="444.68" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/HashMap:.getNode (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/HashMap:.getNode (2 samples, 0.20%)</title><rect x="637.4" y="737" width="2.3" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="640.36" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('read_tsc (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>read_tsc (1 samples, 0.10%)</title><rect x="1061.9" y="177" width="1.2" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="1064.92" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('loopback_xmit (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>loopback_xmit (5 samples, 0.50%)</title><rect x="984.8" y="449" width="6.0" height="15.0" fill="rgb(203,55,55)" rx="2" ry="2" />
<text text-anchor="" x="987.83" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (15 samples, 1.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (15 samples, 1.51%)</title><rect x="593.5" y="721" width="17.8" height="15.0" fill="rgb(93,239,93)" rx="2" ry="2" />
<text text-anchor="" x="596.48" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.bind (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.bind (1 samples, 0.10%)</title><rect x="312.4" y="801" width="1.2" height="15.0" fill="rgb(63,212,63)" rx="2" ry="2" />
<text text-anchor="" x="315.41" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('netif_rx (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>netif_rx (2 samples, 0.20%)</title><rect x="986.0" y="433" width="2.4" height="15.0" fill="rgb(215,72,72)" rx="2" ry="2" />
<text text-anchor="" x="989.02" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (5 samples, 0.50%)</title><rect x="345.6" y="705" width="5.9" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="348.62" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('default_wake_function (25 samples, 2.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>default_wake_function (25 samples, 2.51%)</title><rect x="1077.3" y="145" width="29.7" height="15.0" fill="rgb(213,69,69)" rx="2" ry="2" />
<text text-anchor="" x="1080.34" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >de..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)</title><rect x="733.4" y="721" width="1.2" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="736.42" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="208.1" y="769" width="1.1" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="211.05" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('CardTableExtension::scavenge_contents_parallel (20 samples, 2.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>CardTableExtension::scavenge_contents_parallel (20 samples, 2.01%)</title><rect x="23.0" y="1121" width="23.8" height="15.0" fill="rgb(212,212,63)" rx="2" ry="2" />
<text text-anchor="" x="26.05" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >C..</text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/FileDispatcherImpl:.write0 (203 samples, 20.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/FileDispatcherImpl:.write0 (203 samples, 20.40%)</title><rect x="916.1" y="753" width="240.7" height="15.0" fill="rgb(105,251,105)" rx="2" ry="2" />
<text text-anchor="" x="919.05" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lsun/nio/ch/FileDispatcherImpl:...</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (7 samples, 0.70%)</title><rect x="428.6" y="721" width="8.3" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="431.63" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue (1 samples, 0.10%)</title><rect x="209.2" y="785" width="1.2" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="212.24" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('resched_task (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>resched_task (2 samples, 0.20%)</title><rect x="1101.1" y="81" width="2.3" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="1104.06" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="358.7" y="705" width="1.1" height="15.0" fill="rgb(55,204,55)" rx="2" ry="2" />
<text text-anchor="" x="361.66" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('group_sched_in (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>group_sched_in (4 samples, 0.40%)</title><rect x="935.0" y="561" width="4.8" height="15.0" fill="rgb(202,53,53)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.contains (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.contains (1 samples, 0.10%)</title><rect x="274.5" y="673" width="1.1" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="277.46" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)</title><rect x="260.2" y="721" width="1.2" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="263.23" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundBuffer:.current (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundBuffer:.current (1 samples, 0.10%)</title><rect x="888.8" y="785" width="1.2" height="15.0" fill="rgb(70,218,70)" rx="2" ry="2" />
<text text-anchor="" x="891.77" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="307.7" y="753" width="1.2" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="310.67" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.contains (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.contains (1 samples, 0.10%)</title><rect x="850.8" y="881" width="1.2" height="15.0" fill="rgb(102,247,102)" rx="2" ry="2" />
<text text-anchor="" x="853.82" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.checkSrcIndex (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.checkSrcIndex (3 samples, 0.30%)</title><rect x="234.1" y="737" width="3.6" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="237.14" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.10%)</title><rect x="962.3" y="513" width="1.2" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="965.30" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="519.9" y="737" width="1.2" height="15.0" fill="rgb(58,208,58)" rx="2" ry="2" />
<text text-anchor="" x="522.95" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sys_read (28 samples, 2.81%)')" onmouseout="c()" onclick="zoom(this)">
<title>sys_read (28 samples, 2.81%)</title><rect x="93.0" y="849" width="33.2" height="15.0" fill="rgb(210,65,65)" rx="2" ry="2" />
<text text-anchor="" x="96.02" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sy..</text>
</g>
<g class="func_g" onmouseover="s('tcp_cleanup_rbuf (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_cleanup_rbuf (2 samples, 0.20%)</title><rect x="120.3" y="737" width="2.4" height="15.0" fill="rgb(200,50,50)" rx="2" ry="2" />
<text text-anchor="" x="123.29" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('effective_load.isra.35 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>effective_load.isra.35 (2 samples, 0.20%)</title><rect x="1084.5" y="97" width="2.3" height="15.0" fill="rgb(225,86,86)" rx="2" ry="2" />
<text text-anchor="" x="1087.45" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Monitor::wait (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Monitor::wait (1 samples, 0.10%)</title><rect x="21.9" y="1121" width="1.1" height="15.0" fill="rgb(193,193,56)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_wfree (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_wfree (2 samples, 0.20%)</title><rect x="988.4" y="433" width="2.4" height="15.0" fill="rgb(251,125,125)" rx="2" ry="2" />
<text text-anchor="" x="991.39" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundBuffer:.progress (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundBuffer:.progress (1 samples, 0.10%)</title><rect x="904.2" y="769" width="1.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="907.19" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp (4 samples, 0.40%)</title><rect x="650.4" y="785" width="4.7" height="15.0" fill="rgb(76,224,76)" rx="2" ry="2" />
<text text-anchor="" x="653.40" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.encodeAscii0 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.encodeAscii0 (2 samples, 0.20%)</title><rect x="291.1" y="641" width="2.3" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="294.07" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__netif_receive_skb_core (94 samples, 9.45%)')" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb_core (94 samples, 9.45%)</title><rect x="1005.0" y="385" width="111.5" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="1007.99" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__netif_recei..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.10%)</title><rect x="534.2" y="753" width="1.2" height="15.0" fill="rgb(81,228,81)" rx="2" ry="2" />
<text text-anchor="" x="537.18" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('msecs_to_jiffies (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>msecs_to_jiffies (1 samples, 0.10%)</title><rect x="955.2" y="577" width="1.2" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="958.19" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived (540 samples, 54.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived (540 samples, 54.27%)</title><rect x="167.7" y="881" width="640.4" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="170.73" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived</text>
</g>
<g class="func_g" onmouseover="s('process_backlog (97 samples, 9.75%)')" onmouseout="c()" onclick="zoom(this)">
<title>process_backlog (97 samples, 9.75%)</title><rect x="1001.4" y="417" width="115.1" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="1004.44" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >process_backlog</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/WrapFactory:.wrap (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/WrapFactory:.wrap (1 samples, 0.10%)</title><rect x="182.0" y="865" width="1.1" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="184.96" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (7 samples, 0.70%)</title><rect x="788.0" y="737" width="8.3" height="15.0" fill="rgb(92,239,92)" rx="2" ry="2" />
<text text-anchor="" x="790.97" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeCall:.init (16 samples, 1.61%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeCall:.init (16 samples, 1.61%)</title><rect x="516.4" y="769" width="19.0" height="15.0" fill="rgb(81,228,81)" rx="2" ry="2" />
<text text-anchor="" x="519.39" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/DirectByteBuffer:.duplicate (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/DirectByteBuffer:.duplicate (1 samples, 0.10%)</title><rect x="80.0" y="929" width="1.2" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="82.97" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getTopLevelScope (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getTopLevelScope (1 samples, 0.10%)</title><rect x="189.1" y="849" width="1.2" height="15.0" fill="rgb(55,205,55)" rx="2" ry="2" />
<text text-anchor="" x="192.08" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (9 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (9 samples, 0.90%)</title><rect x="404.9" y="721" width="10.7" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="407.91" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (5 samples, 0.50%)</title><rect x="409.7" y="673" width="5.9" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="412.66" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (2 samples, 0.20%)</title><rect x="390.7" y="721" width="2.4" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="393.68" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lock_sock_nested (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.10%)</title><rect x="114.4" y="737" width="1.1" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="117.36" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__netif_receive_skb (94 samples, 9.45%)')" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb (94 samples, 9.45%)</title><rect x="1005.0" y="401" width="111.5" height="15.0" fill="rgb(205,58,58)" rx="2" ry="2" />
<text text-anchor="" x="1007.99" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__netif_recei..</text>
</g>
<g class="func_g" onmouseover="s('fput (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>fput (1 samples, 0.10%)</title><rect x="924.4" y="689" width="1.1" height="15.0" fill="rgb(211,66,66)" rx="2" ry="2" />
<text text-anchor="" x="927.35" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/http/impl/AssembledFullHttpResponse:.toLastContent (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/http/impl/AssembledFullHttpResponse:.toLastContent (2 samples, 0.20%)</title><rect x="224.7" y="769" width="2.3" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="227.65" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__wake_up_common (25 samples, 2.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_common (25 samples, 2.51%)</title><rect x="1077.3" y="161" width="29.7" height="15.0" fill="rgb(202,54,54)" rx="2" ry="2" />
<text text-anchor="" x="1080.34" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('tcp_rearm_rto (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rearm_rto (5 samples, 0.50%)</title><rect x="958.7" y="561" width="6.0" height="15.0" fill="rgb(231,96,96)" rx="2" ry="2" />
<text text-anchor="" x="961.74" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (3 samples, 0.30%)</title><rect x="436.9" y="737" width="3.6" height="15.0" fill="rgb(93,240,93)" rx="2" ry="2" />
<text text-anchor="" x="439.93" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('update_rq_clock.part.63 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>update_rq_clock.part.63 (1 samples, 0.10%)</title><rect x="1096.3" y="65" width="1.2" height="15.0" fill="rgb(208,62,62)" rx="2" ry="2" />
<text text-anchor="" x="1099.31" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/internal/AppendableCharSequence:.substring (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/internal/AppendableCharSequence:.substring (4 samples, 0.40%)</title><rect x="860.3" y="865" width="4.8" height="15.0" fill="rgb(81,229,81)" rx="2" ry="2" />
<text text-anchor="" x="863.31" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('JavaCalls::call_virtual (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>JavaCalls::call_virtual (956 samples, 96.08%)</title><rect x="55.1" y="1105" width="1133.7" height="15.0" fill="rgb(227,227,68)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaCalls::call_virtual</text>
</g>
<g class="func_g" onmouseover="s('tcp_ack (20 samples, 2.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_ack (20 samples, 2.01%)</title><rect x="1039.4" y="257" width="23.7" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="1042.39" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/HashMap:.getNode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/HashMap:.getNode (1 samples, 0.10%)</title><rect x="658.7" y="737" width="1.2" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="661.70" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('dev_queue_xmit (11 samples, 1.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>dev_queue_xmit (11 samples, 1.11%)</title><rect x="980.1" y="497" width="13.0" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="983.09" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="425.1" y="689" width="1.2" height="15.0" fill="rgb(104,249,104)" rx="2" ry="2" />
<text text-anchor="" x="428.08" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__lll_unlock_wake (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__lll_unlock_wake (1 samples, 0.10%)</title><rect x="21.9" y="1089" width="1.1" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_try_rmem_schedule (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_try_rmem_schedule (2 samples, 0.20%)</title><rect x="1111.7" y="241" width="2.4" height="15.0" fill="rgb(230,94,94)" rx="2" ry="2" />
<text text-anchor="" x="1114.73" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/UnpooledHeapByteBuf:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/UnpooledHeapByteBuf:.init (1 samples, 0.10%)</title><rect x="240.1" y="737" width="1.2" height="15.0" fill="rgb(104,250,104)" rx="2" ry="2" />
<text text-anchor="" x="243.07" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('internal_add_timer (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>internal_add_timer (1 samples, 0.10%)</title><rect x="967.0" y="529" width="1.2" height="15.0" fill="rgb(245,115,115)" rx="2" ry="2" />
<text text-anchor="" x="970.05" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('apparmor_socket_sendmsg (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_socket_sendmsg (1 samples, 0.10%)</title><rect x="933.8" y="641" width="1.2" height="15.0" fill="rgb(208,61,61)" rx="2" ry="2" />
<text text-anchor="" x="936.84" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.10%)</title><rect x="632.6" y="769" width="1.2" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="635.61" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (2 samples, 0.20%)</title><rect x="815.2" y="897" width="2.4" height="15.0" fill="rgb(79,227,79)" rx="2" ry="2" />
<text text-anchor="" x="818.25" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__perf_event_enable (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>__perf_event_enable (4 samples, 0.40%)</title><rect x="935.0" y="577" width="4.8" height="15.0" fill="rgb(250,123,123)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (5 samples, 0.50%)</title><rect x="498.6" y="689" width="5.9" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="501.60" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('InstanceKlass::oop_push_contents (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>InstanceKlass::oop_push_contents (8 samples, 0.80%)</title><rect x="32.5" y="1105" width="9.5" height="15.0" fill="rgb(210,210,62)" rx="2" ry="2" />
<text text-anchor="" x="35.53" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="657.5" y="769" width="1.2" height="15.0" fill="rgb(93,240,93)" rx="2" ry="2" />
<text text-anchor="" x="660.52" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/DefaultChannelPromise:.trySuccess (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/DefaultChannelPromise:.trySuccess (3 samples, 0.30%)</title><rect x="905.4" y="769" width="3.5" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="908.38" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.getByte (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.getByte (1 samples, 0.10%)</title><rect x="866.2" y="881" width="1.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="869.24" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('[unknown] (30 samples, 3.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (30 samples, 3.02%)</title><rect x="90.6" y="881" width="35.6" height="15.0" fill="rgb(250,122,122)" rx="2" ry="2" />
<text text-anchor="" x="93.64" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[un..</text>
</g>
<g class="func_g" onmouseover="s('__wake_up_sync_key (27 samples, 2.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_sync_key (27 samples, 2.71%)</title><rect x="1076.2" y="225" width="32.0" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="1079.15" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/Arrays:.copyOf (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/Arrays:.copyOf (1 samples, 0.10%)</title><rect x="255.5" y="721" width="1.2" height="15.0" fill="rgb(72,220,72)" rx="2" ry="2" />
<text text-anchor="" x="258.49" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (1 samples, 0.10%)</title><rect x="247.2" y="753" width="1.2" height="15.0" fill="rgb(58,207,58)" rx="2" ry="2" />
<text text-anchor="" x="250.19" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('apparmor_file_permission (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_permission (1 samples, 0.10%)</title><rect x="1152.1" y="641" width="1.1" height="15.0" fill="rgb(254,129,129)" rx="2" ry="2" />
<text text-anchor="" x="1155.05" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (1 samples, 0.10%)</title><rect x="563.8" y="753" width="1.2" height="15.0" fill="rgb(102,248,102)" rx="2" ry="2" />
<text text-anchor="" x="566.83" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue (1 samples, 0.10%)</title><rect x="438.1" y="721" width="1.2" height="15.0" fill="rgb(52,201,52)" rx="2" ry="2" />
<text text-anchor="" x="441.12" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)</title><rect x="484.4" y="657" width="1.2" height="15.0" fill="rgb(106,252,106)" rx="2" ry="2" />
<text text-anchor="" x="487.37" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_poll (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_poll (1 samples, 0.10%)</title><rect x="1186.4" y="817" width="1.2" height="15.0" fill="rgb(226,87,87)" rx="2" ry="2" />
<text text-anchor="" x="1189.44" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('common_file_perm (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>common_file_perm (1 samples, 0.10%)</title><rect x="1152.1" y="625" width="1.1" height="15.0" fill="rgb(244,114,114)" rx="2" ry="2" />
<text text-anchor="" x="1155.05" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ep_poll_callback (27 samples, 2.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll_callback (27 samples, 2.71%)</title><rect x="1076.2" y="193" width="32.0" height="15.0" fill="rgb(213,69,69)" rx="2" ry="2" />
<text text-anchor="" x="1079.15" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ep..</text>
</g>
<g class="func_g" onmouseover="s('check_preempt_curr (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>check_preempt_curr (2 samples, 0.20%)</title><rect x="1098.7" y="81" width="2.4" height="15.0" fill="rgb(235,102,102)" rx="2" ry="2" />
<text text-anchor="" x="1101.68" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_aio_read (22 samples, 2.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_read (22 samples, 2.21%)</title><rect x="96.6" y="801" width="26.1" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="99.57" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.findNonWhitespace (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.findNonWhitespace (1 samples, 0.10%)</title><rect x="859.1" y="865" width="1.2" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="862.13" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('bictcp_acked (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>bictcp_acked (1 samples, 0.10%)</title><rect x="1059.5" y="225" width="1.2" height="15.0" fill="rgb(234,100,100)" rx="2" ry="2" />
<text text-anchor="" x="1062.55" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/concurrent/ConcurrentHashMap:.get (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/concurrent/ConcurrentHashMap:.get (3 samples, 0.30%)</title><rect x="164.2" y="881" width="3.5" height="15.0" fill="rgb(102,247,102)" rx="2" ry="2" />
<text text-anchor="" x="167.17" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('inet_recvmsg (17 samples, 1.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>inet_recvmsg (17 samples, 1.71%)</title><rect x="102.5" y="769" width="20.2" height="15.0" fill="rgb(225,87,87)" rx="2" ry="2" />
<text text-anchor="" x="105.50" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)</title><rect x="425.1" y="705" width="1.2" height="15.0" fill="rgb(106,251,106)" rx="2" ry="2" />
<text text-anchor="" x="428.08" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (3 samples, 0.30%)</title><rect x="314.8" y="801" width="3.5" height="15.0" fill="rgb(106,252,106)" rx="2" ry="2" />
<text text-anchor="" x="317.78" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (2 samples, 0.20%)</title><rect x="206.9" y="785" width="2.3" height="15.0" fill="rgb(90,236,90)" rx="2" ry="2" />
<text text-anchor="" x="209.86" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (2 samples, 0.20%)</title><rect x="10.0" y="1153" width="2.4" height="15.0" fill="rgb(95,242,95)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_bh (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_bh (1 samples, 0.10%)</title><rect x="1126.0" y="609" width="1.1" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="1128.96" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/FileDispatcherImpl:.read0 (31 samples, 3.12%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/FileDispatcherImpl:.read0 (31 samples, 3.12%)</title><rect x="90.6" y="897" width="36.8" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="93.64" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lsu..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/PooledByteBuf:.deallocate (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/PooledByteBuf:.deallocate (2 samples, 0.20%)</title><rect x="139.3" y="913" width="2.3" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="142.27" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)</title><rect x="149.9" y="897" width="1.2" height="15.0" fill="rgb(100,246,100)" rx="2" ry="2" />
<text text-anchor="" x="152.94" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.10%)</title><rect x="351.5" y="721" width="1.2" height="15.0" fill="rgb(54,204,54)" rx="2" ry="2" />
<text text-anchor="" x="354.55" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="804.6" y="833" width="1.2" height="15.0" fill="rgb(245,116,116)" rx="2" ry="2" />
<text text-anchor="" x="807.57" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.writeBytes (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.writeBytes (1 samples, 0.10%)</title><rect x="219.9" y="769" width="1.2" height="15.0" fill="rgb(57,206,57)" rx="2" ry="2" />
<text text-anchor="" x="222.91" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="645.7" y="737" width="1.1" height="15.0" fill="rgb(66,214,66)" rx="2" ry="2" />
<text text-anchor="" x="648.66" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (6 samples, 0.60%)</title><rect x="393.1" y="721" width="7.1" height="15.0" fill="rgb(77,225,77)" rx="2" ry="2" />
<text text-anchor="" x="396.06" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_set_skb_tso_segs (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_set_skb_tso_segs (1 samples, 0.10%)</title><rect x="964.7" y="561" width="1.2" height="15.0" fill="rgb(227,89,89)" rx="2" ry="2" />
<text text-anchor="" x="967.67" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)</title><rect x="12.4" y="1153" width="1.2" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="15.37" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ep_poll (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll (4 samples, 0.40%)</title><rect x="1182.9" y="881" width="4.7" height="15.0" fill="rgb(208,62,62)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.ensureWritable (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.ensureWritable (2 samples, 0.20%)</title><rect x="288.7" y="625" width="2.4" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="291.69" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Interpreter (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>Interpreter (956 samples, 96.08%)</title><rect x="55.1" y="1009" width="1133.7" height="15.0" fill="rgb(207,61,61)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Interpreter</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (5 samples, 0.50%)</title><rect x="661.1" y="785" width="5.9" height="15.0" fill="rgb(53,202,53)" rx="2" ry="2" />
<text text-anchor="" x="664.08" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/charset/Charset:.lookup (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/charset/Charset:.lookup (2 samples, 0.20%)</title><rect x="256.7" y="753" width="2.3" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="259.67" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)</title><rect x="186.7" y="849" width="1.2" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="189.70" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.checkSrcIndex (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.checkSrcIndex (1 samples, 0.10%)</title><rect x="230.6" y="753" width="1.2" height="15.0" fill="rgb(79,227,79)" rx="2" ry="2" />
<text text-anchor="" x="233.58" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read (939 samples, 94.37%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read (939 samples, 94.37%)</title><rect x="66.9" y="961" width="1113.6" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="69.92" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (1 samples, 0.10%)</title><rect x="307.7" y="785" width="1.2" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="310.67" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_clean_rtx_queue (14 samples, 1.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_clean_rtx_queue (14 samples, 1.41%)</title><rect x="1046.5" y="241" width="16.6" height="15.0" fill="rgb(201,52,52)" rx="2" ry="2" />
<text text-anchor="" x="1049.50" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (8 samples, 0.80%)</title><rect x="205.7" y="801" width="9.5" height="15.0" fill="rgb(80,228,80)" rx="2" ry="2" />
<text text-anchor="" x="208.68" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue (1 samples, 0.10%)</title><rect x="423.9" y="721" width="1.2" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="426.89" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="651.6" y="769" width="1.2" height="15.0" fill="rgb(65,214,65)" rx="2" ry="2" />
<text text-anchor="" x="654.59" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.init (1 samples, 0.10%)</title><rect x="872.2" y="881" width="1.2" height="15.0" fill="rgb(91,237,91)" rx="2" ry="2" />
<text text-anchor="" x="875.17" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)</title><rect x="283.9" y="641" width="1.2" height="15.0" fill="rgb(69,217,69)" rx="2" ry="2" />
<text text-anchor="" x="286.95" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaObject:.initMembers (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaObject:.initMembers (4 samples, 0.40%)</title><rect x="191.4" y="833" width="4.8" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="194.45" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.bind (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.bind (1 samples, 0.10%)</title><rect x="425.1" y="737" width="1.2" height="15.0" fill="rgb(87,234,87)" rx="2" ry="2" />
<text text-anchor="" x="428.08" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/impl/DefaultVertx:.setContext (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/impl/DefaultVertx:.setContext (1 samples, 0.10%)</title><rect x="806.9" y="865" width="1.2" height="15.0" fill="rgb(78,225,78)" rx="2" ry="2" />
<text text-anchor="" x="809.94" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="651.6" y="753" width="1.2" height="15.0" fill="rgb(91,238,91)" rx="2" ry="2" />
<text text-anchor="" x="654.59" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SocketChannelImpl:.isConnected (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SocketChannelImpl:.isConnected (2 samples, 0.20%)</title><rect x="1159.2" y="785" width="2.3" height="15.0" fill="rgb(56,206,56)" rx="2" ry="2" />
<text text-anchor="" x="1162.17" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (4 samples, 0.40%)</title><rect x="352.7" y="721" width="4.8" height="15.0" fill="rgb(109,254,109)" rx="2" ry="2" />
<text text-anchor="" x="355.73" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('frame::oops_do_internal (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>frame::oops_do_internal (1 samples, 0.10%)</title><rect x="52.7" y="1105" width="1.2" height="15.0" fill="rgb(201,201,59)" rx="2" ry="2" />
<text text-anchor="" x="55.69" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setName (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setName (5 samples, 0.50%)</title><rect x="667.0" y="785" width="5.9" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="670.01" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (3 samples, 0.30%)</title><rect x="575.7" y="769" width="3.5" height="15.0" fill="rgb(100,246,100)" rx="2" ry="2" />
<text text-anchor="" x="578.69" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (1 samples, 0.10%)</title><rect x="63.4" y="961" width="1.2" height="15.0" fill="rgb(55,204,55)" rx="2" ry="2" />
<text text-anchor="" x="66.37" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (4 samples, 0.40%)</title><rect x="529.4" y="753" width="4.8" height="15.0" fill="rgb(79,227,79)" rx="2" ry="2" />
<text text-anchor="" x="532.44" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (6 samples, 0.60%)</title><rect x="522.3" y="753" width="7.1" height="15.0" fill="rgb(54,203,54)" rx="2" ry="2" />
<text text-anchor="" x="525.32" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_release_all (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_release_all (3 samples, 0.30%)</title><rect x="1056.0" y="209" width="3.5" height="15.0" fill="rgb(236,103,103)" rx="2" ry="2" />
<text text-anchor="" x="1058.99" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (30 samples, 3.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (30 samples, 3.02%)</title><rect x="682.4" y="769" width="35.6" height="15.0" fill="rgb(70,218,70)" rx="2" ry="2" />
<text text-anchor="" x="685.42" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lor..</text>
</g>
<g class="func_g" onmouseover="s('sk_reset_timer (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (5 samples, 0.50%)</title><rect x="1029.9" y="225" width="5.9" height="15.0" fill="rgb(238,106,106)" rx="2" ry="2" />
<text text-anchor="" x="1032.90" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (416 samples, 41.81%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (416 samples, 41.81%)</title><rect x="310.0" y="817" width="493.4" height="15.0" fill="rgb(65,213,65)" rx="2" ry="2" />
<text text-anchor="" x="313.04" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_ver..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.hash (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.hash (1 samples, 0.10%)</title><rect x="850.8" y="865" width="1.2" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="853.82" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="662.3" y="753" width="1.1" height="15.0" fill="rgb(69,217,69)" rx="2" ry="2" />
<text text-anchor="" x="665.26" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="565.0" y="769" width="1.2" height="15.0" fill="rgb(234,100,100)" rx="2" ry="2" />
<text text-anchor="" x="568.02" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__slab_alloc (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__slab_alloc (1 samples, 0.10%)</title><rect x="1139.0" y="561" width="1.2" height="15.0" fill="rgb(214,71,71)" rx="2" ry="2" />
<text text-anchor="" x="1142.01" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vtable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.10%)</title><rect x="803.4" y="817" width="1.2" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="806.39" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('PSPromotionManager::drain_stacks_depth (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>PSPromotionManager::drain_stacks_depth (2 samples, 0.20%)</title><rect x="44.4" y="1105" width="2.4" height="15.0" fill="rgb(225,225,68)" rx="2" ry="2" />
<text text-anchor="" x="47.39" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('do_softirq (103 samples, 10.35%)')" onmouseout="c()" onclick="zoom(this)">
<title>do_softirq (103 samples, 10.35%)</title><rect x="994.3" y="481" width="122.2" height="15.0" fill="rgb(205,58,58)" rx="2" ry="2" />
<text text-anchor="" x="997.32" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_softirq</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)</title><rect x="821.2" y="897" width="1.2" height="15.0" fill="rgb(68,216,68)" rx="2" ry="2" />
<text text-anchor="" x="824.18" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.newPromise (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.newPromise (1 samples, 0.10%)</title><rect x="243.6" y="753" width="1.2" height="15.0" fill="rgb(81,228,81)" rx="2" ry="2" />
<text text-anchor="" x="246.63" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/impl/DefaultVertx:.setContext (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/impl/DefaultVertx:.setContext (1 samples, 0.10%)</title><rect x="808.1" y="881" width="1.2" height="15.0" fill="rgb(95,242,95)" rx="2" ry="2" />
<text text-anchor="" x="811.13" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vfs_read (25 samples, 2.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>vfs_read (25 samples, 2.51%)</title><rect x="96.6" y="833" width="29.6" height="15.0" fill="rgb(210,65,65)" rx="2" ry="2" />
<text text-anchor="" x="99.57" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vf..</text>
</g>
<g class="func_g" onmouseover="s('change_protection_range (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>change_protection_range (1 samples, 0.10%)</title><rect x="1188.8" y="1025" width="1.2" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__do_softirq (103 samples, 10.35%)')" onmouseout="c()" onclick="zoom(this)">
<title>__do_softirq (103 samples, 10.35%)</title><rect x="994.3" y="449" width="122.2" height="15.0" fill="rgb(238,105,105)" rx="2" ry="2" />
<text text-anchor="" x="997.32" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__do_softirq</text>
</g>
<g class="func_g" onmouseover="s('ipv4_dst_check (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ipv4_dst_check (1 samples, 0.10%)</title><rect x="1021.6" y="289" width="1.2" height="15.0" fill="rgb(237,105,105)" rx="2" ry="2" />
<text text-anchor="" x="1024.60" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('change_protection (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>change_protection (1 samples, 0.10%)</title><rect x="1188.8" y="1041" width="1.2" height="15.0" fill="rgb(229,93,93)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (7 samples, 0.70%)</title><rect x="447.6" y="721" width="8.3" height="15.0" fill="rgb(58,207,58)" rx="2" ry="2" />
<text text-anchor="" x="450.61" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/Recycler:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/Recycler:.get (1 samples, 0.10%)</title><rect x="882.8" y="913" width="1.2" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="885.84" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.10%)</title><rect x="306.5" y="785" width="1.2" height="15.0" fill="rgb(89,236,89)" rx="2" ry="2" />
<text text-anchor="" x="309.48" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_queue_rcv (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_queue_rcv (2 samples, 0.20%)</title><rect x="1109.4" y="241" width="2.3" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1112.36" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('jlong_disjoint_arraycopy (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>jlong_disjoint_arraycopy (1 samples, 0.10%)</title><rect x="254.3" y="705" width="1.2" height="15.0" fill="rgb(232,96,96)" rx="2" ry="2" />
<text text-anchor="" x="257.30" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete (242 samples, 24.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete (242 samples, 24.32%)</title><rect x="885.2" y="929" width="287.0" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="888.22" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/handler/codec/ByteToMessageD..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (3 samples, 0.30%)</title><rect x="835.4" y="881" width="3.6" height="15.0" fill="rgb(66,214,66)" rx="2" ry="2" />
<text text-anchor="" x="838.41" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>copy_user_generic_string (3 samples, 0.30%)</title><rect x="116.7" y="721" width="3.6" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="119.73" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (5 samples, 0.50%)</title><rect x="339.7" y="673" width="5.9" height="15.0" fill="rgb(93,239,93)" rx="2" ry="2" />
<text text-anchor="" x="342.69" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>sched_clock_cpu (1 samples, 0.10%)</title><rect x="1096.3" y="49" width="1.2" height="15.0" fill="rgb(209,63,63)" rx="2" ry="2" />
<text text-anchor="" x="1099.31" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SocketChannelImpl:.read (40 samples, 4.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SocketChannelImpl:.read (40 samples, 4.02%)</title><rect x="81.2" y="929" width="47.4" height="15.0" fill="rgb(90,237,90)" rx="2" ry="2" />
<text text-anchor="" x="84.16" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lsun..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setName (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setName (2 samples, 0.20%)</title><rect x="440.5" y="737" width="2.4" height="15.0" fill="rgb(67,215,67)" rx="2" ry="2" />
<text text-anchor="" x="443.49" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="427.4" y="721" width="1.2" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="430.45" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="369.3" y="689" width="1.2" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="372.34" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="196.2" y="817" width="1.2" height="15.0" fill="rgb(75,222,75)" rx="2" ry="2" />
<text text-anchor="" x="199.19" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannel:.hashCode (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannel:.hashCode (4 samples, 0.40%)</title><rect x="158.2" y="881" width="4.8" height="15.0" fill="rgb(63,212,63)" rx="2" ry="2" />
<text text-anchor="" x="161.24" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)</title><rect x="210.4" y="785" width="2.4" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="213.42" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBufAllocator:.heapBuffer (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBufAllocator:.heapBuffer (1 samples, 0.10%)</title><rect x="221.1" y="769" width="1.2" height="15.0" fill="rgb(102,248,102)" rx="2" ry="2" />
<text text-anchor="" x="224.10" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="422.7" y="705" width="1.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="425.70" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/Recycler:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/Recycler:.get (1 samples, 0.10%)</title><rect x="302.9" y="689" width="1.2" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="305.92" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectEncoder:.encode (17 samples, 1.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectEncoder:.encode (17 samples, 1.71%)</title><rect x="276.8" y="673" width="20.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="279.83" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.flush (235 samples, 23.62%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.flush (235 samples, 23.62%)</title><rect x="887.6" y="849" width="278.7" height="15.0" fill="rgb(65,213,65)" rx="2" ry="2" />
<text text-anchor="" x="890.59" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHand..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (9 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (9 samples, 0.90%)</title><rect x="538.9" y="753" width="10.7" height="15.0" fill="rgb(69,218,69)" rx="2" ry="2" />
<text text-anchor="" x="541.92" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)</title><rect x="298.2" y="657" width="1.2" height="15.0" fill="rgb(76,224,76)" rx="2" ry="2" />
<text text-anchor="" x="301.18" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="519.9" y="721" width="1.2" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="522.95" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (47 samples, 4.72%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (47 samples, 4.72%)</title><rect x="718.0" y="769" width="55.7" height="15.0" fill="rgb(69,217,69)" rx="2" ry="2" />
<text text-anchor="" x="721.00" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/..</text>
</g>
<g class="func_g" onmouseover="s('ClassLoaderDataGraph::oops_do (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ClassLoaderDataGraph::oops_do (1 samples, 0.10%)</title><rect x="46.8" y="1121" width="1.1" height="15.0" fill="rgb(208,208,62)" rx="2" ry="2" />
<text text-anchor="" x="49.76" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaMethod:.findCachedFunction (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaMethod:.findCachedFunction (2 samples, 0.20%)</title><rect x="639.7" y="785" width="2.4" height="15.0" fill="rgb(87,234,87)" rx="2" ry="2" />
<text text-anchor="" x="642.73" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)</title><rect x="508.1" y="737" width="1.2" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="511.09" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.init (1 samples, 0.10%)</title><rect x="871.0" y="865" width="1.2" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="873.98" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sk_reset_timer (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (2 samples, 0.20%)</title><rect x="967.0" y="561" width="2.4" height="15.0" fill="rgb(227,90,90)" rx="2" ry="2" />
<text text-anchor="" x="970.05" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (1 samples, 0.10%)</title><rect x="249.6" y="737" width="1.1" height="15.0" fill="rgb(95,241,95)" rx="2" ry="2" />
<text text-anchor="" x="252.56" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (5 samples, 0.50%)</title><rect x="655.1" y="785" width="6.0" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="658.15" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/NativeThread:.current (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/NativeThread:.current (1 samples, 0.10%)</title><rect x="1156.8" y="753" width="1.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="1159.79" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('netdev_pick_tx (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>netdev_pick_tx (1 samples, 0.10%)</title><rect x="991.9" y="481" width="1.2" height="15.0" fill="rgb(244,114,114)" rx="2" ry="2" />
<text text-anchor="" x="994.95" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ParallelTaskTerminator::offer_termination (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>ParallelTaskTerminator::offer_termination (2 samples, 0.20%)</title><rect x="47.9" y="1121" width="2.4" height="15.0" fill="rgb(195,195,57)" rx="2" ry="2" />
<text text-anchor="" x="50.95" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="884.0" y="913" width="1.2" height="15.0" fill="rgb(235,102,102)" rx="2" ry="2" />
<text text-anchor="" x="887.03" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="519.9" y="753" width="1.2" height="15.0" fill="rgb(66,215,66)" rx="2" ry="2" />
<text text-anchor="" x="522.95" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__copy_skb_header (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__copy_skb_header (1 samples, 0.10%)</title><rect x="1123.6" y="529" width="1.2" height="15.0" fill="rgb(207,60,60)" rx="2" ry="2" />
<text text-anchor="" x="1126.59" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('pthread_self (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>pthread_self (1 samples, 0.10%)</title><rect x="1158.0" y="753" width="1.2" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="1160.98" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (2 samples, 0.20%)</title><rect x="396.6" y="705" width="2.4" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="399.61" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.checkIndex (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.checkIndex (3 samples, 0.30%)</title><rect x="234.1" y="721" width="3.6" height="15.0" fill="rgb(87,233,87)" rx="2" ry="2" />
<text text-anchor="" x="237.14" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__tcp_ack_snd_check (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_ack_snd_check (5 samples, 0.50%)</title><rect x="1029.9" y="257" width="5.9" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="1032.90" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_send_mss (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send_mss (6 samples, 0.60%)</title><rect x="1142.6" y="609" width="7.1" height="15.0" fill="rgb(231,95,95)" rx="2" ry="2" />
<text text-anchor="" x="1145.56" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_bh (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_bh (1 samples, 0.10%)</title><rect x="115.5" y="721" width="1.2" height="15.0" fill="rgb(203,55,55)" rx="2" ry="2" />
<text text-anchor="" x="118.55" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (1 samples, 0.10%)</title><rect x="1188.8" y="1089" width="1.2" height="15.0" fill="rgb(225,87,87)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="716.8" y="737" width="1.2" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="719.81" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/Integer:.toString (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/Integer:.toString (1 samples, 0.10%)</title><rect x="251.9" y="753" width="1.2" height="15.0" fill="rgb(91,238,91)" rx="2" ry="2" />
<text text-anchor="" x="254.93" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Interpreter (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>Interpreter (956 samples, 96.08%)</title><rect x="55.1" y="1025" width="1133.7" height="15.0" fill="rgb(216,74,74)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1035.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Interpreter</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (513 samples, 51.56%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (513 samples, 51.56%)</title><rect x="197.4" y="849" width="608.4" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="200.38" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (2 samples, 0.20%)</title><rect x="514.0" y="769" width="2.4" height="15.0" fill="rgb(72,220,72)" rx="2" ry="2" />
<text text-anchor="" x="517.02" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.10%)</title><rect x="397.8" y="689" width="1.2" height="15.0" fill="rgb(109,254,109)" rx="2" ry="2" />
<text text-anchor="" x="400.80" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaMethod:.call (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaMethod:.call (10 samples, 1.01%)</title><rect x="627.9" y="785" width="11.8" height="15.0" fill="rgb(83,230,83)" rx="2" ry="2" />
<text text-anchor="" x="630.87" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__GI___mprotect (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__GI___mprotect (1 samples, 0.10%)</title><rect x="1188.8" y="1105" width="1.2" height="15.0" fill="rgb(206,59,59)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__libc_write (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__libc_write (1 samples, 0.10%)</title><rect x="1153.2" y="737" width="1.2" height="15.0" fill="rgb(229,92,92)" rx="2" ry="2" />
<text text-anchor="" x="1156.24" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/Recycler:.recycle (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/Recycler:.recycle (1 samples, 0.10%)</title><rect x="299.4" y="673" width="1.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="302.37" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="533.0" y="737" width="1.2" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="535.99" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('fput (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>fput (1 samples, 0.10%)</title><rect x="920.8" y="705" width="1.2" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="923.79" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (6 samples, 0.60%)</title><rect x="408.5" y="689" width="7.1" height="15.0" fill="rgb(103,249,103)" rx="2" ry="2" />
<text text-anchor="" x="411.47" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('VMThread::run (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>VMThread::run (1 samples, 0.10%)</title><rect x="1188.8" y="1153" width="1.2" height="15.0" fill="rgb(221,221,66)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_send_delayed_ack (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send_delayed_ack (5 samples, 0.50%)</title><rect x="1029.9" y="241" width="5.9" height="15.0" fill="rgb(203,55,55)" rx="2" ry="2" />
<text text-anchor="" x="1032.90" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (24 samples, 2.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (24 samples, 2.41%)</title><rect x="457.1" y="705" width="28.5" height="15.0" fill="rgb(80,228,80)" rx="2" ry="2" />
<text text-anchor="" x="460.10" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lo..</text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock_irqsave (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (2 samples, 0.20%)</title><rect x="1082.1" y="113" width="2.4" height="15.0" fill="rgb(249,121,121)" rx="2" ry="2" />
<text text-anchor="" x="1085.08" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/NativeThread:.current (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/NativeThread:.current (1 samples, 0.10%)</title><rect x="127.4" y="913" width="1.2" height="15.0" fill="rgb(94,240,94)" rx="2" ry="2" />
<text text-anchor="" x="130.41" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (12 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (12 samples, 1.21%)</title><rect x="359.8" y="705" width="14.3" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="362.85" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_v4_md5_lookup (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_md5_lookup (1 samples, 0.10%)</title><rect x="1148.5" y="577" width="1.2" height="15.0" fill="rgb(236,102,102)" rx="2" ry="2" />
<text text-anchor="" x="1151.49" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_check_space (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_check_space (1 samples, 0.10%)</title><rect x="1027.5" y="273" width="1.2" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="1030.53" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (7 samples, 0.70%)</title><rect x="415.6" y="721" width="8.3" height="15.0" fill="rgb(57,207,57)" rx="2" ry="2" />
<text text-anchor="" x="418.59" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ip_local_deliver_finish (89 samples, 8.94%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver_finish (89 samples, 8.94%)</title><rect x="1009.7" y="321" width="105.6" height="15.0" fill="rgb(224,84,84)" rx="2" ry="2" />
<text text-anchor="" x="1012.74" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_local_del..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.validatePromise (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.validatePromise (2 samples, 0.20%)</title><rect x="261.4" y="737" width="2.4" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="264.42" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('SafepointSynchronize::begin (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>SafepointSynchronize::begin (1 samples, 0.10%)</title><rect x="1188.8" y="1121" width="1.2" height="15.0" fill="rgb(224,224,68)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_data_queue (39 samples, 3.92%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_data_queue (39 samples, 3.92%)</title><rect x="1067.8" y="257" width="46.3" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="1070.85" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_..</text>
</g>
<g class="func_g" onmouseover="s('tcp_clean_rtx_queue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_clean_rtx_queue (1 samples, 0.10%)</title><rect x="1066.7" y="257" width="1.1" height="15.0" fill="rgb(228,92,92)" rx="2" ry="2" />
<text text-anchor="" x="1069.66" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('GCTaskThread::run (28 samples, 2.81%)')" onmouseout="c()" onclick="zoom(this)">
<title>GCTaskThread::run (28 samples, 2.81%)</title><rect x="21.9" y="1153" width="33.2" height="15.0" fill="rgb(179,179,51)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >GC..</text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock_bh (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.10%)</title><rect x="114.4" y="721" width="1.1" height="15.0" fill="rgb(251,125,125)" rx="2" ry="2" />
<text text-anchor="" x="117.36" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('security_file_permission (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (2 samples, 0.20%)</title><rect x="122.7" y="801" width="2.3" height="15.0" fill="rgb(245,116,116)" rx="2" ry="2" />
<text text-anchor="" x="125.66" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('call_stub (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>call_stub (956 samples, 96.08%)</title><rect x="55.1" y="1057" width="1133.7" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >call_stub</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (1 samples, 0.10%)</title><rect x="569.8" y="785" width="1.1" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="572.76" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (156 samples, 15.68%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (156 samples, 15.68%)</title><rect x="324.3" y="769" width="185.0" height="15.0" fill="rgb(57,207,57)" rx="2" ry="2" />
<text text-anchor="" x="327.27" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript/..</text>
</g>
<g class="func_g" onmouseover="s('tcp_urg (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_urg (1 samples, 0.10%)</title><rect x="1114.1" y="257" width="1.2" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="1117.10" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="436.9" y="721" width="1.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="439.93" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundBuffer:.decrementPendingOutboundBytes (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundBuffer:.decrementPendingOutboundBytes (1 samples, 0.10%)</title><rect x="903.0" y="769" width="1.2" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="906.01" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/Recycler:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/Recycler:.get (1 samples, 0.10%)</title><rect x="298.2" y="673" width="1.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="301.18" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lock_timer_base.isra.35 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>lock_timer_base.isra.35 (1 samples, 0.10%)</title><rect x="968.2" y="529" width="1.2" height="15.0" fill="rgb(202,54,54)" rx="2" ry="2" />
<text text-anchor="" x="971.23" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('release_sock (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>release_sock (1 samples, 0.10%)</title><rect x="115.5" y="737" width="1.2" height="15.0" fill="rgb(200,50,50)" rx="2" ry="2" />
<text text-anchor="" x="118.55" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.flush (238 samples, 23.92%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.flush (238 samples, 23.92%)</title><rect x="886.4" y="881" width="282.3" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="889.40" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHand..</text>
</g>
<g class="func_g" onmouseover="s('ip_rcv_finish (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv_finish (1 samples, 0.10%)</title><rect x="1115.3" y="369" width="1.2" height="15.0" fill="rgb(208,61,61)" rx="2" ry="2" />
<text text-anchor="" x="1118.29" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>account_entity_enqueue (1 samples, 0.10%)</title><rect x="1091.6" y="33" width="1.2" height="15.0" fill="rgb(203,55,55)" rx="2" ry="2" />
<text text-anchor="" x="1094.57" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('remote_function (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>remote_function (4 samples, 0.40%)</title><rect x="935.0" y="593" width="4.8" height="15.0" fill="rgb(203,55,55)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (8 samples, 0.80%)</title><rect x="553.2" y="705" width="9.4" height="15.0" fill="rgb(57,206,57)" rx="2" ry="2" />
<text text-anchor="" x="556.16" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('fdval (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>fdval (1 samples, 0.10%)</title><rect x="126.2" y="881" width="1.2" height="15.0" fill="rgb(205,58,58)" rx="2" ry="2" />
<text text-anchor="" x="129.22" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.write (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.write (2 samples, 0.20%)</title><rect x="244.8" y="753" width="2.4" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="247.81" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('aa_revalidate_sk (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>aa_revalidate_sk (1 samples, 0.10%)</title><rect x="1149.7" y="625" width="1.2" height="15.0" fill="rgb(233,99,99)" rx="2" ry="2" />
<text text-anchor="" x="1152.68" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('rw_verify_area (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>rw_verify_area (2 samples, 0.20%)</title><rect x="122.7" y="817" width="2.3" height="15.0" fill="rgb(232,97,97)" rx="2" ry="2" />
<text text-anchor="" x="125.66" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/nio/NioEventLoop:.select (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/nio/NioEventLoop:.select (7 samples, 0.70%)</title><rect x="1180.5" y="993" width="8.3" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="1183.51" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="351.5" y="705" width="1.2" height="15.0" fill="rgb(71,220,71)" rx="2" ry="2" />
<text text-anchor="" x="354.55" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (11 samples, 1.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (11 samples, 1.11%)</title><rect x="375.3" y="657" width="13.0" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="378.27" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('JavaCalls::call_helper (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>JavaCalls::call_helper (956 samples, 96.08%)</title><rect x="55.1" y="1073" width="1133.7" height="15.0" fill="rgb(185,185,53)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaCalls::call_helper</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)</title><rect x="563.8" y="769" width="1.2" height="15.0" fill="rgb(80,228,80)" rx="2" ry="2" />
<text text-anchor="" x="566.83" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectEncoder:.encode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectEncoder:.encode (1 samples, 0.10%)</title><rect x="301.7" y="689" width="1.2" height="15.0" fill="rgb(65,214,65)" rx="2" ry="2" />
<text text-anchor="" x="304.74" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.trim (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.trim (1 samples, 0.10%)</title><rect x="876.9" y="897" width="1.2" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="879.91" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vtable stub (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (2 samples, 0.20%)</title><rect x="212.8" y="785" width="2.4" height="15.0" fill="rgb(210,65,65)" rx="2" ry="2" />
<text text-anchor="" x="215.79" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaMethod:.findCachedFunction (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaMethod:.findCachedFunction (1 samples, 0.10%)</title><rect x="305.3" y="785" width="1.2" height="15.0" fill="rgb(50,200,50)" rx="2" ry="2" />
<text text-anchor="" x="308.30" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (77 samples, 7.74%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (77 samples, 7.74%)</title><rect x="217.5" y="801" width="91.4" height="15.0" fill="rgb(53,202,53)" rx="2" ry="2" />
<text text-anchor="" x="220.54" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozil..</text>
</g>
<g class="func_g" onmouseover="s('x86_pmu_commit_txn (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_commit_txn (4 samples, 0.40%)</title><rect x="935.0" y="545" width="4.8" height="15.0" fill="rgb(243,112,112)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_network_protocol (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_network_protocol (1 samples, 0.10%)</title><rect x="990.8" y="433" width="1.1" height="15.0" fill="rgb(223,84,84)" rx="2" ry="2" />
<text text-anchor="" x="993.76" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/net/impl/ConnectionBase:.write (38 samples, 3.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/net/impl/ConnectionBase:.write (38 samples, 3.82%)</title><rect x="260.2" y="753" width="45.1" height="15.0" fill="rgb(94,240,94)" rx="2" ry="2" />
<text text-anchor="" x="263.23" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpMethod:.valueOf (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpMethod:.valueOf (2 samples, 0.20%)</title><rect x="809.3" y="913" width="2.4" height="15.0" fill="rgb(101,246,101)" rx="2" ry="2" />
<text text-anchor="" x="812.32" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (9 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (9 samples, 0.90%)</title><rect x="497.4" y="721" width="10.7" height="15.0" fill="rgb(79,226,79)" rx="2" ry="2" />
<text text-anchor="" x="500.42" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('enqueue_to_backlog (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_to_backlog (1 samples, 0.10%)</title><rect x="987.2" y="417" width="1.2" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="990.21" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="422.7" y="689" width="1.2" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="425.70" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('security_socket_sendmsg (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>security_socket_sendmsg (1 samples, 0.10%)</title><rect x="1149.7" y="641" width="1.2" height="15.0" fill="rgb(220,79,79)" rx="2" ry="2" />
<text text-anchor="" x="1152.68" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('[unknown] (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (10 samples, 1.01%)</title><rect x="10.0" y="1169" width="11.9" height="15.0" fill="rgb(247,119,119)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized (949 samples, 95.38%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized (949 samples, 95.38%)</title><rect x="55.1" y="977" width="1125.4" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundHandlerAdapter:.read (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundHandlerAdapter:.read (2 samples, 0.20%)</title><rect x="1174.6" y="897" width="2.4" height="15.0" fill="rgb(57,206,57)" rx="2" ry="2" />
<text text-anchor="" x="1177.58" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="875.7" y="897" width="1.2" height="15.0" fill="rgb(93,239,93)" rx="2" ry="2" />
<text text-anchor="" x="878.73" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="455.9" y="705" width="1.2" height="15.0" fill="rgb(71,220,71)" rx="2" ry="2" />
<text text-anchor="" x="458.91" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__kmalloc_node_track_caller (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_node_track_caller (1 samples, 0.10%)</title><rect x="1133.1" y="561" width="1.2" height="15.0" fill="rgb(206,59,59)" rx="2" ry="2" />
<text text-anchor="" x="1136.08" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_v4_send_check (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_send_check (1 samples, 0.10%)</title><rect x="1124.8" y="561" width="1.2" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="1127.77" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getPrototype (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getPrototype (1 samples, 0.10%)</title><rect x="776.1" y="785" width="1.2" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="779.11" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (11 samples, 1.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (11 samples, 1.11%)</title><rect x="549.6" y="753" width="13.0" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="552.60" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/IOUtil:.readIntoNativeBuffer (31 samples, 3.12%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/IOUtil:.readIntoNativeBuffer (31 samples, 3.12%)</title><rect x="90.6" y="913" width="36.8" height="15.0" fill="rgb(104,250,104)" rx="2" ry="2" />
<text text-anchor="" x="93.64" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lsu..</text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SocketChannelImpl:.write (209 samples, 21.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SocketChannelImpl:.write (209 samples, 21.01%)</title><rect x="911.3" y="769" width="247.9" height="15.0" fill="rgb(66,214,66)" rx="2" ry="2" />
<text text-anchor="" x="914.31" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lsun/nio/ch/SocketChannelImpl:.wr..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="562.6" y="753" width="1.2" height="15.0" fill="rgb(76,224,76)" rx="2" ry="2" />
<text text-anchor="" x="565.64" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('enqueue_entity (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_entity (5 samples, 0.50%)</title><rect x="1090.4" y="49" width="5.9" height="15.0" fill="rgb(247,118,118)" rx="2" ry="2" />
<text text-anchor="" x="1093.38" y="59.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('fget_light (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (2 samples, 0.20%)</title><rect x="922.0" y="689" width="2.4" height="15.0" fill="rgb(252,126,126)" rx="2" ry="2" />
<text text-anchor="" x="924.98" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpMessage:.init (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpMessage:.init (2 samples, 0.20%)</title><rect x="176.0" y="865" width="2.4" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="179.03" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('enqueue_task (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_task (7 samples, 0.70%)</title><rect x="1089.2" y="81" width="8.3" height="15.0" fill="rgb(238,105,105)" rx="2" ry="2" />
<text text-anchor="" x="1092.20" y="91.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.10%)</title><rect x="986.0" y="417" width="1.2" height="15.0" fill="rgb(210,64,64)" rx="2" ry="2" />
<text text-anchor="" x="989.02" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ttwu_do_activate.constprop.74 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_activate.constprop.74 (1 samples, 0.10%)</title><rect x="1105.8" y="129" width="1.2" height="15.0" fill="rgb(241,109,109)" rx="2" ry="2" />
<text text-anchor="" x="1108.80" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (33 samples, 3.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (33 samples, 3.32%)</title><rect x="734.6" y="721" width="39.1" height="15.0" fill="rgb(79,226,79)" rx="2" ry="2" />
<text text-anchor="" x="737.60" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lor..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/WrapFactory:.setJavaPrimitiveWrap (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/WrapFactory:.setJavaPrimitiveWrap (1 samples, 0.10%)</title><rect x="805.8" y="833" width="1.1" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="808.76" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.hash (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.hash (4 samples, 0.40%)</title><rect x="824.7" y="897" width="4.8" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="827.73" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (24 samples, 2.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>try_to_wake_up (24 samples, 2.41%)</title><rect x="1077.3" y="129" width="28.5" height="15.0" fill="rgb(249,121,121)" rx="2" ry="2" />
<text text-anchor="" x="1080.34" y="139.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tr..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush (232 samples, 23.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush (232 samples, 23.32%)</title><rect x="887.6" y="801" width="275.1" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="890.59" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/DefaultChannelPipe..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (12 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (12 samples, 1.21%)</title><rect x="374.1" y="689" width="14.2" height="15.0" fill="rgb(106,251,106)" rx="2" ry="2" />
<text text-anchor="" x="377.08" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpMethod:.valueOf (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpMethod:.valueOf (2 samples, 0.20%)</title><rect x="829.5" y="897" width="2.3" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="832.48" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__internal_add_timer (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__internal_add_timer (1 samples, 0.10%)</title><rect x="967.0" y="513" width="1.2" height="15.0" fill="rgb(243,113,113)" rx="2" ry="2" />
<text text-anchor="" x="970.05" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock_irqsave (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.10%)</title><rect x="1108.2" y="225" width="1.2" height="15.0" fill="rgb(218,77,77)" rx="2" ry="2" />
<text text-anchor="" x="1111.17" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('generic_smp_call_function_single_interrupt (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>generic_smp_call_function_single_interrupt (4 samples, 0.40%)</title><rect x="935.0" y="609" width="4.8" height="15.0" fill="rgb(209,63,63)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('mod_timer (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (5 samples, 0.50%)</title><rect x="958.7" y="529" width="6.0" height="15.0" fill="rgb(228,90,90)" rx="2" ry="2" />
<text text-anchor="" x="961.74" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (21 samples, 2.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (21 samples, 2.11%)</title><rect x="586.4" y="753" width="24.9" height="15.0" fill="rgb(107,253,107)" rx="2" ry="2" />
<text text-anchor="" x="589.36" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('InstanceKlass::oop_push_contents (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>InstanceKlass::oop_push_contents (1 samples, 0.10%)</title><rect x="52.7" y="1041" width="1.2" height="15.0" fill="rgb(222,222,67)" rx="2" ry="2" />
<text text-anchor="" x="55.69" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="578.1" y="753" width="1.1" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="581.06" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (37 samples, 3.72%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (37 samples, 3.72%)</title><rect x="442.9" y="737" width="43.8" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="445.86" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="436.9" y="705" width="1.2" height="15.0" fill="rgb(53,202,53)" rx="2" ry="2" />
<text text-anchor="" x="439.93" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_rcv_established (73 samples, 7.34%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rcv_established (73 samples, 7.34%)</title><rect x="1028.7" y="273" width="86.6" height="15.0" fill="rgb(228,92,92)" rx="2" ry="2" />
<text text-anchor="" x="1031.71" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_rcv_es..</text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/EPollSelectorImpl:.updateSelectedKeys (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/EPollSelectorImpl:.updateSelectedKeys (1 samples, 0.10%)</title><rect x="1187.6" y="961" width="1.2" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="1190.63" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.10%)</title><rect x="774.9" y="785" width="1.2" height="15.0" fill="rgb(54,204,54)" rx="2" ry="2" />
<text text-anchor="" x="777.92" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('apparmor_file_permission (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_file_permission (1 samples, 0.10%)</title><rect x="122.7" y="785" width="1.1" height="15.0" fill="rgb(203,55,55)" rx="2" ry="2" />
<text text-anchor="" x="125.66" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)</title><rect x="128.6" y="945" width="1.2" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="131.59" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('kmalloc_slab (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>kmalloc_slab (2 samples, 0.20%)</title><rect x="1134.3" y="561" width="2.3" height="15.0" fill="rgb(232,97,97)" rx="2" ry="2" />
<text text-anchor="" x="1137.26" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lock_sock_nested (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.10%)</title><rect x="1127.1" y="609" width="1.2" height="15.0" fill="rgb(220,80,80)" rx="2" ry="2" />
<text text-anchor="" x="1130.15" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.write (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.write (6 samples, 0.60%)</title><rect x="267.3" y="673" width="7.2" height="15.0" fill="rgb(65,214,65)" rx="2" ry="2" />
<text text-anchor="" x="270.35" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_def_readable (32 samples, 3.22%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_def_readable (32 samples, 3.22%)</title><rect x="1071.4" y="241" width="38.0" height="15.0" fill="rgb(218,76,76)" rx="2" ry="2" />
<text text-anchor="" x="1074.41" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >soc..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="549.6" y="737" width="1.2" height="15.0" fill="rgb(76,224,76)" rx="2" ry="2" />
<text text-anchor="" x="552.60" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject$PrototypeValues:.ensureId (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject$PrototypeValues:.ensureId (1 samples, 0.10%)</title><rect x="435.7" y="705" width="1.2" height="15.0" fill="rgb(66,215,66)" rx="2" ry="2" />
<text text-anchor="" x="438.75" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sys_mprotect (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>sys_mprotect (1 samples, 0.10%)</title><rect x="1188.8" y="1073" width="1.2" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.10%)</title><rect x="1080.9" y="113" width="1.2" height="15.0" fill="rgb(205,57,57)" rx="2" ry="2" />
<text text-anchor="" x="1083.89" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SelectorImpl:.select (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SelectorImpl:.select (7 samples, 0.70%)</title><rect x="1180.5" y="977" width="8.3" height="15.0" fill="rgb(56,206,56)" rx="2" ry="2" />
<text text-anchor="" x="1183.51" y="987.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.hash (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.hash (1 samples, 0.10%)</title><rect x="250.7" y="737" width="1.2" height="15.0" fill="rgb(99,244,99)" rx="2" ry="2" />
<text text-anchor="" x="253.74" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="649.2" y="769" width="1.2" height="15.0" fill="rgb(241,110,110)" rx="2" ry="2" />
<text text-anchor="" x="652.22" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (2 samples, 0.20%)</title><rect x="680.1" y="769" width="2.3" height="15.0" fill="rgb(65,214,65)" rx="2" ry="2" />
<text text-anchor="" x="683.05" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.execIdCall (48 samples, 4.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.execIdCall (48 samples, 4.82%)</title><rect x="509.3" y="785" width="56.9" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="512.28" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/m..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (12 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (12 samples, 1.21%)</title><rect x="374.1" y="705" width="14.2" height="15.0" fill="rgb(57,206,57)" rx="2" ry="2" />
<text text-anchor="" x="377.08" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('release_sock (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>release_sock (2 samples, 0.20%)</title><rect x="1128.3" y="609" width="2.4" height="15.0" fill="rgb(201,51,51)" rx="2" ry="2" />
<text text-anchor="" x="1131.33" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.writeBytes (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.writeBytes (5 samples, 0.50%)</title><rect x="231.8" y="753" width="5.9" height="15.0" fill="rgb(67,216,67)" rx="2" ry="2" />
<text text-anchor="" x="234.77" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('mod_timer (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (2 samples, 0.20%)</title><rect x="967.0" y="545" width="2.4" height="15.0" fill="rgb(242,112,112)" rx="2" ry="2" />
<text text-anchor="" x="970.05" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)</title><rect x="440.5" y="721" width="1.2" height="15.0" fill="rgb(68,216,68)" rx="2" ry="2" />
<text text-anchor="" x="443.49" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('select_task_rq_fair (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>select_task_rq_fair (4 samples, 0.40%)</title><rect x="1084.5" y="113" width="4.7" height="15.0" fill="rgb(237,103,103)" rx="2" ry="2" />
<text text-anchor="" x="1087.45" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (17 samples, 1.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (17 samples, 1.71%)</title><rect x="591.1" y="737" width="20.2" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="594.11" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('fget_light (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>fget_light (3 samples, 0.30%)</title><rect x="93.0" y="833" width="3.6" height="15.0" fill="rgb(231,95,95)" rx="2" ry="2" />
<text text-anchor="" x="96.02" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="802.2" y="785" width="1.2" height="15.0" fill="rgb(231,95,95)" rx="2" ry="2" />
<text text-anchor="" x="805.20" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/UnreleasableByteBuf:.duplicate (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/UnreleasableByteBuf:.duplicate (1 samples, 0.10%)</title><rect x="242.4" y="753" width="1.2" height="15.0" fill="rgb(54,204,54)" rx="2" ry="2" />
<text text-anchor="" x="245.44" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ttwu_do_activate.constprop.74 (12 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_activate.constprop.74 (12 samples, 1.21%)</title><rect x="1089.2" y="113" width="14.2" height="15.0" fill="rgb(228,92,92)" rx="2" ry="2" />
<text text-anchor="" x="1092.20" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.encode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.encode (1 samples, 0.10%)</title><rect x="275.6" y="673" width="1.2" height="15.0" fill="rgb(59,208,59)" rx="2" ry="2" />
<text text-anchor="" x="278.65" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/Arrays:.copyOf (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/Arrays:.copyOf (1 samples, 0.10%)</title><rect x="253.1" y="705" width="1.2" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="256.12" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_free_head (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_free_head (1 samples, 0.10%)</title><rect x="1058.4" y="177" width="1.1" height="15.0" fill="rgb(213,69,69)" rx="2" ry="2" />
<text text-anchor="" x="1061.36" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getBase (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getBase (4 samples, 0.40%)</title><rect x="644.5" y="769" width="4.7" height="15.0" fill="rgb(103,248,103)" rx="2" ry="2" />
<text text-anchor="" x="647.47" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/FileDispatcherImpl:.read0 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/FileDispatcherImpl:.read0 (1 samples, 0.10%)</title><rect x="89.5" y="913" width="1.1" height="15.0" fill="rgb(104,249,104)" rx="2" ry="2" />
<text text-anchor="" x="92.46" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('mod_timer (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>mod_timer (5 samples, 0.50%)</title><rect x="1029.9" y="209" width="5.9" height="15.0" fill="rgb(236,102,102)" rx="2" ry="2" />
<text text-anchor="" x="1032.90" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/PooledByteBuf:.deallocate (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/PooledByteBuf:.deallocate (5 samples, 0.50%)</title><rect x="897.1" y="753" width="5.9" height="15.0" fill="rgb(53,202,53)" rx="2" ry="2" />
<text text-anchor="" x="900.08" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sys_write (195 samples, 19.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>sys_write (195 samples, 19.60%)</title><rect x="922.0" y="705" width="231.2" height="15.0" fill="rgb(205,57,57)" rx="2" ry="2" />
<text text-anchor="" x="924.98" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sys_write</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.findPrototypeId (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.findPrototypeId (1 samples, 0.10%)</title><rect x="434.6" y="705" width="1.1" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="437.56" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys (949 samples, 95.38%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys (949 samples, 95.38%)</title><rect x="55.1" y="993" width="1125.4" height="15.0" fill="rgb(55,204,55)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1003.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys</text>
</g>
<g class="func_g" onmouseover="s('getnstimeofday (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>getnstimeofday (3 samples, 0.30%)</title><rect x="1116.5" y="545" width="3.5" height="15.0" fill="rgb(233,98,98)" rx="2" ry="2" />
<text text-anchor="" x="1119.47" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="648.0" y="753" width="1.2" height="15.0" fill="rgb(90,236,90)" rx="2" ry="2" />
<text text-anchor="" x="651.03" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders (22 samples, 2.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders (22 samples, 2.21%)</title><rect x="839.0" y="897" width="26.1" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="841.96" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.10%)</title><rect x="187.9" y="849" width="1.2" height="15.0" fill="rgb(105,250,105)" rx="2" ry="2" />
<text text-anchor="" x="190.89" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('[unknown] (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (10 samples, 1.01%)</title><rect x="10.0" y="1185" width="11.9" height="15.0" fill="rgb(221,81,81)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_clone (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_clone (4 samples, 0.40%)</title><rect x="1120.0" y="561" width="4.8" height="15.0" fill="rgb(208,62,62)" rx="2" ry="2" />
<text text-anchor="" x="1123.03" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/platform/impl/RhinoContextFactory:.onContextCreated (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/platform/impl/RhinoContextFactory:.onContextCreated (1 samples, 0.10%)</title><rect x="805.8" y="849" width="1.1" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="808.76" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (3 samples, 0.30%)</title><rect x="663.4" y="753" width="3.6" height="15.0" fill="rgb(79,227,79)" rx="2" ry="2" />
<text text-anchor="" x="666.45" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getParentScope (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getParentScope (3 samples, 0.30%)</title><rect x="504.5" y="705" width="3.6" height="15.0" fill="rgb(77,225,77)" rx="2" ry="2" />
<text text-anchor="" x="507.53" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpVersion:.compareTo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpVersion:.compareTo (1 samples, 0.10%)</title><rect x="163.0" y="881" width="1.2" height="15.0" fill="rgb(81,228,81)" rx="2" ry="2" />
<text text-anchor="" x="165.98" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/PooledByteBufAllocator:.newDirectBuffer (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/PooledByteBufAllocator:.newDirectBuffer (2 samples, 0.20%)</title><rect x="282.8" y="657" width="2.3" height="15.0" fill="rgb(63,211,63)" rx="2" ry="2" />
<text text-anchor="" x="285.76" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('dst_release (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>dst_release (1 samples, 0.10%)</title><rect x="1035.8" y="257" width="1.2" height="15.0" fill="rgb(205,57,57)" rx="2" ry="2" />
<text text-anchor="" x="1038.83" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/PooledUnsafeDirectByteBuf:.setBytes (42 samples, 4.22%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/PooledUnsafeDirectByteBuf:.setBytes (42 samples, 4.22%)</title><rect x="78.8" y="945" width="49.8" height="15.0" fill="rgb(92,238,92)" rx="2" ry="2" />
<text text-anchor="" x="81.78" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/n..</text>
</g>
<g class="func_g" onmouseover="s('sock_put (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_put (1 samples, 0.10%)</title><rect x="1010.9" y="305" width="1.2" height="15.0" fill="rgb(229,93,93)" rx="2" ry="2" />
<text text-anchor="" x="1013.92" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (23 samples, 2.31%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (23 samples, 2.31%)</title><rect x="584.0" y="769" width="27.3" height="15.0" fill="rgb(80,228,80)" rx="2" ry="2" />
<text text-anchor="" x="586.99" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('OopMapSet::all_do (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>OopMapSet::all_do (1 samples, 0.10%)</title><rect x="52.7" y="1089" width="1.2" height="15.0" fill="rgb(194,194,57)" rx="2" ry="2" />
<text text-anchor="" x="55.69" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__dev_queue_xmit (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>__dev_queue_xmit (10 samples, 1.01%)</title><rect x="980.1" y="481" width="11.8" height="15.0" fill="rgb(225,87,87)" rx="2" ry="2" />
<text text-anchor="" x="983.09" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_schedule_loss_probe (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_schedule_loss_probe (3 samples, 0.30%)</title><rect x="965.9" y="577" width="3.5" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="968.86" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/channels/spi/AbstractInterruptibleChannel:.end (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/channels/spi/AbstractInterruptibleChannel:.end (1 samples, 0.10%)</title><rect x="914.9" y="753" width="1.2" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="917.86" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.equals (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.equals (1 samples, 0.10%)</title><rect x="830.7" y="865" width="1.1" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="833.66" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/WrapFactory:.wrap (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/WrapFactory:.wrap (5 samples, 0.50%)</title><rect x="633.8" y="769" width="5.9" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="636.80" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (4 samples, 0.40%)</title><rect x="1182.9" y="913" width="4.7" height="15.0" fill="rgb(229,93,93)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/PooledByteBufAllocator:.newDirectBuffer (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/PooledByteBufAllocator:.newDirectBuffer (2 samples, 0.20%)</title><rect x="76.4" y="945" width="2.4" height="15.0" fill="rgb(63,211,63)" rx="2" ry="2" />
<text text-anchor="" x="79.41" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('eth_type_trans (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>eth_type_trans (1 samples, 0.10%)</title><rect x="984.8" y="433" width="1.2" height="15.0" fill="rgb(219,77,77)" rx="2" ry="2" />
<text text-anchor="" x="987.83" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (2 samples, 0.20%)</title><rect x="848.5" y="881" width="2.3" height="15.0" fill="rgb(73,221,73)" rx="2" ry="2" />
<text text-anchor="" x="851.45" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.writeBytes (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.writeBytes (4 samples, 0.40%)</title><rect x="286.3" y="641" width="4.8" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="289.32" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (6 samples, 0.60%)</title><rect x="522.3" y="737" width="7.1" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="525.32" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (45 samples, 4.52%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (45 samples, 4.52%)</title><rect x="720.4" y="753" width="53.3" height="15.0" fill="rgb(95,241,95)" rx="2" ry="2" />
<text text-anchor="" x="723.37" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/..</text>
</g>
<g class="func_g" onmouseover="s('internal_add_timer (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>internal_add_timer (1 samples, 0.10%)</title><rect x="1034.6" y="193" width="1.2" height="15.0" fill="rgb(222,83,83)" rx="2" ry="2" />
<text text-anchor="" x="1037.64" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Java_sun_nio_ch_FileDispatcherImpl_write0 (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Java_sun_nio_ch_FileDispatcherImpl_write0 (1 samples, 0.10%)</title><rect x="918.4" y="737" width="1.2" height="15.0" fill="rgb(244,114,114)" rx="2" ry="2" />
<text text-anchor="" x="921.42" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaObject:.get (1 samples, 0.10%)</title><rect x="658.7" y="769" width="1.2" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="661.70" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('start_thread (985 samples, 98.99%)')" onmouseout="c()" onclick="zoom(this)">
<title>start_thread (985 samples, 98.99%)</title><rect x="21.9" y="1185" width="1168.1" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1195.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >start_thread</text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/HashMap:.getNode (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/HashMap:.getNode (2 samples, 0.20%)</title><rect x="829.5" y="881" width="2.3" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="832.48" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_rcv_space_adjust (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_rcv_space_adjust (2 samples, 0.20%)</title><rect x="104.9" y="753" width="2.3" height="15.0" fill="rgb(216,74,74)" rx="2" ry="2" />
<text text-anchor="" x="107.87" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (3 samples, 0.30%)</title><rect x="353.9" y="705" width="3.6" height="15.0" fill="rgb(105,250,105)" rx="2" ry="2" />
<text text-anchor="" x="356.92" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('system_call_after_swapgs (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>system_call_after_swapgs (6 samples, 0.60%)</title><rect x="14.7" y="1137" width="7.2" height="15.0" fill="rgb(208,61,61)" rx="2" ry="2" />
<text text-anchor="" x="17.74" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__getnstimeofday (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__getnstimeofday (1 samples, 0.10%)</title><rect x="1061.9" y="193" width="1.2" height="15.0" fill="rgb(223,84,84)" rx="2" ry="2" />
<text text-anchor="" x="1064.92" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.set (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.set (3 samples, 0.30%)</title><rect x="248.4" y="753" width="3.5" height="15.0" fill="rgb(80,227,80)" rx="2" ry="2" />
<text text-anchor="" x="251.37" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/channels/spi/AbstractInterruptibleChannel:.begin (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/channels/spi/AbstractInterruptibleChannel:.begin (1 samples, 0.10%)</title><rect x="84.7" y="913" width="1.2" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="87.71" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('raw_local_deliver (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>raw_local_deliver (1 samples, 0.10%)</title><rect x="1009.7" y="305" width="1.2" height="15.0" fill="rgb(205,58,58)" rx="2" ry="2" />
<text text-anchor="" x="1012.74" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_recvmsg (13 samples, 1.31%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_recvmsg (13 samples, 1.31%)</title><rect x="107.2" y="753" width="15.5" height="15.0" fill="rgb(210,65,65)" rx="2" ry="2" />
<text text-anchor="" x="110.25" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelDuplexHandler:.read (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelDuplexHandler:.read (3 samples, 0.30%)</title><rect x="1173.4" y="929" width="3.6" height="15.0" fill="rgb(94,240,94)" rx="2" ry="2" />
<text text-anchor="" x="1176.40" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock_bh (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.10%)</title><rect x="1127.1" y="593" width="1.2" height="15.0" fill="rgb(252,125,125)" rx="2" ry="2" />
<text text-anchor="" x="1130.15" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_def_readable (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_def_readable (2 samples, 0.20%)</title><rect x="1037.0" y="257" width="2.4" height="15.0" fill="rgb(252,126,126)" rx="2" ry="2" />
<text text-anchor="" x="1040.02" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_is_cwnd_limited (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_is_cwnd_limited (2 samples, 0.20%)</title><rect x="1044.1" y="225" width="2.4" height="15.0" fill="rgb(212,67,67)" rx="2" ry="2" />
<text text-anchor="" x="1047.13" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)</title><rect x="592.3" y="721" width="1.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="595.29" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Monitor::IWait (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Monitor::IWait (1 samples, 0.10%)</title><rect x="21.9" y="1105" width="1.1" height="15.0" fill="rgb(176,176,50)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('detach_if_pending (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>detach_if_pending (1 samples, 0.10%)</title><rect x="1033.5" y="193" width="1.1" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="1036.46" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getBase (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getBase (2 samples, 0.20%)</title><rect x="670.6" y="769" width="2.3" height="15.0" fill="rgb(94,241,94)" rx="2" ry="2" />
<text text-anchor="" x="673.56" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('local_bh_enable (104 samples, 10.45%)')" onmouseout="c()" onclick="zoom(this)">
<title>local_bh_enable (104 samples, 10.45%)</title><rect x="993.1" y="497" width="123.4" height="15.0" fill="rgb(200,50,50)" rx="2" ry="2" />
<text text-anchor="" x="996.14" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >local_bh_enable</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelDuplexHandler:.flush (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelDuplexHandler:.flush (1 samples, 0.10%)</title><rect x="1168.7" y="881" width="1.1" height="15.0" fill="rgb(89,236,89)" rx="2" ry="2" />
<text text-anchor="" x="1171.65" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (12 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.setAttributes (12 samples, 1.21%)</title><rect x="611.3" y="769" width="14.2" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="614.27" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getTopScopeValue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getTopScopeValue (1 samples, 0.10%)</title><rect x="193.8" y="817" width="1.2" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="196.82" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('inet_ehashfn (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>inet_ehashfn (1 samples, 0.10%)</title><rect x="1020.4" y="289" width="1.2" height="15.0" fill="rgb(203,54,54)" rx="2" ry="2" />
<text text-anchor="" x="1023.41" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (2 samples, 0.20%)</title><rect x="670.6" y="753" width="2.3" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="673.56" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__slab_alloc (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__slab_alloc (1 samples, 0.10%)</title><rect x="1136.6" y="577" width="1.2" height="15.0" fill="rgb(254,129,129)" rx="2" ry="2" />
<text text-anchor="" x="1139.63" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__tcp_v4_send_check (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_v4_send_check (1 samples, 0.10%)</title><rect x="1124.8" y="545" width="1.2" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="1127.77" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder$LineParser:.parse (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder$LineParser:.parse (6 samples, 0.60%)</title><rect x="831.8" y="897" width="7.2" height="15.0" fill="rgb(80,228,80)" rx="2" ry="2" />
<text text-anchor="" x="834.85" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__pthread_disable_asynccancel (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__pthread_disable_asynccancel (1 samples, 0.10%)</title><rect x="1154.4" y="737" width="1.2" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="1157.42" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_aio_write (185 samples, 18.59%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_write (185 samples, 18.59%)</title><rect x="932.7" y="657" width="219.4" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="935.65" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sock_aio_write</text>
</g>
<g class="func_g" onmouseover="s('ip_finish_output (119 samples, 11.96%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_finish_output (119 samples, 11.96%)</title><rect x="975.3" y="513" width="141.2" height="15.0" fill="rgb(216,74,74)" rx="2" ry="2" />
<text text-anchor="" x="978.35" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_finish_output</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)</title><rect x="259.0" y="737" width="1.2" height="15.0" fill="rgb(103,248,103)" rx="2" ry="2" />
<text text-anchor="" x="262.05" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (1 samples, 0.10%)</title><rect x="536.6" y="769" width="1.1" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="539.55" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_j (1 samples, 0.10%)</title><rect x="801.0" y="785" width="1.2" height="15.0" fill="rgb(89,235,89)" rx="2" ry="2" />
<text text-anchor="" x="804.02" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/DefaultChannelPipeline$HeadContext:.read (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/DefaultChannelPipeline$HeadContext:.read (1 samples, 0.10%)</title><rect x="1174.6" y="865" width="1.2" height="15.0" fill="rgb(100,246,100)" rx="2" ry="2" />
<text text-anchor="" x="1177.58" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (21 samples, 2.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (21 samples, 2.11%)</title><rect x="460.7" y="689" width="24.9" height="15.0" fill="rgb(66,214,66)" rx="2" ry="2" />
<text text-anchor="" x="463.65" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/http/impl/ServerConnection:.handleRequest (526 samples, 52.86%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/http/impl/ServerConnection:.handleRequest (526 samples, 52.86%)</title><rect x="183.1" y="865" width="623.8" height="15.0" fill="rgb(82,229,82)" rx="2" ry="2" />
<text text-anchor="" x="186.15" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/vertx/java/core/http/impl/ServerConnection:.handleRequest</text>
</g>
<g class="func_g" onmouseover="s('ktime_get_real (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get_real (1 samples, 0.10%)</title><rect x="1061.9" y="225" width="1.2" height="15.0" fill="rgb(209,63,63)" rx="2" ry="2" />
<text text-anchor="" x="1064.92" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('nmethod::fix_oop_relocations (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>nmethod::fix_oop_relocations (1 samples, 0.10%)</title><rect x="53.9" y="1105" width="1.2" height="15.0" fill="rgb(213,213,63)" rx="2" ry="2" />
<text text-anchor="" x="56.88" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('HandleArea::oops_do (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>HandleArea::oops_do (1 samples, 0.10%)</title><rect x="51.5" y="1105" width="1.2" height="15.0" fill="rgb(187,187,54)" rx="2" ry="2" />
<text text-anchor="" x="54.51" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('OldToYoungRootsTask::do_it (20 samples, 2.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>OldToYoungRootsTask::do_it (20 samples, 2.01%)</title><rect x="23.0" y="1137" width="23.8" height="15.0" fill="rgb(177,177,50)" rx="2" ry="2" />
<text text-anchor="" x="26.05" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >O..</text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/HashMap:.getNode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/HashMap:.getNode (1 samples, 0.10%)</title><rect x="191.4" y="817" width="1.2" height="15.0" fill="rgb(69,218,69)" rx="2" ry="2" />
<text text-anchor="" x="194.45" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.getBytes (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.getBytes (3 samples, 0.30%)</title><rect x="253.1" y="753" width="3.6" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="256.12" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('java_start (985 samples, 98.99%)')" onmouseout="c()" onclick="zoom(this)">
<title>java_start (985 samples, 98.99%)</title><rect x="21.9" y="1169" width="1168.1" height="15.0" fill="rgb(253,128,128)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1179.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >java_start</text>
</g>
<g class="func_g" onmouseover="s('__fsnotify_parent (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__fsnotify_parent (1 samples, 0.10%)</title><rect x="927.9" y="673" width="1.2" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="930.91" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_push (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_push (1 samples, 0.10%)</title><rect x="956.4" y="577" width="1.2" height="15.0" fill="rgb(223,84,84)" rx="2" ry="2" />
<text text-anchor="" x="959.37" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ttwu_do_wakeup (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_wakeup (1 samples, 0.10%)</title><rect x="1103.4" y="113" width="1.2" height="15.0" fill="rgb(222,83,83)" rx="2" ry="2" />
<text text-anchor="" x="1106.43" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ip_rcv (91 samples, 9.15%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv (91 samples, 9.15%)</title><rect x="1007.4" y="369" width="107.9" height="15.0" fill="rgb(214,71,71)" rx="2" ry="2" />
<text text-anchor="" x="1010.37" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_rcv</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 (3 samples, 0.30%)</title><rect x="817.6" y="897" width="3.6" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="820.62" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.construct (156 samples, 15.68%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.construct (156 samples, 15.68%)</title><rect x="324.3" y="785" width="185.0" height="15.0" fill="rgb(55,205,55)" rx="2" ry="2" />
<text text-anchor="" x="327.27" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript/..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush (2 samples, 0.20%)</title><rect x="1163.9" y="817" width="2.4" height="15.0" fill="rgb(107,253,107)" rx="2" ry="2" />
<text text-anchor="" x="1166.91" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (28 samples, 2.81%)')" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (28 samples, 2.81%)</title><rect x="93.0" y="865" width="33.2" height="15.0" fill="rgb(246,116,116)" rx="2" ry="2" />
<text text-anchor="" x="96.02" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sy..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (4 samples, 0.40%)</title><rect x="340.9" y="657" width="4.7" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="343.87" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/WrapFactory:.wrapAsJavaObject (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/WrapFactory:.wrapAsJavaObject (1 samples, 0.10%)</title><rect x="196.2" y="849" width="1.2" height="15.0" fill="rgb(104,250,104)" rx="2" ry="2" />
<text text-anchor="" x="199.19" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.skipControlCharacters (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.skipControlCharacters (1 samples, 0.10%)</title><rect x="881.7" y="913" width="1.1" height="15.0" fill="rgb(104,249,104)" rx="2" ry="2" />
<text text-anchor="" x="884.66" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_md5_do_lookup (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_md5_do_lookup (1 samples, 0.10%)</title><rect x="1022.8" y="289" width="1.2" height="15.0" fill="rgb(254,128,128)" rx="2" ry="2" />
<text text-anchor="" x="1025.78" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('fsnotify (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>fsnotify (1 samples, 0.10%)</title><rect x="123.8" y="785" width="1.2" height="15.0" fill="rgb(238,106,106)" rx="2" ry="2" />
<text text-anchor="" x="126.85" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ttwu_stat (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_stat (1 samples, 0.10%)</title><rect x="1104.6" y="113" width="1.2" height="15.0" fill="rgb(243,113,113)" rx="2" ry="2" />
<text text-anchor="" x="1107.61" y="123.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__srcu_read_lock (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>__srcu_read_lock (2 samples, 0.20%)</title><rect x="929.1" y="673" width="2.4" height="15.0" fill="rgb(202,53,53)" rx="2" ry="2" />
<text text-anchor="" x="932.10" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__skb_clone (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>__skb_clone (4 samples, 0.40%)</title><rect x="1120.0" y="545" width="4.8" height="15.0" fill="rgb(230,94,94)" rx="2" ry="2" />
<text text-anchor="" x="1123.03" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (8 samples, 0.80%)</title><rect x="553.2" y="721" width="9.4" height="15.0" fill="rgb(55,205,55)" rx="2" ry="2" />
<text text-anchor="" x="556.16" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_current_mss (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_current_mss (5 samples, 0.50%)</title><rect x="1143.7" y="593" width="6.0" height="15.0" fill="rgb(228,91,91)" rx="2" ry="2" />
<text text-anchor="" x="1146.75" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/MessageToMessageEncoder:.write (31 samples, 3.12%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/MessageToMessageEncoder:.write (31 samples, 3.12%)</title><rect x="265.0" y="689" width="36.7" height="15.0" fill="rgb(83,230,83)" rx="2" ry="2" />
<text text-anchor="" x="267.97" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio..</text>
</g>
<g class="func_g" onmouseover="s('ksize (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ksize (1 samples, 0.10%)</title><rect x="1140.2" y="577" width="1.2" height="15.0" fill="rgb(252,125,125)" rx="2" ry="2" />
<text text-anchor="" x="1143.19" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaMethod:.findCachedFunction (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaMethod:.findCachedFunction (2 samples, 0.20%)</title><rect x="222.3" y="769" width="2.4" height="15.0" fill="rgb(93,239,93)" rx="2" ry="2" />
<text text-anchor="" x="225.28" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('[unknown] (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (4 samples, 0.40%)</title><rect x="1182.9" y="929" width="4.7" height="15.0" fill="rgb(236,102,102)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sk_reset_timer (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>sk_reset_timer (5 samples, 0.50%)</title><rect x="958.7" y="545" width="6.0" height="15.0" fill="rgb(200,50,50)" rx="2" ry="2" />
<text text-anchor="" x="961.74" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('system_call (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>system_call (1 samples, 0.10%)</title><rect x="13.6" y="1137" width="1.1" height="15.0" fill="rgb(237,105,105)" rx="2" ry="2" />
<text text-anchor="" x="16.56" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders (2 samples, 0.20%)</title><rect x="879.3" y="913" width="2.4" height="15.0" fill="rgb(109,254,109)" rx="2" ry="2" />
<text text-anchor="" x="882.29" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/ArrayList:.ensureCapacityInternal (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/ArrayList:.ensureCapacityInternal (1 samples, 0.10%)</title><rect x="878.1" y="897" width="1.2" height="15.0" fill="rgb(103,249,103)" rx="2" ry="2" />
<text text-anchor="" x="881.10" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Interpreter (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>Interpreter (956 samples, 96.08%)</title><rect x="55.1" y="1041" width="1133.7" height="15.0" fill="rgb(204,57,57)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1051.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Interpreter</text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.10%)</title><rect x="1032.3" y="193" width="1.2" height="15.0" fill="rgb(224,85,85)" rx="2" ry="2" />
<text text-anchor="" x="1035.27" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush (235 samples, 23.62%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush (235 samples, 23.62%)</title><rect x="887.6" y="833" width="278.7" height="15.0" fill="rgb(52,202,52)" rx="2" ry="2" />
<text text-anchor="" x="890.59" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/ChannelOutboundHand..</text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/FileDispatcherImpl:.write0 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/FileDispatcherImpl:.write0 (2 samples, 0.20%)</title><rect x="908.9" y="769" width="2.4" height="15.0" fill="rgb(83,230,83)" rx="2" ry="2" />
<text text-anchor="" x="911.93" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="403.7" y="721" width="1.2" height="15.0" fill="rgb(59,208,59)" rx="2" ry="2" />
<text text-anchor="" x="406.73" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="338.5" y="689" width="1.2" height="15.0" fill="rgb(70,219,70)" rx="2" ry="2" />
<text text-anchor="" x="341.50" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="548.4" y="721" width="1.2" height="15.0" fill="rgb(87,234,87)" rx="2" ry="2" />
<text text-anchor="" x="551.41" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('unsafe_arraycopy (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>unsafe_arraycopy (1 samples, 0.10%)</title><rect x="295.8" y="657" width="1.2" height="15.0" fill="rgb(210,65,65)" rx="2" ry="2" />
<text text-anchor="" x="298.81" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)</title><rect x="805.8" y="817" width="1.1" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="808.76" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/util/concurrent/ConcurrentHashMap:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/util/concurrent/ConcurrentHashMap:.get (1 samples, 0.10%)</title><rect x="1169.8" y="881" width="1.2" height="15.0" fill="rgb(109,254,109)" rx="2" ry="2" />
<text text-anchor="" x="1172.84" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ObjArrayKlass::oop_push_contents (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>ObjArrayKlass::oop_push_contents (2 samples, 0.20%)</title><rect x="42.0" y="1105" width="2.4" height="15.0" fill="rgb(212,212,63)" rx="2" ry="2" />
<text text-anchor="" x="45.02" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (3 samples, 0.30%)</title><rect x="566.2" y="785" width="3.6" height="15.0" fill="rgb(52,202,52)" rx="2" ry="2" />
<text text-anchor="" x="569.20" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.hash (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.hash (2 samples, 0.20%)</title><rect x="852.0" y="881" width="2.4" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="855.01" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sys_epoll_wait (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_wait (4 samples, 0.40%)</title><rect x="1182.9" y="897" width="4.7" height="15.0" fill="rgb(253,127,127)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (196 samples, 19.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>system_call_fastpath (196 samples, 19.70%)</title><rect x="920.8" y="721" width="232.4" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="923.79" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >system_call_fastpath</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (4 samples, 0.40%)</title><rect x="524.7" y="705" width="4.7" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="527.69" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (2 samples, 0.20%)</title><rect x="723.9" y="737" width="2.4" height="15.0" fill="rgb(67,216,67)" rx="2" ry="2" />
<text text-anchor="" x="726.93" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vfs_write (192 samples, 19.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>vfs_write (192 samples, 19.30%)</title><rect x="925.5" y="689" width="227.7" height="15.0" fill="rgb(253,127,127)" rx="2" ry="2" />
<text text-anchor="" x="928.54" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_write</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (12 samples, 1.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (12 samples, 1.21%)</title><rect x="611.3" y="753" width="14.2" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="614.27" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('oopDesc* PSPromotionManager::copy_to_survivor_spacefalse (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>oopDesc* PSPromotionManager::copy_to_survivor_spacefalse (1 samples, 0.10%)</title><rect x="52.7" y="1057" width="1.2" height="15.0" fill="rgb(192,192,56)" rx="2" ry="2" />
<text text-anchor="" x="55.69" y="1067.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (5 samples, 0.50%)</title><rect x="339.7" y="689" width="5.9" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="342.69" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/EPollArrayWrapper:.poll (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/EPollArrayWrapper:.poll (5 samples, 0.50%)</title><rect x="1181.7" y="961" width="5.9" height="15.0" fill="rgb(93,239,93)" rx="2" ry="2" />
<text text-anchor="" x="1184.70" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead (562 samples, 56.48%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead (562 samples, 56.48%)</title><rect x="142.8" y="913" width="666.5" height="15.0" fill="rgb(106,251,106)" rx="2" ry="2" />
<text text-anchor="" x="145.82" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead</text>
</g>
<g class="func_g" onmouseover="s('__dev_queue_xmit (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__dev_queue_xmit (1 samples, 0.10%)</title><rect x="978.9" y="497" width="1.2" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="981.90" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__kmalloc_reserve.isra.26 (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_reserve.isra.26 (3 samples, 0.30%)</title><rect x="1133.1" y="577" width="3.5" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="1136.08" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('GCTaskManager::get_task (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>GCTaskManager::get_task (1 samples, 0.10%)</title><rect x="21.9" y="1137" width="1.1" height="15.0" fill="rgb(218,218,65)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ep_scan_ready_list.isra.9 (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>ep_scan_ready_list.isra.9 (4 samples, 0.40%)</title><rect x="1182.9" y="865" width="4.7" height="15.0" fill="rgb(242,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('security_file_permission (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (1 samples, 0.10%)</title><rect x="125.0" y="817" width="1.2" height="15.0" fill="rgb(226,89,89)" rx="2" ry="2" />
<text text-anchor="" x="128.04" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)</title><rect x="466.6" y="673" width="1.2" height="15.0" fill="rgb(54,204,54)" rx="2" ry="2" />
<text text-anchor="" x="469.58" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue (2 samples, 0.20%)</title><rect x="652.8" y="769" width="2.3" height="15.0" fill="rgb(108,253,108)" rx="2" ry="2" />
<text text-anchor="" x="655.77" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="625.5" y="769" width="1.2" height="15.0" fill="rgb(109,254,109)" rx="2" ry="2" />
<text text-anchor="" x="628.50" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (3 samples, 0.30%)</title><rect x="644.5" y="753" width="3.5" height="15.0" fill="rgb(76,224,76)" rx="2" ry="2" />
<text text-anchor="" x="647.47" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('do_softirq_own_stack (103 samples, 10.35%)')" onmouseout="c()" onclick="zoom(this)">
<title>do_softirq_own_stack (103 samples, 10.35%)</title><rect x="994.3" y="465" width="122.2" height="15.0" fill="rgb(222,82,82)" rx="2" ry="2" />
<text text-anchor="" x="997.32" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_softirq_own_..</text>
</g>
<g class="func_g" onmouseover="s('call_function_single_interrupt (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>call_function_single_interrupt (4 samples, 0.40%)</title><rect x="935.0" y="641" width="4.8" height="15.0" fill="rgb(254,129,129)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="336.1" y="705" width="1.2" height="15.0" fill="rgb(105,251,105)" rx="2" ry="2" />
<text text-anchor="" x="339.13" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="307.7" y="769" width="1.2" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="310.67" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.findPrototypeId (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.findPrototypeId (1 samples, 0.10%)</title><rect x="657.5" y="753" width="1.2" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="660.52" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vtable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.10%)</title><rect x="389.5" y="705" width="1.2" height="15.0" fill="rgb(220,79,79)" rx="2" ry="2" />
<text text-anchor="" x="392.50" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('common_file_perm (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>common_file_perm (1 samples, 0.10%)</title><rect x="122.7" y="769" width="1.1" height="15.0" fill="rgb(254,129,129)" rx="2" ry="2" />
<text text-anchor="" x="125.66" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (3 samples, 0.30%)</title><rect x="667.0" y="769" width="3.6" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="670.01" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder$HeaderParser:.process (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder$HeaderParser:.process (1 samples, 0.10%)</title><rect x="854.4" y="881" width="1.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="857.38" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('intel_pmu_enable_all (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>intel_pmu_enable_all (4 samples, 0.40%)</title><rect x="935.0" y="497" width="4.8" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('idle_cpu (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>idle_cpu (2 samples, 0.20%)</title><rect x="1086.8" y="97" width="2.4" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="1089.82" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (1 samples, 0.10%)</title><rect x="204.5" y="817" width="1.2" height="15.0" fill="rgb(89,236,89)" rx="2" ry="2" />
<text text-anchor="" x="207.49" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="336.1" y="689" width="1.2" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="339.13" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('x86_pmu_enable (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>x86_pmu_enable (4 samples, 0.40%)</title><rect x="935.0" y="513" width="4.8" height="15.0" fill="rgb(235,102,102)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (14 samples, 1.41%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (14 samples, 1.41%)</title><rect x="784.4" y="769" width="16.6" height="15.0" fill="rgb(72,221,72)" rx="2" ry="2" />
<text text-anchor="" x="787.41" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('inet_sendmsg (177 samples, 17.79%)')" onmouseout="c()" onclick="zoom(this)">
<title>inet_sendmsg (177 samples, 17.79%)</title><rect x="939.8" y="641" width="209.9" height="15.0" fill="rgb(220,79,79)" rx="2" ry="2" />
<text text-anchor="" x="942.77" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >inet_sendmsg</text>
</g>
<g class="func_g" onmouseover="s('kfree_skbmem (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>kfree_skbmem (1 samples, 0.10%)</title><rect x="1060.7" y="225" width="1.2" height="15.0" fill="rgb(208,62,62)" rx="2" ry="2" />
<text text-anchor="" x="1063.73" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_Server2_js_1:.call (79 samples, 7.94%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_Server2_js_1:.call (79 samples, 7.94%)</title><rect x="216.4" y="817" width="93.6" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="219.35" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozill..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.newPromise (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.newPromise (1 samples, 0.10%)</title><rect x="260.2" y="737" width="1.2" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="263.23" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (4 samples, 0.40%)</title><rect x="579.2" y="769" width="4.8" height="15.0" fill="rgb(56,205,56)" rx="2" ry="2" />
<text text-anchor="" x="582.25" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/internal/AppendableCharSequence:.substring (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/internal/AppendableCharSequence:.substring (2 samples, 0.20%)</title><rect x="869.8" y="881" width="2.4" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="872.80" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ip_local_deliver (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver (1 samples, 0.10%)</title><rect x="1008.6" y="353" width="1.1" height="15.0" fill="rgb(250,122,122)" rx="2" ry="2" />
<text text-anchor="" x="1011.55" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush (1 samples, 0.10%)</title><rect x="1166.3" y="849" width="1.2" height="15.0" fill="rgb(100,246,100)" rx="2" ry="2" />
<text text-anchor="" x="1169.28" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_aio_read.part.8 (22 samples, 2.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_aio_read.part.8 (22 samples, 2.21%)</title><rect x="96.6" y="785" width="26.1" height="15.0" fill="rgb(224,84,84)" rx="2" ry="2" />
<text text-anchor="" x="99.57" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.encode (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.encode (7 samples, 0.70%)</title><rect x="285.1" y="657" width="8.3" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="288.14" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getParentScope (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getParentScope (1 samples, 0.10%)</title><rect x="399.0" y="705" width="1.2" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="401.98" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/reflect/DelegatingMethodAccessorImpl:.invoke (66 samples, 6.63%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/reflect/DelegatingMethodAccessorImpl:.invoke (66 samples, 6.63%)</title><rect x="227.0" y="769" width="78.3" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="230.03" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lsun/refl..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (40 samples, 4.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (40 samples, 4.02%)</title><rect x="726.3" y="737" width="47.4" height="15.0" fill="rgb(108,253,108)" rx="2" ry="2" />
<text text-anchor="" x="729.30" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg..</text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/channels/spi/AbstractInterruptibleChannel:.begin (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/channels/spi/AbstractInterruptibleChannel:.begin (1 samples, 0.10%)</title><rect x="913.7" y="753" width="1.2" height="15.0" fill="rgb(58,207,58)" rx="2" ry="2" />
<text text-anchor="" x="916.68" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.write (35 samples, 3.52%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.write (35 samples, 3.52%)</title><rect x="263.8" y="737" width="41.5" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="266.79" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)</title><rect x="371.7" y="689" width="2.4" height="15.0" fill="rgb(101,246,101)" rx="2" ry="2" />
<text text-anchor="" x="374.71" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeCall:.init (48 samples, 4.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeCall:.init (48 samples, 4.82%)</title><rect x="570.9" y="785" width="57.0" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="573.94" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/m..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (1 samples, 0.10%)</title><rect x="535.4" y="769" width="1.2" height="15.0" fill="rgb(104,250,104)" rx="2" ry="2" />
<text text-anchor="" x="538.37" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)</title><rect x="823.5" y="881" width="1.2" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="826.55" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('JavaCalls::call_virtual (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>JavaCalls::call_virtual (956 samples, 96.08%)</title><rect x="55.1" y="1089" width="1133.7" height="15.0" fill="rgb(215,215,64)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaCalls::call_virtual</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.execIdCall (60 samples, 6.03%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.execIdCall (60 samples, 6.03%)</title><rect x="329.0" y="737" width="71.2" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="332.02" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/moz..</text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="308.9" y="801" width="1.1" height="15.0" fill="rgb(206,58,58)" rx="2" ry="2" />
<text text-anchor="" x="311.85" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__alloc_skb (9 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>__alloc_skb (9 samples, 0.90%)</title><rect x="1130.7" y="593" width="10.7" height="15.0" fill="rgb(202,54,54)" rx="2" ry="2" />
<text text-anchor="" x="1133.70" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('SpinPause (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>SpinPause (1 samples, 0.10%)</title><rect x="50.3" y="1121" width="1.2" height="15.0" fill="rgb(204,57,57)" rx="2" ry="2" />
<text text-anchor="" x="53.32" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_release_data (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_release_data (3 samples, 0.30%)</title><rect x="1056.0" y="193" width="3.5" height="15.0" fill="rgb(214,70,70)" rx="2" ry="2" />
<text text-anchor="" x="1058.99" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="671.7" y="737" width="1.2" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="674.75" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/charset/CharsetEncoder:.replaceWith (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/charset/CharsetEncoder:.replaceWith (2 samples, 0.20%)</title><rect x="253.1" y="721" width="2.4" height="15.0" fill="rgb(57,207,57)" rx="2" ry="2" />
<text text-anchor="" x="256.12" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('jni_fast_GetIntField (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>jni_fast_GetIntField (1 samples, 0.10%)</title><rect x="1155.6" y="737" width="1.2" height="15.0" fill="rgb(247,118,118)" rx="2" ry="2" />
<text text-anchor="" x="1158.61" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_transmit_skb (132 samples, 13.27%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_transmit_skb (132 samples, 13.27%)</title><rect x="969.4" y="577" width="156.6" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="972.42" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_transmit_skb</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.10%)</title><rect x="486.7" y="737" width="1.2" height="15.0" fill="rgb(87,234,87)" rx="2" ry="2" />
<text text-anchor="" x="489.74" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_unlock_irqrestore (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_unlock_irqrestore (1 samples, 0.10%)</title><rect x="963.5" y="513" width="1.2" height="15.0" fill="rgb(250,123,123)" rx="2" ry="2" />
<text text-anchor="" x="966.49" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('StealTask::do_it (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>StealTask::do_it (3 samples, 0.30%)</title><rect x="47.9" y="1137" width="3.6" height="15.0" fill="rgb(175,175,50)" rx="2" ry="2" />
<text text-anchor="" x="50.95" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_sendmsg (176 samples, 17.69%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_sendmsg (176 samples, 17.69%)</title><rect x="941.0" y="625" width="208.7" height="15.0" fill="rgb(215,73,73)" rx="2" ry="2" />
<text text-anchor="" x="943.95" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_sendmsg</text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)</title><rect x="300.6" y="673" width="1.1" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="303.55" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)</title><rect x="581.6" y="753" width="2.4" height="15.0" fill="rgb(88,235,88)" rx="2" ry="2" />
<text text-anchor="" x="584.62" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace (1 samples, 0.10%)</title><rect x="868.6" y="881" width="1.2" height="15.0" fill="rgb(67,216,67)" rx="2" ry="2" />
<text text-anchor="" x="871.61" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('java-339 (995 samples, 100.00%)')" onmouseout="c()" onclick="zoom(this)">
<title>java-339 (995 samples, 100.00%)</title><rect x="10.0" y="1201" width="1180.0" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1211.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >java-339</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (17 samples, 1.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (17 samples, 1.71%)</title><rect x="487.9" y="737" width="20.2" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="490.93" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('oopDesc* PSPromotionManager::copy_to_survivor_spacefalse (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>oopDesc* PSPromotionManager::copy_to_survivor_spacefalse (2 samples, 0.20%)</title><rect x="44.4" y="1089" width="2.4" height="15.0" fill="rgb(216,216,64)" rx="2" ry="2" />
<text text-anchor="" x="47.39" y="1099.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.findInstanceIdInfo (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.findInstanceIdInfo (4 samples, 0.40%)</title><rect x="429.8" y="705" width="4.8" height="15.0" fill="rgb(108,253,108)" rx="2" ry="2" />
<text text-anchor="" x="432.82" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)</title><rect x="355.1" y="689" width="2.4" height="15.0" fill="rgb(58,207,58)" rx="2" ry="2" />
<text text-anchor="" x="358.11" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.flush (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.flush (1 samples, 0.10%)</title><rect x="885.2" y="897" width="1.2" height="15.0" fill="rgb(54,204,54)" rx="2" ry="2" />
<text text-anchor="" x="888.22" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sk_stream_alloc_skb (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>sk_stream_alloc_skb (10 samples, 1.01%)</title><rect x="1130.7" y="609" width="11.9" height="15.0" fill="rgb(225,86,86)" rx="2" ry="2" />
<text text-anchor="" x="1133.70" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="204.5" y="801" width="1.2" height="15.0" fill="rgb(99,245,99)" rx="2" ry="2" />
<text text-anchor="" x="207.49" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpHeaders:.encodeAscii0 (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpHeaders:.encodeAscii0 (2 samples, 0.20%)</title><rect x="293.4" y="657" width="2.4" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="296.44" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (10 samples, 1.01%)</title><rect x="550.8" y="737" width="11.8" height="15.0" fill="rgb(80,227,80)" rx="2" ry="2" />
<text text-anchor="" x="553.78" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_sendmsg (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_sendmsg (1 samples, 0.10%)</title><rect x="1150.9" y="641" width="1.2" height="15.0" fill="rgb(253,128,128)" rx="2" ry="2" />
<text text-anchor="" x="1153.86" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead (635 samples, 63.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead (635 samples, 63.82%)</title><rect x="132.2" y="929" width="753.0" height="15.0" fill="rgb(81,228,81)" rx="2" ry="2" />
<text text-anchor="" x="135.15" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractReferenceCountedByteBuf:.release (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractReferenceCountedByteBuf:.release (5 samples, 0.50%)</title><rect x="897.1" y="769" width="5.9" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="900.08" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.skipControlCharacters (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.skipControlCharacters (2 samples, 0.20%)</title><rect x="865.1" y="897" width="2.3" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="868.06" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('flush_tlb_mm_range (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>flush_tlb_mm_range (1 samples, 0.10%)</title><rect x="1188.8" y="1009" width="1.2" height="15.0" fill="rgb(209,63,63)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1019.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_poll (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_poll (2 samples, 0.20%)</title><rect x="1185.3" y="833" width="2.3" height="15.0" fill="rgb(204,56,56)" rx="2" ry="2" />
<text text-anchor="" x="1188.26" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (9 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThisHelper (9 samples, 0.90%)</title><rect x="426.3" y="737" width="10.6" height="15.0" fill="rgb(52,202,52)" rx="2" ry="2" />
<text text-anchor="" x="429.26" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (2 samples, 0.20%)</title><rect x="530.6" y="737" width="2.4" height="15.0" fill="rgb(105,251,105)" rx="2" ry="2" />
<text text-anchor="" x="533.62" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ip_local_deliver (89 samples, 8.94%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_deliver (89 samples, 8.94%)</title><rect x="1009.7" y="337" width="105.6" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="1012.74" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_local_del..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="370.5" y="689" width="1.2" height="15.0" fill="rgb(108,253,108)" rx="2" ry="2" />
<text text-anchor="" x="373.52" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('read_tsc (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>read_tsc (2 samples, 0.20%)</title><rect x="1117.7" y="529" width="2.3" height="15.0" fill="rgb(214,71,71)" rx="2" ry="2" />
<text text-anchor="" x="1120.66" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo (1 samples, 0.10%)</title><rect x="205.7" y="785" width="1.2" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="208.68" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getParentScope (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getParentScope (4 samples, 0.40%)</title><rect x="796.3" y="753" width="4.7" height="15.0" fill="rgb(91,238,91)" rx="2" ry="2" />
<text text-anchor="" x="799.27" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/BaseFunction:.construct (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/BaseFunction:.construct (1 samples, 0.10%)</title><rect x="311.2" y="801" width="1.2" height="15.0" fill="rgb(50,200,50)" rx="2" ry="2" />
<text text-anchor="" x="314.23" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ip_rcv_finish (89 samples, 8.94%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv_finish (89 samples, 8.94%)</title><rect x="1009.7" y="353" width="105.6" height="15.0" fill="rgb(215,72,72)" rx="2" ry="2" />
<text text-anchor="" x="1012.74" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_rcv_finish</text>
</g>
<g class="func_g" onmouseover="s('net_rx_action (97 samples, 9.75%)')" onmouseout="c()" onclick="zoom(this)">
<title>net_rx_action (97 samples, 9.75%)</title><rect x="1001.4" y="433" width="115.1" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="1004.44" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >net_rx_action</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getParentScope (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getParentScope (1 samples, 0.10%)</title><rect x="192.6" y="817" width="1.2" height="15.0" fill="rgb(81,229,81)" rx="2" ry="2" />
<text text-anchor="" x="195.63" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vtable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.10%)</title><rect x="485.6" y="721" width="1.1" height="15.0" fill="rgb(222,82,82)" rx="2" ry="2" />
<text text-anchor="" x="488.56" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('JavaThread::run (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>JavaThread::run (956 samples, 96.08%)</title><rect x="55.1" y="1153" width="1133.7" height="15.0" fill="rgb(197,197,58)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaThread::run</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp (1 samples, 0.10%)</title><rect x="313.6" y="801" width="1.2" height="15.0" fill="rgb(70,219,70)" rx="2" ry="2" />
<text text-anchor="" x="316.60" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ep_send_events_proc (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>ep_send_events_proc (4 samples, 0.40%)</title><rect x="1182.9" y="849" width="4.7" height="15.0" fill="rgb(221,81,81)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaObject:.initMembers (1 samples, 0.10%)</title><rect x="196.2" y="833" width="1.2" height="15.0" fill="rgb(52,202,52)" rx="2" ry="2" />
<text text-anchor="" x="199.19" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('vtable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>vtable stub (1 samples, 0.10%)</title><rect x="659.9" y="769" width="1.2" height="15.0" fill="rgb(217,75,75)" rx="2" ry="2" />
<text text-anchor="" x="662.89" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (409 samples, 41.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (409 samples, 41.11%)</title><rect x="318.3" y="801" width="485.1" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="321.34" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_ve..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)</title><rect x="141.6" y="913" width="1.2" height="15.0" fill="rgb(79,227,79)" rx="2" ry="2" />
<text text-anchor="" x="144.64" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (6 samples, 0.60%)</title><rect x="415.6" y="705" width="7.1" height="15.0" fill="rgb(66,215,66)" rx="2" ry="2" />
<text text-anchor="" x="418.59" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (3 samples, 0.30%)</title><rect x="667.0" y="753" width="3.6" height="15.0" fill="rgb(70,218,70)" rx="2" ry="2" />
<text text-anchor="" x="670.01" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('perf_pmu_enable (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>perf_pmu_enable (4 samples, 0.40%)</title><rect x="935.0" y="529" width="4.8" height="15.0" fill="rgb(232,96,96)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SocketChannelImpl:.isConnected (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SocketChannelImpl:.isConnected (1 samples, 0.10%)</title><rect x="1162.7" y="801" width="1.2" height="15.0" fill="rgb(57,207,57)" rx="2" ry="2" />
<text text-anchor="" x="1165.72" y="811.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="646.8" y="737" width="1.2" height="15.0" fill="rgb(86,233,86)" rx="2" ry="2" />
<text text-anchor="" x="649.84" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('PSRootsClosurefalse::do_oop (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>PSRootsClosurefalse::do_oop (1 samples, 0.10%)</title><rect x="52.7" y="1073" width="1.2" height="15.0" fill="rgb(201,201,59)" rx="2" ry="2" />
<text text-anchor="" x="55.69" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (1 samples, 0.10%)</title><rect x="441.7" y="705" width="1.2" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="444.68" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.read (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.read (3 samples, 0.30%)</title><rect x="1173.4" y="913" width="3.6" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="1176.40" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractReferenceCountedByteBuf:.release (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractReferenceCountedByteBuf:.release (4 samples, 0.40%)</title><rect x="134.5" y="913" width="4.8" height="15.0" fill="rgb(104,249,104)" rx="2" ry="2" />
<text text-anchor="" x="137.52" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ip_local_out (121 samples, 12.16%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_local_out (121 samples, 12.16%)</title><rect x="973.0" y="545" width="143.5" height="15.0" fill="rgb(232,97,97)" rx="2" ry="2" />
<text text-anchor="" x="975.97" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_local_out</text>
</g>
<g class="func_g" onmouseover="s('__getnstimeofday (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__getnstimeofday (1 samples, 0.10%)</title><rect x="1116.5" y="529" width="1.2" height="15.0" fill="rgb(234,100,100)" rx="2" ry="2" />
<text text-anchor="" x="1119.47" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ThreadRootsTask::do_it (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>ThreadRootsTask::do_it (3 samples, 0.30%)</title><rect x="51.5" y="1137" width="3.6" height="15.0" fill="rgb(198,198,58)" rx="2" ry="2" />
<text text-anchor="" x="54.51" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.executor (1 samples, 0.10%)</title><rect x="1173.4" y="897" width="1.2" height="15.0" fill="rgb(70,219,70)" rx="2" ry="2" />
<text text-anchor="" x="1176.40" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundBuffer:.decrementPendingOutboundBytes (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundBuffer:.decrementPendingOutboundBytes (2 samples, 0.20%)</title><rect x="890.0" y="785" width="2.3" height="15.0" fill="rgb(106,251,106)" rx="2" ry="2" />
<text text-anchor="" x="892.96" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__kfree_skb (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>__kfree_skb (3 samples, 0.30%)</title><rect x="1056.0" y="225" width="3.5" height="15.0" fill="rgb(226,88,88)" rx="2" ry="2" />
<text text-anchor="" x="1058.99" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('kfree (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>kfree (1 samples, 0.10%)</title><rect x="1058.4" y="161" width="1.1" height="15.0" fill="rgb(225,87,87)" rx="2" ry="2" />
<text text-anchor="" x="1061.36" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (4 samples, 0.40%)</title><rect x="451.2" y="705" width="4.7" height="15.0" fill="rgb(59,208,59)" rx="2" ry="2" />
<text text-anchor="" x="454.17" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__inet_lookup_established (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>__inet_lookup_established (4 samples, 0.40%)</title><rect x="1013.3" y="289" width="4.7" height="15.0" fill="rgb(242,112,112)" rx="2" ry="2" />
<text text-anchor="" x="1016.30" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('thread_entry (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>thread_entry (956 samples, 96.08%)</title><rect x="55.1" y="1121" width="1133.7" height="15.0" fill="rgb(245,116,116)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >thread_entry</text>
</g>
<g class="func_g" onmouseover="s('ip_output (119 samples, 11.96%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_output (119 samples, 11.96%)</title><rect x="975.3" y="529" width="141.2" height="15.0" fill="rgb(211,66,66)" rx="2" ry="2" />
<text text-anchor="" x="978.35" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_output</text>
</g>
<g class="func_g" onmouseover="s('tcp_check_space (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_check_space (3 samples, 0.30%)</title><rect x="1063.1" y="257" width="3.6" height="15.0" fill="rgb(225,87,87)" rx="2" ry="2" />
<text text-anchor="" x="1066.11" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock_irqsave (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_irqsave (1 samples, 0.10%)</title><rect x="1107.0" y="177" width="1.2" height="15.0" fill="rgb(218,76,76)" rx="2" ry="2" />
<text text-anchor="" x="1109.98" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="651.6" y="737" width="1.2" height="15.0" fill="rgb(77,224,77)" rx="2" ry="2" />
<text text-anchor="" x="654.59" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.get (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.get (4 samples, 0.40%)</title><rect x="662.3" y="769" width="4.7" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="665.26" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('inet_sendmsg (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>inet_sendmsg (1 samples, 0.10%)</title><rect x="931.5" y="657" width="1.2" height="15.0" fill="rgb(211,66,66)" rx="2" ry="2" />
<text text-anchor="" x="934.47" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.name (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.name (8 samples, 0.80%)</title><rect x="205.7" y="817" width="9.5" height="15.0" fill="rgb(102,248,102)" rx="2" ry="2" />
<text text-anchor="" x="208.68" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ipv4_mtu (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ipv4_mtu (1 samples, 0.10%)</title><rect x="1142.6" y="593" width="1.1" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="1145.56" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (7 samples, 0.70%)</title><rect x="337.3" y="705" width="8.3" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="340.32" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('aa_file_perm (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>aa_file_perm (1 samples, 0.10%)</title><rect x="122.7" y="753" width="1.1" height="15.0" fill="rgb(208,62,62)" rx="2" ry="2" />
<text text-anchor="" x="125.66" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (242 samples, 24.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (242 samples, 24.32%)</title><rect x="885.2" y="945" width="287.0" height="15.0" fill="rgb(78,226,78)" rx="2" ry="2" />
<text text-anchor="" x="888.22" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHandl..</text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock_bh (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock_bh (1 samples, 0.10%)</title><rect x="1129.5" y="593" width="1.2" height="15.0" fill="rgb(218,77,77)" rx="2" ry="2" />
<text text-anchor="" x="1132.52" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_v4_rcv (87 samples, 8.74%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_rcv (87 samples, 8.74%)</title><rect x="1012.1" y="305" width="103.2" height="15.0" fill="rgb(213,69,69)" rx="2" ry="2" />
<text text-anchor="" x="1015.11" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_v4_rcv</text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/cs/UTF_8$Encoder:.init (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/cs/UTF_8$Encoder:.init (3 samples, 0.30%)</title><rect x="253.1" y="737" width="3.6" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="256.12" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.write (33 samples, 3.32%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.write (33 samples, 3.32%)</title><rect x="265.0" y="705" width="39.1" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="267.97" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio..</text>
</g>
<g class="func_g" onmouseover="s('getnstimeofday (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>getnstimeofday (1 samples, 0.10%)</title><rect x="1061.9" y="209" width="1.2" height="15.0" fill="rgb(221,80,80)" rx="2" ry="2" />
<text text-anchor="" x="1064.92" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_established_options (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_established_options (4 samples, 0.40%)</title><rect x="1143.7" y="577" width="4.8" height="15.0" fill="rgb(230,94,94)" rx="2" ry="2" />
<text text-anchor="" x="1146.75" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.bind (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.bind (7 samples, 0.70%)</title><rect x="642.1" y="785" width="8.3" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="645.10" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('update_curr (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>update_curr (2 samples, 0.20%)</title><rect x="1093.9" y="33" width="2.4" height="15.0" fill="rgb(246,117,117)" rx="2" ry="2" />
<text text-anchor="" x="1096.94" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelDuplexHandler:.read (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelDuplexHandler:.read (1 samples, 0.10%)</title><rect x="1177.0" y="945" width="1.1" height="15.0" fill="rgb(51,200,51)" rx="2" ry="2" />
<text text-anchor="" x="1179.95" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('bictcp_cong_avoid (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>bictcp_cong_avoid (3 samples, 0.30%)</title><rect x="1042.9" y="241" width="3.6" height="15.0" fill="rgb(228,91,91)" rx="2" ry="2" />
<text text-anchor="" x="1045.94" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('jlong_disjoint_arraycopy (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>jlong_disjoint_arraycopy (1 samples, 0.10%)</title><rect x="253.1" y="689" width="1.2" height="15.0" fill="rgb(254,129,129)" rx="2" ry="2" />
<text text-anchor="" x="256.12" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('security_file_permission (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>security_file_permission (1 samples, 0.10%)</title><rect x="1152.1" y="657" width="1.1" height="15.0" fill="rgb(246,118,118)" rx="2" ry="2" />
<text text-anchor="" x="1155.05" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 (3 samples, 0.30%)</title><rect x="844.9" y="881" width="3.6" height="15.0" fill="rgb(79,227,79)" rx="2" ry="2" />
<text text-anchor="" x="847.89" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.splitInitialLine (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.splitInitialLine (5 samples, 0.50%)</title><rect x="867.4" y="897" width="6.0" height="15.0" fill="rgb(104,250,104)" rx="2" ry="2" />
<text text-anchor="" x="870.43" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.splitHeader (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.splitHeader (8 samples, 0.80%)</title><rect x="855.6" y="881" width="9.5" height="15.0" fill="rgb(106,251,106)" rx="2" ry="2" />
<text text-anchor="" x="858.57" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete (240 samples, 24.12%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete (240 samples, 24.12%)</title><rect x="886.4" y="897" width="284.6" height="15.0" fill="rgb(54,203,54)" rx="2" ry="2" />
<text text-anchor="" x="889.40" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/vertx/java/core/net/impl/VertxHan..</text>
</g>
<g class="func_g" onmouseover="s('JavaThread::oops_do (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>JavaThread::oops_do (3 samples, 0.30%)</title><rect x="51.5" y="1121" width="3.6" height="15.0" fill="rgb(207,207,61)" rx="2" ry="2" />
<text text-anchor="" x="54.51" y="1131.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (2 samples, 0.20%)</title><rect x="771.4" y="705" width="2.3" height="15.0" fill="rgb(57,206,57)" rx="2" ry="2" />
<text text-anchor="" x="774.37" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AdaptiveRecvByteBufAllocator$HandleImpl:.record (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AdaptiveRecvByteBufAllocator$HandleImpl:.record (2 samples, 0.20%)</title><rect x="64.6" y="961" width="2.3" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="67.55" y="971.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpVersion:.compareTo (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpVersion:.compareTo (2 samples, 0.20%)</title><rect x="178.4" y="865" width="2.4" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="181.40" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpObjectDecoder:.decode (57 samples, 5.73%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpObjectDecoder:.decode (57 samples, 5.73%)</title><rect x="811.7" y="913" width="67.6" height="15.0" fill="rgb(81,228,81)" rx="2" ry="2" />
<text text-anchor="" x="814.69" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/net..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaObject:.initMembers (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaObject:.initMembers (4 samples, 0.40%)</title><rect x="635.0" y="753" width="4.7" height="15.0" fill="rgb(52,202,52)" rx="2" ry="2" />
<text text-anchor="" x="637.98" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="585.2" y="753" width="1.2" height="15.0" fill="rgb(105,251,105)" rx="2" ry="2" />
<text text-anchor="" x="588.18" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/channels/spi/AbstractInterruptibleChannel:.end (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/channels/spi/AbstractInterruptibleChannel:.end (3 samples, 0.30%)</title><rect x="85.9" y="913" width="3.6" height="15.0" fill="rgb(72,220,72)" rx="2" ry="2" />
<text text-anchor="" x="88.90" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/ThreadLocal:.get (1 samples, 0.10%)</title><rect x="180.8" y="865" width="1.2" height="15.0" fill="rgb(98,244,98)" rx="2" ry="2" />
<text text-anchor="" x="183.77" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ktime_get_real (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>ktime_get_real (3 samples, 0.30%)</title><rect x="1116.5" y="561" width="3.5" height="15.0" fill="rgb(251,125,125)" rx="2" ry="2" />
<text text-anchor="" x="1119.47" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ScavengeRootsTask::do_it (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ScavengeRootsTask::do_it (1 samples, 0.10%)</title><rect x="46.8" y="1137" width="1.1" height="15.0" fill="rgb(205,205,61)" rx="2" ry="2" />
<text text-anchor="" x="49.76" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('dev_hard_start_xmit (9 samples, 0.90%)')" onmouseout="c()" onclick="zoom(this)">
<title>dev_hard_start_xmit (9 samples, 0.90%)</title><rect x="981.3" y="465" width="10.6" height="15.0" fill="rgb(240,108,108)" rx="2" ry="2" />
<text text-anchor="" x="984.28" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('all (995 samples, 100%)')" onmouseout="c()" onclick="zoom(this)">
<title>all (995 samples, 100%)</title><rect x="10.0" y="1217" width="1180.0" height="15.0" fill="rgb(227,89,89)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1227.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('msecs_to_jiffies (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>msecs_to_jiffies (1 samples, 0.10%)</title><rect x="1000.3" y="433" width="1.1" height="15.0" fill="rgb(200,51,51)" rx="2" ry="2" />
<text text-anchor="" x="1003.25" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead (1 samples, 0.10%)</title><rect x="1178.1" y="945" width="1.2" height="15.0" fill="rgb(51,201,51)" rx="2" ry="2" />
<text text-anchor="" x="1181.14" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write (34 samples, 3.42%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write (34 samples, 3.42%)</title><rect x="265.0" y="721" width="40.3" height="15.0" fill="rgb(57,206,57)" rx="2" ry="2" />
<text text-anchor="" x="267.97" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lor..</text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="1167.5" y="849" width="1.2" height="15.0" fill="rgb(227,90,90)" rx="2" ry="2" />
<text text-anchor="" x="1170.47" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_write_xmit (147 samples, 14.77%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_write_xmit (147 samples, 14.77%)</title><rect x="951.6" y="593" width="174.4" height="15.0" fill="rgb(249,122,122)" rx="2" ry="2" />
<text text-anchor="" x="954.63" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_write_xmit</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/UnpooledHeapByteBuf:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/UnpooledHeapByteBuf:.init (1 samples, 0.10%)</title><rect x="241.3" y="753" width="1.1" height="15.0" fill="rgb(92,239,92)" rx="2" ry="2" />
<text text-anchor="" x="244.26" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('apparmor_socket_recvmsg (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>apparmor_socket_recvmsg (5 samples, 0.50%)</title><rect x="96.6" y="769" width="5.9" height="15.0" fill="rgb(205,58,58)" rx="2" ry="2" />
<text text-anchor="" x="99.57" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ksize (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>ksize (1 samples, 0.10%)</title><rect x="1141.4" y="593" width="1.2" height="15.0" fill="rgb(246,118,118)" rx="2" ry="2" />
<text text-anchor="" x="1144.38" y="603.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('lock_sock_nested (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>lock_sock_nested (1 samples, 0.10%)</title><rect x="103.7" y="753" width="1.2" height="15.0" fill="rgb(231,95,95)" rx="2" ry="2" />
<text text-anchor="" x="106.69" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('PSScavengeKlassClosure::do_klass (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>PSScavengeKlassClosure::do_klass (1 samples, 0.10%)</title><rect x="46.8" y="1105" width="1.1" height="15.0" fill="rgb(215,215,64)" rx="2" ry="2" />
<text text-anchor="" x="49.76" y="1115.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('[unknown] (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (7 samples, 0.70%)</title><rect x="13.6" y="1153" width="8.3" height="15.0" fill="rgb(210,65,65)" rx="2" ry="2" />
<text text-anchor="" x="16.56" y="1163.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="773.7" y="769" width="1.2" height="15.0" fill="rgb(63,212,63)" rx="2" ry="2" />
<text text-anchor="" x="776.74" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaMethod:.call (74 samples, 7.44%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaMethod:.call (74 samples, 7.44%)</title><rect x="217.5" y="785" width="87.8" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="220.54" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozil..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (86 samples, 8.64%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (86 samples, 8.64%)</title><rect x="672.9" y="785" width="102.0" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="675.93" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (1 samples, 0.10%)</title><rect x="537.7" y="753" width="1.2" height="15.0" fill="rgb(65,213,65)" rx="2" ry="2" />
<text text-anchor="" x="540.74" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.toObjectOrNull (1 samples, 0.10%)</title><rect x="215.2" y="817" width="1.2" height="15.0" fill="rgb(77,225,77)" rx="2" ry="2" />
<text text-anchor="" x="218.17" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__wake_up_locked (25 samples, 2.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_locked (25 samples, 2.51%)</title><rect x="1077.3" y="177" width="29.7" height="15.0" fill="rgb(214,70,70)" rx="2" ry="2" />
<text text-anchor="" x="1080.34" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('JavaThread::thread_main_inner (956 samples, 96.08%)')" onmouseout="c()" onclick="zoom(this)">
<title>JavaThread::thread_main_inner (956 samples, 96.08%)</title><rect x="55.1" y="1137" width="1133.7" height="15.0" fill="rgb(227,227,68)" rx="2" ry="2" />
<text text-anchor="" x="58.07" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JavaThread::thread_main_inner</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite (225 samples, 22.61%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite (225 samples, 22.61%)</title><rect x="892.3" y="785" width="266.9" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="895.33" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/nio/AbstractNioBy..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction (1 samples, 0.10%)</title><rect x="537.7" y="769" width="1.2" height="15.0" fill="rgb(107,252,107)" rx="2" ry="2" />
<text text-anchor="" x="540.74" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpMessage:.init (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpMessage:.init (2 samples, 0.20%)</title><rect x="822.4" y="897" width="2.3" height="15.0" fill="rgb(59,208,59)" rx="2" ry="2" />
<text text-anchor="" x="825.36" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getBase (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getBase (1 samples, 0.10%)</title><rect x="425.1" y="721" width="1.2" height="15.0" fill="rgb(103,249,103)" rx="2" ry="2" />
<text text-anchor="" x="428.08" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (8 samples, 0.80%)</title><rect x="708.5" y="753" width="9.5" height="15.0" fill="rgb(63,212,63)" rx="2" ry="2" />
<text text-anchor="" x="711.51" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('update_cfs_rq_blocked_load (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>update_cfs_rq_blocked_load (1 samples, 0.10%)</title><rect x="1092.8" y="33" width="1.1" height="15.0" fill="rgb(212,68,68)" rx="2" ry="2" />
<text text-anchor="" x="1095.75" y="43.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/DefaultChannelPipeline$HeadContext:.write (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/DefaultChannelPipeline$HeadContext:.write (6 samples, 0.60%)</title><rect x="267.3" y="657" width="7.2" height="15.0" fill="rgb(80,227,80)" rx="2" ry="2" />
<text text-anchor="" x="270.35" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('skb_copy_datagram_iovec (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>skb_copy_datagram_iovec (3 samples, 0.30%)</title><rect x="116.7" y="737" width="3.6" height="15.0" fill="rgb(251,124,124)" rx="2" ry="2" />
<text text-anchor="" x="119.73" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/PooledByteBuf:.deallocate (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/PooledByteBuf:.deallocate (2 samples, 0.20%)</title><rect x="136.9" y="897" width="2.4" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="139.89" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SocketChannelImpl:.isConnected (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SocketChannelImpl:.isConnected (1 samples, 0.10%)</title><rect x="1175.8" y="865" width="1.2" height="15.0" fill="rgb(59,208,59)" rx="2" ry="2" />
<text text-anchor="" x="1178.77" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (2 samples, 0.20%)</title><rect x="668.2" y="737" width="2.4" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="671.19" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (20 samples, 2.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (20 samples, 2.01%)</title><rect x="777.3" y="785" width="23.7" height="15.0" fill="rgb(92,239,92)" rx="2" ry="2" />
<text text-anchor="" x="780.30" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>native_write_msr_safe (4 samples, 0.40%)</title><rect x="935.0" y="481" width="4.8" height="15.0" fill="rgb(235,101,101)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_v4_do_rcv (77 samples, 7.74%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_v4_do_rcv (77 samples, 7.74%)</title><rect x="1024.0" y="289" width="91.3" height="15.0" fill="rgb(252,127,127)" rx="2" ry="2" />
<text text-anchor="" x="1026.97" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tcp_v4_do_..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeCall:.init (20 samples, 2.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeCall:.init (20 samples, 2.01%)</title><rect x="401.4" y="737" width="23.7" height="15.0" fill="rgb(63,212,63)" rx="2" ry="2" />
<text text-anchor="" x="404.36" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.writeBytes (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.writeBytes (3 samples, 0.30%)</title><rect x="279.2" y="657" width="3.6" height="15.0" fill="rgb(58,207,58)" rx="2" ry="2" />
<text text-anchor="" x="282.21" y="667.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('[unknown] (197 samples, 19.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>[unknown] (197 samples, 19.80%)</title><rect x="919.6" y="737" width="233.6" height="15.0" fill="rgb(237,104,104)" rx="2" ry="2" />
<text text-anchor="" x="922.61" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[unknown]</text>
</g>
<g class="func_g" onmouseover="s('Ljava/nio/DirectByteBuffer:.duplicate (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/nio/DirectByteBuffer:.duplicate (1 samples, 0.10%)</title><rect x="1179.3" y="945" width="1.2" height="15.0" fill="rgb(73,221,73)" rx="2" ry="2" />
<text text-anchor="" x="1182.33" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (1 samples, 0.10%)</title><rect x="388.3" y="705" width="1.2" height="15.0" fill="rgb(77,225,77)" rx="2" ry="2" />
<text text-anchor="" x="391.31" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (2 samples, 0.20%)</title><rect x="1018.0" y="289" width="2.4" height="15.0" fill="rgb(215,72,72)" rx="2" ry="2" />
<text text-anchor="" x="1021.04" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.flush (233 samples, 23.42%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.flush (233 samples, 23.42%)</title><rect x="887.6" y="817" width="276.3" height="15.0" fill="rgb(85,232,85)" rx="2" ry="2" />
<text text-anchor="" x="890.59" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHand..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead (637 samples, 64.02%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead (637 samples, 64.02%)</title><rect x="129.8" y="945" width="755.4" height="15.0" fill="rgb(90,237,90)" rx="2" ry="2" />
<text text-anchor="" x="132.78" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead</text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="533.0" y="721" width="1.2" height="15.0" fill="rgb(109,254,109)" rx="2" ry="2" />
<text text-anchor="" x="535.99" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.init (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.init (4 samples, 0.40%)</title><rect x="860.3" y="849" width="4.8" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="863.31" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sys_futex (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>sys_futex (1 samples, 0.10%)</title><rect x="21.9" y="1073" width="1.1" height="15.0" fill="rgb(206,60,60)" rx="2" ry="2" />
<text text-anchor="" x="24.86" y="1083.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__kmalloc_node_track_caller (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__kmalloc_node_track_caller (1 samples, 0.10%)</title><rect x="1131.9" y="577" width="1.2" height="15.0" fill="rgb(223,84,84)" rx="2" ry="2" />
<text text-anchor="" x="1134.89" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('ttwu_do_wakeup (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>ttwu_do_wakeup (5 samples, 0.50%)</title><rect x="1097.5" y="97" width="5.9" height="15.0" fill="rgb(254,129,129)" rx="2" ry="2" />
<text text-anchor="" x="1100.50" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/EPollArrayWrapper:.epollWait (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/EPollArrayWrapper:.epollWait (4 samples, 0.40%)</title><rect x="1182.9" y="945" width="4.7" height="15.0" fill="rgb(74,222,74)" rx="2" ry="2" />
<text text-anchor="" x="1185.88" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/http/impl/AssembledFullHttpResponse:.toLastContent (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/http/impl/AssembledFullHttpResponse:.toLastContent (1 samples, 0.10%)</title><rect x="259.0" y="753" width="1.2" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="262.05" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('native_read_tsc (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>native_read_tsc (1 samples, 0.10%)</title><rect x="1061.9" y="161" width="1.2" height="15.0" fill="rgb(234,100,100)" rx="2" ry="2" />
<text text-anchor="" x="1064.92" y="171.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (11 samples, 1.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (11 samples, 1.11%)</title><rect x="375.3" y="673" width="13.0" height="15.0" fill="rgb(84,232,84)" rx="2" ry="2" />
<text text-anchor="" x="378.27" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/JavaMembers:.get (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/JavaMembers:.get (1 samples, 0.10%)</title><rect x="658.7" y="753" width="1.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="661.70" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('_raw_spin_lock (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>_raw_spin_lock (1 samples, 0.10%)</title><rect x="987.2" y="401" width="1.2" height="15.0" fill="rgb(245,115,115)" rx="2" ry="2" />
<text text-anchor="" x="990.21" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="459.5" y="689" width="1.2" height="15.0" fill="rgb(60,209,60)" rx="2" ry="2" />
<text text-anchor="" x="462.47" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeCall:.init (15 samples, 1.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeCall:.init (15 samples, 1.51%)</title><rect x="333.8" y="721" width="17.7" height="15.0" fill="rgb(53,203,53)" rx="2" ry="2" />
<text text-anchor="" x="336.76" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (6 samples, 0.60%)</title><rect x="497.4" y="705" width="7.1" height="15.0" fill="rgb(65,214,65)" rx="2" ry="2" />
<text text-anchor="" x="500.42" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/DefaultHttpHeaders:.init (1 samples, 0.10%)</title><rect x="177.2" y="849" width="1.2" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="180.22" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.put (25 samples, 2.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.put (25 samples, 2.51%)</title><rect x="455.9" y="721" width="29.7" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="458.91" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lo..</text>
</g>
<g class="func_g" onmouseover="s('activate_task (7 samples, 0.70%)')" onmouseout="c()" onclick="zoom(this)">
<title>activate_task (7 samples, 0.70%)</title><rect x="1089.2" y="97" width="8.3" height="15.0" fill="rgb(227,89,89)" rx="2" ry="2" />
<text text-anchor="" x="1092.20" y="107.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/internal/AppendableCharSequence:.substring (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/internal/AppendableCharSequence:.substring (2 samples, 0.20%)</title><rect x="873.4" y="897" width="2.3" height="15.0" fill="rgb(105,250,105)" rx="2" ry="2" />
<text text-anchor="" x="876.36" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__slab_free (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>__slab_free (1 samples, 0.10%)</title><rect x="1058.4" y="145" width="1.1" height="15.0" fill="rgb(215,73,73)" rx="2" ry="2" />
<text text-anchor="" x="1061.36" y="155.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead (555 samples, 55.78%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead (555 samples, 55.78%)</title><rect x="151.1" y="897" width="658.2" height="15.0" fill="rgb(101,247,101)" rx="2" ry="2" />
<text text-anchor="" x="154.13" y="907.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead</text>
</g>
<g class="func_g" onmouseover="s('Lsun/nio/ch/SocketChannelImpl:.write (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lsun/nio/ch/SocketChannelImpl:.write (1 samples, 0.10%)</title><rect x="1161.5" y="785" width="1.2" height="15.0" fill="rgb(80,227,80)" rx="2" ry="2" />
<text text-anchor="" x="1164.54" y="795.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/WrapFactory:.wrap (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/WrapFactory:.wrap (5 samples, 0.50%)</title><rect x="190.3" y="849" width="5.9" height="15.0" fill="rgb(68,216,68)" rx="2" ry="2" />
<text text-anchor="" x="193.26" y="859.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (154 samples, 15.48%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (154 samples, 15.48%)</title><rect x="326.6" y="753" width="182.7" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="329.64" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript..</text>
</g>
<g class="func_g" onmouseover="s('tcp_init_tso_segs (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_init_tso_segs (1 samples, 0.10%)</title><rect x="964.7" y="577" width="1.2" height="15.0" fill="rgb(239,106,106)" rx="2" ry="2" />
<text text-anchor="" x="967.67" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/MessageToMessageEncoder:.write (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/MessageToMessageEncoder:.write (1 samples, 0.10%)</title><rect x="304.1" y="705" width="1.2" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="307.11" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.read (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.read (2 samples, 0.20%)</title><rect x="1174.6" y="881" width="2.4" height="15.0" fill="rgb(70,219,70)" rx="2" ry="2" />
<text text-anchor="" x="1177.58" y="891.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelOutboundBuffer:.incrementPendingOutboundBytes (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelOutboundBuffer:.incrementPendingOutboundBytes (1 samples, 0.10%)</title><rect x="273.3" y="641" width="1.2" height="15.0" fill="rgb(95,241,95)" rx="2" ry="2" />
<text text-anchor="" x="276.28" y="651.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('sock_wfree (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>sock_wfree (1 samples, 0.10%)</title><rect x="989.6" y="417" width="1.2" height="15.0" fill="rgb(217,75,75)" rx="2" ry="2" />
<text text-anchor="" x="992.58" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.read (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.read (4 samples, 0.40%)</title><rect x="1172.2" y="945" width="4.8" height="15.0" fill="rgb(55,205,55)" rx="2" ry="2" />
<text text-anchor="" x="1175.21" y="955.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__wake_up_common (27 samples, 2.71%)')" onmouseout="c()" onclick="zoom(this)">
<title>__wake_up_common (27 samples, 2.71%)</title><rect x="1076.2" y="209" width="32.0" height="15.0" fill="rgb(226,89,89)" rx="2" ry="2" />
<text text-anchor="" x="1079.15" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.setAttributes (5 samples, 0.50%)</title><rect x="345.6" y="689" width="5.9" height="15.0" fill="rgb(64,213,64)" rx="2" ry="2" />
<text text-anchor="" x="348.62" y="699.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('tcp_event_new_data_sent (6 samples, 0.60%)')" onmouseout="c()" onclick="zoom(this)">
<title>tcp_event_new_data_sent (6 samples, 0.60%)</title><rect x="957.6" y="577" width="7.1" height="15.0" fill="rgb(200,51,51)" rx="2" ry="2" />
<text text-anchor="" x="960.56" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (4 samples, 0.40%)</title><rect x="524.7" y="721" width="4.7" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="527.69" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (511 samples, 51.36%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_2 (511 samples, 51.36%)</title><rect x="198.6" y="833" width="606.0" height="15.0" fill="rgb(96,242,96)" rx="2" ry="2" />
<text text-anchor="" x="201.56" y="843.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lorg/mozilla/javascript/gen/file__root_vert_x_2_1_5_sys_mods_io_vertx_lang_js_1_1_0..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.createSlot (15 samples, 1.51%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.createSlot (15 samples, 1.51%)</title><rect x="467.8" y="673" width="17.8" height="15.0" fill="rgb(65,214,65)" rx="2" ry="2" />
<text text-anchor="" x="470.77" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.addKnownAbsentSlot (1 samples, 0.10%)</title><rect x="610.1" y="705" width="1.2" height="15.0" fill="rgb(57,207,57)" rx="2" ry="2" />
<text text-anchor="" x="613.08" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.putImpl (8 samples, 0.80%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.putImpl (8 samples, 0.80%)</title><rect x="406.1" y="705" width="9.5" height="15.0" fill="rgb(61,210,61)" rx="2" ry="2" />
<text text-anchor="" x="409.10" y="715.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('VMThread::loop (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>VMThread::loop (1 samples, 0.10%)</title><rect x="1188.8" y="1137" width="1.2" height="15.0" fill="rgb(176,176,50)" rx="2" ry="2" />
<text text-anchor="" x="1191.81" y="1147.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue (1 samples, 0.10%)</title><rect x="439.3" y="721" width="1.2" height="15.0" fill="rgb(75,223,75)" rx="2" ry="2" />
<text text-anchor="" x="442.31" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('smp_call_function_single_interrupt (4 samples, 0.40%)')" onmouseout="c()" onclick="zoom(this)">
<title>smp_call_function_single_interrupt (4 samples, 0.40%)</title><rect x="935.0" y="625" width="4.8" height="15.0" fill="rgb(214,70,70)" rx="2" ry="2" />
<text text-anchor="" x="938.03" y="635.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('do_sync_read (22 samples, 2.21%)')" onmouseout="c()" onclick="zoom(this)">
<title>do_sync_read (22 samples, 2.21%)</title><rect x="96.6" y="817" width="26.1" height="15.0" fill="rgb(225,87,87)" rx="2" ry="2" />
<text text-anchor="" x="99.57" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s('fdval (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>fdval (1 samples, 0.10%)</title><rect x="918.4" y="721" width="1.2" height="15.0" fill="rgb(243,113,113)" rx="2" ry="2" />
<text text-anchor="" x="921.42" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Ljava/lang/String:.hashCode (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Ljava/lang/String:.hashCode (1 samples, 0.10%)</title><rect x="552.0" y="721" width="1.2" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="554.97" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('__tcp_push_pending_frames (149 samples, 14.97%)')" onmouseout="c()" onclick="zoom(this)">
<title>__tcp_push_pending_frames (149 samples, 14.97%)</title><rect x="949.3" y="609" width="176.7" height="15.0" fill="rgb(206,59,59)" rx="2" ry="2" />
<text text-anchor="" x="952.26" y="619.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__tcp_push_pending_fra..</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBuf:.writeBytes (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBuf:.writeBytes (1 samples, 0.10%)</title><rect x="266.2" y="673" width="1.1" height="15.0" fill="rgb(67,216,67)" rx="2" ry="2" />
<text text-anchor="" x="269.16" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (28 samples, 2.81%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (28 samples, 2.81%)</title><rect x="357.5" y="721" width="33.2" height="15.0" fill="rgb(78,225,78)" rx="2" ry="2" />
<text text-anchor="" x="360.48" y="731.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lo..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptableObject:.getSlot (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptableObject:.getSlot (3 samples, 0.30%)</title><rect x="546.0" y="737" width="3.6" height="15.0" fill="rgb(97,243,97)" rx="2" ry="2" />
<text text-anchor="" x="549.04" y="747.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/util/Recycler:.get (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/util/Recycler:.get (2 samples, 0.20%)</title><rect x="76.4" y="929" width="2.4" height="15.0" fill="rgb(76,223,76)" rx="2" ry="2" />
<text text-anchor="" x="79.41" y="939.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('rw_verify_area (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>rw_verify_area (1 samples, 0.10%)</title><rect x="1152.1" y="673" width="1.1" height="15.0" fill="rgb(209,63,63)" rx="2" ry="2" />
<text text-anchor="" x="1155.05" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/IdScriptableObject:.has (1 samples, 0.10%)</title><rect x="521.1" y="753" width="1.2" height="15.0" fill="rgb(62,211,62)" rx="2" ry="2" />
<text text-anchor="" x="524.14" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('do_sync_write (186 samples, 18.69%)')" onmouseout="c()" onclick="zoom(this)">
<title>do_sync_write (186 samples, 18.69%)</title><rect x="931.5" y="673" width="220.6" height="15.0" fill="rgb(240,109,109)" rx="2" ry="2" />
<text text-anchor="" x="934.47" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_sync_write</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype (1 samples, 0.10%)</title><rect x="626.7" y="769" width="1.2" height="15.0" fill="rgb(71,219,71)" rx="2" ry="2" />
<text text-anchor="" x="629.68" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/ChannelDuplexHandler:.flush (237 samples, 23.82%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/ChannelDuplexHandler:.flush (237 samples, 23.82%)</title><rect x="887.6" y="865" width="281.1" height="15.0" fill="rgb(93,239,93)" rx="2" ry="2" />
<text text-anchor="" x="890.59" y="875.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/ChannelDuplexHandle..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeJavaMethod:.findFunction (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeJavaMethod:.findFunction (2 samples, 0.20%)</title><rect x="630.2" y="769" width="2.4" height="15.0" fill="rgb(92,238,92)" rx="2" ry="2" />
<text text-anchor="" x="633.24" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (5 samples, 0.50%)')" onmouseout="c()" onclick="zoom(this)">
<title>enqueue_task_fair (5 samples, 0.50%)</title><rect x="1090.4" y="65" width="5.9" height="15.0" fill="rgb(220,79,79)" rx="2" ry="2" />
<text text-anchor="" x="1093.38" y="75.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('kmem_cache_alloc_node (2 samples, 0.20%)')" onmouseout="c()" onclick="zoom(this)">
<title>kmem_cache_alloc_node (2 samples, 0.20%)</title><rect x="1137.8" y="577" width="2.4" height="15.0" fill="rgb(239,107,107)" rx="2" ry="2" />
<text text-anchor="" x="1140.82" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="1171.0" y="913" width="1.2" height="15.0" fill="rgb(203,54,54)" rx="2" ry="2" />
<text text-anchor="" x="1174.03" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/buffer/AbstractByteBufAllocator:.heapBuffer (3 samples, 0.30%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/buffer/AbstractByteBufAllocator:.heapBuffer (3 samples, 0.30%)</title><rect x="237.7" y="753" width="3.6" height="15.0" fill="rgb(72,220,72)" rx="2" ry="2" />
<text text-anchor="" x="240.70" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (21 samples, 2.11%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp (21 samples, 2.11%)</title><rect x="538.9" y="769" width="24.9" height="15.0" fill="rgb(87,234,87)" rx="2" ry="2" />
<text text-anchor="" x="541.92" y="779.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s('ip_queue_xmit (122 samples, 12.26%)')" onmouseout="c()" onclick="zoom(this)">
<title>ip_queue_xmit (122 samples, 12.26%)</title><rect x="971.8" y="561" width="144.7" height="15.0" fill="rgb(230,93,93)" rx="2" ry="2" />
<text text-anchor="" x="974.79" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ip_queue_xmit</text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (241 samples, 24.22%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete (241 samples, 24.22%)</title><rect x="885.2" y="913" width="285.8" height="15.0" fill="rgb(84,231,84)" rx="2" ry="2" />
<text text-anchor="" x="888.22" y="923.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Lio/netty/channel/AbstractChannelHandl..</text>
</g>
<g class="func_g" onmouseover="s('Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (10 samples, 1.01%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lorg/mozilla/javascript/NativeFunction:.initScriptFunction (10 samples, 1.01%)</title><rect x="784.4" y="753" width="11.9" height="15.0" fill="rgb(80,227,80)" rx="2" ry="2" />
<text text-anchor="" x="787.41" y="763.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('itable stub (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>itable stub (1 samples, 0.10%)</title><rect x="195.0" y="817" width="1.2" height="15.0" fill="rgb(248,120,120)" rx="2" ry="2" />
<text text-anchor="" x="198.01" y="827.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('netif_skb_dev_features (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>netif_skb_dev_features (1 samples, 0.10%)</title><rect x="990.8" y="449" width="1.1" height="15.0" fill="rgb(232,97,97)" rx="2" ry="2" />
<text text-anchor="" x="993.76" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('Lio/netty/handler/codec/http/HttpResponseEncoder:.acceptOutboundMessage (1 samples, 0.10%)')" onmouseout="c()" onclick="zoom(this)">
<title>Lio/netty/handler/codec/http/HttpResponseEncoder:.acceptOutboundMessage (1 samples, 0.10%)</title><rect x="297.0" y="673" width="1.2" height="15.0" fill="rgb(70,218,70)" rx="2" ry="2" />
<text text-anchor="" x="299.99" y="683.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
</svg>
