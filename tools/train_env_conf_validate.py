#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import os
import toml


def read_usr_conf(usr_conf_file, logger):
    """
    read usr conf
    读取配置文件
    """
    if not usr_conf_file or not os.path.exists(usr_conf_file):
        return None

    try:
        with open(usr_conf_file, "r") as file:
            loaded_usr_conf = toml.load(file)
        return loaded_usr_conf

    except Exception as e:
        logger.exception(f"toml.load failed, error msg is {str(e)}, {usr_conf_file} please check")
        return None
