# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class UpdateReqData(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = UpdateReqData()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsUpdateReqData(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # UpdateReqData
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # UpdateReqData
    def AgentId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # UpdateReqData
    def Frames(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from .FrameData import FrameData

            obj = FrameData()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # UpdateReqData
    def FramesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # UpdateReqData
    def FramesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0


def UpdateReqDataStart(builder):
    builder.StartObject(2)


def Start(builder):
    return UpdateReqDataStart(builder)


def UpdateReqDataAddAgentId(builder, agentId):
    builder.PrependUint32Slot(0, agentId, 0)


def AddAgentId(builder, agentId):
    return UpdateReqDataAddAgentId(builder, agentId)


def UpdateReqDataAddFrames(builder, frames):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(frames), 0)


def AddFrames(builder, frames):
    return UpdateReqDataAddFrames(builder, frames)


def UpdateReqDataStartFramesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)


def StartFramesVector(builder, numElems):
    return UpdateReqDataStartFramesVector(builder, numElems)


def UpdateReqDataEnd(builder):
    return builder.EndObject()


def End(builder):
    return UpdateReqDataEnd(builder)
