# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class Response(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Response()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsResponse(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # Response
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Response
    def SeqNo(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # Response
    def MsgType(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # Response
    def Msg(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            from flatbuffers.table import Table

            obj = Table(bytearray(), 0)
            self._tab.Union(obj, o)
            return obj
        return None


def ResponseStart(builder):
    builder.StartObject(3)


def Start(builder):
    return ResponseStart(builder)


def ResponseAddSeqNo(builder, seqNo):
    builder.PrependUint32Slot(0, seqNo, 0)


def AddSeqNo(builder, seqNo):
    return ResponseAddSeqNo(builder, seqNo)


def ResponseAddMsgType(builder, msgType):
    builder.PrependUint8Slot(1, msgType, 0)


def AddMsgType(builder, msgType):
    return ResponseAddMsgType(builder, msgType)


def ResponseAddMsg(builder, msg):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(msg), 0)


def AddMsg(builder, msg):
    return ResponseAddMsg(builder, msg)


def ResponseEnd(builder):
    return builder.EndObject()


def End(builder):
    return ResponseEnd(builder)
