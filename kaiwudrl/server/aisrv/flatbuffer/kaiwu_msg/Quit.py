# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class Quit(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Quit()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsQuit(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # Quit
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Quit
    def ClientId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Quit
    def QuitCode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # Quit
    def Message(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None


def QuitStart(builder):
    builder.StartObject(3)


def Start(builder):
    return QuitStart(builder)


def QuitAddClientId(builder, clientId):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(clientId), 0)


def AddClientId(builder, clientId):
    return QuitAddClientId(builder, clientId)


def QuitAddQuitCode(builder, quitCode):
    builder.PrependInt32Slot(1, quitCode, 0)


def AddQuitCode(builder, quitCode):
    return QuitAddQuitCode(builder, quitCode)


def QuitAddMessage(builder, message):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(message), 0)


def AddMessage(builder, message):
    return QuitAddMessage(builder, message)


def QuitEnd(builder):
    return builder.EndObject()


def End(builder):
    return QuitEnd(builder)
