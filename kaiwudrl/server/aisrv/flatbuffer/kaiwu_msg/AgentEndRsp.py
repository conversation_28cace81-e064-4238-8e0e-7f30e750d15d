# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class AgentEndRsp(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AgentEndRsp()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAgentEndRsp(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # AgentEndRsp
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AgentEndRsp
    def RetCode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # AgentEndRsp
    def EpId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # AgentEndRsp
    def AgentId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0


def AgentEndRspStart(builder):
    builder.StartObject(3)


def Start(builder):
    return AgentEndRspStart(builder)


def AgentEndRspAddRetCode(builder, retCode):
    builder.PrependInt32Slot(0, retCode, 0)


def AddRetCode(builder, retCode):
    return AgentEndRspAddRetCode(builder, retCode)


def AgentEndRspAddEpId(builder, epId):
    builder.PrependUint32Slot(1, epId, 0)


def AddEpId(builder, epId):
    return AgentEndRspAddEpId(builder, epId)


def AgentEndRspAddAgentId(builder, agentId):
    builder.PrependUint32Slot(2, agentId, 0)


def AddAgentId(builder, agentId):
    return AgentEndRspAddAgentId(builder, agentId)


def AgentEndRspEnd(builder):
    return builder.EndObject()


def End(builder):
    return AgentEndRspEnd(builder)
