# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class EpEndRsp(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = EpEndRsp()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsEpEndRsp(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # EpEndRsp
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # EpEndRsp
    def RetCode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # EpEndRsp
    def EpId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0


def EpEndRspStart(builder):
    builder.StartObject(2)


def Start(builder):
    return EpEndRspStart(builder)


def EpEndRspAddRetCode(builder, retCode):
    builder.PrependInt32Slot(0, retCode, 0)


def AddRetCode(builder, retCode):
    return EpEndRspAddRetCode(builder, retCode)


def EpEndRspAddEpId(builder, epId):
    builder.PrependUint32Slot(1, epId, 0)


def AddEpId(builder, epId):
    return EpEndRspAddEpId(builder, epId)


def EpEndRspEnd(builder):
    return builder.EndObject()


def End(builder):
    return EpEndRspEnd(builder)
