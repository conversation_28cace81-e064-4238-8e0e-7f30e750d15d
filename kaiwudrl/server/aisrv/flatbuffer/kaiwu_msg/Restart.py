# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class Restart(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Restart()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsRestart(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # Restart
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Restart
    def ClientId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Restart
    def EpId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # Restart
    def Data(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(
                flatbuffers.number_types.Int8Flags,
                a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 1),
            )
        return 0

    # Restart
    def DataAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Int8Flags, o)
        return 0

    # Restart
    def DataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # Restart
    def DataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        return o == 0


def RestartStart(builder):
    builder.StartObject(3)


def Start(builder):
    return RestartStart(builder)


def RestartAddClientId(builder, clientId):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(clientId), 0)


def AddClientId(builder, clientId):
    return RestartAddClientId(builder, clientId)


def RestartAddEpId(builder, epId):
    builder.PrependUint32Slot(1, epId, 0)


def AddEpId(builder, epId):
    return RestartAddEpId(builder, epId)


def RestartAddData(builder, data):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(data), 0)


def AddData(builder, data):
    return RestartAddData(builder, data)


def RestartStartDataVector(builder, numElems):
    return builder.StartVector(1, numElems, 1)


def StartDataVector(builder, numElems):
    return RestartStartDataVector(builder, numElems)


def RestartEnd(builder):
    return builder.EndObject()


def End(builder):
    return RestartEnd(builder)
