# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class AgentEndReq(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AgentEndReq()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAgentEndReq(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # AgentEndReq
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AgentEndReq
    def ClientId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # AgentEndReq
    def EpId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # AgentEndReq
    def AgentId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # AgentEndReq
    def Data(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(
                flatbuffers.number_types.Int8Flags,
                a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 1),
            )
        return 0

    # AgentEndReq
    def DataAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Int8Flags, o)
        return 0

    # AgentEndReq
    def DataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # AgentEndReq
    def DataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        return o == 0


def AgentEndReqStart(builder):
    builder.StartObject(4)


def Start(builder):
    return AgentEndReqStart(builder)


def AgentEndReqAddClientId(builder, clientId):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(clientId), 0)


def AddClientId(builder, clientId):
    return AgentEndReqAddClientId(builder, clientId)


def AgentEndReqAddEpId(builder, epId):
    builder.PrependUint32Slot(1, epId, 0)


def AddEpId(builder, epId):
    return AgentEndReqAddEpId(builder, epId)


def AgentEndReqAddAgentId(builder, agentId):
    builder.PrependUint32Slot(2, agentId, 0)


def AddAgentId(builder, agentId):
    return AgentEndReqAddAgentId(builder, agentId)


def AgentEndReqAddData(builder, data):
    builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(data), 0)


def AddData(builder, data):
    return AgentEndReqAddData(builder, data)


def AgentEndReqStartDataVector(builder, numElems):
    return builder.StartVector(1, numElems, 1)


def StartDataVector(builder, numElems):
    return AgentEndReqStartDataVector(builder, numElems)


def AgentEndReqEnd(builder):
    return builder.EndObject()


def End(builder):
    return AgentEndReqEnd(builder)
