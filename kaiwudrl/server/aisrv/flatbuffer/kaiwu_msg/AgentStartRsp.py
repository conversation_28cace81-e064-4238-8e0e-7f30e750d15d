# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class AgentStartRsp(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AgentStartRsp()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAgentStartRsp(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # AgentStartRsp
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AgentStartRsp
    def RetCode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # AgentStartRsp
    def EpId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # AgentStartRsp
    def AgentId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0


def AgentStartRspStart(builder):
    builder.StartObject(3)


def Start(builder):
    return AgentStartRspStart(builder)


def AgentStartRspAddRetCode(builder, retCode):
    builder.PrependInt32Slot(0, retCode, 0)


def AddRetCode(builder, retCode):
    return AgentStartRspAddRetCode(builder, retCode)


def AgentStartRspAddEpId(builder, epId):
    builder.PrependUint32Slot(1, epId, 0)


def AddEpId(builder, epId):
    return AgentStartRspAddEpId(builder, epId)


def AgentStartRspAddAgentId(builder, agentId):
    builder.PrependUint32Slot(2, agentId, 0)


def AddAgentId(builder, agentId):
    return AgentStartRspAddAgentId(builder, agentId)


def AgentStartRspEnd(builder):
    return builder.EndObject()


def End(builder):
    return AgentStartRspEnd(builder)
