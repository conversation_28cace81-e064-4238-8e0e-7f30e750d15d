# automatically generated by the FlatBuffers compiler, do not modify

# namespace: kaiwu_msg

import flatbuffers
from flatbuffers.compat import import_numpy

np = import_numpy()


class UpdateRspData(object):
    __slots__ = ["_tab"]

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = UpdateRspData()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsUpdateRspData(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)

    # UpdateRspData
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # UpdateRspData
    def AgentId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # UpdateRspData
    def Action(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(
                flatbuffers.number_types.Int8Flags,
                a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 1),
            )
        return 0

    # UpdateRspData
    def ActionAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Int8Flags, o)
        return 0

    # UpdateRspData
    def ActionLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # UpdateRspData
    def ActionIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0


def UpdateRspDataStart(builder):
    builder.StartObject(2)


def Start(builder):
    return UpdateRspDataStart(builder)


def UpdateRspDataAddAgentId(builder, agentId):
    builder.PrependUint32Slot(0, agentId, 0)


def AddAgentId(builder, agentId):
    return UpdateRspDataAddAgentId(builder, agentId)


def UpdateRspDataAddAction(builder, action):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(action), 0)


def AddAction(builder, action):
    return UpdateRspDataAddAction(builder, action)


def UpdateRspDataStartActionVector(builder, numElems):
    return builder.StartVector(1, numElems, 1)


def StartActionVector(builder, numElems):
    return UpdateRspDataStartActionVector(builder, numElems)


def UpdateRspDataEnd(builder):
    return builder.EndObject()


def End(builder):
    return UpdateRspDataEnd(builder)
