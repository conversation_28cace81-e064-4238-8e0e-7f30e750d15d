#!/usr/bin/env python3
# -*- coding:utf-8 -*-


# @file replay_buffer_benchmark_common.py
# @brief
# <AUTHOR>
# @date 2023-11-28


import numpy as np
from kaiwudrl.common.config.config_control import CONFIG
from kaiwudrl.common.utils.kaiwudrl_define import KaiwuDRLDefine
from kaiwudrl.common.utils.choose_deep_learning_frameworks import *


class BenchmarkData(object):
    BIG_DATA_SCALE = "big"
    MID_DATA_SCALE = "mid"
    SMALL_DATA_SCALE = "small"

    def __init__(self, data_scale):
        self.data_scale = data_scale
        self.tensor_spec = self._generate_tensor_spec()
        self.tensor_spec.sort(key=lambda x: x[0])

    def _generate_tensor_spec(self):
        if self.data_scale == self.SMALL_DATA_SCALE:
            return self.small_tensor_spec()
        elif self.data_scale == self.BIG_DATA_SCALE:
            return self.big_tensor_spec()
        return self.medium_tensor_spec()

    def tensor_names(self):
        return [s[0] for s in self.tensor_spec]

    def tensor_dtypes(self):
        return [s[1] for s in self.tensor_spec]

    def tensor_shapes(self):
        return [s[2] for s in self.tensor_spec]

    def sample(self, batch_size):
        names = self.tensor_names()
        dtypes = self.tensor_dtypes()
        shapes = self.tensor_shapes()
        shapes = [(batch_size,) + shape for shape in shapes]
        data = {}
        for i, name in enumerate(names):
            if dtypes[i] == tf.int64 or dtypes[i] == tf.int32:
                dtype = np.int64 if dtypes[i] == tf.int64 else np.int32
                data[name] = np.random.randint(0, 100, dtype=dtype, size=shapes[i])
            elif dtypes[i] == tf.float32 or dtypes[i] == tf.float64:
                dtype = np.float32 if dtypes[i] == tf.float32 else np.float64
                data[name] = np.random.rand(*shapes[i]).astype(dtype)
            elif dtypes[i] == tf.bool:
                data[name] = np.random.randint(2).astype(np.bool)
        return data

    @staticmethod
    def big_tensor_spec():
        """Fake moba data"""
        return [
            ["key0", tf.float32, (4, 5, 5, 3)],
            ["key1", tf.float32, (4, 5, 30, 3)],
            ["key2", tf.float32, (4, 5, 28, 3)],
            ["key3", tf.float32, (4, 1, 5, 1)],
            ["key4", tf.float32, (4, 1, 5, 21)],
            ["key5", tf.float32, (4, 1, 5, 1)],
            ["key6", tf.float32, (4, 1, 5, 1)],
            ["key7", tf.float32, (4, 1, 5, 1)],
            ["key8", tf.float32, (4, 1, 5, 1)],
            ["key9", tf.float32, (4, 1, 5, 20, 1)],
            ["key10", tf.float32, (4, 1, 5, 20, 8)],
            ["key11", tf.float32, (4, 1, 5, 20, 3, 1)],
            ["key12", tf.float32, (4, 1, 5, 20, 1)],
            ["key13", tf.float32, (4, 1, 5, 20, 1)],
            ["key14", tf.float32, (4, 1, 5, 20, 1)],
            ["key15", tf.float32, (4, 1, 5, 20, 1)],
            ["key16", tf.float32, (4, 1, 5, 69)],
            ["key17", tf.float32, (4, 1, 5, 1)],
            ["key18", tf.float32, (4, 1, 5, 1)],
            ["key19", tf.float32, (4, 1, 5, 1)],
            ["key20", tf.float32, (4, 1, 5, 1)],
            ["key21", tf.float32, (4, 1, 5, 1)],
            ["key22", tf.float32, (4, 1, 5, 1)],
            ["key23", tf.float32, (4, 1, 5, 1)],
            ["key24", tf.float32, (4, 1, 5, 1)],
            ["key25", tf.float32, (4, 1, 5, 1)],
            ["key26", tf.float32, (4, 1, 5, 11, 1)],
            ["key27", tf.float32, (4, 1, 5, 11, 32)],
            ["key28", tf.float32, (4, 1, 5, 11, 1)],
            ["key29", tf.float32, (4, 1, 5, 11, 1)],
            ["key30", tf.float32, (4, 1, 5, 11, 5, 1)],
            ["key31", tf.float32, (4, 1, 5, 11, 1)],
            ["key32", tf.float32, (4, 1, 5, 11, 1)],
            ["key33", tf.float32, (4, 1, 5, 1)],
            ["key34", tf.float32, (4, 1, 5, 1)],
            ["key35", tf.float32, (4, 1, 5, 9)],
            ["key36", tf.float32, (4, 1, 5, 1)],
            ["key37", tf.float32, (4, 1, 30, 1)],
            ["key38", tf.float32, (4, 1, 30, 21)],
            ["key39", tf.float32, (4, 1, 30, 1)],
            ["key40", tf.float32, (4, 1, 30, 1)],
            ["key41", tf.float32, (4, 1, 30, 2, 1)],
            ["key42", tf.float32, (4, 1, 30, 2, 8)],
            ["key43", tf.float32, (4, 1, 30, 2, 3, 1)],
            ["key44", tf.float32, (4, 1, 30, 2, 1)],
            ["key45", tf.float32, (4, 1, 30, 2, 1)],
            ["key46", tf.float32, (4, 1, 30, 2, 1)],
            ["key47", tf.float32, (4, 1, 30, 2, 1)],
            ["key48", tf.float32, (4, 1, 30, 3)],
            ["key49", tf.float32, (4, 1, 30, 1)],
            ["key50", tf.float32, (4, 1, 28, 1)],
            ["key51", tf.float32, (4, 1, 28, 21)],
            ["key52", tf.float32, (4, 1, 28, 1)],
            ["key53", tf.float32, (4, 1, 28, 1)],
            ["key54", tf.float32, (4, 1, 28, 2, 1)],
            ["key55", tf.float32, (4, 1, 28, 2, 8)],
            ["key56", tf.float32, (4, 1, 28, 2, 3, 1)],
            ["key57", tf.float32, (4, 1, 28, 2, 1)],
            ["key58", tf.float32, (4, 1, 28, 2, 1)],
            ["key59", tf.float32, (4, 1, 28, 2, 1)],
            ["key60", tf.float32, (4, 1, 28, 2, 1)],
            ["key61", tf.float32, (4, 1, 28, 7)],
            ["key62", tf.float32, (4, 1, 28, 2, 1)],
            ["key63", tf.float32, (4, 1, 28, 2, 32)],
            ["key64", tf.float32, (4, 1, 28, 2, 1)],
            ["key65", tf.float32, (4, 1, 28, 2, 1)],
            ["key66", tf.float32, (4, 1, 28, 2, 5, 1)],
            ["key67", tf.float32, (4, 1, 28, 2, 1)],
            ["key68", tf.float32, (4, 1, 28, 2, 1)],
            ["key69", tf.float32, (4, 1, 28, 1)],
            ["key70", tf.float32, (4, 5, 10, 3)],
            ["key71", tf.float32, (4, 5, 5, 3)],
            ["key72", tf.float32, (4, 5, 30, 3)],
            ["key73", tf.float32, (4, 5, 10, 3)],
            ["key74", tf.float32, (4, 5, 3)],
            ["key75", tf.float32, (4, 5, 431)],
            ["key76", tf.float32, (4, 5, 84)],
            ["key77", tf.float32, (4, 5, 15)],
            ["key78", tf.float32, (4, 5, 2)],
            ["key79", tf.float32, (4, 5, 2, 166)],
            ["key80", tf.float32, (4, 5, 114)],
            ["key81", tf.float32, (4, 5, 20, 3)],
            ["key82", tf.float32, (4, 1, 10, 2, 1)],
            ["key83", tf.float32, (4, 1, 10, 2, 8)],
            ["key84", tf.float32, (4, 1, 10, 2, 3, 1)],
            ["key85", tf.float32, (4, 1, 10, 2, 1)],
            ["key86", tf.float32, (4, 1, 10, 2, 1)],
            ["key87", tf.float32, (4, 1, 10, 2, 1)],
            ["key88", tf.float32, (4, 1, 10, 2, 1)],
            ["key89", tf.float32, (4, 1, 10, 1)],
            ["key90", tf.float32, (4, 1, 10, 21)],
            ["key91", tf.float32, (4, 1, 10, 1)],
            ["key92", tf.float32, (4, 1, 10, 1)],
            ["key93", tf.float32, (4, 1, 10, 1)],
            ["key94", tf.float32, (4, 1, 10, 2, 1)],
            ["key95", tf.float32, (4, 1, 10, 2, 8)],
            ["key96", tf.float32, (4, 1, 10, 2, 3, 1)],
            ["key97", tf.float32, (4, 1, 10, 2, 1)],
            ["key98", tf.float32, (4, 1, 10, 2, 1)],
            ["key99", tf.float32, (4, 1, 10, 2, 1)],
            ["key100", tf.float32, (4, 1, 10, 2, 1)],
            ["key101", tf.float32, (4, 1, 10, 1)],
            ["key102", tf.float32, (4, 1, 10, 21)],
            ["key103", tf.float32, (4, 1, 10, 1)],
            ["key104", tf.float32, (4, 1, 10, 1)],
            ["key105", tf.float32, (4, 1, 10, 1)],
            ["key106", tf.float32, (4, 1, 59)],
            ["key107", tf.float32, (4, 1, 1)],
            ["key108", tf.float32, (4, 1, 56, 1)],
            ["key109", tf.float32, (4, 1, 56, 6)],
            ["key110", tf.float32, (4, 1, 56, 1)],
            ["key111", tf.float32, (4, 1, 46)],
            ["key112", tf.float32, (4, 1, 20, 1)],
            ["key113", tf.float32, (4, 1, 20, 1)],
            ["key114", tf.float32, (4, 1, 20, 6)],
            ["key115", tf.float32, (4, 5, 5, 3)],
            ["key116", tf.float32, (4, 5, 30, 3)],
            ["key117", tf.float32, (4, 5, 28, 3)],
            ["key118", tf.float32, (4, 1, 5, 1)],
            ["key119", tf.float32, (4, 1, 5, 1)],
            ["key120", tf.float32, (4, 1, 5, 1)],
            ["key121", tf.float32, (4, 1, 5, 21)],
            ["key122", tf.float32, (4, 1, 5, 1)],
            ["key123", tf.float32, (4, 1, 5, 1)],
            ["key124", tf.float32, (4, 1, 5, 1)],
            ["key125", tf.float32, (4, 1, 5, 1)],
            ["key126", tf.float32, (4, 1, 5, 20, 1)],
            ["key127", tf.float32, (4, 1, 5, 20, 8)],
            ["key128", tf.float32, (4, 1, 5, 20, 3, 1)],
            ["key129", tf.float32, (4, 1, 5, 20, 1)],
            ["key130", tf.float32, (4, 1, 5, 20, 1)],
            ["key131", tf.float32, (4, 1, 5, 20, 1)],
            ["key132", tf.float32, (4, 1, 5, 20, 1)],
            ["key133", tf.float32, (4, 1, 5, 69)],
            ["key134", tf.float32, (4, 1, 5, 1)],
            ["key135", tf.float32, (4, 1, 5, 1)],
            ["key136", tf.float32, (4, 1, 5, 1)],
            ["key137", tf.float32, (4, 1, 5, 1)],
            ["key138", tf.float32, (4, 1, 5, 1)],
            ["key139", tf.float32, (4, 1, 5, 1)],
            ["key140", tf.float32, (4, 1, 5, 1)],
            ["key141", tf.float32, (4, 1, 5, 1)],
            ["key142", tf.float32, (4, 1, 5, 1)],
            ["key143", tf.float32, (4, 1, 5, 11, 1)],
            ["key144", tf.float32, (4, 1, 5, 11, 32)],
            ["key145", tf.float32, (4, 1, 5, 11, 1)],
            ["key146", tf.float32, (4, 1, 5, 11, 1)],
            ["key147", tf.float32, (4, 1, 5, 11, 5, 1)],
            ["key148", tf.float32, (4, 1, 5, 11, 1)],
            ["key149", tf.float32, (4, 1, 5, 11, 1)],
            ["key150", tf.float32, (4, 1, 5, 1)],
            ["key151", tf.float32, (4, 1, 5, 1)],
            ["key152", tf.float32, (4, 1, 5, 4)],
            ["key153", tf.float32, (4, 1, 5, 1)],
            ["key154", tf.float32, (4, 1, 5, 15, 1)],
            ["key155", tf.float32, (4, 1, 5, 15, 1)],
            ["key156", tf.float32, (4, 1, 5, 1)],
            ["key157", tf.float32, (4, 1, 5, 9)],
            ["key158", tf.float32, (4, 1, 5, 1)],
            ["key159", tf.float32, (4, 1, 30, 1)],
            ["key160", tf.float32, (4, 1, 30, 21)],
            ["key161", tf.float32, (4, 1, 30, 1)],
            ["key162", tf.float32, (4, 1, 30, 1)],
            ["key163", tf.float32, (4, 1, 30, 2, 1)],
            ["key164", tf.float32, (4, 1, 30, 2, 8)],
            ["key165", tf.float32, (4, 1, 30, 2, 3, 1)],
            ["key166", tf.float32, (4, 1, 30, 2, 1)],
            ["key167", tf.float32, (4, 1, 30, 2, 1)],
            ["key168", tf.float32, (4, 1, 30, 2, 1)],
            ["key169", tf.float32, (4, 1, 30, 2, 1)],
            ["key170", tf.float32, (4, 1, 30, 3)],
            ["key171", tf.float32, (4, 1, 30, 1)],
            ["key172", tf.float32, (4, 1, 5, 1)],
            ["key173", tf.float32, (4, 1, 5, 21)],
            ["key174", tf.float32, (4, 1, 5, 1)],
            ["key175", tf.float32, (4, 1, 5, 1)],
            ["key176", tf.float32, (4, 1, 5, 1)],
            ["key177", tf.float32, (4, 1, 5, 1)],
            ["key178", tf.float32, (4, 1, 5, 20, 1)],
            ["key179", tf.float32, (4, 1, 5, 20, 8)],
            ["key180", tf.float32, (4, 1, 5, 20, 3, 1)],
            ["key181", tf.float32, (4, 1, 5, 20, 1)],
            ["key182", tf.float32, (4, 1, 5, 20, 1)],
            ["key183", tf.float32, (4, 1, 5, 20, 1)],
            ["key184", tf.float32, (4, 1, 5, 20, 1)],
            ["key185", tf.float32, (4, 1, 5, 69)],
            ["key186", tf.float32, (4, 1, 5, 1)],
            ["key187", tf.float32, (4, 1, 5, 1)],
            ["key188", tf.float32, (4, 1, 5, 1)],
            ["key189", tf.float32, (4, 1, 5, 1)],
            ["key190", tf.float32, (4, 1, 5, 1)],
            ["key191", tf.float32, (4, 1, 5, 1)],
            ["key192", tf.float32, (4, 1, 5, 1)],
            ["key193", tf.float32, (4, 1, 5, 1)],
            ["key194", tf.float32, (4, 1, 5, 1)],
            ["key195", tf.float32, (4, 1, 5, 11, 1)],
            ["key196", tf.float32, (4, 1, 5, 11, 32)],
            ["key197", tf.float32, (4, 1, 5, 11, 1)],
            ["key198", tf.float32, (4, 1, 5, 11, 1)],
            ["key199", tf.float32, (4, 1, 5, 11, 5, 1)],
            ["key200", tf.float32, (4, 1, 5, 11, 1)],
            ["key201", tf.float32, (4, 1, 5, 11, 1)],
            ["key202", tf.float32, (4, 1, 5, 1)],
            ["key203", tf.float32, (4, 1, 5, 1)],
            ["key204", tf.float32, (4, 1, 5, 9)],
            ["key205", tf.float32, (4, 1, 5, 1)],
            ["key206", tf.float32, (4, 1, 30, 1)],
            ["key207", tf.float32, (4, 1, 30, 21)],
            ["key208", tf.float32, (4, 1, 30, 1)],
            ["key209", tf.float32, (4, 1, 30, 1)],
            ["key210", tf.float32, (4, 1, 30, 2, 1)],
            ["key211", tf.float32, (4, 1, 30, 2, 8)],
            ["key212", tf.float32, (4, 1, 30, 2, 3, 1)],
            ["key213", tf.float32, (4, 1, 30, 2, 1)],
            ["key214", tf.float32, (4, 1, 30, 2, 1)],
            ["key215", tf.float32, (4, 1, 30, 2, 1)],
            ["key216", tf.float32, (4, 1, 30, 2, 1)],
            ["key217", tf.float32, (4, 1, 30, 3)],
            ["key218", tf.float32, (4, 1, 30, 1)],
            ["key219", tf.float32, (4, 1, 28, 1)],
            ["key220", tf.float32, (4, 1, 28, 21)],
            ["key221", tf.float32, (4, 1, 28, 1)],
            ["key222", tf.float32, (4, 1, 28, 1)],
            ["key223", tf.float32, (4, 1, 28, 2, 1)],
            ["key224", tf.float32, (4, 1, 28, 2, 8)],
            ["key225", tf.float32, (4, 1, 28, 2, 3, 1)],
            ["key226", tf.float32, (4, 1, 28, 2, 1)],
            ["key227", tf.float32, (4, 1, 28, 2, 1)],
            ["key228", tf.float32, (4, 1, 28, 2, 1)],
            ["key229", tf.float32, (4, 1, 28, 2, 1)],
            ["key230", tf.float32, (4, 1, 28, 7)],
            ["key231", tf.float32, (4, 1, 28, 2, 1)],
            ["key232", tf.float32, (4, 1, 28, 2, 32)],
            ["key233", tf.float32, (4, 1, 28, 2, 1)],
            ["key234", tf.float32, (4, 1, 28, 2, 1)],
            ["key235", tf.float32, (4, 1, 28, 2, 5, 1)],
            ["key236", tf.float32, (4, 1, 28, 2, 1)],
            ["key237", tf.float32, (4, 1, 28, 2, 1)],
            ["key238", tf.float32, (4, 1, 28, 1)],
            ["key239", tf.float32, (4, 5)],
            ["key240", tf.float32, (4, 5)],
            ["key241", tf.float32, (4, 5)],
            ["key242", tf.float32, (4, 5)],
            ["key243", tf.float32, (4, 5, 97)],
            ["key244", tf.float32, (4, 11)],
            ["key245", tf.float32, (1, 5, 1, 512)],
            ["key246", tf.float32, (1, 5, 1, 512)],
            ["key247", tf.float32, (4, 5)],
            ["key248", tf.float32, (4, 5)],
            ["key249", tf.float32, (4, 5)],
            ["key250", tf.float32, (4, 5)],
            ["key251", tf.float32, (4, 5)],
            ["key252", tf.float32, (4, 5)],
            ["key253", tf.float32, (4, 5)],
            ["key254", tf.float32, (4, 5)],
            ["key255", tf.float32, (4, 5)],
            ["key256", tf.float32, (4, 5)],
            ["key257", tf.float32, (4, 5)],
            ["key258", tf.float32, (4, 5)],
            ["key259", tf.float32, (4, 5)],
            ["key260", tf.float32, (4, 5)],
            ["key261", tf.float32, (4, 5)],
            ["key262", tf.float32, (4, 5)],
            ["key263", tf.float32, (4, 5)],
            ["key264", tf.float32, (4, 5)],
            ["key265", tf.float32, (4, 5)],
            ["key266", tf.float32, (4, 5)],
            ["key267", tf.float32, (4, 5)],
            ["key268", tf.float32, (4, 5)],
            ["key269", tf.float32, (4, 5)],
            ["key270", tf.float32, (4, 5, 97)],
            ["key271", tf.float32, (4, 5)],
            ["key272", tf.float32, (4, 11)],
        ]

    @staticmethod
    def medium_tensor_spec():
        return [
            ["key0", tf.float32, (4, 5, 5, 3)],
            ["key1", tf.float32, (4, 5, 30, 3)],
            ["key2", tf.float32, (4, 5, 28, 3)],
            ["key3", tf.float32, (4, 1, 5, 1)],
            ["key4", tf.float32, (4, 1, 5, 21)],
            ["key5", tf.float32, (4, 1, 5, 1)],
            ["key6", tf.float32, (4, 1, 5, 1)],
            ["key7", tf.float32, (4, 1, 5, 1)],
            ["key8", tf.float32, (4, 1, 5, 1)],
            ["key9", tf.float32, (4, 1, 5, 20, 1)],
            ["key10", tf.float32, (4, 1, 5, 20, 8)],
            ["key11", tf.float32, (4, 1, 5, 20, 3, 1)],
            ["key12", tf.float32, (4, 1, 5, 20, 1)],
            ["key13", tf.float32, (4, 1, 5, 20, 1)],
            ["key14", tf.float32, (4, 1, 5, 20, 1)],
            ["key15", tf.float32, (4, 1, 5, 20, 1)],
            ["key16", tf.float32, (4, 1, 5, 69)],
            ["key17", tf.float32, (4, 1, 5, 1)],
            ["key18", tf.float32, (4, 1, 5, 1)],
            ["key19", tf.float32, (4, 1, 5, 1)],
            ["key20", tf.float32, (4, 1, 5, 1)],
            ["key21", tf.float32, (4, 1, 5, 1)],
            ["key22", tf.float32, (4, 1, 5, 1)],
            ["key23", tf.float32, (4, 1, 5, 1)],
            ["key24", tf.float32, (4, 1, 5, 1)],
            ["key25", tf.float32, (4, 1, 5, 1)],
            ["key26", tf.float32, (4, 1, 5, 11, 1)],
            ["key27", tf.float32, (4, 1, 5, 11, 32)],
            ["key28", tf.float32, (4, 1, 5, 11, 1)],
            ["key29", tf.float32, (4, 1, 5, 11, 1)],
            ["key30", tf.float32, (4, 1, 5, 11, 5, 1)],
            ["key31", tf.float32, (4, 1, 5, 11, 1)],
            ["key32", tf.float32, (4, 1, 5, 11, 1)],
            ["key33", tf.float32, (4, 1, 5, 1)],
            ["key34", tf.float32, (4, 1, 5, 1)],
            ["key35", tf.float32, (4, 1, 5, 9)],
            ["key36", tf.float32, (4, 1, 5, 1)],
            ["key37", tf.float32, (4, 1, 30, 1)],
            ["key38", tf.float32, (4, 1, 30, 21)],
            ["key39", tf.float32, (4, 1, 30, 1)],
            ["key40", tf.float32, (4, 1, 30, 1)],
            ["key41", tf.float32, (4, 1, 30, 2, 1)],
            ["key42", tf.float32, (4, 1, 30, 2, 8)],
            ["key43", tf.float32, (4, 1, 30, 2, 3, 1)],
            ["key44", tf.float32, (4, 1, 30, 2, 1)],
            ["key45", tf.float32, (4, 1, 30, 2, 1)],
            ["key46", tf.float32, (4, 1, 30, 2, 1)],
            ["key47", tf.float32, (4, 1, 30, 2, 1)],
            ["key48", tf.float32, (4, 1, 30, 3)],
            ["key49", tf.float32, (4, 1, 30, 1)],
            ["key50", tf.float32, (4, 1, 28, 1)],
            ["key51", tf.float32, (4, 1, 28, 21)],
            ["key52", tf.float32, (4, 1, 28, 1)],
            ["key53", tf.float32, (4, 1, 28, 1)],
            ["key54", tf.float32, (4, 1, 28, 2, 1)],
            ["key55", tf.float32, (4, 1, 28, 2, 8)],
            ["key56", tf.float32, (4, 1, 28, 2, 3, 1)],
            ["key57", tf.float32, (4, 1, 28, 2, 1)],
            ["key58", tf.float32, (4, 1, 28, 2, 1)],
            ["key59", tf.float32, (4, 1, 28, 2, 1)],
            ["key60", tf.float32, (4, 1, 28, 2, 1)],
            ["key61", tf.float32, (4, 1, 28, 7)],
            ["key62", tf.float32, (4, 1, 28, 2, 1)],
            ["key63", tf.float32, (4, 1, 28, 2, 32)],
            ["key64", tf.float32, (4, 1, 28, 2, 1)],
            ["key65", tf.float32, (4, 1, 28, 2, 1)],
            ["key66", tf.float32, (4, 1, 28, 2, 5, 1)],
            ["key67", tf.float32, (4, 1, 28, 2, 1)],
            ["key68", tf.float32, (4, 1, 28, 2, 1)],
            ["key69", tf.float32, (4, 1, 28, 1)],
            ["key70", tf.float32, (4, 5, 10, 3)],
            ["key71", tf.float32, (4, 5, 5, 3)],
            ["key72", tf.float32, (4, 5, 30, 3)],
            ["key73", tf.float32, (4, 5, 10, 3)],
            ["key74", tf.float32, (4, 5, 3)],
            ["key75", tf.float32, (4, 5, 431)],
            ["key76", tf.float32, (4, 5, 84)],
            ["key77", tf.float32, (4, 5, 15)],
            ["key78", tf.float32, (4, 5, 2)],
            ["key79", tf.float32, (4, 5, 2, 166)],
            ["key80", tf.float32, (4, 5, 114)],
            ["key81", tf.float32, (4, 5, 20, 3)],
            ["key82", tf.float32, (4, 1, 10, 2, 1)],
            ["key83", tf.float32, (4, 1, 10, 2, 8)],
            ["key84", tf.float32, (4, 1, 10, 2, 3, 1)],
            ["key85", tf.float32, (4, 1, 10, 2, 1)],
            ["key86", tf.float32, (4, 1, 10, 2, 1)],
            ["key87", tf.float32, (4, 1, 10, 2, 1)],
            ["key88", tf.float32, (4, 1, 10, 2, 1)],
            ["key89", tf.float32, (4, 1, 10, 1)],
            ["key90", tf.float32, (4, 1, 10, 21)],
            ["key91", tf.float32, (4, 1, 10, 1)],
            ["key92", tf.float32, (4, 1, 10, 1)],
            ["key93", tf.float32, (4, 1, 10, 1)],
            ["key94", tf.float32, (4, 1, 10, 2, 1)],
            ["key95", tf.float32, (4, 1, 10, 2, 8)],
            ["key96", tf.float32, (4, 1, 10, 2, 3, 1)],
            ["key97", tf.float32, (4, 1, 10, 2, 1)],
            ["key98", tf.float32, (4, 1, 10, 2, 1)],
            ["key99", tf.float32, (4, 1, 10, 2, 1)],
            ["key100", tf.float32, (4, 1, 10, 2, 1)],
            ["key101", tf.float32, (4, 1, 10, 1)],
            ["key102", tf.float32, (4, 1, 10, 21)],
            ["key103", tf.float32, (4, 1, 10, 1)],
            ["key104", tf.float32, (4, 1, 10, 1)],
            ["key105", tf.float32, (4, 1, 10, 1)],
            ["key106", tf.float32, (4, 1, 59)],
            ["key107", tf.float32, (4, 1, 1)],
            ["key108", tf.float32, (4, 1, 56, 1)],
            ["key109", tf.float32, (4, 1, 56, 6)],
            ["key110", tf.float32, (4, 1, 56, 1)],
            ["key111", tf.float32, (4, 1, 46)],
            ["key112", tf.float32, (4, 1, 20, 1)],
            ["key113", tf.float32, (4, 1, 20, 1)],
            ["key114", tf.float32, (4, 1, 20, 6)],
            ["key115", tf.float32, (4, 5, 5, 3)],
            ["key116", tf.float32, (4, 5, 30, 3)],
            ["key117", tf.float32, (4, 5, 28, 3)],
        ]

    @staticmethod
    def small_tensor_spec():
        return [
            ["key0", tf.int32, (1,)],
            ["key1", tf.int32, (1,)],
            ["key2", tf.int32, (300,)],
            ["key3", tf.int32, (300,)],
            ["key4", tf.float32, (3600,)],
            ["key5", tf.float32, (66,)],
            ["key6", tf.float32, (615,)],
        ]
