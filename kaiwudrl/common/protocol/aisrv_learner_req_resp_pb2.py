# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: aisrv_learner_req_resp.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1c\x61isrv_learner_req_resp.proto\x12\x0bkaiwu_aisvr\x1a google/protobuf/descriptor.proto"7\n\x13\x41isrvLearnerRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x02\x12\x12\n\nbatch_size\x18\x02 \x02(\x05\x42\x02H\x03'
)

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "aisrv_learner_req_resp_pb2", globals())
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"H\003"
    _AISRVLEARNERREQUEST._serialized_start = 79
    _AISRVLEARNERREQUEST._serialized_end = 134
# @@protoc_insertion_point(module_scope)
