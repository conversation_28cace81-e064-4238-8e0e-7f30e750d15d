// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_AISRVLEARNERREQRESP_KAIWU_MSG_H_
#define FLATBUFFERS_GENERATED_AISRVLEARNERREQRESP_KAIWU_MSG_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 23 &&
              FLATBUFFERS_VERSION_MINOR == 1 &&
              FLATBUFFERS_VERSION_REVISION == 21,
             "Non-compatible flatbuffers version included");

namespace kaiwu_msg {

struct AisrvLearnerRequest;
struct AisrvLearnerRequestBuilder;

struct AisrvLearnerRequest FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef AisrvLearnerRequestBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DATA = 4,
    VT_BATCH_SIZE = 6
  };
  const ::flatbuffers::Vector<float> *data() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_DATA);
  }
  int32_t batch_size() const {
    return GetField<int32_t>(VT_BATCH_SIZE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           VerifyField<int32_t>(verifier, VT_BATCH_SIZE, 4) &&
           verifier.EndTable();
  }
};

struct AisrvLearnerRequestBuilder {
  typedef AisrvLearnerRequest Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<float>> data) {
    fbb_.AddOffset(AisrvLearnerRequest::VT_DATA, data);
  }
  void add_batch_size(int32_t batch_size) {
    fbb_.AddElement<int32_t>(AisrvLearnerRequest::VT_BATCH_SIZE, batch_size, 0);
  }
  explicit AisrvLearnerRequestBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<AisrvLearnerRequest> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<AisrvLearnerRequest>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<AisrvLearnerRequest> CreateAisrvLearnerRequest(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> data = 0,
    int32_t batch_size = 0) {
  AisrvLearnerRequestBuilder builder_(_fbb);
  builder_.add_batch_size(batch_size);
  builder_.add_data(data);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<AisrvLearnerRequest> CreateAisrvLearnerRequestDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *data = nullptr,
    int32_t batch_size = 0) {
  auto data__ = data ? _fbb.CreateVector<float>(*data) : 0;
  return kaiwu_msg::CreateAisrvLearnerRequest(
      _fbb,
      data__,
      batch_size);
}

inline const kaiwu_msg::AisrvLearnerRequest *GetAisrvLearnerRequest(const void *buf) {
  return ::flatbuffers::GetRoot<kaiwu_msg::AisrvLearnerRequest>(buf);
}

inline const kaiwu_msg::AisrvLearnerRequest *GetSizePrefixedAisrvLearnerRequest(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<kaiwu_msg::AisrvLearnerRequest>(buf);
}

inline bool VerifyAisrvLearnerRequestBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<kaiwu_msg::AisrvLearnerRequest>(nullptr);
}

inline bool VerifySizePrefixedAisrvLearnerRequestBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<kaiwu_msg::AisrvLearnerRequest>(nullptr);
}

inline void FinishAisrvLearnerRequestBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<kaiwu_msg::AisrvLearnerRequest> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedAisrvLearnerRequestBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<kaiwu_msg::AisrvLearnerRequest> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace kaiwu_msg

#endif  // FLATBUFFERS_GENERATED_AISRVLEARNERREQRESP_KAIWU_MSG_H_
