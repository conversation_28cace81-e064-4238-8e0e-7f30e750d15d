// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: aisrv_learner_req_resp.proto

#include "aisrv_learner_req_resp.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
namespace kaiwu_aisvr {
class AisrvLearnerRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<AisrvLearnerRequest> _instance;
} _AisrvLearnerRequest_default_instance_;
}  // namespace kaiwu_aisvr
static void InitDefaultsscc_info_AisrvLearnerRequest_aisrv_5flearner_5freq_5fresp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::kaiwu_aisvr::_AisrvLearnerRequest_default_instance_;
    new (ptr) ::kaiwu_aisvr::AisrvLearnerRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::kaiwu_aisvr::AisrvLearnerRequest::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_AisrvLearnerRequest_aisrv_5flearner_5freq_5fresp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_AisrvLearnerRequest_aisrv_5flearner_5freq_5fresp_2eproto}, {}};

namespace kaiwu_aisvr {

// ===================================================================

void AisrvLearnerRequest::InitAsDefaultInstance() {
}
class AisrvLearnerRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<AisrvLearnerRequest>()._has_bits_);
  static void set_has_batch_size(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

AisrvLearnerRequest::AisrvLearnerRequest()
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:kaiwu_aisvr.AisrvLearnerRequest)
}
AisrvLearnerRequest::AisrvLearnerRequest(const AisrvLearnerRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      data_(from.data_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  batch_size_ = from.batch_size_;
  // @@protoc_insertion_point(copy_constructor:kaiwu_aisvr.AisrvLearnerRequest)
}

void AisrvLearnerRequest::SharedCtor() {
  batch_size_ = 0;
}

AisrvLearnerRequest::~AisrvLearnerRequest() {
  // @@protoc_insertion_point(destructor:kaiwu_aisvr.AisrvLearnerRequest)
  SharedDtor();
}

void AisrvLearnerRequest::SharedDtor() {
}

void AisrvLearnerRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const AisrvLearnerRequest& AisrvLearnerRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AisrvLearnerRequest_aisrv_5flearner_5freq_5fresp_2eproto.base);
  return *internal_default_instance();
}


void AisrvLearnerRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:kaiwu_aisvr.AisrvLearnerRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.Clear();
  batch_size_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* AisrvLearnerRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated float data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          ptr -= 1;
          do {
            ptr += 1;
            add_data(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
            ptr += sizeof(float);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 13);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(mutable_data(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required int32 batch_size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_batch_size(&has_bits);
          batch_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool AisrvLearnerRequest::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  ::PROTOBUF_NAMESPACE_ID::internal::LiteUnknownFieldSetter unknown_fields_setter(
      &_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::io::StringOutputStream unknown_fields_output(
      unknown_fields_setter.buffer());
  ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_output, false);
  // @@protoc_insertion_point(parse_start:kaiwu_aisvr.AisrvLearnerRequest)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated float data = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (13 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 13u, input, this->mutable_data())));
        } else if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_data())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // required int32 batch_size = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {
          _Internal::set_has_batch_size(&_has_bits_);
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &batch_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:kaiwu_aisvr.AisrvLearnerRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:kaiwu_aisvr.AisrvLearnerRequest)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void AisrvLearnerRequest::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:kaiwu_aisvr.AisrvLearnerRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float data = 1;
  for (int i = 0, n = this->data_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(
      1, this->data(i), output);
  }

  cached_has_bits = _has_bits_[0];
  // required int32 batch_size = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(2, this->batch_size(), output);
  }

  output->WriteRaw(_internal_metadata_.unknown_fields().data(),
                   static_cast<int>(_internal_metadata_.unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:kaiwu_aisvr.AisrvLearnerRequest)
}

size_t AisrvLearnerRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:kaiwu_aisvr.AisrvLearnerRequest)
  size_t total_size = 0;

  total_size += _internal_metadata_.unknown_fields().size();

  // required int32 batch_size = 2;
  if (has_batch_size()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->batch_size());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated float data = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->data_size());
    size_t data_size = 4UL * count;
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->data_size());
    total_size += data_size;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AisrvLearnerRequest::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const AisrvLearnerRequest*>(
      &from));
}

void AisrvLearnerRequest::MergeFrom(const AisrvLearnerRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:kaiwu_aisvr.AisrvLearnerRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  data_.MergeFrom(from.data_);
  if (from.has_batch_size()) {
    set_batch_size(from.batch_size());
  }
}

void AisrvLearnerRequest::CopyFrom(const AisrvLearnerRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:kaiwu_aisvr.AisrvLearnerRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AisrvLearnerRequest::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;
  return true;
}

void AisrvLearnerRequest::InternalSwap(AisrvLearnerRequest* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  data_.InternalSwap(&other->data_);
  swap(batch_size_, other->batch_size_);
}

std::string AisrvLearnerRequest::GetTypeName() const {
  return "kaiwu_aisvr.AisrvLearnerRequest";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace kaiwu_aisvr
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::kaiwu_aisvr::AisrvLearnerRequest* Arena::CreateMaybeMessage< ::kaiwu_aisvr::AisrvLearnerRequest >(Arena* arena) {
  return Arena::CreateInternal< ::kaiwu_aisvr::AisrvLearnerRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
