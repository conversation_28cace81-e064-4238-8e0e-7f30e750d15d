// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: aisrv_actor_req_resp.proto

#include "aisrv_actor_req_resp.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
namespace kaiwu_aisvr {
class AisrvActorRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<AisrvActorRequest> _instance;
} _AisrvActorRequest_default_instance_;
class AisrvActorResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<AisrvActorResponse> _instance;
} _AisrvActorResponse_default_instance_;
}  // namespace kaiwu_aisvr
static void InitDefaultsscc_info_AisrvActorRequest_aisrv_5factor_5freq_5fresp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::kaiwu_aisvr::_AisrvActorRequest_default_instance_;
    new (ptr) ::kaiwu_aisvr::AisrvActorRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::kaiwu_aisvr::AisrvActorRequest::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_AisrvActorRequest_aisrv_5factor_5freq_5fresp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_AisrvActorRequest_aisrv_5factor_5freq_5fresp_2eproto}, {}};

static void InitDefaultsscc_info_AisrvActorResponse_aisrv_5factor_5freq_5fresp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::kaiwu_aisvr::_AisrvActorResponse_default_instance_;
    new (ptr) ::kaiwu_aisvr::AisrvActorResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::kaiwu_aisvr::AisrvActorResponse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_AisrvActorResponse_aisrv_5factor_5freq_5fresp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_AisrvActorResponse_aisrv_5factor_5freq_5fresp_2eproto}, {}};

namespace kaiwu_aisvr {

// ===================================================================

void AisrvActorRequest::InitAsDefaultInstance() {
}
class AisrvActorRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<AisrvActorRequest>()._has_bits_);
  static void set_has_client_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_sample_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

AisrvActorRequest::AisrvActorRequest()
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:kaiwu_aisvr.AisrvActorRequest)
}
AisrvActorRequest::AisrvActorRequest(const AisrvActorRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      compose_id_(from.compose_id_),
      feature_(from.feature_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&client_id_, &from.client_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&sample_size_) -
    reinterpret_cast<char*>(&client_id_)) + sizeof(sample_size_));
  // @@protoc_insertion_point(copy_constructor:kaiwu_aisvr.AisrvActorRequest)
}

void AisrvActorRequest::SharedCtor() {
  ::memset(&client_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sample_size_) -
      reinterpret_cast<char*>(&client_id_)) + sizeof(sample_size_));
}

AisrvActorRequest::~AisrvActorRequest() {
  // @@protoc_insertion_point(destructor:kaiwu_aisvr.AisrvActorRequest)
  SharedDtor();
}

void AisrvActorRequest::SharedDtor() {
}

void AisrvActorRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const AisrvActorRequest& AisrvActorRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AisrvActorRequest_aisrv_5factor_5freq_5fresp_2eproto.base);
  return *internal_default_instance();
}


void AisrvActorRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:kaiwu_aisvr.AisrvActorRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  compose_id_.Clear();
  feature_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&client_id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&sample_size_) -
        reinterpret_cast<char*>(&client_id_)) + sizeof(sample_size_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* AisrvActorRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required int32 client_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_client_id(&has_bits);
          client_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 compose_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ptr -= 1;
          do {
            ptr += 1;
            add_compose_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 16);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(mutable_compose_id(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated float feature = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          ptr -= 1;
          do {
            ptr += 1;
            add_feature(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
            ptr += sizeof(float);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 29);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(mutable_feature(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required int32 sample_size = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_sample_size(&has_bits);
          sample_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool AisrvActorRequest::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  ::PROTOBUF_NAMESPACE_ID::internal::LiteUnknownFieldSetter unknown_fields_setter(
      &_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::io::StringOutputStream unknown_fields_output(
      unknown_fields_setter.buffer());
  ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_output, false);
  // @@protoc_insertion_point(parse_start:kaiwu_aisvr.AisrvActorRequest)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int32 client_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (8 & 0xFF)) {
          _Internal::set_has_client_id(&_has_bits_);
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &client_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 compose_id = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 1, 16u, input, this->mutable_compose_id())));
        } else if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_compose_id())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float feature = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (29 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 29u, input, this->mutable_feature())));
        } else if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_feature())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // required int32 sample_size = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (32 & 0xFF)) {
          _Internal::set_has_sample_size(&_has_bits_);
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &sample_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:kaiwu_aisvr.AisrvActorRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:kaiwu_aisvr.AisrvActorRequest)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void AisrvActorRequest::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:kaiwu_aisvr.AisrvActorRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required int32 client_id = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(1, this->client_id(), output);
  }

  // repeated int32 compose_id = 2;
  for (int i = 0, n = this->compose_id_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(
      2, this->compose_id(i), output);
  }

  // repeated float feature = 3;
  for (int i = 0, n = this->feature_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(
      3, this->feature(i), output);
  }

  // required int32 sample_size = 4;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(4, this->sample_size(), output);
  }

  output->WriteRaw(_internal_metadata_.unknown_fields().data(),
                   static_cast<int>(_internal_metadata_.unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:kaiwu_aisvr.AisrvActorRequest)
}

size_t AisrvActorRequest::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:kaiwu_aisvr.AisrvActorRequest)
  size_t total_size = 0;

  if (has_client_id()) {
    // required int32 client_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->client_id());
  }

  if (has_sample_size()) {
    // required int32 sample_size = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->sample_size());
  }

  return total_size;
}
size_t AisrvActorRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:kaiwu_aisvr.AisrvActorRequest)
  size_t total_size = 0;

  total_size += _internal_metadata_.unknown_fields().size();

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required int32 client_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->client_id());

    // required int32 sample_size = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->sample_size());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 compose_id = 2;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->compose_id_);
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->compose_id_size());
    total_size += data_size;
  }

  // repeated float feature = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->feature_size());
    size_t data_size = 4UL * count;
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->feature_size());
    total_size += data_size;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AisrvActorRequest::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const AisrvActorRequest*>(
      &from));
}

void AisrvActorRequest::MergeFrom(const AisrvActorRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:kaiwu_aisvr.AisrvActorRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  compose_id_.MergeFrom(from.compose_id_);
  feature_.MergeFrom(from.feature_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      client_id_ = from.client_id_;
    }
    if (cached_has_bits & 0x00000002u) {
      sample_size_ = from.sample_size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void AisrvActorRequest::CopyFrom(const AisrvActorRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:kaiwu_aisvr.AisrvActorRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AisrvActorRequest::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;
  return true;
}

void AisrvActorRequest::InternalSwap(AisrvActorRequest* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  compose_id_.InternalSwap(&other->compose_id_);
  feature_.InternalSwap(&other->feature_);
  swap(client_id_, other->client_id_);
  swap(sample_size_, other->sample_size_);
}

std::string AisrvActorRequest::GetTypeName() const {
  return "kaiwu_aisvr.AisrvActorRequest";
}


// ===================================================================

void AisrvActorResponse::InitAsDefaultInstance() {
}
class AisrvActorResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<AisrvActorResponse>()._has_bits_);
};

AisrvActorResponse::AisrvActorResponse()
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:kaiwu_aisvr.AisrvActorResponse)
}
AisrvActorResponse::AisrvActorResponse(const AisrvActorResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _internal_metadata_(nullptr),
      _has_bits_(from._has_bits_),
      compose_id_(from.compose_id_),
      output_(from.output_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:kaiwu_aisvr.AisrvActorResponse)
}

void AisrvActorResponse::SharedCtor() {
}

AisrvActorResponse::~AisrvActorResponse() {
  // @@protoc_insertion_point(destructor:kaiwu_aisvr.AisrvActorResponse)
  SharedDtor();
}

void AisrvActorResponse::SharedDtor() {
}

void AisrvActorResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const AisrvActorResponse& AisrvActorResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AisrvActorResponse_aisrv_5factor_5freq_5fresp_2eproto.base);
  return *internal_default_instance();
}


void AisrvActorResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:kaiwu_aisvr.AisrvActorResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  compose_id_.Clear();
  output_.Clear();
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* AisrvActorResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated int32 compose_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ptr -= 1;
          do {
            ptr += 1;
            add_compose_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 8);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(mutable_compose_id(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated float output = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          ptr -= 1;
          do {
            ptr += 1;
            add_output(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
            ptr += sizeof(float);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 21);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(mutable_output(), ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool AisrvActorResponse::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  ::PROTOBUF_NAMESPACE_ID::internal::LiteUnknownFieldSetter unknown_fields_setter(
      &_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::io::StringOutputStream unknown_fields_output(
      unknown_fields_setter.buffer());
  ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_output, false);
  // @@protoc_insertion_point(parse_start:kaiwu_aisvr.AisrvActorResponse)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 compose_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (8 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 1, 8u, input, this->mutable_compose_id())));
        } else if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_compose_id())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float output = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (21 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 21u, input, this->mutable_output())));
        } else if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_output())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:kaiwu_aisvr.AisrvActorResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:kaiwu_aisvr.AisrvActorResponse)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void AisrvActorResponse::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:kaiwu_aisvr.AisrvActorResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 compose_id = 1;
  for (int i = 0, n = this->compose_id_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(
      1, this->compose_id(i), output);
  }

  // repeated float output = 2;
  for (int i = 0, n = this->output_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(
      2, this->output(i), output);
  }

  output->WriteRaw(_internal_metadata_.unknown_fields().data(),
                   static_cast<int>(_internal_metadata_.unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:kaiwu_aisvr.AisrvActorResponse)
}

size_t AisrvActorResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:kaiwu_aisvr.AisrvActorResponse)
  size_t total_size = 0;

  total_size += _internal_metadata_.unknown_fields().size();

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 compose_id = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->compose_id_);
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->compose_id_size());
    total_size += data_size;
  }

  // repeated float output = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->output_size());
    size_t data_size = 4UL * count;
    total_size += 1 *
                  ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->output_size());
    total_size += data_size;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AisrvActorResponse::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const AisrvActorResponse*>(
      &from));
}

void AisrvActorResponse::MergeFrom(const AisrvActorResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:kaiwu_aisvr.AisrvActorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  compose_id_.MergeFrom(from.compose_id_);
  output_.MergeFrom(from.output_);
}

void AisrvActorResponse::CopyFrom(const AisrvActorResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:kaiwu_aisvr.AisrvActorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AisrvActorResponse::IsInitialized() const {
  return true;
}

void AisrvActorResponse::InternalSwap(AisrvActorResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  compose_id_.InternalSwap(&other->compose_id_);
  output_.InternalSwap(&other->output_);
}

std::string AisrvActorResponse::GetTypeName() const {
  return "kaiwu_aisvr.AisrvActorResponse";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace kaiwu_aisvr
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::kaiwu_aisvr::AisrvActorRequest* Arena::CreateMaybeMessage< ::kaiwu_aisvr::AisrvActorRequest >(Arena* arena) {
  return Arena::CreateInternal< ::kaiwu_aisvr::AisrvActorRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::kaiwu_aisvr::AisrvActorResponse* Arena::CreateMaybeMessage< ::kaiwu_aisvr::AisrvActorResponse >(Arena* arena) {
  return Arena::CreateInternal< ::kaiwu_aisvr::AisrvActorResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
