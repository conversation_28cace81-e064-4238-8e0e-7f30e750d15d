# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: aisrv_actor_req_resp.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1a\x61isrv_actor_req_resp.proto\x12\x0bkaiwu_aisvr\x1a google/protobuf/descriptor.proto"\xbb\x01\n\x11\x41isrvActorRequest\x12\x11\n\tclient_id\x18\x01 \x02(\x05\x12\x12\n\ncompose_id\x18\x02 \x03(\x05\x12\x13\n\x0bsample_size\x18\x03 \x02(\x05\x12\x13\n\x0bobservation\x18\x04 \x03(\x02\x12\x14\n\x0clegal_action\x18\x05 \x03(\x02\x12\x17\n\x0fsub_action_mask\x18\x06 \x03(\x02\x12\x13\n\x0blstm_hidden\x18\x07 \x03(\x02\x12\x11\n\tlstm_cell\x18\x08 \x03(\x02"o\n\x12\x41isrvActorResponse\x12\x12\n\ncompose_id\x18\x01 \x03(\x05\x12\x15\n\rformat_action\x18\x02 \x03(\x02\x12\x1b\n\x13network_sample_info\x18\x03 \x03(\x02\x12\x11\n\tlstm_info\x18\x04 \x03(\x02\x42\x02H\x03'
)

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "aisrv_actor_req_resp_pb2", globals())
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"H\003"
    _AISRVACTORREQUEST._serialized_start = 78
    _AISRVACTORREQUEST._serialized_end = 265
    _AISRVACTORRESPONSE._serialized_start = 267
    _AISRVACTORRESPONSE._serialized_end = 378
# @@protoc_insertion_point(module_scope)
