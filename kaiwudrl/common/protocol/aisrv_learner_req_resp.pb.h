// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: aisrv_learner_req_resp.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_aisrv_5flearner_5freq_5fresp_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_aisrv_5flearner_5freq_5fresp_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/descriptor.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_aisrv_5flearner_5freq_5fresp_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_aisrv_5flearner_5freq_5fresp_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
namespace kaiwu_aisvr {
class AisrvLearnerRequest;
class AisrvLearnerRequestDefaultTypeInternal;
extern AisrvLearnerRequestDefaultTypeInternal _AisrvLearnerRequest_default_instance_;
}  // namespace kaiwu_aisvr
PROTOBUF_NAMESPACE_OPEN
template<> ::kaiwu_aisvr::AisrvLearnerRequest* Arena::CreateMaybeMessage<::kaiwu_aisvr::AisrvLearnerRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace kaiwu_aisvr {

// ===================================================================

class AisrvLearnerRequest :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:kaiwu_aisvr.AisrvLearnerRequest) */ {
 public:
  AisrvLearnerRequest();
  virtual ~AisrvLearnerRequest();

  AisrvLearnerRequest(const AisrvLearnerRequest& from);
  AisrvLearnerRequest(AisrvLearnerRequest&& from) noexcept
    : AisrvLearnerRequest() {
    *this = ::std::move(from);
  }

  inline AisrvLearnerRequest& operator=(const AisrvLearnerRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AisrvLearnerRequest& operator=(AisrvLearnerRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const AisrvLearnerRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AisrvLearnerRequest* internal_default_instance() {
    return reinterpret_cast<const AisrvLearnerRequest*>(
               &_AisrvLearnerRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AisrvLearnerRequest& a, AisrvLearnerRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AisrvLearnerRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AisrvLearnerRequest* New() const final {
    return CreateMaybeMessage<AisrvLearnerRequest>(nullptr);
  }

  AisrvLearnerRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AisrvLearnerRequest>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)
    final;
  void CopyFrom(const AisrvLearnerRequest& from);
  void MergeFrom(const AisrvLearnerRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  void DiscardUnknownFields();
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AisrvLearnerRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "kaiwu_aisvr.AisrvLearnerRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kBatchSizeFieldNumber = 2,
  };
  // repeated float data = 1;
  int data_size() const;
  void clear_data();
  float data(int index) const;
  void set_data(int index, float value);
  void add_data(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_data();

  // required int32 batch_size = 2;
  bool has_batch_size() const;
  void clear_batch_size();
  ::PROTOBUF_NAMESPACE_ID::int32 batch_size() const;
  void set_batch_size(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:kaiwu_aisvr.AisrvLearnerRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArenaLite _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > data_;
  ::PROTOBUF_NAMESPACE_ID::int32 batch_size_;
  friend struct ::TableStruct_aisrv_5flearner_5freq_5fresp_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AisrvLearnerRequest

// repeated float data = 1;
inline int AisrvLearnerRequest::data_size() const {
  return data_.size();
}
inline void AisrvLearnerRequest::clear_data() {
  data_.Clear();
}
inline float AisrvLearnerRequest::data(int index) const {
  // @@protoc_insertion_point(field_get:kaiwu_aisvr.AisrvLearnerRequest.data)
  return data_.Get(index);
}
inline void AisrvLearnerRequest::set_data(int index, float value) {
  data_.Set(index, value);
  // @@protoc_insertion_point(field_set:kaiwu_aisvr.AisrvLearnerRequest.data)
}
inline void AisrvLearnerRequest::add_data(float value) {
  data_.Add(value);
  // @@protoc_insertion_point(field_add:kaiwu_aisvr.AisrvLearnerRequest.data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AisrvLearnerRequest::data() const {
  // @@protoc_insertion_point(field_list:kaiwu_aisvr.AisrvLearnerRequest.data)
  return data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AisrvLearnerRequest::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:kaiwu_aisvr.AisrvLearnerRequest.data)
  return &data_;
}

// required int32 batch_size = 2;
inline bool AisrvLearnerRequest::has_batch_size() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AisrvLearnerRequest::clear_batch_size() {
  batch_size_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AisrvLearnerRequest::batch_size() const {
  // @@protoc_insertion_point(field_get:kaiwu_aisvr.AisrvLearnerRequest.batch_size)
  return batch_size_;
}
inline void AisrvLearnerRequest::set_batch_size(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000001u;
  batch_size_ = value;
  // @@protoc_insertion_point(field_set:kaiwu_aisvr.AisrvLearnerRequest.batch_size)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace kaiwu_aisvr

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_aisrv_5flearner_5freq_5fresp_2eproto
