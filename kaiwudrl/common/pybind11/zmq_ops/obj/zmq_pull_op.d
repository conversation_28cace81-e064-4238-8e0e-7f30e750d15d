obj/zmq_pull_op.o obj/zmq_pull_op.d: zmq_pull_op.cc \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/op.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/op_def_builder.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/op_def.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/port_def.inc \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/port_undef.inc \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/io/coded_stream.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/common.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/port.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/platform_macros.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/macros.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/port.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/arena.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/arena_impl.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/logging.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/arenastring.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/fastmem.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/generated_message_table_driven.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/generated_enum_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/message_lite.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/once.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/strutil.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/stringpiece.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/hash.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map_type_handler.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/parse_context.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/io/zero_copy_stream.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/implicit_weak_message.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/metadata_lite.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/generated_message_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/has_bits.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/wire_format_lite.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/repeated_field.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/casts.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map_entry_lite.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map_field_lite.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/inlined_string_field.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/metadata.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/unknown_field_set.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/generated_message_reflection.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/descriptor.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/mutex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/generated_enum_reflection.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/message.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/extension_set.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/attr_value.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map_entry.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/reflection_ops.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map_field_inl.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/map_field.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tensor.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/resource_handle.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tensor_shape.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/types.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/core/status.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/status.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/logging.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/platform.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/types.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/bfloat16.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/byte_order.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/Eigen/Core \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/Core \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/DisableStupidWarnings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/Macros.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/ConfigureVectorization.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/MKL_support.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/Constants.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/Meta.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/ForwardDeclarations.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/StaticAssert.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/XprHelper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/Memory.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/IntegralConstant.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/SymbolicIndex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/NumTraits.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/MathFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/GenericPacketMath.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/MathFunctionsImpl.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/ConjHelper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/Half.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/BFloat16.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/TypeCasting.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/SSE/PacketMath.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/SSE/Complex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX/PacketMath.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX/Complex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX512/Complex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/Settings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/functors/TernaryFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/functors/BinaryFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/functors/UnaryFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/functors/NullaryFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/functors/StlFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/functors/AssignmentFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/IndexedViewHelper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/ReshapedHelper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/ArithmeticSequence.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/IO.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/DenseCoeffsBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/DenseBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/CommonCwiseUnaryOps.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/BlockMethods.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/IndexedViewMethods.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/IndexedViewMethods.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/ReshapedMethods.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/ReshapedMethods.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/MatrixBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/CommonCwiseBinaryOps.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/MatrixCwiseUnaryOps.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/MatrixCwiseBinaryOps.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/EigenBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Product.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CoreEvaluators.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/AssignEvaluator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Assign.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/ArrayBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/ArrayCwiseUnaryOps.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/../plugins/ArrayCwiseBinaryOps.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/BlasUtil.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/DenseStorage.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/NestByValue.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/ReturnByValue.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/NoAlias.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/PlainObjectBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Matrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Array.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CwiseTernaryOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CwiseBinaryOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CwiseUnaryOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CwiseNullaryOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CwiseUnaryView.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Dot.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/StableNorm.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Stride.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/MapBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Map.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Ref.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Block.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/VectorBlock.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/IndexedView.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Reshaped.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Transpose.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/DiagonalMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Diagonal.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/DiagonalProduct.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Redux.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Visitor.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Fuzzy.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Swap.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CommaInitializer.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/GeneralProduct.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Solve.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Inverse.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/SolverBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/PermutationMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Transpositions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/TriangularMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/SelfAdjointView.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/Parallelizer.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/ProductEvaluators.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/GeneralMatrixVector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/SolveTriangular.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/SelfadjointProduct.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/TriangularMatrixVector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/products/TriangularSolverVector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/BandMatrix.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/CoreIterators.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/ConditionEstimator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/BooleanRedux.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Select.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/VectorwiseOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/PartialReduxEvaluator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Random.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Replicate.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/Reverse.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/ArrayWrapper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/StlIterators.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/GlobalFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/tstring.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/cord.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/cord.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/ctstring.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/ctstring_internal.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/string_view.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/config.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/options.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/policy_checks.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/throw_delegate.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/macros.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/attributes.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/optimization.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/port.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/integral_types.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/logging.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/log_severity.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/macros.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/stringpiece.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/protobuf/error_codes.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/core/stringpiece.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/op_def_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/api_def.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/protobuf.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/io/tokenizer.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/io/zero_copy_stream_impl_lite.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/callback.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/stl_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/descriptor.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/dynamic_message.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/text_format.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/util/json_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/util/type_resolver.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/type.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/any.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/any.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/source_context.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/status.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/stubs/bytestream.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/google/protobuf/util/type_resolver_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/selective_registration.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/registration_options.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/core/errors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/errors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/str_join.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/internal/str_join_internal.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/internal/ostringstream.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/internal/resize_uninitialized.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/meta/type_traits.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/str_cat.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/numbers.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/bits.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/endian.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/unaligned_access.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/numeric/int128.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/numeric/int128_have_intrinsic.inc \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/str_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/str_split.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/raw_logging.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/atomic_hook.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/internal/str_split_internal.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/strip.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/ascii.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/strings/match.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/strcat.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/numbers.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/strings/str_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/strings/strcat.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/mutex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/thread_annotations.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/mutex_data.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/mutex.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/op_kernel.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/allocator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/types/optional.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/utility/utility.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/inline_variable.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/identity.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/base/internal/invoke.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/types/bad_optional_access.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/types/internal/optional.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/memory/memory.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/numeric_types.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/Tensor \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/Tensor \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../../../Eigen/Core \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../SpecialFunctions \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../../../Eigen/Core \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../../../Eigen/src/Core/util/DisableStupidWarnings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/BesselFunctionsImpl.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/BesselFunctionsPacketMath.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/BesselFunctionsBFloat16.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/BesselFunctionsHalf.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/BesselFunctionsFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/BesselFunctionsArrayAPI.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/SpecialFunctionsImpl.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/SpecialFunctionsPacketMath.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/SpecialFunctionsBFloat16.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/SpecialFunctionsHalf.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/SpecialFunctionsFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../src/SpecialFunctions/SpecialFunctionsArrayAPI.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../../../Eigen/src/Core/util/ReenableStupidWarnings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../../../Eigen/src/Core/util/DisableStupidWarnings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/util/CXX11Meta.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/util/EmulateArray.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/util/CXX11Workarounds.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/util/MaxSizeVector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorMacros.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorForwardDeclarations.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorMeta.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorFunctors.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorCostModel.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDeviceDefault.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDeviceThreadPool.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDeviceGpu.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDeviceSycl.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorIndexList.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDimensionList.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDimensions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorInitializer.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorTraits.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorRandom.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorUInt128.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorIntDiv.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorGlobalFunctions.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorBase.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorBlock.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorEvaluator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorExpr.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorReduction.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorReductionGpu.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorArgMax.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorConcatenation.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorContractionMapper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorContractionBlocking.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorContraction.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorContractionThreadPool.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorContractionGpu.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorConversion.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorConvolution.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorFFT.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorPatch.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorImagePatch.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorVolumePatch.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorBroadcasting.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorChipping.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorInflation.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorLayoutSwap.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorMorphing.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorPadding.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorReverse.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorShuffling.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorStriding.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorCustomOp.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorEvalTo.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorForcedEval.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorGenerator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorAssign.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorScan.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorTrace.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorExecutor.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorDevice.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorStorage.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/Tensor.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorFixedSize.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorMap.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorRef.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/src/Tensor/TensorIO.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/unsupported/Eigen/CXX11/../../../Eigen/src/Core/util/ReenableStupidWarnings.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/FixedPoint \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/src/FixedPoint/FixedPointTypes.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/src/FixedPoint/PacketMathAVX512.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/src/FixedPoint/PacketMathAVX2.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/src/FixedPoint/TypeCastingAVX512.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/src/FixedPoint/MatMatProduct.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/third_party/eigen3/unsupported/Eigen/CXX11/src/FixedPoint/MatVecProduct.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/type_traits.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/numa.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/cancellation.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/core/notification.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/notification.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/notification.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/gtl/flatmap.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/gtl/flatrep.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/prefetch.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/hash/hash.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/hash.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/control_flow.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/device_base.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/device_attributes.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tensor.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tensor_shape.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/gtl/array_slice.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/types/span.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/types/internal/span.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/algorithm/algorithm.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/gtl/inlined_vector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/container/inlined_vector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/container/internal/inlined_vector.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/absl/container/internal/compressed_tuple.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tensor_types.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/types.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/bfloat16.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/resource_handle.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/tensor_coding.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/refcount.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/core/refcount.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/mem.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/dynamic_annotations.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/default/dynamic_annotations.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/graph.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/function.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/node_def.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/versions.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/kernel_def.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/kernel_def_builder.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/node_def_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/attr_value_util.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/partial_tensor_shape.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/util/padding.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/util/tensor_format.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/node_properties.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/op_requires.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/rendezvous.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/util/device_name_utils.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/session_state.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tracking_allocator.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/lib/gtl/manual_constructor.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/env.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/env_time.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/file_system.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/file_statistics.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/profile_utils/cpu_utils.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/platform/profile_utils/i_cpu_utils_helper.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/protobuf/config.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/cost_graph.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/step_stats.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/allocation_description.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/tensor_description.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/protobuf/cluster.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/protobuf/debug.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/protobuf/rewriter_config.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/protobuf/verifier_config.pb.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/resource_op_kernel.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/resource_mgr.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/common_shape_fns.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/shape_inference.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/type_index.h \
 /root/anaconda3/envs/kaiwu/lib/python3.7/site-packages/tensorflow/include/tensorflow/core/framework/variant_tensor_data.h \
 zmq_conn.h zmq.hpp
