{"time": "2025-08-05 05:52:47.977318", "level": "INFO", "message": "learner train process start at pid is 16167", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:52:48.024641", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:52:48.037301", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:52:48.752797", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 16290", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 16290}
{"time": "2025-08-05 05:53:52.199978", "level": "INFO", "message": "learner policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.238077", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "691", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.266021", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-0.pkl successfully (episode: 0)", "file": "agent.py", "line": "302", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.562343", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/back_to_the_realm_v2_ppo/kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz key model.ckpt_back_to_the_realm_v2_ppo_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.563446", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "723", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.567490", "level": "INFO", "message": "learner train process start success at 16167, on-policy/off-policy is off-policy, ppo trainer global step -1.0, load app back_to_the_realm_v2 algo ppo model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "756", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.581490", "level": "INFO", "message": "learner train process now input ready size is 0", "file": "on_policy_trainer.py", "line": "513", "module": "learner", "process": "on_policy_trainer", "function": "get_training_metrics_dicts", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.583437", "level": "INFO", "message": "learner train process now train count is 0, global step is 0, train once cost time is 0 ms, filter sample count is 0, replay buffer monitor is None", "file": "on_policy_trainer.py", "line": "514", "module": "learner", "process": "on_policy_trainer", "function": "get_training_metrics_dicts", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.690600", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "57", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:53:52.886993", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 17269}
{"time": "2025-08-05 05:53:52.891596", "level": "INFO", "message": "model_file_sync process pid is 17269", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 17269}
{"time": "2025-08-05 05:53:52.893250", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 17269}
{"time": "2025-08-05 05:53:52.895702", "level": "INFO", "message": "learner ppid is 138048092374848", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 17269}
{"time": "2025-08-05 05:53:53.512377", "level": "INFO", "message": "learner ppid is 138048092374848", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 17274}
{"time": "2025-08-05 05:53:53.515617", "level": "INFO", "message": "model_file_save process start success at pid 17274", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 17274}
{"time": "2025-08-05 05:56:16.526051", "level": "INFO", "message": "learner train_step is 0, so not save_model", "file": "standard_model_wrapper_pytorch.py", "line": "238", "module": "learner", "process": "standard_model_wrapper_pytorch", "function": "save_param_detail", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:56:16.527530", "level": "INFO", "message": "learner train learner save_param_by_framework is success, ip is 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "974", "module": "learner", "process": "on_policy_trainer", "function": "save_model_detail", "stack": "", "pid": 16167}
{"time": "2025-08-05 05:56:16.528745", "level": "INFO", "message": "learner train learner really save_model from aisrv 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "1055", "module": "learner", "process": "on_policy_trainer", "function": "learner_process_message_by_aisrv", "stack": "", "pid": 16167}
