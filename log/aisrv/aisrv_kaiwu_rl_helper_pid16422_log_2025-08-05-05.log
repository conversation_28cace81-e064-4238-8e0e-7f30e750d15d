{"time": "2025-08-05 05:52:59.184826", "level": "INFO", "message": "aisrv kaiwu_rl_helper start at pid 16422, ppid is 138048092374848, thread id is -1", "file": "kaiwu_rl_helper_standard.py", "line": "134", "module": "aisrv", "process": "kaiwu_rl_helper_standard", "function": "__init__", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:52:59.188045", "level": "INFO", "message": "aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRLStandardHelper(kaiwu_rl_helper_0, initial daemon)>", "file": "aisrv_server_standard.py", "line": "794", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:52:59.190142", "level": "INFO", "message": "aisrv aisrvhandle established connect to 172.20.0.4:5566, slot id is 0, min_slot_id is 0", "file": "aisrv_server_standard.py", "line": "797", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:52:59.192703", "level": "INFO", "message": "aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['127.0.0.1:9999']", "file": "aisrv_server_standard.py", "line": "806", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:52:59.194837", "level": "INFO", "message": "aisrv aisrvhandle start success at pid 16422", "file": "aisrv_server_standard.py", "line": "837", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:54:41.848148", "level": "INFO", "message": "aisrv env proxy, zmq_client connect to 172.20.0.4:5566 success, client_id 24041725", "file": "env_proxy_lazy.py", "line": "53", "module": "aisrv", "process": "env_proxy_lazy", "function": "__init__", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:54:41.852218", "level": "INFO", "message": "aisrv kaiwu_rl_helper start agent 0 with train_one", "file": "kaiwu_rl_helper_standard.py", "line": "1018", "module": "aisrv", "process": "kaiwu_rl_helper_standard", "function": "start_agent", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:54:41.962381", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0.0, 'sample_production_and_consumption_ratio': 0.0, 'episode_cnt': 0, 'sample_receive_cnt': 0.0, 'predict_succ_cnt': 0.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0, 'value_loss': 11.95, 'policy_loss': 6.01, 'entropy_loss': 2.34}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "104", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:54:42.195711", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:56:16.516441", "level": "WARNING", "message": "aisrv EnvProxy step timeout, so return", "file": "env_proxy_lazy.py", "line": "123", "module": "aisrv", "process": "env_proxy_lazy", "function": "step", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:56:16.517391", "level": "WARNING", "message": "aisrv _extra_info.result_code is 2,                         _extra_info.result_message is env proxy step timeout, so return", "file": "train_workflow.py", "line": "147", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:56:16.521917", "level": "INFO", "message": "aisrv Latest model saved at episode 0", "file": "train_workflow.py", "line": "64", "module": "aisrv", "process": "train_workflow", "function": "workflow", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:56:16.533772", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0.0, 'sample_production_and_consumption_ratio': 0.0, 'episode_cnt': 0, 'sample_receive_cnt': 0.0, 'predict_succ_cnt': 1.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0, 'value_loss': 11.95, 'policy_loss': 6.01, 'entropy_loss': 2.34}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "104", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:56:16.537591", "level": "INFO", "message": "aisrv env proxy, zmq_client reconnect to 172.20.0.4:5566 success, client_id 59153405", "file": "env_proxy_lazy.py", "line": "63", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:56:16.638016", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:57:19.480872", "level": "WARNING", "message": "aisrv EnvProxy step timeout, so return", "file": "env_proxy_lazy.py", "line": "123", "module": "aisrv", "process": "env_proxy_lazy", "function": "step", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:57:19.482221", "level": "WARNING", "message": "aisrv _extra_info.result_code is 2,                         _extra_info.result_message is env proxy step timeout, so return", "file": "train_workflow.py", "line": "147", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:57:19.489804", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0.0, 'sample_production_and_consumption_ratio': 0.0, 'episode_cnt': 0, 'sample_receive_cnt': 0.0, 'predict_succ_cnt': 2.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0, 'value_loss': 11.95, 'policy_loss': 6.01, 'entropy_loss': 2.34}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "104", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:57:19.491946", "level": "INFO", "message": "aisrv env proxy, zmq_client reconnect to 172.20.0.4:5566 success, client_id 17968125", "file": "env_proxy_lazy.py", "line": "63", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:57:19.582909", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:00.318360", "level": "WARNING", "message": "aisrv EnvProxy step timeout, so return", "file": "env_proxy_lazy.py", "line": "123", "module": "aisrv", "process": "env_proxy_lazy", "function": "step", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:00.319497", "level": "WARNING", "message": "aisrv _extra_info.result_code is 2,                         _extra_info.result_message is env proxy step timeout, so return", "file": "train_workflow.py", "line": "147", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:00.328889", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0.0, 'sample_production_and_consumption_ratio': 0.0, 'episode_cnt': 0, 'sample_receive_cnt': 0.0, 'predict_succ_cnt': 3.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0, 'value_loss': 11.95, 'policy_loss': 6.01, 'entropy_loss': 2.34}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "104", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:00.333910", "level": "INFO", "message": "aisrv env proxy, zmq_client reconnect to 172.20.0.4:5566 success, client_id 66967805", "file": "env_proxy_lazy.py", "line": "63", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:00.406345", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:54.677630", "level": "WARNING", "message": "aisrv EnvProxy step timeout, so return", "file": "env_proxy_lazy.py", "line": "123", "module": "aisrv", "process": "env_proxy_lazy", "function": "step", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:54.678907", "level": "WARNING", "message": "aisrv _extra_info.result_code is 2,                         _extra_info.result_message is env proxy step timeout, so return", "file": "train_workflow.py", "line": "147", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:54.685566", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0.0, 'sample_production_and_consumption_ratio': 0.0, 'episode_cnt': 0, 'sample_receive_cnt': 0.0, 'predict_succ_cnt': 4.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0, 'value_loss': 11.95, 'policy_loss': 6.01, 'entropy_loss': 2.34}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "104", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:54.687836", "level": "INFO", "message": "aisrv env proxy, zmq_client reconnect to 172.20.0.4:5566 success, client_id 32914685", "file": "env_proxy_lazy.py", "line": "63", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 05:59:54.764156", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 06:00:52.029931", "level": "WARNING", "message": "aisrv EnvProxy step timeout, so return", "file": "env_proxy_lazy.py", "line": "123", "module": "aisrv", "process": "env_proxy_lazy", "function": "step", "stack": "", "pid": 16422}
{"time": "2025-08-05 06:00:52.031019", "level": "WARNING", "message": "aisrv _extra_info.result_code is 2,                         _extra_info.result_message is env proxy step timeout, so return", "file": "train_workflow.py", "line": "147", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 06:00:52.036484", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0.0, 'sample_production_and_consumption_ratio': 0.0, 'episode_cnt': 0, 'sample_receive_cnt': 0.0, 'predict_succ_cnt': 4.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0, 'value_loss': 11.95, 'policy_loss': 6.01, 'entropy_loss': 2.34}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "104", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 16422}
{"time": "2025-08-05 06:00:52.038582", "level": "INFO", "message": "aisrv env proxy, zmq_client reconnect to 172.20.0.4:5566 success, client_id 18958845", "file": "env_proxy_lazy.py", "line": "63", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
{"time": "2025-08-05 06:00:52.107477", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 16422}
