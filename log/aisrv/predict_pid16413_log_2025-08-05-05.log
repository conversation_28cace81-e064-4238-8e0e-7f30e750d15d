{"time": "2025-08-05 05:52:59.834974", "level": "INFO", "message": "aisrv monitor_proxy process start success at pid is 16463", "file": "monitor_proxy_process.py", "line": "73", "module": "aisrv", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 16463}
{"time": "2025-08-05 05:53:58.554725", "level": "INFO", "message": "aisrv policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "aisrv", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:53:58.558172", "level": "INFO", "message": "aisrv predict just no model_pool files or copy_model_files failed, please check", "file": "multi_model_common.py", "line": "240", "module": "aisrv", "process": "multi_model_common", "function": "init_load_models", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:53:58.559294", "level": "INFO", "message": "predict policy_name: train_one, start success at pid 16413, on-policy/off-policy is off-policy, actor_receive_cost_time_ms: 1, predict_batch_size: 1", "file": "actor_proxy_local.py", "line": "663", "module": "aisrv", "process": "actor_proxy_local", "function": "before_run", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:53:58.562387", "level": "INFO", "message": "aisrv predict now predict count is 0", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:54:42.204030", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:55:33.619878", "level": "INFO", "message": "aisrv predict now predict count is 1", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:56:16.643412", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:57:06.666498", "level": "INFO", "message": "aisrv predict now predict count is 2", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:57:19.586671", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:58:39.891535", "level": "INFO", "message": "aisrv predict now predict count is 3", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:59:00.409409", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:59:44.670690", "level": "INFO", "message": "aisrv predict now predict count is 4", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 16413}
{"time": "2025-08-05 05:59:54.767632", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 16413}
{"time": "2025-08-05 06:00:52.111557", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 16413}
{"time": "2025-08-05 06:00:52.112974", "level": "INFO", "message": "aisrv predict now predict count is 5", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 16413}
