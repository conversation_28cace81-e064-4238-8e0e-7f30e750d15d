{"time": "2025-08-05 05:53:00.437956", "level": "INFO", "message": "aisrv learner_proxy reverb client Client, server_address=127.0.0.1:9999 127.0.0.1:9999 connect to reverb server", "file": "reverb_util.py", "line": "22", "module": "aisrv", "process": "reverb_util", "function": "__init__", "stack": "", "pid": 16415}
{"time": "2025-08-05 05:53:00.439817", "level": "INFO", "message": "learner_proxy send reverb server use reverb, tables is ['reverb_replay_buffer_table_0']", "file": "learner_proxy.py", "line": "118", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 16415}
{"time": "2025-08-05 05:53:00.442787", "level": "INFO", "message": "learner_proxy zmq client connect at 127.0.0.1:9997 with client_id 74053629", "file": "learner_proxy.py", "line": "155", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 16415}
{"time": "2025-08-05 05:53:00.461443", "level": "INFO", "message": "learner_proxy policy_name: train_one, start success at pid 16415", "file": "learner_proxy.py", "line": "174", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 16415}
{"time": "2025-08-05 05:56:16.523775", "level": "INFO", "message": "learner_proxy send save_model data to learner", "file": "learner_proxy.py", "line": "253", "module": "aisrv", "process": "learner_proxy", "function": "run_once", "stack": "", "pid": 16415}
{"time": "2025-08-05 05:56:16.526229", "level": "INFO", "message": "learner_proxy recv save_model data result from learner success", "file": "learner_proxy.py", "line": "264", "module": "aisrv", "process": "learner_proxy", "function": "run_once", "stack": "", "pid": 16415}
{"time": "2025-08-05 05:56:16.533963", "level": "INFO", "message": "learner_proxy send reverb server stat, succ_cnt is 0, error_cnt is 0", "file": "learner_proxy.py", "line": "212", "module": "aisrv", "process": "learner_proxy", "function": "sample_server_stat", "stack": "", "pid": 16415}
