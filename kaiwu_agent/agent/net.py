import torch
import numpy as np
from torch import nn
import torch.nn.functional as F


class Net(nn.Module):
    def __init__(self, layer_num, state_shape, action_shape=0, device='cpu',
                 softmax=False):
        super().__init__()
        self.device = device
        self.layers = [
            nn.Linear(np.prod(state_shape), 128),
            nn.ReLU(inplace=True)]
        for i in range(layer_num):
            self.layers += [nn.Linear(128, 128), nn.ReLU(inplace=True)]
        if action_shape:
            self.layers += [nn.Linear(128, np.prod(action_shape))]
        if softmax:
            self.layers += [nn.Softmax(dim=-1)]
        self.model = nn.Sequential(*self.layers)

    def forward(self, s, state=None, info={}):
        if not isinstance(s, torch.Tensor):
            s = torch.tensor(s, device=self.device, dtype=torch.float)
        batch = s.shape[0]
        s = s.view(batch, -1)
        logits = self.model(s)
        return logits, state


class ActorCriticNet(nn.Module):
    def __init__(self, preprocess_net, action_shape):
        super().__init__()
        self.preprocess = preprocess_net
        self.policy_layer = nn.Linear(128, np.prod(action_shape))
        self.value_layer = nn.Linear(128, 1)

    def forward(self, s, state=None, info={}):
        _mid, h = self.preprocess(s, state)
        logits = F.softmax(self.policy_layer(_mid), dim=-1)
        value = self.value_layer(_mid)
        return logits, value