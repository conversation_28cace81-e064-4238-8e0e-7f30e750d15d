[main_system]
alloc_process_address = "*************:8080"
prometheus_user = 1258344700
prometheus_pwd = ""
prometheus_pushgateway = "prometheus-pushgateway:9091"
alloc_process_assign_limit_learner = 10000
alloc_process_assign_limit_actor = 10000
alloc_process_assign_limit_aisrv = 10000
use_prometheus = true
push_to_cos = false
use_alloc = true
rotation = "100MB"
encoding = "utf-8"
compression = "zip"
retention = "10 days"
serialize = false
level = "INFO"
log_dir = "/data/projects/Metagent/log"
sock_buff_size = 31457280
socket_timeout = 5
backlog_size = 1024
socket_retry_times = 100
tcp_keep_alive = 1
tcp_keep_alive_idle = 60
tcp_keep_alive_intvl = 1
zmq_ops_hwm = 1024
zmq_io_threads = 2
idle_sleep_second = 0.001
idle_sleep_count = 5
alloc_process_per_seconds = 15
alloc_process_role = 7
alloc_process_assign_limit = 1
cos_local_target_dir = "/data/cos_local_target_dir/"
cos_local_keep_file_num = 10
cos_secret_id = "cos_secret_id"
cos_secret_key = "cos_secret_key"
cos_bucket = "dataservice-use-1252931805"
cos_region = "ap-nanjing"
cos_token = "cos_token"
prometheus_stat_per_minutes = 1
prometheus_instance = "kaiwu-drl"
prometheus_db = "kaiwu-drl"
use_rainbow = true
rainbow_url = "api.rainbow.oa.com:8080"
rainbow_app_id = "02e8fe72-db77-4007-bc38-5d383b9e2b66"
rainbow_user_id = "rainbow_user_id"
rainbow_secret_key = "rainbow_secret_key"
rainbow_activate_per_minutes = 10
rainbow_group = "main_system"
rainbow_env_name = "gorge_walk_dev"
use_compress_decompress = true
compress_decompress_algorithms = "lz4"
lz4_uncompressed_size = 3145728
lz4_learner_uncompressed_size = 314572800
redis_host = "redis_host"
redis_port = 6379
queue_size = 1024
set_name = "gorge_walk_set1"
task_id = "uuid"
port = 5566
svr_name = "arena"
